<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748060811445" clover="3.2.0">
  <project timestamp="1748060811445" name="All files">
    <metrics statements="1810" coveredstatements="52" conditionals="523" coveredconditionals="7" methods="338" coveredmethods="10" elements="2671" coveredelements="69" complexity="0" loc="1810" ncloc="1810" packages="22" files="67" classes="67"/>
    <package name="src">
      <metrics statements="23" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="index.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\index.ts">
        <metrics statements="22" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
      <file name="types.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\types.ts">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.api">
      <metrics statements="36" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="server.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\api\server.ts">
        <metrics statements="36" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.api.routes">
      <metrics statements="50" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="26" coveredmethods="0"/>
      <file name="dataSourceRoutes.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\api\routes\dataSourceRoutes.ts">
        <metrics statements="13" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\api\routes\index.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
      </file>
      <file name="nodeRoutes.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\api\routes\nodeRoutes.ts">
        <metrics statements="13" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
      </file>
      <file name="workflowRoutes.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\api\routes\workflowRoutes.ts">
        <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.config">
      <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
      <file name="index.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\config\index.ts">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.core.errors">
      <metrics statements="208" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="41" coveredmethods="0"/>
      <file name="ApplicationError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\ApplicationError.ts">
        <metrics statements="11" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="43" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
      </file>
      <file name="DatabaseError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\DatabaseError.ts">
        <metrics statements="16" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
      </file>
      <file name="ErrorCodes.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\ErrorCodes.ts">
        <metrics statements="47" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="4" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
      <file name="ErrorHandler.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\ErrorHandler.ts">
        <metrics statements="42" coveredstatements="0" conditionals="41" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="139" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="9"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="190" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="195" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="200" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
      </file>
      <file name="JSONRPCError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\JSONRPCError.ts">
        <metrics statements="5" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="11" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
      </file>
      <file name="MCPError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\MCPError.ts">
        <metrics statements="20" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="4" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
      <file name="NetworkError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\NetworkError.ts">
        <metrics statements="16" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
      </file>
      <file name="NodeError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\NodeError.ts">
        <metrics statements="13" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
      </file>
      <file name="ValidationError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\ValidationError.ts">
        <metrics statements="10" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
      <file name="WorkflowError.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\WorkflowError.ts">
        <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\core\errors\index.ts">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.functions">
      <metrics statements="14" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="EchoFunction.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\functions\EchoFunction.ts">
        <metrics statements="14" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.infrastructure.database">
      <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="config.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\config.ts">
        <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.infrastructure.database.entities">
      <metrics statements="97" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="DataSource.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\DataSource.entity.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
      </file>
      <file name="MCPFunction.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\MCPFunction.entity.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
      <file name="NodeConfig.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\NodeConfig.entity.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
      </file>
      <file name="NodeMetrics.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\NodeMetrics.entity.ts">
        <metrics statements="13" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
      </file>
      <file name="Workflow.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\Workflow.entity.ts">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
      </file>
      <file name="WorkflowExecution.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\WorkflowExecution.entity.ts">
        <metrics statements="14" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
      </file>
      <file name="WorkflowMetrics.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\WorkflowMetrics.entity.ts">
        <metrics statements="12" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
      </file>
      <file name="WorkflowNodeExecution.entity.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\entities\WorkflowNodeExecution.entity.ts">
        <metrics statements="16" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.infrastructure.database.migrations">
      <metrics statements="65" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="14" coveredmethods="0"/>
      <file name="1621500000000-CreateMCPFunctionsTable.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1621500000000-CreateMCPFunctionsTable.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
      </file>
      <file name="1621500000001-CreateWorkflowTables.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1621500000001-CreateWorkflowTables.ts">
        <metrics statements="19" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
      </file>
      <file name="1716559000000-CreateMetricsTables.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1716559000000-CreateMetricsTables.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
      </file>
      <file name="1716560000000-AlterNodeMetricsTable.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1716560000000-AlterNodeMetricsTable.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
      </file>
      <file name="1716561000000-AddMetricsTimeIndexes.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1716561000000-AddMetricsTimeIndexes.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
      </file>
      <file name="1716562000000-AddExecutionTimeIndexes.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1716562000000-AddExecutionTimeIndexes.ts">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
      </file>
      <file name="1716600000000-CreateNodeConfigAndDataSourceTables.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\database\migrations\1716600000000-CreateNodeConfigAndDataSourceTables.ts">
        <metrics statements="15" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.infrastructure.redis">
      <metrics statements="80" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="RedisClient.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\redis\RedisClient.ts">
        <metrics statements="80" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="183" count="0" type="stmt"/>
        <line num="193" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="205" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="208" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="230" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.infrastructure.script-engine">
      <metrics statements="17" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
      <file name="VM2ScriptEngine.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\infrastructure\script-engine\VM2ScriptEngine.ts">
        <metrics statements="17" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.mcp.protocol">
      <metrics statements="106" coveredstatements="0" conditionals="41" coveredconditionals="0" methods="15" coveredmethods="0"/>
      <file name="MCPServer.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\mcp\protocol\MCPServer.ts">
        <metrics statements="46" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
      </file>
      <file name="MessageRouter.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\mcp\protocol\MessageRouter.ts">
        <metrics statements="60" coveredstatements="0" conditionals="39" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="136" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="159" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="199" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.mcp.sdk">
      <metrics statements="23" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="8" coveredmethods="0"/>
      <file name="CustomMcpServer.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\mcp\sdk\CustomMcpServer.ts">
        <metrics statements="23" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.mcp.tools">
      <metrics statements="28" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="BaseMCPTool.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\mcp\tools\BaseMCPTool.ts">
        <metrics statements="9" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
      </file>
      <file name="JavaScriptTool.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\mcp\tools\JavaScriptTool.ts">
        <metrics statements="19" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.server">
      <metrics statements="248" coveredstatements="19" conditionals="66" coveredconditionals="0" methods="44" coveredmethods="3"/>
      <file name="DynamicMcpServer.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\server\DynamicMcpServer.ts">
        <metrics statements="53" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
      </file>
      <file name="MCPServer.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\server\MCPServer.ts">
        <metrics statements="70" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="159" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="171" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
      </file>
      <file name="MessageRouter.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\server\MessageRouter.ts">
        <metrics statements="68" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="70" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="109" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="119" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="136" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="207" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\server\index.ts">
        <metrics statements="57" coveredstatements="19" conditionals="6" coveredconditionals="0" methods="13" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="102" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="66" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="DynamicServiceLoader.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\services\DynamicServiceLoader.ts">
        <metrics statements="66" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="199" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.utils">
      <metrics statements="56" coveredstatements="29" conditionals="34" coveredconditionals="7" methods="18" coveredmethods="6"/>
      <file name="PinoLogger.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\utils\PinoLogger.ts">
        <metrics statements="13" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
      </file>
      <file name="env.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\utils\env.ts">
        <metrics statements="21" coveredstatements="17" conditionals="5" coveredconditionals="3" methods="3" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="5" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="9" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="24" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="25" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="26" count="4" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="4" type="stmt"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="5" type="stmt"/>
        <line num="40" count="5" type="stmt"/>
        <line num="41" count="5" type="cond" truecount="0" falsecount="1"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="5" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="logger.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\utils\logger.ts">
        <metrics statements="22" coveredstatements="12" conditionals="13" coveredconditionals="4" methods="7" coveredmethods="4"/>
        <line num="3" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="18" count="1" type="cond" truecount="1" falsecount="4"/>
        <line num="20" count="1" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="34" count="0" type="stmt"/>
        <line num="39" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="40" count="1" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.workflow.engine">
      <metrics statements="47" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="RedisWorkflowMemory.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\engine\RedisWorkflowMemory.ts">
        <metrics statements="38" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
      </file>
      <file name="WorkflowMemory.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\engine\WorkflowMemory.ts">
        <metrics statements="9" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.workflow.nodes">
      <metrics statements="295" coveredstatements="0" conditionals="176" coveredconditionals="0" methods="52" coveredmethods="0"/>
      <file name="BaseNode.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\BaseNode.ts">
        <metrics statements="6" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="38" count="0" type="stmt"/>
      </file>
      <file name="HTTPNode.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\HTTPNode.ts">
        <metrics statements="52" coveredstatements="0" conditionals="35" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="122" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
      </file>
      <file name="JavaScriptNode.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\JavaScriptNode.ts">
        <metrics statements="19" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
      </file>
      <file name="LiteLLMNode.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\LiteLLMNode.ts">
        <metrics statements="86" coveredstatements="0" conditionals="76" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="108" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="215" count="0" type="stmt"/>
        <line num="218" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="225" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
      </file>
      <file name="LiteLLMNodeFactory.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\LiteLLMNodeFactory.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
      </file>
      <file name="NodeRegistry.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\NodeRegistry.ts">
        <metrics statements="9" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
      </file>
      <file name="RedisNode.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\RedisNode.ts">
        <metrics statements="55" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="88" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="162" count="0" type="stmt"/>
      </file>
      <file name="RedisNodeFactory.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\RedisNodeFactory.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
      <file name="SQLNode.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\SQLNode.ts">
        <metrics statements="48" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="140" count="0" type="stmt"/>
      </file>
      <file name="SQLNodeFactory.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\nodes\SQLNodeFactory.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.workflow.observability">
      <metrics statements="178" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="17" coveredmethods="0"/>
      <file name="ObservabilityManager.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\observability\ObservabilityManager.ts">
        <metrics statements="178" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="197" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="208" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="286" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="331" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="344" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="371" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="389" count="0" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="393" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="394" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="434" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="457" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="458" count="0" type="stmt"/>
        <line num="463" count="0" type="stmt"/>
        <line num="473" count="0" type="stmt"/>
        <line num="476" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="528" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="536" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="543" count="0" type="stmt"/>
        <line num="546" count="0" type="stmt"/>
        <line num="547" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="548" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="590" count="0" type="stmt"/>
        <line num="596" count="0" type="stmt"/>
        <line num="602" count="0" type="stmt"/>
        <line num="604" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="611" count="0" type="stmt"/>
        <line num="612" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="618" count="0" type="stmt"/>
        <line num="619" count="0" type="stmt"/>
        <line num="620" count="0" type="stmt"/>
        <line num="621" count="0" type="stmt"/>
        <line num="624" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="647" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="664" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.workflow.retry">
      <metrics statements="129" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="25" coveredmethods="0"/>
      <file name="CircuitBreaker.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\retry\CircuitBreaker.ts">
        <metrics statements="87" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="186" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="200" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="276" count="0" type="stmt"/>
      </file>
      <file name="RetryPolicy.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\retry\RetryPolicy.ts">
        <metrics statements="42" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="121" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="153" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="167" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="177" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.workflow.tools">
      <metrics statements="21" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="WorkflowBasedTool.ts" path="C:\NTT\SVN\TO2\__O2-gitlab\aidcc-ccczautomationmcpserver\src\workflow\tools\WorkflowBasedTool.ts">
        <metrics statements="21" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
