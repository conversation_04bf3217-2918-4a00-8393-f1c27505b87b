
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>29/58</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.58% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>7/34</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">33.33% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/18</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">51.78% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>29/56</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="PinoLogger.ts"><a href="PinoLogger.ts.html">PinoLogger.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="15" class="abs low">0/15</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="16" class="abs low">0/16</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	</tr>

<tr>
	<td class="file high" data-value="env.ts"><a href="env.ts.html">env.ts</a></td>
	<td data-value="80.95" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 80%"></div><div class="cover-empty" style="width: 20%"></div></div>
	</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="5" class="abs medium">3/5</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	</tr>

<tr>
	<td class="file medium" data-value="logger.ts"><a href="logger.ts.html">logger.ts</a></td>
	<td data-value="54.54" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="22" class="abs medium">12/22</td>
	<td data-value="30.76" class="pct low">30.76%</td>
	<td data-value="13" class="abs low">4/13</td>
	<td data-value="57.14" class="pct medium">57.14%</td>
	<td data-value="7" class="abs medium">4/7</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="22" class="abs medium">12/22</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-24T04:26:50.900Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    