# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.
# ---------------------------------------------------------------
# Source: https://network.git.cz.o2/ntwcl/gitopsntw/-/tree/main/templates/repository/gitops/.gitlab-ci.yml
# Examples of extending this CI/CD configuration: https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_CICD_MODS.md
# ---------------------------------------------------------------
include:
  - project: 'ntwcl/gitopsntw'
    ref: HEAD
    file:
      - '/templates/cicd/gitlab-ci-deployment_form.yml'
      - '/templates/cicd/gitlab-ci-deployment.yml'
# ---------------------------------------------------------------

stages:
  - gitops:deployment
  - preparation
  - test
  - build
  - deploy
  - versioning

variables:
  NODEJS_IMAGE: node:22-bullseye

## PREPARE
create release:
  image: $NODEJS_IMAGE
  stage: deploy
  tags:
    - baremetaldind
  script:
    - echo "$O2_CA" > custom_ca.crt
    - cp *.crt /usr/local/share/ca-certificates/
    - update-ca-certificates
    - export NODE_EXTRA_CA_CERTS=/etc/ssl/certs/ca-certificates.crt
    - yarn install --frozen-lockfile
    - git config --global http.sslVerify false # we dont have ca root cert
    - export NODE_OPTIONS=--use-openssl-ca
    - yarn semantic-release
    - echo "Version bumped to $(<version)."
  rules:
    - if: $CI_PIPELINE_SOURCE == 'web' && ($ACTION != '-- select --' || $CLUSTER_NAME != '-- select --')
      when: never
    - if: '$CI_PROJECT_NAME != "aidcc-ccczautomationmcpserver"'
      when: manual
    - if: '$CI_COMMIT_MESSAGE =~ /\[release\]/'
      when: never
    - if: '$CI_COMMIT_MESSAGE =~ /^chore\(release\):/'
      when: never
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: always
  needs: []

## DOCKER BUILD & PUSH
### Building backend docker image and pushing to local gitlab repository
build backend docker image and push:
  stage: deploy
  tags:
    - shell
  before_script:
    - export CURRENT_VERSION_ID="$(<version)"
    - echo ${CI_REGISTRY_PASSWORD} | docker login -u ${CI_REGISTRY_USER} ${CI_REGISTRY} --password-stdin
  script:
    - docker build --pull -t ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID} -f Dockerfile .
    - docker push ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
    - docker rmi ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
  needs:
    - job: 'create release'
      optional: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'web' && ($ACTION != '-- select --' || $CLUSTER_NAME != '-- select --')
      when: never
    - if: '$CI_COMMIT_TAG'
      when: always
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: manual

### Building frontend docker image and pushing to local gitlab repository
build frontend docker image and push:
  stage: deploy
  tags:
    - shell
  before_script:
    - export CURRENT_VERSION_ID="$(<version)"
    - echo ${CI_REGISTRY_PASSWORD} | docker login -u ${CI_REGISTRY_USER} ${CI_REGISTRY} --password-stdin
  script:
    - docker build --pull -t ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID} -f frontend/Dockerfile ./frontend
    - docker push ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID}
    - docker rmi ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID}
  needs:
    - job: 'create release'
      optional: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'web' && ($ACTION != '-- select --' || $CLUSTER_NAME != '-- select --')
      when: never
    - if: '$CI_COMMIT_TAG'
      when: always
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: manual

## Pull Request checks
### Run backend typecheck
backend typecheck:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - yarn install --frozen-lockfile
    - yarn build
  allow_failure: false
  only:
    - merge_requests

### Run frontend typecheck
frontend typecheck:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - cd frontend
    - yarn install --frozen-lockfile
    - yarn build
  allow_failure: false
  only:
    - merge_requests

## run backend lint
backend lint:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - yarn install --frozen-lockfile
    - yarn lint
  allow_failure: false
  only:
    - merge_requests

## run frontend lint
frontend lint:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - cd frontend
    - yarn install --frozen-lockfile
    - yarn lint
  allow_failure: false
  only:
    - merge_requests

## run backend tests
backend test:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - yarn install --frozen-lockfile
    - yarn test
  allow_failure: false
  only:
    - merge_requests
