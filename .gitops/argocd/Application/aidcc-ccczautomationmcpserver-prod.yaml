apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: aidcc-ccczautomationmcpserver-prod
  namespace: argocd
  labels:
    framework: None
    environment: production
spec:
  project: aidcc
  source:
    path: charts/ccczautomationmcpserver
    repoURL: https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver.git
    targetRevision:
    helm:
      valueFiles:
      parameters:
  destination:
    name: ''
    namespace: aidcc-ccczautomationmcpserver-prod
    server: 'https://kubernetes.default.svc'
  syncPolicy:
    automated:
  info:
  - name: "Application human-readable name"
    value: "URL to the GUI endpoint"
