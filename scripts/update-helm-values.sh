#!/bin/bash

# <PERSON>ript to update Helm values with new version
# Usage: ./scripts/update-helm-values.sh <version>

if [ -z "$1" ]; then
    echo "Usage: $0 <version>"
    exit 1
fi

VERSION=$1

echo "Updating Helm values with version: $VERSION"

# Update dev values
sed -i "s/tag: '[^']*'/tag: '$VERSION'/g" charts/ccczautomationmcpserver/values-dev.yaml

# Update prod values  
sed -i "s/tag: '[^']*'/tag: '$VERSION'/g" charts/ccczautomationmcpserver/values-prod.yaml

# Update Chart.yaml
sed -i "s/appVersion: .*/appVersion: $VERSION/g" charts/ccczautomationmcpserver/Chart.yaml

echo "Helm values updated successfully"
