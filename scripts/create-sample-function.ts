import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { typeOrmConfig } from '../src/infrastructure/database/config';
import { MCPFunction } from '../src/infrastructure/database/entities/MCPFunction.entity';

/**
 * Create a sample MCP function in the database
 */
async function createSampleFunction(): Promise<void> {
  console.log('Creating sample MCP function...');

  // Create a data source
  const dataSource = new DataSource(typeOrmConfig);

  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection initialized');

    // Create a repository
    const repository = dataSource.getRepository(MCPFunction);

    // Create a sample function
    const sampleFunction = repository.create({
      name: 'getDateTime',
      description: 'Get the current date and time in various formats',
      input_schema: {
        type: 'object',
        properties: {
          format: {
            type: 'string',
            enum: ['ISO', 'UTC', 'local'],
            description: 'The format of the date and time'
          }
        },
        required: []
      },
      handler_config: {
        type: 'script',
        script_content: `
          const now = new Date();
          let result;

          switch (params.format) {
            case 'UTC':
              result = now.toUTCString();
              break;
            case 'local':
              result = now.toString();
              break;
            case 'ISO':
            default:
              result = now.toISOString();
              break;
          }

          // Return result in MCP format
          return {
            content: [
              {
                type: 'text',
                text: \`Current date and time: \${result}\`
              }
            ]
          };
        `
      },
      enabled: true
    });

    // Save the function
    await repository.save(sampleFunction);
    console.log('Sample function created successfully');
  } catch (error) {
    console.error('Error creating sample function:', error);
  } finally {
    // Close the data source
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the function
createSampleFunction().catch(console.error);
