import { Client } from 'pg';
import { getEnv } from '../src/utils/env';

/**
 * Create the database if it doesn't exist
 */
async function createDatabase(): Promise<void> {
  const dbName = getEnv('DB_DATABASE', 'mcp_server');
  
  // Connect to the default 'postgres' database to create our database
  const client = new Client({
    host: getEnv('DB_HOST', 'localhost'),
    port: parseInt(getEnv('DB_PORT', '5432')),
    user: getEnv('DB_USERNAME', 'postgres'),
    password: getEnv('DB_PASSWORD', 'password'),
    database: 'postgres'
  });

  try {
    await client.connect();
    console.log('Connected to PostgreSQL');

    // Check if database exists
    const checkResult = await client.query(
      `SELECT 1 FROM pg_database WHERE datname = $1`,
      [dbName]
    );

    if (checkResult.rowCount === 0) {
      // Database doesn't exist, create it
      console.log(`Creating database ${dbName}...`);
      await client.query(`CREATE DATABASE ${dbName}`);
      console.log(`Database ${dbName} created successfully`);
    } else {
      console.log(`Database ${dbName} already exists`);
    }
  } catch (error) {
    console.error('Error creating database:', error);
    throw error;
  } finally {
    await client.end();
    console.log('Disconnected from PostgreSQL');
  }
}

// Run the function
createDatabase().catch(console.error);
