/**
 * Message Processor Workflow
 *
 * This workflow processes incoming REST API requests by:
 * 1. Accepting a POST payload with a "message" string
 * 2. Storing it as "rawMessage" variable
 * 3. Converting rawMessage to uppercase as "resultMessage"
 * 4. Generating a random number between 1-10 as "randomNumber"
 * 5. Returning all three variables in the response
 */

export const messageProcessorWorkflow = {
  id: 'message-processor-workflow',
  name: 'Message Processor Workflow',
  description: 'Processes incoming messages by converting to uppercase and adding a random number',
  version: '1.0.0',
  enabled: true,
  tags: ['message-processing', 'rest-api', 'text-transformation'],
  input_schema: {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        description: 'The message string to process'
      }
    },
    required: ['message']
  },
  output_schema: {
    type: 'object',
    properties: {
      rawMessage: {
        type: 'string',
        description: 'The original input message'
      },
      resultMessage: {
        type: 'string',
        description: 'The message converted to uppercase'
      },
      randomNumber: {
        type: 'number',
        description: 'A random number between 1-10'
      }
    }
  },
  nodes_config: {
    nodes: [
      {
        id: 'rest-trigger',
        type: 'rest-api-trigger',
        name: 'REST API Trigger',
        position: { x: 100, y: 100 },
        config: {
          method: 'POST',
          path: '/api/process-message',
          authentication: false,
          validation: true,
          headers: {
            'Content-Type': 'application/json'
          },
          bodySchema: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                description: 'The message to process'
              }
            },
            required: ['message']
          }
        }
      },
      {
        id: 'store-raw-message',
        type: 'javascript-action',
        name: 'Store Raw Message',
        position: { x: 300, y: 100 },
        config: {
          script: `
// Extract the message from REST API trigger input
// The POST data is nested in input.request.body
const message = input.request?.body?.message;

// Validate input
if (!message || typeof message !== 'string') {
  throw new Error('Invalid input: message must be a non-empty string. Received input structure: ' + JSON.stringify(input, null, 2));
}

// Store as rawMessage variable in workflow context
workflowContext.variables.rawMessage = message;

console.log('Stored raw message:', message);
console.log('Full input structure:', JSON.stringify(input, null, 2));

// Return the message for next node
return {
  rawMessage: message
};
          `,
          timeout: 5000,
          enableConsole: true,
          allowAsync: false
        }
      },
      {
        id: 'process-message',
        type: 'javascript-action',
        name: 'Process Message',
        position: { x: 500, y: 100 },
        config: {
          script: `
// Get the raw message from previous node or context
const rawMessage = input.rawMessage || workflowContext.variables.rawMessage;

// Convert to uppercase
const resultMessage = rawMessage.toUpperCase();

// Generate random number between 1-10
const randomNumber = Math.floor(Math.random() * 10) + 1;

// Store in workflow context
workflowContext.variables.resultMessage = resultMessage;
workflowContext.variables.randomNumber = randomNumber;

console.log('Processed message:', resultMessage);
console.log('Generated random number:', randomNumber);

// Return all three variables
return {
  rawMessage: rawMessage,
  resultMessage: resultMessage,
  randomNumber: randomNumber
};
          `,
          timeout: 5000,
          enableConsole: true,
          allowAsync: false
        }
      },
      {
        id: 'format-response',
        type: 'response-terminator',
        name: 'Format Response',
        position: { x: 700, y: 100 },
        config: {
          responseTemplate: {
            success: true,
            data: {
              rawMessage: '${input.rawMessage}',
              resultMessage: '${input.resultMessage}',
              randomNumber: '${input.randomNumber}'
            },
            message: 'Message processed successfully',
            timestamp: '${variables.timestamp}',
            metadata: {
              workflowId: '${workflowId}',
              executionId: '${executionId}'
            }
          },
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      }
    ],
    edges: [
      {
        id: 'edge-1',
        source: 'rest-trigger',
        target: 'store-raw-message',
        type: 'default'
      },
      {
        id: 'edge-2',
        source: 'store-raw-message',
        target: 'process-message',
        type: 'default'
      },
      {
        id: 'edge-3',
        source: 'process-message',
        target: 'format-response',
        type: 'default'
      }
    ]
  },
  retry_config: {
    maxRetries: 3,
    retryDelay: 1000,
    retryBackoffMultiplier: 2,
    retryableErrors: ['NetworkError', 'TimeoutError']
  },
  observability_config: {
    logLevel: 'info',
    enableMetrics: true,
    enableTracing: true
  }
};

// Export as default for easy import
export default messageProcessorWorkflow;
