import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Workflow } from '../src/infrastructure/database/entities/Workflow.entity';
import { MCPFunction } from '../src/infrastructure/database/entities/MCPFunction.entity';
import dataSource from '../src/infrastructure/database/config';

/**
 * Create a sample echo workflow and function
 */
async function createSampleWorkflow(): Promise<void> {
  try {
    console.log('Creating sample workflow...');

    // Initialize database connection
    await dataSource.initialize();
    console.log('Database connection initialized');

    // Create sample workflow
    const workflowId = uuidv4();
    const workflow = new Workflow();
    workflow.id = workflowId;
    workflow.name = 'echo-workflow';
    workflow.description = 'Echo the input with a timestamp';
    workflow.version = 1;
    workflow.input_schema = {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          description: 'Message to echo'
        }
      },
      required: ['message']
    };
    workflow.output_schema = {
      type: 'object',
      properties: {
        message: {
          type: 'string'
        },
        timestamp: {
          type: 'string',
          format: 'date-time'
        }
      }
    };
    workflow.nodes_config = {
      nodes: [
        {
          id: 'input',
          type: 'javascript',
          name: 'Process Input',
          config: {
            script: 'return args;'
          }
        },
        {
          id: 'addTimestamp',
          type: 'javascript',
          name: 'Add Timestamp',
          config: {
            script: 'return { message: args.message, timestamp: new Date().toISOString() };'
          }
        }
      ],
      edges: [
        {
          source: 'input',
          target: 'addTimestamp'
        }
      ]
    };
    workflow.enabled = true;

    // Save workflow
    await dataSource.getRepository(Workflow).save(workflow);
    console.log(`Workflow created with ID: ${workflowId}`);

    // Create workflow-based function
    const functionId = uuidv4();
    const mcpFunction = new MCPFunction();
    mcpFunction.id = functionId;
    mcpFunction.name = 'echo-workflow';
    mcpFunction.description = 'Echo the input with a timestamp (workflow-based)';
    mcpFunction.input_schema = workflow.input_schema;
    mcpFunction.handler_config = {
      type: 'workflow',
      workflow_id: workflowId
    };
    mcpFunction.enabled = true;

    // Save function
    await dataSource.getRepository(MCPFunction).save(mcpFunction);
    console.log(`Function created with ID: ${functionId}`);

    console.log('Sample workflow and function created successfully');
  } catch (error) {
    console.error('Error creating sample workflow:', error);
  } finally {
    // Close database connection
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the script
createSampleWorkflow();
