import 'reflect-metadata';
import { Container } from 'inversify';
import { configureContainer } from '../src/inversify.config';
import { TYPES } from '../src/types';
import { IWorkflowEngine } from '../src/core/interfaces/IWorkflowEngine';
import { ILogger } from '../src/core/interfaces/ILogger';
import { IDatabase } from '../src/core/interfaces/IDatabase';

/**
 * Test the workflow engine by executing a workflow
 */
// Store container globally for cleanup
let container: Container;

async function testWorkflow(): Promise<void> {
  try {
    console.log('Testing workflow engine...');

    // Configure dependency injection
    container = configureContainer();

    // Get logger
    const logger = container.get<ILogger>(TYPES.Logger);

    // Get database and initialize it
    const database = container.get<IDatabase>(TYPES.Database);
    await database.initialize();
    logger.info('Database initialized');

    // Get workflow engine and initialize it
    const workflowEngine = container.get<IWorkflowEngine>(TYPES.WorkflowEngine);
    await workflowEngine.initialize();
    logger.info('Workflow engine initialized');

    // Execute workflow
    const workflowId = process.argv[2];
    if (!workflowId) {
      console.error('Please provide a workflow ID as the first argument');
      process.exit(1);
    }

    let input;
    try {
      input = JSON.parse(process.argv[3] || '{}');
    } catch (error) {
      // Fallback to a simple message if JSON parsing fails
      input = { message: process.argv[3] || 'Hello, world!' };
    }

    logger.info(`Executing workflow ${workflowId} with input: ${JSON.stringify(input)}`);
    const result = await workflowEngine.executeWorkflow(workflowId, input);
    logger.info(`Workflow execution result: ${JSON.stringify(result)}`);
  } catch (error) {
    console.error('Error testing workflow:', error);
    process.exit(1);
  } finally {
    // Close database connection
    try {
      const database = container.get<IDatabase>(TYPES.Database);
      await database.close();
      console.log('Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the script
testWorkflow();
