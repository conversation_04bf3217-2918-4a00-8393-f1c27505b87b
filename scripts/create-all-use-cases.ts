#!/usr/bin/env ts-node

import { createUseCase1Workflow } from './create-use-case-1-workflow';
import { createUseCase2Workflow } from './create-use-case-2-workflow';
import { createUseCase3WorkflowAndFunction } from './create-use-case-3-workflow';

/**
 * Master script to create all Phase 5 Use Cases
 */
async function createAllUseCases() {
  console.log('🚀 Creating all Phase 5 Use Cases...\n');

  try {
    // Create Use Case 1: REST API to JavaScript
    console.log('📝 Creating Use Case 1: REST API to JavaScript...');
    await createUseCase1Workflow();
    console.log('✅ Use Case 1 completed\n');

    // Create Use Case 2: Database + Redis Caching
    console.log('📝 Creating Use Case 2: Database + Redis Caching...');
    await createUseCase2Workflow();
    console.log('✅ Use Case 2 completed\n');

    // Create Use Case 3: MCP Function + Redis Lookup
    console.log('📝 Creating Use Case 3: MCP Function + Redis Lookup...');
    await createUseCase3WorkflowAndFunction();
    console.log('✅ Use Case 3 completed\n');

    console.log('🎉 All Use Cases created successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Use Case 1: REST API to JavaScript Processing');
    console.log('✅ Use Case 2: Database Integration with Redis Caching');
    console.log('✅ Use Case 3: MCP Function with Redis Lookup');

    console.log('\n🔧 Next Steps:');
    console.log('1. Ensure PostgreSQL and Redis are running');
    console.log('2. Create the items table for Use Case 2:');
    console.log('   CREATE TABLE items (');
    console.log('     id UUID PRIMARY KEY,');
    console.log('     name VARCHAR(255) NOT NULL,');
    console.log('     description TEXT,');
    console.log('     category VARCHAR(100) NOT NULL,');
    console.log('     price DECIMAL(10,2) DEFAULT 0,');
    console.log('     metadata JSONB,');
    console.log('     created_at TIMESTAMPTZ DEFAULT NOW(),');
    console.log('     updated_at TIMESTAMPTZ DEFAULT NOW()');
    console.log('   );');
    console.log('3. Start the MCP server to test Use Case 3');
    console.log('4. Use the WorkflowTester component to test workflows');

    console.log('\n🧪 Testing Examples:');
    console.log('Use Case 1 - REST API:');
    console.log('  POST /api/workflows/{id}/trigger');
    console.log('  Body: {"type": "user", "id": 123, "name": "John"}');
    console.log('');
    console.log('Use Case 2 - Database + Cache:');
    console.log('  POST /api/workflows/{id}/trigger');
    console.log('  Body: {"name": "Laptop", "category": "electronics", "price": 999.99}');
    console.log('');
    console.log('Use Case 3 - MCP Function:');
    console.log('  MCP call: redis-lookup({"action": "get", "key": "item:123"})');

  } catch (error) {
    console.error('❌ Error creating use cases:', error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  createAllUseCases()
    .then(() => {
      console.log('\n✅ All Phase 5 Use Cases setup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Failed to create use cases:', error);
      process.exit(1);
    });
}

export { createAllUseCases };
