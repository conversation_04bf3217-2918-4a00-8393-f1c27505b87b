/**
 * Test script for public REST API endpoints
 * This demonstrates how to call workflow REST API endpoints without authentication
 */

import axios from 'axios';

// Configuration
const BASE_URL = 'http://localhost:7002';
const PUBLIC_API_PREFIX = '/api/public';

async function testPublicEndpoint() {
  console.log('Testing public REST API endpoint...\n');

  try {
    // Test endpoint: /api/public/process-message
    const endpoint = `${BASE_URL}${PUBLIC_API_PREFIX}/process-message`;

    console.log(`Calling: POST ${endpoint}`);
    console.log('Request body:', { message: 'ahojky' });

    const response = await axios.post(
      endpoint,
      {
        message: 'ahojky'
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('\nResponse status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('\nError response:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      if (error.response?.status === 404) {
        console.log('\n❌ Endpoint not found. Make sure:');
        console.log('1. The workflow is created with a REST API trigger');
        console.log('2. The trigger path is set to "/process-message"');
        console.log('3. The trigger method is set to "POST"');
        console.log('4. The workflow is enabled');
      } else if (error.response?.status === 401) {
        console.log('\n❌ Authentication required. Check if:');
        console.log('1. The REST API trigger has "authentication: false"');
        console.log('2. The public routes are properly configured');
      }
    } else {
      console.error('\nUnexpected error:', error);
    }
  }
}

async function testWithAuthentication() {
  console.log('\n\nTesting authenticated endpoint for comparison...\n');

  try {
    // For comparison, test the authenticated endpoint
    const endpoint = `${BASE_URL}/api/workflows/{workflow-id}/trigger`;

    console.log(`This would require authentication: POST ${endpoint}`);
    console.log('You would need to:');
    console.log('1. Get a JWT token from /auth/login');
    console.log('2. Include it in the Authorization header');
    console.log('3. Replace {workflow-id} with your actual workflow ID');
  } catch (error) {
    console.error('Error:', error);
  }
}

async function showWorkflowExample() {
  console.log('\n\n📋 Example Workflow Configuration:');
  console.log('================================\n');

  const exampleWorkflow = {
    name: 'message-processor-workflow',
    description: 'Processes messages via REST API',
    enabled: true,
    nodes_config: {
      nodes: [
        {
          id: 'rest-trigger',
          type: 'rest-api-trigger',
          name: 'REST API Trigger',
          config: {
            method: 'POST',
            path: '/process-message',
            authentication: false, // This allows public access
            validation: true
          }
        },
        {
          id: 'process',
          type: 'javascript-action',
          name: 'Process Message',
          config: {
            code: `
              // Extract message from REST API request
              const message = input.request?.body?.message;
              
              if (!message) {
                return {
                  statusCode: 400,
                  body: { error: 'Message is required' }
                };
              }
              
              // Process the message
              const result = {
                statusCode: 200,
                body: {
                  original: message,
                  processed: message.toUpperCase(),
                  timestamp: new Date().toISOString()
                }
              };
              
              return result;
            `
          }
        }
      ],
      edges: [
        {
          source: 'rest-trigger',
          target: 'process'
        }
      ]
    }
  };

  console.log(JSON.stringify(exampleWorkflow, null, 2));
}

// Run the tests
async function main() {
  console.log('🚀 Public REST API Test Script');
  console.log('==============================\n');

  await testPublicEndpoint();
  await testWithAuthentication();
  await showWorkflowExample();

  console.log('\n\n✅ Test completed!');
}

// Check if axios is installed
try {
  require('axios');
  main().catch(console.error);
} catch (error) {
  console.error('Please install axios first: npm install axios');
}
