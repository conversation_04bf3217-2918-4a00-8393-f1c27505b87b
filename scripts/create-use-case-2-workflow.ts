#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Workflow } from '../src/infrastructure/database/entities/Workflow.entity';
import { createDataSource } from '../src/infrastructure/database/dataSource';

/**
 * <PERSON>ript to create Use Case 2: Database + Redis Caching workflow template
 */
async function createUseCase2Workflow() {
  console.log('Creating Use Case 2: Database + Redis Caching workflow...');

  // Initialize database connection
  const dataSource: DataSource = createDataSource();
  await dataSource.initialize();

  try {
    // Create workflow
    const workflowId = uuidv4();
    const workflow = new Workflow();
    
    workflow.id = workflowId;
    workflow.name = 'Database Integration with Redis Caching';
    workflow.description = 'Use Case 2: Stores data in database and caches in Redis for fast retrieval';
    workflow.version = '1.0.0';
    
    // Input schema for the workflow
    workflow.input_schema = {
      type: 'object',
      properties: {
        payload: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Item name' },
            description: { type: 'string', description: 'Item description' },
            category: { type: 'string', description: 'Item category' },
            price: { type: 'number', description: 'Item price' },
            metadata: { type: 'object', description: 'Additional metadata' }
          },
          required: ['name', 'category']
        }
      },
      required: ['payload']
    };

    // Workflow configuration with nodes and edges
    workflow.nodes_config = {
      nodes: [
        {
          id: 'data-validation',
          type: 'javascript',
          position: { x: 100, y: 100 },
          config: {
            script: `
// Validate and prepare data for database insertion
const { payload } = context.input;

console.log('Validating input data:', payload);

// Validate required fields
if (!payload.name || !payload.category) {
  throw new Error('Name and category are required fields');
}

// Generate ID and prepare data
const itemId = uuidv4();
const timestamp = new Date().toISOString();

const validatedData = {
  id: itemId,
  name: payload.name.trim(),
  description: payload.description || '',
  category: payload.category.toLowerCase(),
  price: payload.price || 0,
  metadata: payload.metadata || {},
  created_at: timestamp,
  updated_at: timestamp
};

// Store in context for next nodes
context.variables.validatedData = validatedData;
context.variables.itemId = itemId;

console.log('Data validated successfully:', validatedData);

return validatedData;
            `.trim()
          }
        },
        {
          id: 'database-insert',
          type: 'sql',
          position: { x: 400, y: 100 },
          config: {
            query: `
              INSERT INTO items (id, name, description, category, price, metadata, created_at, updated_at)
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
              RETURNING *
            `,
            params: [
              '${context.variables.validatedData.id}',
              '${context.variables.validatedData.name}',
              '${context.variables.validatedData.description}',
              '${context.variables.validatedData.category}',
              '${context.variables.validatedData.price}',
              '${JSON.stringify(context.variables.validatedData.metadata)}',
              '${context.variables.validatedData.created_at}',
              '${context.variables.validatedData.updated_at}'
            ],
            options: {
              returnAsObject: true
            }
          }
        },
        {
          id: 'data-transformation',
          type: 'javascript',
          position: { x: 700, y: 100 },
          config: {
            script: `
// Transform data for caching and response
const dbResult = context.nodeResults['database-insert'];
const validatedData = context.variables.validatedData;

console.log('Database insert result:', dbResult);

// Transform data for cache
const cacheData = {
  id: validatedData.id,
  name: validatedData.name,
  description: validatedData.description,
  category: validatedData.category,
  price: validatedData.price,
  metadata: validatedData.metadata,
  created_at: validatedData.created_at,
  updated_at: validatedData.updated_at,
  // Add computed fields
  slug: validatedData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
  searchTerms: [
    validatedData.name.toLowerCase(),
    validatedData.category.toLowerCase(),
    validatedData.description.toLowerCase()
  ].filter(Boolean),
  cacheTimestamp: new Date().toISOString()
};

// Store transformed data
context.variables.cacheData = cacheData;
context.variables.cacheKey = \`item:\${validatedData.id}\`;
context.variables.categoryKey = \`category:\${validatedData.category}\`;

console.log('Data transformed for caching:', cacheData);

return cacheData;
            `.trim()
          }
        },
        {
          id: 'redis-cache',
          type: 'redis',
          position: { x: 1000, y: 100 },
          config: {
            command: 'SET',
            args: [
              '${context.variables.cacheKey}',
              '${JSON.stringify(context.variables.cacheData)}',
              'EX',
              3600  // 1 hour TTL
            ]
          }
        },
        {
          id: 'category-cache-update',
          type: 'redis',
          position: { x: 1000, y: 250 },
          config: {
            command: 'SADD',
            args: [
              '${context.variables.categoryKey}',
              '${context.variables.itemId}'
            ]
          }
        },
        {
          id: 'response-formatter',
          type: 'javascript',
          position: { x: 1300, y: 100 },
          config: {
            script: `
// Format final response
const cacheData = context.variables.cacheData;
const cacheKey = context.variables.cacheKey;

console.log('Formatting response for item:', cacheData.id);

// Create response
const response = {
  success: true,
  statusCode: 201,
  body: {
    message: 'Item created and cached successfully',
    data: {
      id: cacheData.id,
      name: cacheData.name,
      description: cacheData.description,
      category: cacheData.category,
      price: cacheData.price,
      metadata: cacheData.metadata,
      created_at: cacheData.created_at,
      slug: cacheData.slug
    },
    cache: {
      key: cacheKey,
      ttl: 3600,
      cached_at: cacheData.cacheTimestamp
    },
    links: {
      self: \`/api/items/\${cacheData.id}\`,
      category: \`/api/categories/\${cacheData.category}\`
    }
  },
  metadata: {
    workflowId: context.workflowId,
    executionId: context.executionId,
    processingTime: Date.now() - new Date(context.startedAt).getTime() + 'ms'
  }
};

console.log('Response formatted successfully');

return response;
            `.trim()
          }
        }
      ],
      edges: [
        {
          id: 'edge-1',
          source: 'data-validation',
          target: 'database-insert'
        },
        {
          id: 'edge-2',
          source: 'database-insert',
          target: 'data-transformation'
        },
        {
          id: 'edge-3',
          source: 'data-transformation',
          target: 'redis-cache'
        },
        {
          id: 'edge-4',
          source: 'data-transformation',
          target: 'category-cache-update'
        },
        {
          id: 'edge-5',
          source: 'redis-cache',
          target: 'response-formatter'
        },
        {
          id: 'edge-6',
          source: 'category-cache-update',
          target: 'response-formatter'
        }
      ]
    };

    workflow.enabled = true;
    workflow.tags = ['use-case-2', 'database', 'redis', 'caching', 'template'];

    // Save workflow
    await dataSource.getRepository(Workflow).save(workflow);
    console.log(`✅ Use Case 2 workflow created with ID: ${workflowId}`);

    // Print usage instructions
    console.log('\n📋 Usage Instructions:');
    console.log(`1. Admin execute: POST /api/workflows/${workflowId}/execute`);
    console.log(`   Body: { "input": { "payload": { "name": "Laptop", "category": "electronics", "price": 999.99 } } }`);
    console.log('');
    console.log(`2. Public trigger: POST /api/workflows/${workflowId}/trigger`);
    console.log(`   Body: { "name": "Smartphone", "category": "electronics", "price": 699.99, "description": "Latest model" }`);
    console.log('');
    console.log('🎯 This workflow demonstrates:');
    console.log('- Data validation and preparation');
    console.log('- SQL database insertion with parameters');
    console.log('- Data transformation pipeline');
    console.log('- Redis caching with TTL');
    console.log('- Category indexing in Redis');
    console.log('- Performance monitoring');

    // Print database setup instructions
    console.log('\n🗄️ Database Setup Required:');
    console.log('CREATE TABLE items (');
    console.log('  id UUID PRIMARY KEY,');
    console.log('  name VARCHAR(255) NOT NULL,');
    console.log('  description TEXT,');
    console.log('  category VARCHAR(100) NOT NULL,');
    console.log('  price DECIMAL(10,2) DEFAULT 0,');
    console.log('  metadata JSONB,');
    console.log('  created_at TIMESTAMPTZ DEFAULT NOW(),');
    console.log('  updated_at TIMESTAMPTZ DEFAULT NOW()');
    console.log(');');

  } catch (error) {
    console.error('❌ Error creating workflow:', error);
    throw error;
  } finally {
    await dataSource.destroy();
  }
}

// Run the script
if (require.main === module) {
  createUseCase2Workflow()
    .then(() => {
      console.log('✅ Use Case 2 workflow creation completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Failed to create Use Case 2 workflow:', error);
      process.exit(1);
    });
}

export { createUseCase2Workflow };
