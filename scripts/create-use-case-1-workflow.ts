#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Workflow } from '../src/infrastructure/database/entities/Workflow.entity';
import { createDataSource } from '../src/infrastructure/database/dataSource';

/**
 * <PERSON>ript to create Use Case 1: REST API to JavaScript workflow template
 */
async function createUseCase1Workflow() {
  console.log('Creating Use Case 1: REST API to JavaScript workflow...');

  // Initialize database connection
  const dataSource: DataSource = createDataSource();
  await dataSource.initialize();

  try {
    // Create workflow
    const workflowId = uuidv4();
    const workflow = new Workflow();
    
    workflow.id = workflowId;
    workflow.name = 'REST API to JavaScript Processing';
    workflow.description = 'Use Case 1: Processes REST API requests with custom JavaScript logic';
    workflow.version = '1.0.0';
    
    // Input schema for the workflow
    workflow.input_schema = {
      type: 'object',
      properties: {
        payload: {
          type: 'object',
          description: 'Request payload data'
        },
        headers: {
          type: 'object',
          description: 'Request headers'
        },
        method: {
          type: 'string',
          description: 'HTTP method'
        },
        url: {
          type: 'string',
          description: 'Request URL'
        },
        timestamp: {
          type: 'string',
          description: 'Request timestamp'
        }
      },
      required: ['payload']
    };

    // Workflow configuration with nodes and edges
    workflow.nodes_config = {
      nodes: [
        {
          id: 'payload-extraction',
          type: 'javascript',
          position: { x: 100, y: 100 },
          config: {
            script: `
// Extract and validate payload data
const { payload, headers, method, timestamp } = context.input;

console.log('Processing REST API request:', {
  method,
  timestamp,
  payloadKeys: Object.keys(payload || {})
});

// Extract payload data with validation
const extractedData = {
  ...payload,
  requestMetadata: {
    method,
    timestamp,
    userAgent: headers['user-agent'] || 'unknown',
    contentType: headers['content-type'] || 'unknown'
  }
};

// Store in context for next node
context.variables.extractedData = extractedData;

return extractedData;
            `.trim()
          }
        },
        {
          id: 'javascript-processing',
          type: 'javascript',
          position: { x: 400, y: 100 },
          config: {
            script: `
// Custom JavaScript processing logic
const inputData = context.variables.extractedData;

console.log('Processing data with custom logic:', inputData);

// Example processing: transform and enrich data
const processedData = {
  id: inputData.id || uuidv4(),
  originalData: inputData,
  processedAt: new Date().toISOString(),
  status: 'processed',
  metadata: {
    ...inputData.requestMetadata,
    processingDuration: Date.now() - new Date(inputData.requestMetadata.timestamp).getTime()
  }
};

// Add custom business logic here
if (inputData.type === 'user') {
  processedData.category = 'user-data';
  processedData.priority = inputData.priority || 'normal';
} else if (inputData.type === 'order') {
  processedData.category = 'order-data';
  processedData.priority = 'high';
} else {
  processedData.category = 'general';
  processedData.priority = 'low';
}

// Store result in context
context.variables.processedData = processedData;

return processedData;
            `.trim()
          }
        },
        {
          id: 'response-formatter',
          type: 'javascript',
          position: { x: 700, y: 100 },
          config: {
            script: `
// Format response for REST API
const processedData = context.variables.processedData;

console.log('Formatting response:', processedData);

// Create formatted response
const response = {
  success: true,
  data: processedData,
  message: 'Data processed successfully',
  timestamp: new Date().toISOString(),
  statusCode: 200,
  body: {
    result: processedData,
    metadata: {
      workflowId: context.workflowId,
      executionId: context.executionId,
      processingTime: processedData.metadata.processingDuration + 'ms'
    }
  }
};

// Store final response
context.variables.response = response;

return response;
            `.trim()
          }
        }
      ],
      edges: [
        {
          id: 'edge-1',
          source: 'payload-extraction',
          target: 'javascript-processing'
        },
        {
          id: 'edge-2',
          source: 'javascript-processing',
          target: 'response-formatter'
        }
      ]
    };

    workflow.enabled = true;
    workflow.tags = ['use-case-1', 'rest-api', 'javascript', 'template'];

    // Save workflow
    await dataSource.getRepository(Workflow).save(workflow);
    console.log(`✅ Use Case 1 workflow created with ID: ${workflowId}`);

    // Print usage instructions
    console.log('\n📋 Usage Instructions:');
    console.log(`1. Admin execute: POST /api/workflows/${workflowId}/execute`);
    console.log(`   Body: { "input": { "payload": { "type": "user", "id": 123, "name": "John" } } }`);
    console.log('');
    console.log(`2. Public trigger: POST /api/workflows/${workflowId}/trigger`);
    console.log(`   Body: { "type": "user", "id": 123, "name": "John", "priority": "high" }`);
    console.log('');
    console.log('🎯 This workflow demonstrates:');
    console.log('- REST API payload extraction');
    console.log('- Custom JavaScript processing');
    console.log('- Response formatting');
    console.log('- Context variable management');
    console.log('- Error handling and validation');

  } catch (error) {
    console.error('❌ Error creating workflow:', error);
    throw error;
  } finally {
    await dataSource.destroy();
  }
}

// Run the script
if (require.main === module) {
  createUseCase1Workflow()
    .then(() => {
      console.log('✅ Use Case 1 workflow creation completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Failed to create Use Case 1 workflow:', error);
      process.exit(1);
    });
}

export { createUseCase1Workflow };
