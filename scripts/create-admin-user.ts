#!/usr/bin/env node -r ts-node/register

import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from '../src/types';
import { PostgresDatabase } from '../src/infrastructure/database/PostgresDatabase';
import { TenantManager } from '../src/services/TenantManager';
import { TenantRepository } from '../src/infrastructure/database/repositories/TenantRepository';
import { TenantUserRepository } from '../src/infrastructure/database/repositories/TenantUserRepository';
import { Tenant } from '../src/infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../src/infrastructure/database/entities/TenantUser.entity';
import { Logger } from 'pino';
import pino from 'pino';

/**
 * Script to create default admin tenant and user
 */
async function createAdminUser() {
  const logger = pino({ level: 'info' });
  
  // Create container
  const container = new Container();
  
  // Bind dependencies
  container.bind<Logger>(TYPES.Logger).toConstantValue(logger);
  container.bind<PostgresDatabase>(TYPES.Database).to(PostgresDatabase).inSingletonScope();
  container.bind<TenantRepository>(TYPES.TenantRepository).to(TenantRepository);
  container.bind<TenantUserRepository>(TYPES.TenantUserRepository).to(TenantUserRepository);
  container.bind<TenantManager>(TYPES.TenantManager).to(TenantManager);

  const database = container.get<PostgresDatabase>(TYPES.Database);
  const tenantManager = container.get<TenantManager>(TYPES.TenantManager);

  try {
    // Initialize database
    console.log('📊 Initializing database...');
    await database.initialize();
    console.log('✅ Database initialized\n');

    // Check if default tenant already exists
    const existingTenant = await tenantManager.getTenantByName('default');
    if (existingTenant) {
      console.log('✅ Default tenant already exists');
      
      // Check if admin user exists
      const users = await tenantManager.getTenantUsers(existingTenant.id, { page: 1, limit: 100 });
      const adminUser = users.data.find(u => u.username === 'admin');
      
      if (adminUser) {
        console.log('✅ Admin user already exists');
        console.log('\n🔑 Login credentials:');
        console.log('   Tenant: default');
        console.log('   Username: admin');
        console.log('   Password: admin123');
        console.log('\n🌐 Admin API URL: http://localhost:7002');
        console.log('   Login endpoint: POST /auth/login');
        console.log('   Admin endpoints: /admin/*');
        return;
      }
    }

    // Create default tenant
    let tenant: Tenant;
    if (!existingTenant) {
      console.log('🏗️  Creating default tenant...');
      tenant = await tenantManager.createTenant({
        name: 'default',
        displayName: 'Default Tenant',
        createdBy: 'system',
        settings: {
          ...Tenant.getDefaultSettings(),
          max_concurrent_workflows: 100,
          enable_custom_functions: true,
          enable_external_apis: true
        }
      });
      console.log(`✅ Created tenant: ${tenant.name} (${tenant.id})`);
    } else {
      tenant = existingTenant;
    }

    // Create admin user
    console.log('👤 Creating admin user...');
    const adminUser = await tenantManager.createTenantUser(tenant.id, {
      username: 'admin',
      email: 'admin@localhost',
      passwordHash: 'admin123', // Will be hashed by TenantManager
      roles: [TenantUser.getAdminRole()]
    });

    console.log(`✅ Created admin user: ${adminUser.username}`);
    
    console.log('\n🎉 Setup completed successfully!');
    console.log('\n🔑 Login credentials:');
    console.log('   Tenant: default');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    
    console.log('\n🌐 Admin API URL: http://localhost:7002');
    console.log('   Login endpoint: POST /auth/login');
    console.log('   Admin endpoints: /admin/*');
    
    console.log('\n📝 Example login request:');
    console.log('curl -X POST http://localhost:7002/auth/login \\');
    console.log('  -H "Content-Type: application/json" \\');
    console.log('  -d \'{"tenantName": "default", "username": "admin", "password": "admin123"}\'');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  } finally {
    await database.close();
    console.log('\n✅ Database connection closed');
  }
}

// Run the script
if (require.main === module) {
  createAdminUser().catch(console.error);
}

export { createAdminUser };
