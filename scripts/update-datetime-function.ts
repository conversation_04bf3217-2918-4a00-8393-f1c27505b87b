import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { typeOrmConfig } from '../src/infrastructure/database/config';
import { MCPFunction } from '../src/infrastructure/database/entities/MCPFunction.entity';

/**
 * Update the getDateTime function in the database
 */
async function updateDateTimeFunction(): Promise<void> {
  console.log('Updating getDateTime function...');

  // Create a data source
  const dataSource = new DataSource(typeOrmConfig);
  
  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection initialized');

    // Create a repository
    const repository = dataSource.getRepository(MCPFunction);

    // Find the getDateTime function
    const dateTimeFunction = await repository.findOneBy({ name: 'getDateTime' });
    
    if (dateTimeFunction) {
      console.log('Found getDateTime function, updating...');
      
      // Update the function
      dateTimeFunction.handler_config = {
        type: 'script',
        script_content: `
          // Get the format parameter or use default
          const format = params.format || 'ISO';
          
          // Get current date
          const now = new Date();
          let result;

          // Format the date based on the format parameter
          switch (format) {
            case 'UTC':
              result = now.toUTCString();
              break;
            case 'local':
              result = now.toString();
              break;
            case 'ISO':
            default:
              result = now.toISOString();
              break;
          }

          // Return result in MCP format
          return {
            content: [
              {
                type: 'text',
                text: \`Current date and time: \${result}\`
              }
            ]
          };
        `
      };
      
      // Save the updated function
      await repository.save(dateTimeFunction);
      console.log('getDateTime function updated successfully');
    } else {
      console.log('getDateTime function not found');
    }
  } catch (error) {
    console.error('Error updating getDateTime function:', error);
  } finally {
    // Close the data source
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the function
updateDateTimeFunction().catch(console.error);
