/**
 * Test script for REST API trigger with POST data
 * This script demonstrates how the REST API trigger processes POST requests
 */

import { Container } from 'inversify';
import { WorkflowEngine } from '../src/workflow/engine/WorkflowEngine';
import { messageProcessorWorkflow } from './message-processor-workflow';

// Mock input that simulates how the REST API trigger would format POST data
const mockRestApiTriggerInput = {
  trigger: 'rest-api',
  request: {
    method: 'POST',
    path: '/api/process-message',
    headers: {
      'Content-Type': 'application/json'
    },
    query: {},
    body: {
      message: 'ahojky' // This is your original POST data
    },
    params: {}
  },
  timestamp: new Date().toISOString(),
  workflowId: 'message-processor-workflow',
  executionId: 'test-execution-id'
};

async function testWorkflow() {
  try {
    console.log('Testing message processor workflow with POST data...');
    console.log('Input data structure:', JSON.stringify(mockRestApiTriggerInput, null, 2));

    // Note: This is a simplified test. In reality, you would need to:
    // 1. Set up the full DI container
    // 2. Initialize the workflow engine
    // 3. Execute the workflow through the proper API endpoint

    console.log('\n=== Expected Behavior ===');
    console.log('1. REST API Trigger receives POST: {"message":"ahojky"}');
    console.log('2. REST API Trigger wraps it in request structure');
    console.log('3. JavaScript Action extracts: input.request.body.message');
    console.log('4. Workflow processes: "ahojky" -> "AHOJKY" + random number');

    console.log('\n=== Key Points ===');
    console.log('- POST data is accessible via: input.request.body');
    console.log('- Use input.request.body.message to get the message field');
    console.log('- Always validate the input structure');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Alternative: Direct test of the data extraction logic
function testDataExtraction() {
  console.log('\n=== Testing Data Extraction Logic ===');

  const input = mockRestApiTriggerInput;

  // This is the corrected extraction logic
  const message = input.request?.body?.message;

  console.log('Extracted message:', message);
  console.log('Message type:', typeof message);
  console.log('Is valid:', !!(message && typeof message === 'string'));

  if (message && typeof message === 'string') {
    const resultMessage = message.toUpperCase();
    const randomNumber = Math.floor(Math.random() * 10) + 1;

    console.log('Processed result:', {
      rawMessage: message,
      resultMessage: resultMessage,
      randomNumber: randomNumber
    });
  }
}

// Run the tests
if (require.main === module) {
  testWorkflow();
  testDataExtraction();
}

export { mockRestApiTriggerInput, testDataExtraction };
