#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Get version from command line argument
const version = process.argv[2];

if (!version) {
  console.error('Usage: node update-helm-values.js <version>');
  process.exit(1);
}

console.log(`Updating Helm values with version: ${version}`);

// Update values-dev.yaml
const devValuesPath = path.join(
  __dirname,
  '..',
  'charts',
  'ccczautomationmcpserver',
  'values-dev.yaml'
);
let devValues = fs.readFileSync(devValuesPath, 'utf8');
devValues = devValues.replace(/tag: '[^']*'/g, `tag: '${version}'`);
fs.writeFileSync(devValuesPath, devValues);

// Update values-prod.yaml
const prodValuesPath = path.join(
  __dirname,
  '..',
  'charts',
  'ccczautomationmcpserver',
  'values-prod.yaml'
);
let prodValues = fs.readFileSync(prodValuesPath, 'utf8');
prodValues = prodValues.replace(/tag: '[^']*'/g, `tag: '${version}'`);
fs.writeFileSync(prodValuesPath, prodValues);

// Update Chart.yaml
const chartPath = path.join(__dirname, '..', 'charts', 'ccczautomationmcpserver', 'Chart.yaml');
let chartYaml = fs.readFileSync(chartPath, 'utf8');
chartYaml = chartYaml.replace(/^appVersion: .*/gm, `appVersion: ${version}`);
chartYaml = chartYaml.replace(/^version: .*/gm, `version: ${version}`);
fs.writeFileSync(chartPath, chartYaml);

// Update package.json
const packagePath = path.join(__dirname, '..', 'package.json');
let packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
packageJson.version = version;
fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n');

console.log('Helm values and package.json updated successfully');
