#!/usr/bin/env ts-node

import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { Workflow } from '../src/infrastructure/database/entities/Workflow.entity';
import { MCPFunction } from '../src/infrastructure/database/entities/MCPFunction.entity';
import { createDataSource } from '../src/infrastructure/database/dataSource';

/**
 * <PERSON><PERSON>t to create Use Case 3: MCP Function + Redis Lookup workflow and function
 */
async function createUseCase3WorkflowAndFunction() {
  console.log('Creating Use Case 3: MCP Function + Redis Lookup...');

  // Initialize database connection
  const dataSource: DataSource = createDataSource();
  await dataSource.initialize();

  try {
    // Create workflow first
    const workflowId = uuidv4();
    const workflow = new Workflow();
    
    workflow.id = workflowId;
    workflow.name = 'MCP Function with Redis Lookup';
    workflow.description = 'Use Case 3: MCP function that performs Redis lookups and data processing';
    workflow.version = '1.0.0';
    
    // Input schema for the workflow (will be used by MCP function)
    workflow.input_schema = {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['get', 'search', 'list'],
          description: 'Action to perform'
        },
        key: {
          type: 'string',
          description: 'Redis key to lookup (for get action)'
        },
        pattern: {
          type: 'string',
          description: 'Search pattern (for search action)'
        },
        category: {
          type: 'string',
          description: 'Category to list (for list action)'
        },
        includeMetadata: {
          type: 'boolean',
          default: false,
          description: 'Include metadata in response'
        }
      },
      required: ['action']
    };

    // Workflow configuration with nodes and edges
    workflow.nodes_config = {
      nodes: [
        {
          id: 'input-validation',
          type: 'javascript',
          position: { x: 100, y: 100 },
          config: {
            script: `
// Validate MCP function input
const { action, key, pattern, category, includeMetadata } = context.input;

console.log('MCP Function called with:', { action, key, pattern, category, includeMetadata });

// Validate action
if (!['get', 'search', 'list'].includes(action)) {
  throw new Error('Invalid action. Must be one of: get, search, list');
}

// Validate required parameters for each action
if (action === 'get' && !key) {
  throw new Error('Key is required for get action');
}

if (action === 'search' && !pattern) {
  throw new Error('Pattern is required for search action');
}

if (action === 'list' && !category) {
  throw new Error('Category is required for list action');
}

// Store validated input
context.variables.action = action;
context.variables.key = key;
context.variables.pattern = pattern;
context.variables.category = category;
context.variables.includeMetadata = includeMetadata || false;

console.log('Input validation completed');

return { action, key, pattern, category, includeMetadata };
            `.trim()
          }
        },
        {
          id: 'redis-lookup',
          type: 'redis',
          position: { x: 400, y: 100 },
          config: {
            command: 'GET',
            args: ['${context.variables.key}']
          }
        },
        {
          id: 'redis-search',
          type: 'redis',
          position: { x: 400, y: 250 },
          config: {
            command: 'KEYS',
            args: ['${context.variables.pattern}']
          }
        },
        {
          id: 'redis-list-category',
          type: 'redis',
          position: { x: 400, y: 400 },
          config: {
            command: 'SMEMBERS',
            args: ['category:${context.variables.category}']
          }
        },
        {
          id: 'data-processing',
          type: 'javascript',
          position: { x: 700, y: 200 },
          config: {
            script: `
// Process Redis lookup results based on action
const action = context.variables.action;
const includeMetadata = context.variables.includeMetadata;

console.log('Processing data for action:', action);

let result;
let processedData;

if (action === 'get') {
  // Process single item lookup
  const redisResult = context.nodeResults['redis-lookup'];
  
  if (!redisResult) {
    return {
      success: false,
      error: 'Item not found',
      key: context.variables.key
    };
  }

  try {
    processedData = JSON.parse(redisResult);
    
    // Add metadata if requested
    if (includeMetadata) {
      processedData.metadata = {
        ...processedData.metadata,
        retrievedAt: new Date().toISOString(),
        source: 'redis-cache',
        key: context.variables.key
      };
    }

    result = {
      success: true,
      data: processedData,
      key: context.variables.key
    };
  } catch (error) {
    result = {
      success: false,
      error: 'Failed to parse cached data',
      key: context.variables.key
    };
  }

} else if (action === 'search') {
  // Process search results
  const searchKeys = context.nodeResults['redis-search'] || [];
  
  result = {
    success: true,
    data: {
      pattern: context.variables.pattern,
      matchingKeys: searchKeys,
      count: searchKeys.length
    }
  };

  if (includeMetadata) {
    result.data.metadata = {
      searchedAt: new Date().toISOString(),
      source: 'redis-search'
    };
  }

} else if (action === 'list') {
  // Process category listing
  const categoryItems = context.nodeResults['redis-list-category'] || [];
  
  result = {
    success: true,
    data: {
      category: context.variables.category,
      items: categoryItems,
      count: categoryItems.length
    }
  };

  if (includeMetadata) {
    result.data.metadata = {
      listedAt: new Date().toISOString(),
      source: 'redis-category-index'
    };
  }
}

console.log('Data processing completed:', result);

// Store final result
context.variables.finalResult = result;

return result;
            `.trim()
          }
        },
        {
          id: 'mcp-response-formatter',
          type: 'javascript',
          position: { x: 1000, y: 200 },
          config: {
            script: `
// Format response for MCP function
const finalResult = context.variables.finalResult;

console.log('Formatting MCP response:', finalResult);

// Create MCP-compatible response
const mcpResponse = {
  content: [
    {
      type: 'text',
      text: JSON.stringify(finalResult, null, 2)
    }
  ],
  isError: !finalResult.success
};

// Add metadata for MCP
if (context.variables.includeMetadata) {
  mcpResponse.metadata = {
    workflowId: context.workflowId,
    executionId: context.executionId,
    action: context.variables.action,
    executionTime: Date.now() - new Date(context.startedAt).getTime() + 'ms'
  };
}

console.log('MCP response formatted successfully');

return mcpResponse;
            `.trim()
          }
        }
      ],
      edges: [
        {
          id: 'edge-1',
          source: 'input-validation',
          target: 'redis-lookup',
          condition: 'action === "get"'
        },
        {
          id: 'edge-2',
          source: 'input-validation',
          target: 'redis-search',
          condition: 'action === "search"'
        },
        {
          id: 'edge-3',
          source: 'input-validation',
          target: 'redis-list-category',
          condition: 'action === "list"'
        },
        {
          id: 'edge-4',
          source: 'redis-lookup',
          target: 'data-processing'
        },
        {
          id: 'edge-5',
          source: 'redis-search',
          target: 'data-processing'
        },
        {
          id: 'edge-6',
          source: 'redis-list-category',
          target: 'data-processing'
        },
        {
          id: 'edge-7',
          source: 'data-processing',
          target: 'mcp-response-formatter'
        }
      ]
    };

    workflow.enabled = true;
    workflow.tags = ['use-case-3', 'mcp-function', 'redis', 'lookup', 'template'];

    // Save workflow
    await dataSource.getRepository(Workflow).save(workflow);
    console.log(`✅ Use Case 3 workflow created with ID: ${workflowId}`);

    // Create MCP Function that uses this workflow
    const functionId = uuidv4();
    const mcpFunction = new MCPFunction();
    
    mcpFunction.id = functionId;
    mcpFunction.name = 'redis-lookup';
    mcpFunction.description = 'Perform Redis lookups and data processing operations';
    mcpFunction.input_schema = workflow.input_schema;
    mcpFunction.handler_config = {
      type: 'workflow',
      workflow_id: workflowId
    };
    mcpFunction.enabled = true;

    // Save MCP function
    await dataSource.getRepository(MCPFunction).save(mcpFunction);
    console.log(`✅ MCP Function created with ID: ${functionId}`);

    // Print usage instructions
    console.log('\n📋 Usage Instructions:');
    console.log('1. MCP Function calls:');
    console.log('   - Get item: redis-lookup({"action": "get", "key": "item:123"})');
    console.log('   - Search: redis-lookup({"action": "search", "pattern": "item:*"})');
    console.log('   - List category: redis-lookup({"action": "list", "category": "electronics"})');
    console.log('');
    console.log(`2. Direct workflow execution: POST /api/workflows/${workflowId}/execute`);
    console.log(`   Body: { "input": { "action": "get", "key": "item:123", "includeMetadata": true } }`);
    console.log('');
    console.log('🎯 This demonstrates:');
    console.log('- MCP function with workflow-based handler');
    console.log('- Conditional workflow execution');
    console.log('- Redis pattern matching and lookups');
    console.log('- Data processing and transformation');
    console.log('- MCP-compatible response formatting');

  } catch (error) {
    console.error('❌ Error creating workflow and function:', error);
    throw error;
  } finally {
    await dataSource.destroy();
  }
}

// Run the script
if (require.main === module) {
  createUseCase3WorkflowAndFunction()
    .then(() => {
      console.log('✅ Use Case 3 workflow and MCP function creation completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Failed to create Use Case 3:', error);
      process.exit(1);
    });
}

export { createUseCase3WorkflowAndFunction };
