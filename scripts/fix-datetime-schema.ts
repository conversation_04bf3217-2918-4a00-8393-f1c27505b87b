import 'reflect-metadata';
import { DataSource } from 'typeorm';
import { typeOrmConfig } from '../src/infrastructure/database/config';
import { MCPFunction } from '../src/infrastructure/database/entities/MCPFunction.entity';

/**
 * Fix the getDateTime function schema in the database
 */
async function fixDateTimeSchema(): Promise<void> {
  console.log('Fixing getDateTime function schema...');

  // Create a data source
  const dataSource = new DataSource(typeOrmConfig);
  
  try {
    // Initialize the data source
    await dataSource.initialize();
    console.log('Database connection initialized');

    // Create a repository
    const repository = dataSource.getRepository(MCPFunction);

    // Find the getDateTime function
    const dateTimeFunction = await repository.findOneBy({ name: 'getDateTime' });
    
    if (dateTimeFunction) {
      console.log('Found getDateTime function, updating schema...');
      
      // Update the function schema
      dateTimeFunction.input_schema = {
        type: 'object',
        properties: {
          format: {
            type: 'string',
            enum: ['ISO', 'UTC', 'local'],
            description: 'The format of the date and time'
          }
        },
        required: []
      };
      
      // Save the updated function
      await repository.save(dateTimeFunction);
      console.log('getDateTime function schema updated successfully');
    } else {
      console.log('getDateTime function not found');
    }
  } catch (error) {
    console.error('Error fixing getDateTime function schema:', error);
  } finally {
    // Close the data source
    if (dataSource.isInitialized) {
      await dataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the function
fixDateTimeSchema().catch(console.error);
