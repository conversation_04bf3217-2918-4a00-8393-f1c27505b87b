import { getEnv, getEnvNumber } from '../utils/env';

export interface ServerConfig {
  port: number;
  host: string;
  apiPort: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  environment: 'development' | 'production' | 'test';
  data: {
    retentionDays: number;
    cleanupIntervalMinutes: number;
    cleanupBatchSize: number;
  };
}

export const defaultConfig: ServerConfig = {
  port: getEnvNumber('PORT', 7000),
  host: getEnv('HOST', '0.0.0.0'),
  apiPort: getEnvNumber('API_PORT', 7001),
  logLevel: getEnv('LOG_LEVEL', 'info') as 'debug' | 'info' | 'warn' | 'error',
  environment: getEnv('NODE_ENV', 'development') as 'development' | 'production' | 'test',
  data: {
    retentionDays: getEnvNumber('DATA_RETENTION_DAYS', 5),
    cleanupIntervalMinutes: getEnvNumber('DATA_CLEANUP_INTERVAL_MINUTES', 60),
    cleanupBatchSize: getEnvNumber('DATA_CLEANUP_BATCH_SIZE', 1000)
  }
};

export function getConfig(): ServerConfig {
  return {
    ...defaultConfig
  };
}
