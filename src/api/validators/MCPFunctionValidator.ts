import { injectable, inject } from 'inversify';
import { z } from 'zod';
import { BaseValidator } from './BaseValidator';
import { MCPFunction } from '../../infrastructure/database/entities/MCPFunction.entity';
import { ValidationError } from '../../core/errors/ValidationError';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * Validator for MCP Function entities
 */
@injectable()
export class MCPFunctionValidator extends BaseValidator<MCPFunction> {
  /**
   * Constructor
   * @param logger Logger
   */
  constructor(@inject(TYPES.Logger) logger: ILogger) {
    super(logger);
  }
  /**
   * Schema for handler configuration
   */
  private handlerConfigSchema = z
    .object({
      type: z.enum(['script', 'workflow'], {
        errorMap: () => ({ message: 'Handler type must be either "script" or "workflow"' })
      }),
      script_content: z.string().optional(),
      workflow_id: z.string().uuid().optional()
    })
    .refine(
      (data) => {
        if (data.type === 'script' && !data.script_content) {
          return false;
        }
        if (data.type === 'workflow' && !data.workflow_id) {
          return false;
        }
        return true;
      },
      {
        message: 'Script handler requires script_content, workflow handler requires workflow_id'
      }
    );

  /**
   * Schema for input schema validation
   */
  private inputSchemaSchema = z
    .object({
      type: z.literal('object'),
      properties: z.record(z.any()).optional(),
      required: z.array(z.string()).optional(),
      additionalProperties: z.boolean().optional()
    })
    .or(z.record(z.any())); // Allow any valid JSON schema

  /**
   * Schema for creating a new MCP function
   */
  protected createSchema = z.object({
    name: z
      .string()
      .min(1, 'Name is required')
      .max(255, 'Name must be less than 255 characters')
      .regex(
        /^[a-zA-Z][a-zA-Z0-9_-]*$/,
        'Name must start with a letter and contain only letters, numbers, underscores, and hyphens'
      ),
    description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
    input_schema: this.inputSchemaSchema,
    handler_config: this.handlerConfigSchema,
    enabled: z.boolean().default(true)
  });

  /**
   * Schema for updating an MCP function
   */
  protected updateSchema = z.object({
    name: z
      .string()
      .min(1, 'Name is required')
      .max(255, 'Name must be less than 255 characters')
      .regex(
        /^[a-zA-Z][a-zA-Z0-9_-]*$/,
        'Name must start with a letter and contain only letters, numbers, underscores, and hyphens'
      )
      .optional(),
    description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
    input_schema: this.inputSchemaSchema.optional(),
    handler_config: this.handlerConfigSchema.optional(),
    enabled: z.boolean().optional()
  });

  /**
   * Validate function for creation
   * @param data Function data
   * @returns Validated function data
   */
  async validateForCreate(data: any): Promise<MCPFunction> {
    try {
      const validated = this.createSchema.parse(data);

      // Additional validation for script content
      if (validated.handler_config.type === 'script') {
        this.validateScriptContent(validated.handler_config.script_content!);
      }

      return validated as MCPFunction;
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError(this.formatZodError(error));
      }
      throw error;
    }
  }

  /**
   * Validate function for update
   * @param id Function ID
   * @param data Function data
   * @returns Validated function data
   */
  async validateForUpdate(id: string, data: any): Promise<Partial<MCPFunction>> {
    try {
      const validated = this.updateSchema.parse(data);

      // Additional validation for script content if provided
      if (validated.handler_config?.type === 'script' && validated.handler_config.script_content) {
        this.validateScriptContent(validated.handler_config.script_content);
      }

      return validated;
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError(this.formatZodError(error));
      }
      throw error;
    }
  }

  /**
   * Validate script content
   * @param scriptContent Script content to validate
   */
  private validateScriptContent(scriptContent: string): void {
    // Basic validation - check for dangerous patterns
    const dangerousPatterns = [
      /require\s*\(\s*['"]fs['"]\s*\)/,
      /require\s*\(\s*['"]child_process['"]\s*\)/,
      /require\s*\(\s*['"]os['"]\s*\)/,
      /process\.exit/,
      /process\.kill/,
      /eval\s*\(/,
      /Function\s*\(/,
      /setTimeout\s*\(/,
      /setInterval\s*\(/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(scriptContent)) {
        throw new ValidationError(
          `Script content contains potentially dangerous code: ${pattern.source}`
        );
      }
    }

    // Check script length
    if (scriptContent.length > 50000) {
      throw new ValidationError('Script content is too long (maximum 50,000 characters)');
    }

    // Basic syntax check - try to parse as JavaScript
    try {
      new Function(scriptContent);
    } catch (error) {
      throw new ValidationError(`Script content has syntax errors: ${(error as Error).message}`);
    }
  }

  /**
   * Validate function name uniqueness
   * @param name Function name
   * @param excludeId Optional ID to exclude from check
   */
  async validateNameUniqueness(name: string, excludeId?: string): Promise<void> {
    // This would typically check against the repository
    // For now, we'll implement basic validation
    if (!name || name.trim().length === 0) {
      throw new ValidationError('Function name is required');
    }

    // Reserved function names
    const reservedNames = ['echo', 'test', 'admin', 'system', 'workflow'];
    if (reservedNames.includes(name.toLowerCase())) {
      throw new ValidationError(`Function name "${name}" is reserved`);
    }
  }

  /**
   * Validate input schema
   * @param schema Input schema to validate
   */
  validateInputSchema(schema: any): void {
    try {
      this.inputSchemaSchema.parse(schema);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError(`Invalid input schema: ${this.formatZodError(error)}`);
      }
      throw error;
    }
  }

  /**
   * Validate handler configuration
   * @param config Handler configuration to validate
   */
  validateHandlerConfig(config: any): void {
    try {
      this.handlerConfigSchema.parse(config);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new ValidationError(`Invalid handler configuration: ${this.formatZodError(error)}`);
      }
      throw error;
    }
  }

  /**
   * Format Zod error for user-friendly display
   * @param error Zod error
   * @returns Formatted error message
   */
  private formatZodError(error: z.ZodError): string {
    return error.errors.map((err) => `${err.path.join('.')}: ${err.message}`).join(', ');
  }
}
