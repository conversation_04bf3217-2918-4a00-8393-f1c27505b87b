import { injectable, inject } from 'inversify';
import { IValidator } from '../../core/interfaces/IValidator';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';
import { validate as uuidValidate } from 'uuid';

/**
 * Base validator class
 * Provides common validation functionality for all validators
 */
@injectable()
export abstract class BaseValidator<T> implements IValidator<T> {
  /**
   * Constructor
   * @param logger Logger
   */
  constructor(@inject(TYPES.Logger) protected logger: ILogger) {}

  /**
   * Validate entity for creation
   * @param data Entity data
   * @returns Validated entity data
   * @throws ValidationError if validation fails
   */
  public abstract validateForCreate(data: any): Promise<T>;

  /**
   * Validate entity for update
   * @param id Entity ID
   * @param data Entity data
   * @returns Validated entity data
   * @throws ValidationError if validation fails
   */
  public abstract validateForUpdate(id: string, data: any): Promise<Partial<T>>;

  /**
   * Validate entity ID
   * @param id Entity ID
   * @throws ValidationError if validation fails
   */
  public validateId(id: string): void {
    if (!id) {
      throw new ValidationError('ID is required');
    }

    if (!uuidValidate(id)) {
      throw new ValidationError('Invalid ID format', {
        errors: [
          {
            field: 'id',
            message: 'ID must be a valid UUID',
            code: ErrorCode.INVALID_FORMAT,
            value: id
          }
        ]
      });
    }
  }

  /**
   * Validate required fields
   * @param data Data to validate
   * @param requiredFields Required fields
   * @throws ValidationError if validation fails
   */
  protected validateRequiredFields(data: any, requiredFields: string[]): void {
    const errors = [];

    for (const field of requiredFields) {
      if (data[field] === undefined || data[field] === null || data[field] === '') {
        errors.push({
          field,
          message: `${field} is required`,
          code: ErrorCode.REQUIRED_FIELD,
          value: data[field]
        });
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate string field
   * @param data Data to validate
   * @param field Field name
   * @param options Validation options
   * @throws ValidationError if validation fails
   */
  protected validateStringField(
    data: any,
    field: string,
    options: {
      required?: boolean;
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
    } = {}
  ): void {
    const { required = false, minLength, maxLength, pattern } = options;
    const value = data[field];

    // Check if required
    if (required && (value === undefined || value === null || value === '')) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} is required`,
            code: ErrorCode.REQUIRED_FIELD,
            value
          }
        ]
      });
    }

    // Skip further validation if value is not provided and not required
    if ((value === undefined || value === null || value === '') && !required) {
      return;
    }

    // Check type
    if (typeof value !== 'string') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} must be a string`,
            code: ErrorCode.INVALID_TYPE,
            value,
            expected: 'string'
          }
        ]
      });
    }

    // Check min length
    if (minLength !== undefined && value.length < minLength) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} must be at least ${minLength} characters`,
            code: ErrorCode.INVALID_LENGTH,
            value,
            expected: `>= ${minLength} characters`
          }
        ]
      });
    }

    // Check max length
    if (maxLength !== undefined && value.length > maxLength) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} must be at most ${maxLength} characters`,
            code: ErrorCode.INVALID_LENGTH,
            value,
            expected: `<= ${maxLength} characters`
          }
        ]
      });
    }

    // Check pattern
    if (pattern !== undefined && !pattern.test(value)) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} has invalid format`,
            code: ErrorCode.INVALID_FORMAT,
            value,
            expected: pattern.toString()
          }
        ]
      });
    }
  }

  /**
   * Validate boolean field
   * @param data Data to validate
   * @param field Field name
   * @param required Whether the field is required
   * @throws ValidationError if validation fails
   */
  protected validateBooleanField(data: any, field: string, required = false): void {
    const value = data[field];

    // Check if required
    if (required && (value === undefined || value === null)) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} is required`,
            code: ErrorCode.REQUIRED_FIELD,
            value
          }
        ]
      });
    }

    // Skip further validation if value is not provided and not required
    if ((value === undefined || value === null) && !required) {
      return;
    }

    // Check type
    if (typeof value !== 'boolean') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field,
            message: `${field} must be a boolean`,
            code: ErrorCode.INVALID_TYPE,
            value,
            expected: 'boolean'
          }
        ]
      });
    }
  }
}
