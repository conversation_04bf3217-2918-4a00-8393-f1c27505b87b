import { injectable, inject } from 'inversify';
import { BaseValidator } from './BaseValidator';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';
import { DataSource as DataSourceEntity } from '../../infrastructure/database/entities/DataSource.entity';

/**
 * Data source validator
 * Validates data source configurations
 */
@injectable()
export class DataSourceValidator extends BaseValidator<DataSourceEntity> {
  /**
   * Constructor
   * @param logger Logger
   */
  constructor(@inject(TYPES.Logger) logger: ILogger) {
    super(logger);
  }

  /**
   * Validate data source for creation
   * @param data Data source data
   * @returns Validated data source data
   * @throws ValidationError if validation fails
   */
  public async validateForCreate(data: any): Promise<DataSourceEntity> {
    this.logger.debug('Validating data source for creation', { data });

    // Validate required fields
    this.validateRequiredFields(data, ['name', 'type', 'connection_config']);

    // Validate string fields
    this.validateStringField(data, 'name', { required: true, maxLength: 255 });
    this.validateStringField(data, 'description', { maxLength: 1000 });
    this.validateStringField(data, 'type', { required: true, maxLength: 50 });

    // Validate connection config
    this.validateConnectionConfig(data.type, data.connection_config);

    // Validate boolean fields
    this.validateBooleanField(data, 'enabled');

    // Set default values
    const dataSource: DataSourceEntity = {
      ...data,
      enabled: data.enabled !== undefined ? data.enabled : true,
      // The connection_config will be encrypted by the controller before saving
      connection_config_encrypted: ''
    } as DataSourceEntity;

    return dataSource;
  }

  /**
   * Validate data source for update
   * @param id Data source ID
   * @param data Data source data
   * @returns Validated data source data
   * @throws ValidationError if validation fails
   */
  public async validateForUpdate(id: string, data: any): Promise<Partial<DataSourceEntity>> {
    this.logger.debug('Validating data source for update', { id, data });

    // Validate ID
    this.validateId(id);

    // Validate string fields if provided
    if (data.name !== undefined) {
      this.validateStringField(data, 'name', { required: true, maxLength: 255 });
    }

    if (data.description !== undefined) {
      this.validateStringField(data, 'description', { maxLength: 1000 });
    }

    if (data.type !== undefined) {
      this.validateStringField(data, 'type', { required: true, maxLength: 50 });
    }

    // Validate connection config if provided
    if (data.connection_config !== undefined) {
      if (data.type !== undefined) {
        this.validateConnectionConfig(data.type, data.connection_config);
      } else {
        // If type is not provided in the update, we need to get it from the existing data source
        // This would typically be done in the controller, but we'll assume it's valid for now
        this.validateConnectionConfig('unknown', data.connection_config);
      }
    }

    // Validate boolean fields if provided
    if (data.enabled !== undefined) {
      this.validateBooleanField(data, 'enabled');
    }

    // The connection_config will be encrypted by the controller before saving
    const updatedDataSource: Partial<DataSourceEntity> = { ...data } as any;
    delete (updatedDataSource as any).connection_config;
    updatedDataSource.connection_config_encrypted = '';

    return updatedDataSource;
  }

  /**
   * Validate connection configuration
   * @param type Data source type
   * @param config Connection configuration
   * @throws ValidationError if validation fails
   */
  private validateConnectionConfig(type: string, config: any): void {
    if (!config || typeof config !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'connection_config',
            message: 'connection_config must be an object',
            code: ErrorCode.INVALID_TYPE,
            value: config,
            expected: 'object'
          }
        ]
      });
    }

    // Specific validation for different data source types
    switch (type) {
      case 'postgresql':
        this.validatePostgresqlConfig(config);
        break;
      case 'mysql':
        this.validateMysqlConfig(config);
        break;
      case 'redis':
        this.validateRedisConfig(config);
        break;
      case 'rest':
        this.validateRestConfig(config);
        break;
      case 'litellm':
        this.validateLiteLlmConfig(config);
        break;
      default:
        // For unknown or custom data source types, we just validate that it's an object
        break;
    }
  }

  /**
   * Validate PostgreSQL connection configuration
   * @param config Connection configuration
   * @throws ValidationError if validation fails
   */
  private validatePostgresqlConfig(config: any): void {
    const errors = [];

    // Validate host
    if (!config.host) {
      errors.push({
        field: 'connection_config.host',
        message: 'host is required for PostgreSQL data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.host
      });
    } else if (typeof config.host !== 'string') {
      errors.push({
        field: 'connection_config.host',
        message: 'host must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.host,
        expected: 'string'
      });
    }

    // Validate port
    if (config.port !== undefined) {
      if (typeof config.port !== 'number' || config.port <= 0 || config.port > 65535) {
        errors.push({
          field: 'connection_config.port',
          message: 'port must be a number between 1 and 65535',
          code: ErrorCode.INVALID_TYPE,
          value: config.port,
          expected: 'number between 1 and 65535'
        });
      }
    }

    // Validate database
    if (!config.database) {
      errors.push({
        field: 'connection_config.database',
        message: 'database is required for PostgreSQL data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.database
      });
    } else if (typeof config.database !== 'string') {
      errors.push({
        field: 'connection_config.database',
        message: 'database must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.database,
        expected: 'string'
      });
    }

    // Validate username
    if (!config.username) {
      errors.push({
        field: 'connection_config.username',
        message: 'username is required for PostgreSQL data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.username
      });
    } else if (typeof config.username !== 'string') {
      errors.push({
        field: 'connection_config.username',
        message: 'username must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.username,
        expected: 'string'
      });
    }

    // Validate password
    if (!config.password) {
      errors.push({
        field: 'connection_config.password',
        message: 'password is required for PostgreSQL data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.password
      });
    } else if (typeof config.password !== 'string') {
      errors.push({
        field: 'connection_config.password',
        message: 'password must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.password,
        expected: 'string'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate MySQL connection configuration
   * @param config Connection configuration
   * @throws ValidationError if validation fails
   */
  private validateMysqlConfig(config: any): void {
    // MySQL validation is similar to PostgreSQL
    this.validatePostgresqlConfig(config);
  }

  /**
   * Validate Redis connection configuration
   * @param config Connection configuration
   * @throws ValidationError if validation fails
   */
  private validateRedisConfig(config: any): void {
    const errors = [];

    // Validate host
    if (!config.host) {
      errors.push({
        field: 'connection_config.host',
        message: 'host is required for Redis data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.host
      });
    } else if (typeof config.host !== 'string') {
      errors.push({
        field: 'connection_config.host',
        message: 'host must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.host,
        expected: 'string'
      });
    }

    // Validate port
    if (config.port !== undefined) {
      if (typeof config.port !== 'number' || config.port <= 0 || config.port > 65535) {
        errors.push({
          field: 'connection_config.port',
          message: 'port must be a number between 1 and 65535',
          code: ErrorCode.INVALID_TYPE,
          value: config.port,
          expected: 'number between 1 and 65535'
        });
      }
    }

    // Validate password if provided
    if (config.password !== undefined && typeof config.password !== 'string') {
      errors.push({
        field: 'connection_config.password',
        message: 'password must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.password,
        expected: 'string'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate REST API connection configuration
   * @param config Connection configuration
   * @throws ValidationError if validation fails
   */
  private validateRestConfig(config: any): void {
    const errors = [];

    // Validate baseUrl
    if (!config.baseUrl) {
      errors.push({
        field: 'connection_config.baseUrl',
        message: 'baseUrl is required for REST API data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.baseUrl
      });
    } else if (typeof config.baseUrl !== 'string') {
      errors.push({
        field: 'connection_config.baseUrl',
        message: 'baseUrl must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.baseUrl,
        expected: 'string'
      });
    }

    // Validate headers if provided
    if (config.headers !== undefined && typeof config.headers !== 'object') {
      errors.push({
        field: 'connection_config.headers',
        message: 'headers must be an object',
        code: ErrorCode.INVALID_TYPE,
        value: config.headers,
        expected: 'object'
      });
    }

    // Validate auth if provided
    if (config.auth !== undefined) {
      if (typeof config.auth !== 'object') {
        errors.push({
          field: 'connection_config.auth',
          message: 'auth must be an object',
          code: ErrorCode.INVALID_TYPE,
          value: config.auth,
          expected: 'object'
        });
      } else {
        // Validate auth type
        if (!config.auth.type) {
          errors.push({
            field: 'connection_config.auth.type',
            message: 'auth.type is required when auth is provided',
            code: ErrorCode.REQUIRED_FIELD,
            value: config.auth.type
          });
        } else if (typeof config.auth.type !== 'string') {
          errors.push({
            field: 'connection_config.auth.type',
            message: 'auth.type must be a string',
            code: ErrorCode.INVALID_TYPE,
            value: config.auth.type,
            expected: 'string'
          });
        } else if (!['basic', 'bearer', 'apiKey'].includes(config.auth.type)) {
          errors.push({
            field: 'connection_config.auth.type',
            message: 'auth.type must be one of: basic, bearer, apiKey',
            code: ErrorCode.INVALID_VALUE,
            value: config.auth.type,
            expected: 'basic, bearer, apiKey'
          });
        }

        // Validate auth credentials based on type
        switch (config.auth.type) {
          case 'basic':
            if (!config.auth.username) {
              errors.push({
                field: 'connection_config.auth.username',
                message: 'auth.username is required for basic authentication',
                code: ErrorCode.REQUIRED_FIELD,
                value: config.auth.username
              });
            }
            if (!config.auth.password) {
              errors.push({
                field: 'connection_config.auth.password',
                message: 'auth.password is required for basic authentication',
                code: ErrorCode.REQUIRED_FIELD,
                value: config.auth.password
              });
            }
            break;
          case 'bearer':
            if (!config.auth.token) {
              errors.push({
                field: 'connection_config.auth.token',
                message: 'auth.token is required for bearer authentication',
                code: ErrorCode.REQUIRED_FIELD,
                value: config.auth.token
              });
            }
            break;
          case 'apiKey':
            if (!config.auth.key) {
              errors.push({
                field: 'connection_config.auth.key',
                message: 'auth.key is required for apiKey authentication',
                code: ErrorCode.REQUIRED_FIELD,
                value: config.auth.key
              });
            }
            if (!config.auth.value) {
              errors.push({
                field: 'connection_config.auth.value',
                message: 'auth.value is required for apiKey authentication',
                code: ErrorCode.REQUIRED_FIELD,
                value: config.auth.value
              });
            }
            if (!config.auth.in) {
              errors.push({
                field: 'connection_config.auth.in',
                message: 'auth.in is required for apiKey authentication',
                code: ErrorCode.REQUIRED_FIELD,
                value: config.auth.in
              });
            } else if (!['header', 'query'].includes(config.auth.in)) {
              errors.push({
                field: 'connection_config.auth.in',
                message: 'auth.in must be one of: header, query',
                code: ErrorCode.INVALID_VALUE,
                value: config.auth.in,
                expected: 'header, query'
              });
            }
            break;
        }
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate LiteLLM connection configuration
   * @param config Connection configuration
   * @throws ValidationError if validation fails
   */
  private validateLiteLlmConfig(config: any): void {
    const errors = [];

    // Validate apiUrl
    if (!config.apiUrl) {
      errors.push({
        field: 'connection_config.apiUrl',
        message: 'apiUrl is required for LiteLLM data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.apiUrl
      });
    } else if (typeof config.apiUrl !== 'string') {
      errors.push({
        field: 'connection_config.apiUrl',
        message: 'apiUrl must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.apiUrl,
        expected: 'string'
      });
    }

    // Validate apiKey
    if (!config.apiKey) {
      errors.push({
        field: 'connection_config.apiKey',
        message: 'apiKey is required for LiteLLM data sources',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.apiKey
      });
    } else if (typeof config.apiKey !== 'string') {
      errors.push({
        field: 'connection_config.apiKey',
        message: 'apiKey must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.apiKey,
        expected: 'string'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }
}
