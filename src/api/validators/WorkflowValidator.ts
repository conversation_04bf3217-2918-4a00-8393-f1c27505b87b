import { injectable, inject } from 'inversify';
import { BaseValidator } from './BaseValidator';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';
import { Workflow } from '../../infrastructure/database/entities/Workflow.entity';
import type { INodeRegistry } from '../../core/interfaces/INodeRegistry';

/**
 * Workflow validator
 * Validates workflow configurations
 */
@injectable()
export class WorkflowValidator extends BaseValidator<Workflow> {
  /**
   * Constructor
   * @param logger Logger
   * @param nodeRegistry Node registry
   */
  constructor(
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.NodeRegistry) private nodeRegistry: INodeRegistry
  ) {
    super(logger);
  }

  /**
   * Validate workflow for creation
   * @param data Workflow data
   * @returns Validated workflow data
   * @throws ValidationError if validation fails
   */
  public async validateForCreate(data: any): Promise<Workflow> {
    this.logger.debug('Validating workflow for creation', { data });

    // Add default values for missing required fields
    if (!data.input_schema) {
      data.input_schema = {
        type: 'object',
        properties: {}
      };
    }

    if (!data.nodes_config) {
      data.nodes_config = {
        nodes: [],
        edges: []
      };
    }

    // Validate required fields
    this.validateRequiredFields(data, ['name', 'input_schema', 'nodes_config']);

    // Validate string fields
    this.validateStringField(data, 'name', { required: true, maxLength: 255 });
    this.validateStringField(data, 'description', { maxLength: 1000 });

    // Validate input schema
    this.validateSchema(data.input_schema, 'input_schema');

    // Validate output schema if provided
    if (data.output_schema) {
      this.validateSchema(data.output_schema, 'output_schema');
    }

    // Validate nodes configuration
    this.validateNodesConfig(data.nodes_config);

    // Validate retry configuration if provided
    if (data.retry_config) {
      this.validateRetryConfig(data.retry_config);
    }

    // Validate observability configuration if provided
    if (data.observability_config) {
      this.validateObservabilityConfig(data.observability_config);
    }

    // Set default values
    const workflow: Workflow = {
      ...data,
      version: data.version || 1,
      enabled: data.enabled !== undefined ? data.enabled : true
    } as Workflow;

    return workflow;
  }

  /**
   * Validate workflow for update
   * @param id Workflow ID
   * @param data Workflow data
   * @returns Validated workflow data
   * @throws ValidationError if validation fails
   */
  public async validateForUpdate(id: string, data: any): Promise<Partial<Workflow>> {
    this.logger.debug('Validating workflow for update', { id, data });

    // Validate ID
    this.validateId(id);

    // Validate string fields if provided
    if (data.name !== undefined) {
      this.validateStringField(data, 'name', { required: true, maxLength: 255 });
    }

    if (data.description !== undefined) {
      this.validateStringField(data, 'description', { maxLength: 1000 });
    }

    // Validate input schema if provided
    if (data.input_schema !== undefined) {
      this.validateSchema(data.input_schema, 'input_schema');
    }

    // Validate output schema if provided
    if (data.output_schema !== undefined) {
      this.validateSchema(data.output_schema, 'output_schema');
    }

    // Validate nodes configuration if provided
    if (data.nodes_config !== undefined) {
      this.validateNodesConfig(data.nodes_config);
    }

    // Validate retry configuration if provided
    if (data.retry_config !== undefined) {
      this.validateRetryConfig(data.retry_config);
    }

    // Validate observability configuration if provided
    if (data.observability_config !== undefined) {
      this.validateObservabilityConfig(data.observability_config);
    }

    // Validate boolean fields if provided
    if (data.enabled !== undefined) {
      this.validateBooleanField(data, 'enabled');
    }

    // Increment version if nodes_config is updated
    const updatedWorkflow: Partial<Workflow> = { ...data };
    if (data.nodes_config !== undefined) {
      updatedWorkflow.version = data.version !== undefined ? data.version : undefined;
    }

    return updatedWorkflow;
  }

  /**
   * Validate JSON schema
   * @param schema JSON schema
   * @param fieldName Field name
   * @throws ValidationError if validation fails
   */
  private validateSchema(schema: any, fieldName: string): void {
    if (!schema || typeof schema !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: fieldName,
            message: `${fieldName} must be a valid JSON schema object`,
            code: ErrorCode.INVALID_TYPE,
            value: schema,
            expected: 'object'
          }
        ]
      });
    }

    // Basic schema validation - could be extended with more comprehensive validation
    if (schema.type === 'object' && schema.properties && typeof schema.properties !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: `${fieldName}.properties`,
            message: 'properties must be an object',
            code: ErrorCode.INVALID_TYPE,
            value: schema.properties,
            expected: 'object'
          }
        ]
      });
    }
  }

  /**
   * Validate nodes configuration
   * @param nodesConfig Nodes configuration
   * @throws ValidationError if validation fails
   */
  private validateNodesConfig(nodesConfig: any): void {
    if (!nodesConfig || typeof nodesConfig !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'nodes_config',
            message: 'nodes_config must be an object',
            code: ErrorCode.INVALID_TYPE,
            value: nodesConfig,
            expected: 'object'
          }
        ]
      });
    }

    // Validate nodes array
    if (!Array.isArray(nodesConfig.nodes)) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'nodes_config.nodes',
            message: 'nodes must be an array',
            code: ErrorCode.INVALID_TYPE,
            value: nodesConfig.nodes,
            expected: 'array'
          }
        ]
      });
    }

    // Allow empty nodes array for new workflows (they can be configured later)
    // if (nodesConfig.nodes.length === 0) {
    //   throw new ValidationError('Validation failed', {
    //     errors: [
    //       {
    //         field: 'nodes_config.nodes',
    //         message: 'nodes array cannot be empty',
    //         code: ErrorCode.INVALID_LENGTH,
    //         value: nodesConfig.nodes,
    //         expected: 'non-empty array'
    //       }
    //     ]
    //   });
    // }

    // Validate each node
    const nodeIds = new Set<string>();
    const errors = [];

    for (let i = 0; i < nodesConfig.nodes.length; i++) {
      const node = nodesConfig.nodes[i];

      // Check required fields
      if (!node.id) {
        errors.push({
          field: `nodes_config.nodes[${i}].id`,
          message: 'node id is required',
          code: ErrorCode.REQUIRED_FIELD,
          value: node.id
        });
      } else if (nodeIds.has(node.id)) {
        errors.push({
          field: `nodes_config.nodes[${i}].id`,
          message: `duplicate node id: ${node.id}`,
          code: ErrorCode.DUPLICATE_VALUE,
          value: node.id
        });
      } else {
        nodeIds.add(node.id);
      }

      if (!node.type) {
        errors.push({
          field: `nodes_config.nodes[${i}].type`,
          message: 'node type is required',
          code: ErrorCode.REQUIRED_FIELD,
          value: node.type
        });
      } else {
        // Check if node type is supported
        try {
          const availableTypes = this.nodeRegistry.getRegisteredNodeTypes();
          if (!availableTypes.includes(node.type)) {
            errors.push({
              field: `nodes_config.nodes[${i}].type`,
              message: `unsupported node type: ${node.type}`,
              code: ErrorCode.INVALID_VALUE,
              value: node.type
            });
          }
        } catch (error) {
          errors.push({
            field: `nodes_config.nodes[${i}].type`,
            message: `failed to validate node type: ${node.type}`,
            code: ErrorCode.VALIDATION_ERROR,
            value: node.type
          });
        }
      }

      if (!node.name) {
        errors.push({
          field: `nodes_config.nodes[${i}].name`,
          message: 'node name is required',
          code: ErrorCode.REQUIRED_FIELD,
          value: node.name
        });
      }

      if (!node.config || typeof node.config !== 'object') {
        errors.push({
          field: `nodes_config.nodes[${i}].config`,
          message: 'node config must be an object',
          code: ErrorCode.INVALID_TYPE,
          value: node.config,
          expected: 'object'
        });
      }
    }

    // Validate edges array
    if (!Array.isArray(nodesConfig.edges)) {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'nodes_config.edges',
            message: 'edges must be an array',
            code: ErrorCode.INVALID_TYPE,
            value: nodesConfig.edges,
            expected: 'array'
          }
        ]
      });
    }

    // Validate each edge
    for (let i = 0; i < nodesConfig.edges.length; i++) {
      const edge = nodesConfig.edges[i];

      // Check required fields
      if (!edge.source) {
        errors.push({
          field: `nodes_config.edges[${i}].source`,
          message: 'edge source is required',
          code: ErrorCode.REQUIRED_FIELD,
          value: edge.source
        });
      } else if (!nodeIds.has(edge.source)) {
        errors.push({
          field: `nodes_config.edges[${i}].source`,
          message: `edge source references non-existent node: ${edge.source}`,
          code: ErrorCode.INVALID_REFERENCE,
          value: edge.source
        });
      }

      if (!edge.target) {
        errors.push({
          field: `nodes_config.edges[${i}].target`,
          message: 'edge target is required',
          code: ErrorCode.REQUIRED_FIELD,
          value: edge.target
        });
      } else if (!nodeIds.has(edge.target)) {
        errors.push({
          field: `nodes_config.edges[${i}].target`,
          message: `edge target references non-existent node: ${edge.target}`,
          code: ErrorCode.INVALID_REFERENCE,
          value: edge.target
        });
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate retry configuration
   * @param retryConfig Retry configuration
   * @throws ValidationError if validation fails
   */
  private validateRetryConfig(retryConfig: any): void {
    if (!retryConfig || typeof retryConfig !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'retry_config',
            message: 'retry_config must be an object',
            code: ErrorCode.INVALID_TYPE,
            value: retryConfig,
            expected: 'object'
          }
        ]
      });
    }

    const errors = [];

    // Validate maxRetries
    if (retryConfig.maxRetries !== undefined) {
      if (typeof retryConfig.maxRetries !== 'number' || retryConfig.maxRetries < 0) {
        errors.push({
          field: 'retry_config.maxRetries',
          message: 'maxRetries must be a non-negative number',
          code: ErrorCode.INVALID_TYPE,
          value: retryConfig.maxRetries,
          expected: 'non-negative number'
        });
      }
    }

    // Validate retryDelay
    if (retryConfig.retryDelay !== undefined) {
      if (typeof retryConfig.retryDelay !== 'number' || retryConfig.retryDelay < 0) {
        errors.push({
          field: 'retry_config.retryDelay',
          message: 'retryDelay must be a non-negative number',
          code: ErrorCode.INVALID_TYPE,
          value: retryConfig.retryDelay,
          expected: 'non-negative number'
        });
      }
    }

    // Validate retryBackoffMultiplier
    if (retryConfig.retryBackoffMultiplier !== undefined) {
      if (
        typeof retryConfig.retryBackoffMultiplier !== 'number' ||
        retryConfig.retryBackoffMultiplier < 1
      ) {
        errors.push({
          field: 'retry_config.retryBackoffMultiplier',
          message: 'retryBackoffMultiplier must be a number greater than or equal to 1',
          code: ErrorCode.INVALID_TYPE,
          value: retryConfig.retryBackoffMultiplier,
          expected: 'number >= 1'
        });
      }
    }

    // Validate retryableErrors
    if (retryConfig.retryableErrors !== undefined) {
      if (!Array.isArray(retryConfig.retryableErrors)) {
        errors.push({
          field: 'retry_config.retryableErrors',
          message: 'retryableErrors must be an array',
          code: ErrorCode.INVALID_TYPE,
          value: retryConfig.retryableErrors,
          expected: 'array'
        });
      } else {
        for (let i = 0; i < retryConfig.retryableErrors.length; i++) {
          if (typeof retryConfig.retryableErrors[i] !== 'string') {
            errors.push({
              field: `retry_config.retryableErrors[${i}]`,
              message: 'retryableErrors must contain only strings',
              code: ErrorCode.INVALID_TYPE,
              value: retryConfig.retryableErrors[i],
              expected: 'string'
            });
          }
        }
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate observability configuration
   * @param observabilityConfig Observability configuration
   * @throws ValidationError if validation fails
   */
  private validateObservabilityConfig(observabilityConfig: any): void {
    if (!observabilityConfig || typeof observabilityConfig !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'observability_config',
            message: 'observability_config must be an object',
            code: ErrorCode.INVALID_TYPE,
            value: observabilityConfig,
            expected: 'object'
          }
        ]
      });
    }

    const errors = [];

    // Validate logLevel
    if (observabilityConfig.logLevel !== undefined) {
      const validLogLevels = ['error', 'warn', 'info', 'debug', 'trace'];
      if (!validLogLevels.includes(observabilityConfig.logLevel)) {
        errors.push({
          field: 'observability_config.logLevel',
          message: `logLevel must be one of: ${validLogLevels.join(', ')}`,
          code: ErrorCode.INVALID_VALUE,
          value: observabilityConfig.logLevel,
          expected: validLogLevels.join(', ')
        });
      }
    }

    // Validate metricsEnabled
    if (observabilityConfig.metricsEnabled !== undefined) {
      if (typeof observabilityConfig.metricsEnabled !== 'boolean') {
        errors.push({
          field: 'observability_config.metricsEnabled',
          message: 'metricsEnabled must be a boolean',
          code: ErrorCode.INVALID_TYPE,
          value: observabilityConfig.metricsEnabled,
          expected: 'boolean'
        });
      }
    }

    // Validate logInputOutput
    if (observabilityConfig.logInputOutput !== undefined) {
      if (typeof observabilityConfig.logInputOutput !== 'boolean') {
        errors.push({
          field: 'observability_config.logInputOutput',
          message: 'logInputOutput must be a boolean',
          code: ErrorCode.INVALID_TYPE,
          value: observabilityConfig.logInputOutput,
          expected: 'boolean'
        });
      }
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }
}
