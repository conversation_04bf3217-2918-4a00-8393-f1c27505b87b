import { injectable, inject } from 'inversify';
import { BaseValidator } from './BaseValidator';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';
import { NodeConfig } from '../../infrastructure/database/entities/NodeConfig.entity';
import type { INodeRegistry } from '../../core/interfaces/INodeRegistry';

/**
 * Node validator
 * Validates node configurations
 */
@injectable()
export class NodeValidator extends BaseValidator<NodeConfig> {
  /**
   * Constructor
   * @param logger Logger
   * @param nodeRegistry Node registry
   */
  constructor(
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.NodeRegistry) private nodeRegistry: INodeRegistry
  ) {
    super(logger);
  }

  /**
   * Validate node configuration for creation
   * @param data Node configuration data
   * @returns Validated node configuration data
   * @throws ValidationError if validation fails
   */
  public async validateForCreate(data: any): Promise<NodeConfig> {
    this.logger.debug('Validating node configuration for creation', { data });

    // Validate required fields
    this.validateRequiredFields(data, ['name', 'node_type', 'config']);

    // Validate string fields
    this.validateStringField(data, 'name', { required: true, maxLength: 255 });
    this.validateStringField(data, 'description', { maxLength: 1000 });
    this.validateStringField(data, 'node_type', { required: true, maxLength: 50 });

    // Validate node type
    this.validateNodeType(data.node_type);

    // Validate config
    this.validateNodeConfig(data.node_type, data.config);

    // Validate boolean fields
    this.validateBooleanField(data, 'enabled');

    // Set default values
    const nodeConfig: NodeConfig = {
      ...data,
      enabled: data.enabled !== undefined ? data.enabled : true
    } as NodeConfig;

    return nodeConfig;
  }

  /**
   * Validate node configuration for update
   * @param id Node configuration ID
   * @param data Node configuration data
   * @returns Validated node configuration data
   * @throws ValidationError if validation fails
   */
  public async validateForUpdate(id: string, data: any): Promise<Partial<NodeConfig>> {
    this.logger.debug('Validating node configuration for update', { id, data });

    // Validate ID
    this.validateId(id);

    // Validate string fields if provided
    if (data.name !== undefined) {
      this.validateStringField(data, 'name', { required: true, maxLength: 255 });
    }

    if (data.description !== undefined) {
      this.validateStringField(data, 'description', { maxLength: 1000 });
    }

    if (data.node_type !== undefined) {
      this.validateStringField(data, 'node_type', { required: true, maxLength: 50 });
      this.validateNodeType(data.node_type);
    }

    // Validate config if provided
    if (data.config !== undefined) {
      if (data.node_type !== undefined) {
        this.validateNodeConfig(data.node_type, data.config);
      } else {
        // If node_type is not provided in the update, we need to get it from the existing node config
        // This would typically be done in the controller, but we'll assume it's valid for now
        this.validateNodeConfig('unknown', data.config);
      }
    }

    // Validate boolean fields if provided
    if (data.enabled !== undefined) {
      this.validateBooleanField(data, 'enabled');
    }

    return data;
  }

  /**
   * Validate node type
   * @param nodeType Node type
   * @throws ValidationError if validation fails
   */
  private validateNodeType(nodeType: string): void {
    try {
      const availableNodeTypes = this.nodeRegistry.getRegisteredNodeTypes();
      if (!availableNodeTypes.includes(nodeType)) {
        throw new ValidationError('Validation failed', {
          errors: [
            {
              field: 'node_type',
              message: `Unsupported node type: ${nodeType}`,
              code: ErrorCode.INVALID_VALUE,
              value: nodeType,
              expected: availableNodeTypes.join(', ')
            }
          ]
        });
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError('Failed to validate node type', {
        errors: [
          {
            field: 'node_type',
            message: 'Failed to validate node type',
            code: ErrorCode.VALIDATION_ERROR,
            value: nodeType
          }
        ]
      });
    }
  }

  /**
   * Validate node configuration
   * @param nodeType Node type
   * @param config Node configuration
   * @throws ValidationError if validation fails
   */
  private validateNodeConfig(nodeType: string, config: any): void {
    if (!config || typeof config !== 'object') {
      throw new ValidationError('Validation failed', {
        errors: [
          {
            field: 'config',
            message: 'config must be an object',
            code: ErrorCode.INVALID_TYPE,
            value: config,
            expected: 'object'
          }
        ]
      });
    }

    // Specific validation for different node types
    switch (nodeType) {
      case 'javascript-action':
        this.validateJavaScriptActionNodeConfig(config);
        break;
      case 'http-action':
        this.validateHttpActionNodeConfig(config);
        break;
      case 'sql-action':
        this.validateSqlActionNodeConfig(config);
        break;
      case 'redis-action':
        this.validateRedisActionNodeConfig(config);
        break;
      case 'litellm-action':
        this.validateLiteLlmActionNodeConfig(config);
        break;
      default:
        // For unknown or custom node types, we just validate that it's an object
        break;
    }
  }

  /**
   * Validate JavaScript Action node configuration
   * @param config Node configuration
   * @throws ValidationError if validation fails
   */
  private validateJavaScriptActionNodeConfig(config: any): void {
    const errors = [];

    // Validate script
    if (!config.script) {
      errors.push({
        field: 'config.script',
        message: 'script is required for JavaScript Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.script
      });
    } else if (typeof config.script !== 'string') {
      errors.push({
        field: 'config.script',
        message: 'script must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.script,
        expected: 'string'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate HTTP Action node configuration
   * @param config Node configuration
   * @throws ValidationError if validation fails
   */
  private validateHttpActionNodeConfig(config: any): void {
    const errors = [];

    // Validate url
    if (!config.url) {
      errors.push({
        field: 'config.url',
        message: 'url is required for HTTP Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.url
      });
    } else if (typeof config.url !== 'string') {
      errors.push({
        field: 'config.url',
        message: 'url must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.url,
        expected: 'string'
      });
    }

    // Validate method
    if (config.method && typeof config.method !== 'string') {
      errors.push({
        field: 'config.method',
        message: 'method must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.method,
        expected: 'string'
      });
    }

    // Validate headers
    if (config.headers && typeof config.headers !== 'object') {
      errors.push({
        field: 'config.headers',
        message: 'headers must be an object',
        code: ErrorCode.INVALID_TYPE,
        value: config.headers,
        expected: 'object'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate SQL Action node configuration
   * @param config Node configuration
   * @throws ValidationError if validation fails
   */
  private validateSqlActionNodeConfig(config: any): void {
    const errors = [];

    // Validate dataSource or connectionString
    if (!config.dataSource && !config.connectionString) {
      errors.push({
        field: 'config',
        message: 'Either dataSource or connectionString is required for SQL Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config
      });
    }

    // Validate query
    if (!config.query) {
      errors.push({
        field: 'config.query',
        message: 'query is required for SQL Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.query
      });
    } else if (typeof config.query !== 'string') {
      errors.push({
        field: 'config.query',
        message: 'query must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.query,
        expected: 'string'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate Redis Action node configuration
   * @param config Node configuration
   * @throws ValidationError if validation fails
   */
  private validateRedisActionNodeConfig(config: any): void {
    const errors = [];

    // Validate command or cacheKey
    if (!config.command && !config.cacheKey) {
      errors.push({
        field: 'config',
        message: 'Either command or cacheKey is required for Redis Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config
      });
    }

    // Validate command
    if (config.command && typeof config.command !== 'string') {
      errors.push({
        field: 'config.command',
        message: 'command must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.command,
        expected: 'string'
      });
    }

    // Validate args
    if (config.args && !Array.isArray(config.args)) {
      errors.push({
        field: 'config.args',
        message: 'args must be an array',
        code: ErrorCode.INVALID_TYPE,
        value: config.args,
        expected: 'array'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }

  /**
   * Validate LiteLLM Action node configuration
   * @param config Node configuration
   * @throws ValidationError if validation fails
   */
  private validateLiteLlmActionNodeConfig(config: any): void {
    const errors = [];

    // Validate apiKey or dataSource
    if (!config.apiKey && !config.dataSource) {
      errors.push({
        field: 'config',
        message: 'Either apiKey or dataSource is required for LiteLLM Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config
      });
    }

    // Validate model
    if (!config.model) {
      errors.push({
        field: 'config.model',
        message: 'model is required for LiteLLM Action nodes',
        code: ErrorCode.REQUIRED_FIELD,
        value: config.model
      });
    } else if (typeof config.model !== 'string') {
      errors.push({
        field: 'config.model',
        message: 'model must be a string',
        code: ErrorCode.INVALID_TYPE,
        value: config.model,
        expected: 'string'
      });
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', { errors });
    }
  }
}
