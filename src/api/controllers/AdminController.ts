import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../types';
import type { ITenantManager } from '../../core/interfaces/ITenantManager';
import type { IDatabase } from '../../core/interfaces/IDatabase';

interface DashboardStats {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  activeUsers: number;
  totalWorkflows: number;
  runningWorkflows: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  lastUpdated: string;
}

interface TenantMetrics {
  tenantId: string;
  tenantName: string;
  userCount: number;
  workflowCount: number;
  activeWorkflows: number;
  memoryUsage: number;
  storageUsage: number;
  lastActivity: string;
}

@injectable()
export class AdminController {
  constructor(
    @inject(TYPES.TenantManager) private tenantManager: ITenantManager,
    @inject(TYPES.Database) private database: IDatabase
  ) {}

  /**
   * Get dashboard statistics
   */
  async getDashboardStats(req: Request, res: Response): Promise<void> {
    try {
      // Get tenant statistics
      const tenants = await this.tenantManager.listTenants({ page: 1, limit: 1000 });
      const activeTenants = tenants.data.filter((t) => t.enabled).length;

      // Get user statistics
      let totalUsers = 0;
      let activeUsers = 0;

      for (const tenant of tenants.data) {
        const users = await this.tenantManager.getTenantUsers(tenant.id, { page: 1, limit: 1000 });
        totalUsers += users.total;
        activeUsers += users.data.filter((u) => u.enabled).length;
      }

      // Get workflow statistics from database
      const totalWorkflowsQuery = await this.database.query(
        'SELECT COUNT(*) as count FROM mcp_workflows'
      );
      const totalWorkflows = parseInt(totalWorkflowsQuery[0]?.count || '0');

      const runningWorkflowsQuery = await this.database.query(
        'SELECT COUNT(*) as count FROM mcp_workflow_executions WHERE status = $1',
        ['RUNNING']
      );
      const runningWorkflows = parseInt(runningWorkflowsQuery[0]?.count || '0');

      // Determine system health
      let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (runningWorkflows > totalWorkflows * 0.8) {
        systemHealth = 'warning';
      }
      if (runningWorkflows > totalWorkflows * 0.9) {
        systemHealth = 'critical';
      }

      const stats: DashboardStats = {
        totalTenants: tenants.total,
        activeTenants,
        totalUsers,
        activeUsers,
        totalWorkflows,
        runningWorkflows,
        systemHealth,
        lastUpdated: new Date().toISOString()
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Dashboard stats error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get dashboard statistics'
      });
    }
  }

  /**
   * Get tenant metrics
   */
  async getTenantMetrics(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.query;

      let tenantsToProcess;
      if (tenantId) {
        const tenant = await this.tenantManager.getTenant(tenantId as string);
        tenantsToProcess = tenant ? [tenant] : [];
      } else {
        const allTenants = await this.tenantManager.listTenants({ page: 1, limit: 1000 });
        tenantsToProcess = allTenants.data;
      }

      const metrics: TenantMetrics[] = [];

      for (const tenant of tenantsToProcess) {
        const users = await this.tenantManager.getTenantUsers(tenant.id, { page: 1, limit: 1000 });

        // Get real workflow data from database
        const workflowCountQuery = await this.database.query(
          'SELECT COUNT(*) as count FROM mcp_workflows WHERE tenant_id = $1',
          [tenant.id]
        );
        const workflowCount = parseInt(workflowCountQuery[0]?.count || '0');

        // Get active workflow executions
        const activeWorkflowsQuery = await this.database.query(
          'SELECT COUNT(*) as count FROM mcp_workflow_executions WHERE workflow_id IN (SELECT id FROM mcp_workflows WHERE tenant_id = $1) AND status = $2',
          [tenant.id, 'RUNNING']
        );
        const activeWorkflows = parseInt(activeWorkflowsQuery[0]?.count || '0');

        // Get last activity from workflow executions
        const lastActivityQuery = await this.database.query(
          'SELECT MAX(started_at) as last_activity FROM mcp_workflow_executions WHERE workflow_id IN (SELECT id FROM mcp_workflows WHERE tenant_id = $1)',
          [tenant.id]
        );
        const lastActivity =
          lastActivityQuery[0]?.last_activity || new Date(Date.now() - 24 * 60 * 60 * 1000);

        // Mock resource usage data (these would come from monitoring system in production)
        const memoryUsage = Math.floor(Math.random() * 40) + 40; // 40-80%
        const storageUsage = Math.floor(Math.random() * 50) + 30; // 30-80%

        metrics.push({
          tenantId: tenant.id,
          tenantName: tenant.name,
          userCount: users.total,
          workflowCount,
          activeWorkflows,
          memoryUsage,
          storageUsage,
          lastActivity: lastActivity.toISOString()
        });
      }

      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      console.error('Tenant metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get tenant metrics'
      });
    }
  }

  /**
   * Get system health
   */
  async getSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      // Check database connection
      let dbStatus = 'healthy';
      try {
        await this.database.query('SELECT 1');
      } catch {
        dbStatus = 'unhealthy';
      }

      // Mock other health checks
      const healthData = {
        status: dbStatus === 'healthy' ? 'healthy' : 'critical',
        details: {
          database: dbStatus,
          memory: 'healthy',
          disk: 'healthy',
          network: 'healthy',
          services: {
            tenantManager: 'healthy',
            workflowEngine: 'healthy',
            adminAPI: 'healthy'
          }
        },
        timestamp: new Date().toISOString()
      };

      res.json({
        success: true,
        data: healthData
      });
    } catch (error) {
      console.error('System health error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get system health'
      });
    }
  }

  /**
   * List all tenants
   */
  async getTenants(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const sortBy = (req.query.sortBy as string) || 'createdAt';
      const sortOrder = (req.query.sortOrder as string) || 'DESC';
      const search = req.query.search as string;

      const params = {
        page,
        limit,
        sortBy,
        sortOrder: sortOrder as 'ASC' | 'DESC',
        search
      };

      const result = await this.tenantManager.listTenants(params);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Get tenants error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get tenants'
      });
    }
  }

  /**
   * Get single tenant
   */
  async getTenant(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenant = await this.tenantManager.getTenant(id);

      if (!tenant) {
        res.status(404).json({
          success: false,
          error: 'Tenant not found'
        });
        return;
      }

      res.json({
        success: true,
        data: tenant
      });
    } catch (error) {
      console.error('Get tenant error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get tenant'
      });
    }
  }

  /**
   * Create new tenant
   */
  async createTenant(req: Request, res: Response): Promise<void> {
    try {
      const tenantData = req.body;
      const tenant = await this.tenantManager.createTenant(tenantData);

      res.status(201).json({
        success: true,
        data: tenant,
        message: 'Tenant created successfully'
      });
    } catch (error) {
      console.error('Create tenant error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create tenant'
      });
    }
  }

  /**
   * Update tenant
   */
  async updateTenant(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const tenant = await this.tenantManager.updateTenant(id, updateData);

      res.json({
        success: true,
        data: tenant,
        message: 'Tenant updated successfully'
      });
    } catch (error) {
      console.error('Update tenant error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update tenant'
      });
    }
  }

  /**
   * Delete tenant
   */
  async deleteTenant(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const success = await this.tenantManager.deleteTenant(id);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'Tenant not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Tenant deleted successfully'
      });
    } catch (error) {
      console.error('Delete tenant error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete tenant'
      });
    }
  }

  /**
   * Validate tenant settings
   */
  async validateTenantSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = req.body;
      const validation = await this.tenantManager.validateTenantSettings(settings);

      res.json({
        success: true,
        data: validation
      });
    } catch (error) {
      console.error('Validate settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to validate settings'
      });
    }
  }

  /**
   * Get tenant users
   */
  async getTenantUsers(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const sortBy = (req.query.sortBy as string) || 'createdAt';
      const sortOrder = (req.query.sortOrder as string) || 'DESC';
      const search = req.query.search as string;

      const params = {
        page,
        limit,
        sortBy,
        sortOrder: sortOrder as 'ASC' | 'DESC',
        search
      };

      const result = await this.tenantManager.getTenantUsers(tenantId, params);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('Get tenant users error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get tenant users'
      });
    }
  }

  /**
   * Create tenant user
   */
  async createTenantUser(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId } = req.params;
      const userData = req.body;

      const user = await this.tenantManager.createTenantUser(tenantId, userData);

      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully'
      });
    } catch (error) {
      console.error('Create tenant user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create user'
      });
    }
  }

  /**
   * Update tenant user
   */
  async updateTenantUser(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId, userId } = req.params;
      const updateData = req.body;

      const user = await this.tenantManager.updateTenantUser(tenantId, userId, updateData);

      res.json({
        success: true,
        data: user,
        message: 'User updated successfully'
      });
    } catch (error) {
      console.error('Update tenant user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update user'
      });
    }
  }

  /**
   * Delete tenant user
   */
  async deleteTenantUser(req: Request, res: Response): Promise<void> {
    try {
      const { tenantId, userId } = req.params;
      const success = await this.tenantManager.deleteTenantUser(tenantId, userId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      console.error('Delete tenant user error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete user'
      });
    }
  }
}
