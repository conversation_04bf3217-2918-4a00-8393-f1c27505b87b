import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { BaseController } from './BaseController';
import { INodeController } from '../../core/interfaces/INodeController';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { IValidator } from '../../core/interfaces/IValidator';
import type { IRepository } from '../../core/interfaces/IRepository';
import type { INodeRegistry } from '../../core/interfaces/INodeRegistry';
import { TYPES } from '../../types';
import { NodeConfig } from '../../infrastructure/database/entities/NodeConfig.entity';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';

/**
 * Node controller
 * Handles node-related API requests
 */
@injectable()
export class NodeController extends BaseController<NodeConfig> implements INodeController {
  /**
   * Constructor
   * @param repository Node repository
   * @param validator Node validator
   * @param logger Logger
   * @param nodeRegistry Node registry
   */
  constructor(
    @inject(TYPES.NodeConfigRepository) repository: IRepository<NodeConfig>,
    @inject(TYPES.NodeValidator) validator: IValidator<NodeConfig>,
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.NodeRegistry) private nodeRegistry: INodeRegistry
  ) {
    super(repository, validator, logger);
  }

  /**
   * Test a node configuration without saving it
   * @param req Express request
   * @param res Express response
   */
  public async test(req: Request, res: Response): Promise<void> {
    try {
      const { node, input } = req.body;

      if (!node) {
        throw new ValidationError('Node configuration is required');
      }

      if (!input) {
        throw new ValidationError('Input data is required');
      }

      // Validate node configuration
      const validatedNode = await this.validator.validateForCreate(node);

      // Create node instance
      const nodeInstance = this.nodeRegistry.getNode(validatedNode.node_type, validatedNode.config);

      // Execute node
      const result = await nodeInstance.execute(input, {
        executionId: 'test',
        workflowId: 'test',
        input,
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      });

      res.status(200).json({
        result,
        success: true
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get available node types
   * @param req Express request
   * @param res Express response
   */
  public async getNodeTypes(req: Request, res: Response): Promise<void> {
    try {
      const nodeTypes = this.nodeRegistry.getRegisteredNodeTypes();
      res.status(200).json(nodeTypes);
    } catch (error) {
      this.handleError(error, res);
    }
  }
}
