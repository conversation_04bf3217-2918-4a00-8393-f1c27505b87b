import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { IController } from '../../core/interfaces/IController';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { IValidator } from '../../core/interfaces/IValidator';
import type { IRepository } from '../../core/interfaces/IRepository';
import { TYPES } from '../../types';
import { ValidationError } from '../../core/errors/ValidationError';
import { ApplicationError } from '../../core/errors/ApplicationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';

/**
 * Base controller class
 * Provides common CRUD operations for all controllers
 */
@injectable()
export abstract class BaseController<T> implements IController {
  /**
   * Constructor
   * @param repository Repository
   * @param validator Validator
   * @param logger Logger
   */
  constructor(
    protected repository: IRepository<T>,
    protected validator: IValidator<T>,
    @inject(TYPES.Logger) protected logger: ILogger
  ) {}

  /**
   * Get all entities
   * @param req Express request
   * @param res Express response
   */
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const entities = await this.repository.findAll();
      res.status(200).json(entities);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get entity by ID
   * @param req Express request
   * @param res Express response
   */
  public async getById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      const entity = await this.repository.findById(id);
      if (!entity) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Entity not found'
          }
        });
        return;
      }

      res.status(200).json(entity);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Create a new entity
   * @param req Express request
   * @param res Express response
   */
  public async create(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug('Creating entity with data:', req.body);
      const data = await this.validator.validateForCreate(req.body);
      const entity = await this.repository.create(data);
      res.status(201).json(entity);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update an existing entity
   * @param req Express request
   * @param res Express response
   */
  public async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      // Check if entity exists
      const existingEntity = await this.repository.findById(id);
      if (!existingEntity) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Entity not found'
          }
        });
        return;
      }

      // Validate and update
      const data = await this.validator.validateForUpdate(id, req.body);
      const updatedEntity = await this.repository.update(id, data);
      res.status(200).json(updatedEntity);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete an entity
   * @param req Express request
   * @param res Express response
   */
  public async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      // Check if entity exists
      const existingEntity = await this.repository.findById(id);
      if (!existingEntity) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Entity not found'
          }
        });
        return;
      }

      await this.repository.delete(id);
      res.status(204).send();
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle error
   * @param error Error
   * @param res Express response
   */
  protected handleError(error: any, res: Response): void {
    this.logger.error(`Controller error: ${error.message}`, error);

    if (error instanceof ValidationError) {
      res.status(400).json({
        error: {
          code: error.code,
          message: error.message,
          details: error.details
        }
      });
      return;
    }

    if (error instanceof ApplicationError) {
      const statusCode = this.getStatusCodeForError(error.code);
      res.status(statusCode).json({
        error: {
          code: error.code,
          message: error.message,
          details: error.details
        }
      });
      return;
    }

    // Generic error
    res.status(500).json({
      error: {
        code: ErrorCode.INTERNAL_ERROR,
        message: 'Internal server error'
      }
    });
  }

  /**
   * Get HTTP status code for error code
   * @param errorCode Error code
   * @returns HTTP status code
   */
  private getStatusCodeForError(errorCode: ErrorCode): number {
    switch (errorCode) {
      case ErrorCode.NOT_FOUND:
        return 404;
      case ErrorCode.VALIDATION_ERROR:
      case ErrorCode.INVALID_FORMAT:
      case ErrorCode.INVALID_TYPE:
      case ErrorCode.INVALID_LENGTH:
      case ErrorCode.REQUIRED_FIELD:
        return 400;
      case ErrorCode.UNAUTHORIZED:
        return 401;
      case ErrorCode.FORBIDDEN:
        return 403;
      case ErrorCode.CONFLICT:
        return 409;
      default:
        return 500;
    }
  }
}
