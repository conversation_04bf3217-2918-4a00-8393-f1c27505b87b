import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../types';
import { BaseController } from './BaseController';
import { MCPFunction } from '../../infrastructure/database/entities/MCPFunction.entity';
import type { IRepository } from '../../core/interfaces/IRepository';
import type { IValidator } from '../../core/interfaces/IValidator';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { DynamicServiceLoader } from '../../services/DynamicServiceLoader';

/**
 * Controller for MCP Function management
 */
@injectable()
export class MCPFunctionController extends BaseController<MCPFunction> {
  constructor(
    @inject(TYPES.MCPFunctionRepository) repository: IRepository<MCPFunction>,
    @inject(TYPES.MCPFunctionValidator) validator: IValidator<MCPFunction>,
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.DynamicServiceLoader) private serviceLoader: DynamicServiceLoader
  ) {
    super(repository, validator, logger);
  }

  /**
   * Create a new MCP function
   * @param req Express request
   * @param res Express response
   */
  public async create(req: Request, res: Response): Promise<void> {
    try {
      const functionData = req.body;

      // Validate function data
      const validatedFunction = await this.validator.validateForCreate(functionData);

      // Create function in database
      const createdFunction = await this.repository.create(validatedFunction);

      // Reload functions in service loader (don't let errors here fail the response)
      try {
        await this.serviceLoader.loadAllFunctions();
      } catch (reloadError) {
        this.logger.warn('Error reloading functions after create', reloadError);
      }

      res.status(201).json({
        success: true,
        data: createdFunction,
        message: 'MCP Function created successfully'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update an existing MCP function
   * @param req Express request
   * @param res Express response
   */
  public async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      this.validator.validateId(id);

      // Check if function exists
      const existingFunction = await this.repository.findById(id);
      if (!existingFunction) {
        res.status(404).json({
          success: false,
          error: 'MCP Function not found'
        });
        return;
      }

      // Validate update data
      const validatedData = await this.validator.validateForUpdate(id, updateData);

      // Update function
      const updatedFunction = await this.repository.update(id, validatedData);

      // Reload functions in service loader (don't let errors here fail the response)
      try {
        await this.serviceLoader.loadAllFunctions();
      } catch (reloadError) {
        this.logger.warn('Error reloading functions after update', reloadError);
      }

      res.status(200).json({
        success: true,
        data: updatedFunction,
        message: 'MCP Function updated successfully'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete an MCP function
   * @param req Express request
   * @param res Express response
   */
  public async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      this.validator.validateId(id);

      // Check if function exists
      const existingFunction = await this.repository.findById(id);
      if (!existingFunction) {
        res.status(404).json({
          success: false,
          error: 'MCP Function not found'
        });
        return;
      }

      // Delete function
      await this.repository.delete(id);

      // Reload functions in service loader (don't let errors here fail the response)
      try {
        await this.serviceLoader.loadAllFunctions();
      } catch (reloadError) {
        this.logger.warn('Error reloading functions after delete', reloadError);
      }

      res.status(200).json({
        success: true,
        message: 'MCP Function deleted successfully'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Test an MCP function without saving it
   * @param req Express request
   * @param res Express response
   */
  public async test(req: Request, res: Response): Promise<void> {
    try {
      const { function: functionConfig, input } = req.body;

      if (!functionConfig) {
        res.status(400).json({
          success: false,
          error: 'Function configuration is required'
        });
        return;
      }

      if (!input) {
        res.status(400).json({
          success: false,
          error: 'Input data is required'
        });
        return;
      }

      // Validate function configuration
      const validatedFunction = await this.validator.validateForCreate(functionConfig);

      // Create temporary tool from function
      const tool = await this.serviceLoader.createToolFromEntity(validatedFunction as any);

      // Execute tool
      const result = await tool.execute(input);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Function test completed successfully'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all MCP functions with optional filtering
   * @param req Express request
   * @param res Express response
   */
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const { enabled } = req.query;

      let functions;
      if (enabled !== undefined) {
        const isEnabled = enabled === 'true';
        functions = await this.repository.findBy({ enabled: isEnabled });
      } else {
        functions = await this.repository.findAll();
      }

      res.status(200).json({
        success: true,
        data: functions
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get MCP function by ID
   * @param req Express request
   * @param res Express response
   */
  public async getById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      this.validator.validateId(id);

      const func = await this.repository.findById(id);
      if (!func) {
        res.status(404).json({
          success: false,
          error: 'MCP Function not found'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: func
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Enable/disable an MCP function
   * @param req Express request
   * @param res Express response
   */
  public async toggleEnabled(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { enabled } = req.body;

      this.validator.validateId(id);

      // Check if function exists
      const existingFunction = await this.repository.findById(id);
      if (!existingFunction) {
        res.status(404).json({
          success: false,
          error: 'MCP Function not found'
        });
        return;
      }

      // Update enabled status
      const updatedFunction = await this.repository.update(id, { enabled });

      // Reload functions in service loader (don't let errors here fail the response)
      try {
        await this.serviceLoader.loadAllFunctions();
      } catch (reloadError) {
        this.logger.warn('Error reloading functions after toggle', reloadError);
      }

      res.status(200).json({
        success: true,
        data: updatedFunction,
        message: `MCP Function ${enabled ? 'enabled' : 'disabled'} successfully`
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get function execution statistics
   * @param req Express request
   * @param res Express response
   */
  public async getStats(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      this.validator.validateId(id);

      // Check if function exists
      const func = await this.repository.findById(id);
      if (!func) {
        res.status(404).json({
          success: false,
          error: 'MCP Function not found'
        });
        return;
      }

      // TODO: Implement actual statistics gathering
      // For now, return mock data
      const stats = {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0,
        lastExecuted: null
      };

      res.status(200).json({
        success: true,
        data: stats
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }
}
