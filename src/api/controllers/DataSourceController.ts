import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { BaseController } from './BaseController';
import { IDataSourceController } from '../../core/interfaces/IDataSourceController';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { IValidator } from '../../core/interfaces/IValidator';
import type { IRepository } from '../../core/interfaces/IRepository';
import { TYPES } from '../../types';
import { DataSource as DataSourceEntity } from '../../infrastructure/database/entities/DataSource.entity';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';
import * as crypto from 'crypto';

/**
 * Data source controller
 * Handles data source-related API requests
 */
@injectable()
export class DataSourceController
  extends BaseController<DataSourceEntity>
  implements IDataSourceController
{
  // Encryption key and IV should be stored securely in environment variables
  private readonly encryptionKey =
    process.env.ENCRYPTION_KEY || 'default-encryption-key-for-development';
  private readonly encryptionIv = process.env.ENCRYPTION_IV || 'default-iv-16-chr';

  /**
   * Constructor
   * @param repository Data source repository
   * @param validator Data source validator
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.DataSourceRepository) repository: IRepository<DataSourceEntity>,
    @inject(TYPES.DataSourceValidator) validator: IValidator<DataSourceEntity>,
    @inject(TYPES.Logger) logger: ILogger
  ) {
    super(repository, validator, logger);
  }

  /**
   * Create a new data source
   * @param req Express request
   * @param res Express response
   */
  public async create(req: Request, res: Response): Promise<void> {
    try {
      const data = await this.validator.validateForCreate(req.body);

      // Encrypt connection config
      const connectionConfig = req.body.connection_config;
      const encryptedConfig = this.encryptConnectionConfig(connectionConfig);

      // Create data source with encrypted config
      const dataSource = await this.repository.create({
        ...data,
        connection_config_encrypted: encryptedConfig
      });

      // Remove sensitive data from response
      const response = { ...dataSource } as any;
      delete response.connection_config_encrypted;

      res.status(201).json(response);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update an existing data source
   * @param req Express request
   * @param res Express response
   */
  public async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      // Check if data source exists
      const existingDataSource = await this.repository.findById(id);
      if (!existingDataSource) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Data source not found'
          }
        });
        return;
      }

      // Validate update data
      const data = await this.validator.validateForUpdate(id, req.body);

      // Encrypt connection config if provided
      if (req.body.connection_config) {
        const connectionConfig = req.body.connection_config;
        const encryptedConfig = this.encryptConnectionConfig(connectionConfig);
        data.connection_config_encrypted = encryptedConfig;
      }

      // Update data source
      const updatedDataSource = await this.repository.update(id, data);

      // Remove sensitive data from response
      const response = { ...updatedDataSource } as any;
      delete response.connection_config_encrypted;

      res.status(200).json(response);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get data source by ID
   * @param req Express request
   * @param res Express response
   */
  public async getById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      const dataSource = await this.repository.findById(id);
      if (!dataSource) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Data source not found'
          }
        });
        return;
      }

      // Decrypt connection config
      const connectionConfig = this.decryptConnectionConfig(dataSource.connection_config_encrypted);

      // Remove sensitive data from response
      const response = { ...dataSource } as any;
      delete response.connection_config_encrypted;
      response.connection_config = connectionConfig;

      res.status(200).json(response);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get all data sources
   * @param req Express request
   * @param res Express response
   */
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const dataSources = await this.repository.findAll();

      // Remove sensitive data from response
      const response = dataSources.map((dataSource) => {
        const result = { ...dataSource } as any;
        delete result.connection_config_encrypted;
        return result;
      });

      res.status(200).json(response);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Test a data source connection without saving it
   * @param req Express request
   * @param res Express response
   */
  public async testConnection(req: Request, res: Response): Promise<void> {
    try {
      const { dataSource } = req.body;

      if (!dataSource) {
        throw new ValidationError('Data source configuration is required');
      }

      // Validate data source configuration
      await this.validator.validateForCreate(dataSource);

      // In a real implementation, we would test the connection here
      // For now, we'll just return success
      res.status(200).json({
        success: true,
        message: 'Connection test successful'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get available data source types
   * @param req Express request
   * @param res Express response
   */
  public async getDataSourceTypes(req: Request, res: Response): Promise<void> {
    try {
      // In a real implementation, this would come from a registry or configuration
      const dataSourceTypes = ['postgresql', 'mysql', 'redis', 'rest', 'litellm'];

      res.status(200).json(dataSourceTypes);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Test a data source connection
   * @param req Express request
   * @param res Express response
   */
  public async test(req: Request, res: Response): Promise<void> {
    return this.testConnection(req, res);
  }

  /**
   * Encrypt connection configuration
   * @param connectionConfig Connection configuration
   * @returns Encrypted connection configuration
   */
  private encryptConnectionConfig(connectionConfig: any): string {
    try {
      const cipher = crypto.createCipheriv(
        'aes-256-cbc',
        Buffer.from(this.encryptionKey),
        Buffer.from(this.encryptionIv)
      );
      const configString = JSON.stringify(connectionConfig);
      let encrypted = cipher.update(configString, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      return encrypted;
    } catch (error) {
      this.logger.error('Failed to encrypt connection config', error);
      throw new Error('Failed to encrypt connection config');
    }
  }

  /**
   * Decrypt connection configuration
   * @param encryptedConfig Encrypted connection configuration
   * @returns Decrypted connection configuration
   */
  private decryptConnectionConfig(encryptedConfig: string): any {
    try {
      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        Buffer.from(this.encryptionKey),
        Buffer.from(this.encryptionIv)
      );
      let decrypted = decipher.update(encryptedConfig, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      return JSON.parse(decrypted);
    } catch (error) {
      this.logger.error('Failed to decrypt connection config', error);
      throw new Error('Failed to decrypt connection config');
    }
  }
}
