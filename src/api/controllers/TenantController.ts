import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../types';
import type { ITenantManager } from '../../core/interfaces/ITenantManager';
import type { ITenantContext } from '../../core/interfaces/ITenantContext';
import type { ILogger } from '../../core/interfaces/ILogger';
import { ValidationError } from '../../core/errors/ValidationError';
import { NotFoundError } from '../../core/errors/NotFoundError';

/**
 * Tenant controller
 * Handles tenant-related API requests for multi-tenancy management
 */
@injectable()
export class TenantController {
  constructor(
    @inject(TYPES.TenantManager) private tenantManager: ITenantManager,
    @inject(TYPES.TenantContext) private tenantContext: ITenantContext,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Get all tenants
   */
  async getAllTenants(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const sortBy = (req.query.sortBy as string) || 'createdAt';
      const sortOrder = (req.query.sortOrder as string)?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

      const result = await this.tenantManager.listTenants({
        page,
        limit,
        sortBy,
        sortOrder
      });

      res.status(200).json(result);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get tenant by ID
   */
  async getTenantById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const tenant = await this.tenantManager.getTenant(id);

      if (!tenant) {
        throw new NotFoundError(`Tenant with ID '${id}' not found`);
      }

      res.status(200).json(tenant);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Create new tenant
   */
  async createTenant(req: Request, res: Response): Promise<void> {
    try {
      const tenantData = req.body;

      // Validate required fields
      if (!tenantData.name || !tenantData.displayName) {
        throw new ValidationError('Name and display name are required');
      }

      // Validate settings if provided
      if (tenantData.settings) {
        const validation = await this.tenantManager.validateTenantSettings(tenantData.settings);
        if (!validation.isValid) {
          throw new ValidationError(`Invalid tenant settings: ${validation.errors.join(', ')}`);
        }
      }

      const tenant = await this.tenantManager.createTenant(tenantData);
      res.status(201).json(tenant);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update tenant
   */
  async updateTenant(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updates = req.body;

      // Validate settings if provided
      if (updates.settings) {
        const validation = await this.tenantManager.validateTenantSettings(updates.settings);
        if (!validation.isValid) {
          throw new ValidationError(`Invalid tenant settings: ${validation.errors.join(', ')}`);
        }
      }

      const tenant = await this.tenantManager.updateTenant(id, updates);
      res.status(200).json(tenant);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete tenant
   */
  async deleteTenant(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const success = await this.tenantManager.deleteTenant(id);

      if (success) {
        res.status(204).send();
      } else {
        throw new NotFoundError(`Tenant with ID '${id}' not found`);
      }
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get tenant users
   */
  async getTenantUsers(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const sortBy = (req.query.sortBy as string) || 'createdAt';
      const sortOrder = (req.query.sortOrder as string)?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

      const result = await this.tenantManager.getTenantUsers(id, {
        page,
        limit,
        sortBy,
        sortOrder
      });

      res.status(200).json(result);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Create tenant user
   */
  async createTenantUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userData = req.body;

      // Validate required fields
      if (!userData.username || !userData.email) {
        throw new ValidationError('Username and email are required');
      }

      const user = await this.tenantManager.createTenantUser(id, userData);

      // Remove password hash from response
      const response = { ...user };
      delete (response as any).passwordHash;

      res.status(201).json(response);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Update tenant user
   */
  async updateTenantUser(req: Request, res: Response): Promise<void> {
    try {
      const { id, userId } = req.params;
      const updates = req.body;

      const user = await this.tenantManager.updateTenantUser(id, userId, updates);

      // Remove password hash from response
      const response = { ...user };
      delete (response as any).passwordHash;

      res.status(200).json(response);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Delete tenant user
   */
  async deleteTenantUser(req: Request, res: Response): Promise<void> {
    try {
      const { id, userId } = req.params;
      const success = await this.tenantManager.deleteTenantUser(id, userId);

      if (success) {
        res.status(204).send();
      } else {
        throw new NotFoundError(`User with ID '${userId}' not found in tenant '${id}'`);
      }
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Authenticate user
   */
  async authenticateUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { username, password } = req.body;

      if (!username || !password) {
        throw new ValidationError('Username and password are required');
      }

      const user = await this.tenantManager.authenticateUser(id, username, password);

      if (!user) {
        res.status(401).json({ message: 'Invalid credentials' });
        return;
      }

      // Remove password hash from response
      const response = { ...user };
      delete (response as any).passwordHash;

      res.status(200).json({
        user: response,
        message: 'Authentication successful'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Validate tenant settings
   */
  async validateSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = req.body;
      const validation = await this.tenantManager.validateTenantSettings(settings);
      res.status(200).json(validation);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Handle errors
   */
  private handleError(error: any, res: Response): void {
    this.logger.error('Tenant controller error', { error });

    if (error instanceof ValidationError) {
      res.status(400).json({ message: error.message });
    } else if (error instanceof NotFoundError) {
      res.status(404).json({ message: error.message });
    } else {
      res.status(500).json({ message: 'Internal server error' });
    }
  }
}
