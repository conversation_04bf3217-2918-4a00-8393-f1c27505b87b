import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { BaseController } from './BaseController';
import { IWorkflowController } from '../../core/interfaces/IWorkflowController';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { IValidator } from '../../core/interfaces/IValidator';
import type { IRepository } from '../../core/interfaces/IRepository';
import type { IWorkflowEngine } from '../../core/interfaces/IWorkflowEngine';
import type { IObservabilityManager } from '../../core/interfaces/IObservabilityManager';
import { TYPES } from '../../types';
import { Workflow } from '../../infrastructure/database/entities/Workflow.entity';
import { ValidationError } from '../../core/errors/ValidationError';
import { ErrorCode } from '../../core/errors/ErrorCodes';

/**
 * Workflow controller
 * Handles workflow-related API requests
 */
@injectable()
export class WorkflowController extends BaseController<Workflow> implements IWorkflowController {
  /**
   * Constructor
   * @param repository Workflow repository
   * @param validator Workflow validator
   * @param logger Logger
   * @param workflowEngine Workflow engine
   * @param observabilityManager Observability manager
   */
  constructor(
    @inject(TYPES.WorkflowRepository) repository: IRepository<Workflow>,
    @inject(TYPES.WorkflowValidator) validator: IValidator<Workflow>,
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.WorkflowEngine) private workflowEngine: IWorkflowEngine,
    @inject(TYPES.ObservabilityManager) private observabilityManager: IObservabilityManager
  ) {
    super(repository, validator, logger);
  }

  /**
   * Test a workflow without saving it
   * @param req Express request
   * @param res Express response
   */
  public async test(req: Request, res: Response): Promise<void> {
    try {
      const { workflow, input } = req.body;

      if (!workflow) {
        throw new ValidationError('Workflow configuration is required');
      }

      if (!input) {
        throw new ValidationError('Input data is required');
      }

      // Validate workflow configuration
      const validatedWorkflow = await this.validator.validateForCreate(workflow);

      // Execute workflow
      const result = await this.workflowEngine.executeWorkflowConfig(validatedWorkflow, input);

      res.status(200).json({
        result,
        success: true
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get workflow execution history
   * @param req Express request
   * @param res Express response
   */
  public async getExecutionHistory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      // Check if workflow exists
      const workflow = await this.repository.findById(id);
      if (!workflow) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Workflow not found'
          }
        });
        return;
      }

      // Get execution history
      const executionHistory = await this.workflowEngine.getWorkflowExecutionHistory(id);

      res.status(200).json(executionHistory);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Get workflow metrics
   * @param req Express request
   * @param res Express response
   */
  public async getMetrics(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      this.validator.validateId(id);

      // Check if workflow exists
      const workflow = await this.repository.findById(id);
      if (!workflow) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Workflow not found'
          }
        });
        return;
      }

      // Parse time range from query parameters
      let timeRange;
      if (req.query.startTime && req.query.endTime) {
        timeRange = {
          startTime: new Date(req.query.startTime as string),
          endTime: new Date(req.query.endTime as string)
        };
      }

      // Get workflow metrics
      const metrics = await this.observabilityManager.getWorkflowMetrics(id, timeRange);

      res.status(200).json(metrics);
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Execute a workflow with provided input data
   * @param req Express request
   * @param res Express response
   */
  public async execute(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { input } = req.body;

      this.validator.validateId(id);

      if (!input) {
        throw new ValidationError('Input data is required');
      }

      // Check if workflow exists
      const workflow = await this.repository.findById(id);
      if (!workflow) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Workflow not found'
          }
        });
        return;
      }

      // Check if workflow is enabled
      if (!workflow.enabled) {
        res.status(400).json({
          error: {
            code: ErrorCode.VALIDATION_ERROR,
            message: 'Workflow is disabled'
          }
        });
        return;
      }

      // Execute workflow
      const result = await this.workflowEngine.executeWorkflow(id, input);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Workflow executed successfully'
      });
    } catch (error) {
      this.handleError(error, res);
    }
  }

  /**
   * Public endpoint to trigger a workflow (for REST API triggers)
   * @param req Express request
   * @param res Express response
   */
  public async trigger(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const input = req.body; // Use entire request body as input

      this.validator.validateId(id);

      // Check if workflow exists
      const workflow = await this.repository.findById(id);
      if (!workflow) {
        res.status(404).json({
          error: {
            code: ErrorCode.NOT_FOUND,
            message: 'Workflow not found'
          }
        });
        return;
      }

      // Check if workflow is enabled
      if (!workflow.enabled) {
        res.status(400).json({
          error: {
            code: ErrorCode.VALIDATION_ERROR,
            message: 'Workflow is disabled'
          }
        });
        return;
      }

      // Execute workflow with request metadata
      const workflowInput = {
        payload: input,
        headers: req.headers,
        method: req.method,
        url: req.url,
        timestamp: new Date().toISOString()
      };

      const result = await this.workflowEngine.executeWorkflow(id, workflowInput);

      // Return result based on workflow output
      if (result && typeof result === 'object' && result.statusCode) {
        res.status(result.statusCode).json(result.body || result);
      } else {
        res.status(200).json(result);
      }
    } catch (error) {
      this.handleError(error, res);
    }
  }
}
