import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import type { IWorkflowVersionManager } from '../../core/interfaces/IWorkflowVersionManager';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * Controller for workflow version management endpoints
 */
@injectable()
export class WorkflowVersionController {
  constructor(
    @inject(TYPES.WorkflowVersionManager) private versionManager: IWorkflowVersionManager,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Create a new version of a workflow
   * POST /api/workflows/:workflowId/versions
   */
  async createVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId } = req.params;
      const { definition, changeDescription } = req.body;
      const createdBy = (req.headers['x-user-id'] as string) || 'anonymous';

      this.logger.info('Creating workflow version', {
        workflowId,
        createdBy,
        changeDescription
      });

      const version = await this.versionManager.createVersion(
        workflowId,
        definition,
        createdBy,
        changeDescription
      );

      res.status(201).json({
        success: true,
        data: version,
        message: 'Workflow version created successfully'
      });
    } catch (error) {
      this.logger.error('Failed to create workflow version', {
        workflowId: req.params.workflowId,
        error: error instanceof Error ? error instanceof Error ? error.message : 'Unknown error occurred' : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error instanceof Error ? error.message : 'Unknown error occurred' : 'Unknown error occurred',
        message: 'Failed to create workflow version'
      });
    }
  }

  /**
   * Get version history for a workflow
   * GET /api/workflows/:workflowId/versions
   */
  async getVersionHistory(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId } = req.params;
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;

      this.logger.debug('Getting workflow version history', {
        workflowId,
        limit,
        offset
      });

      const versions = await this.versionManager.getVersionHistory(workflowId, limit, offset);

      res.json({
        success: true,
        data: {
          versions,
          pagination: {
            limit,
            offset,
            total: versions.length
          }
        },
        message: 'Version history retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Failed to get version history', {
        workflowId: req.params.workflowId,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to retrieve version history'
      });
    }
  }

  /**
   * Get a specific version of a workflow
   * GET /api/workflows/:workflowId/versions/:version
   */
  async getVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, version } = req.params;
      const versionNumber = parseInt(version);

      this.logger.debug('Getting workflow version', {
        workflowId,
        version: versionNumber
      });

      const workflowVersion = await this.versionManager.getVersion(workflowId, versionNumber);

      if (!workflowVersion) {
        res.status(404).json({
          success: false,
          message: `Version ${versionNumber} not found for workflow ${workflowId}`
        });
        return;
      }

      res.json({
        success: true,
        data: workflowVersion,
        message: 'Workflow version retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Failed to get workflow version', {
        workflowId: req.params.workflowId,
        version: req.params.version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to retrieve workflow version'
      });
    }
  }

  /**
   * Get the currently active version of a workflow
   * GET /api/workflows/:workflowId/versions/active
   */
  async getActiveVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId } = req.params;

      this.logger.debug('Getting active workflow version', { workflowId });

      const activeVersion = await this.versionManager.getActiveVersion(workflowId);

      if (!activeVersion) {
        res.status(404).json({
          success: false,
          message: `No active version found for workflow ${workflowId}`
        });
        return;
      }

      res.json({
        success: true,
        data: activeVersion,
        message: 'Active workflow version retrieved successfully'
      });
    } catch (error) {
      this.logger.error('Failed to get active workflow version', {
        workflowId: req.params.workflowId,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to retrieve active workflow version'
      });
    }
  }

  /**
   * Activate a specific version of a workflow
   * POST /api/workflows/:workflowId/versions/:version/activate
   */
  async activateVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, version } = req.params;
      const versionNumber = parseInt(version);
      const deployment = req.body.deployment;

      this.logger.info('Activating workflow version', {
        workflowId,
        version: versionNumber,
        deployment
      });

      const result = await this.versionManager.activateVersion(
        workflowId,
        versionNumber,
        deployment
      );

      if (!result.success) {
        res.status(400).json({
          success: false,
          message: 'Failed to activate workflow version'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          activeVersion: result.activeVersion,
          previousVersion: result.previousVersion
        },
        message: 'Workflow version activated successfully'
      });
    } catch (error) {
      this.logger.error('Failed to activate workflow version', {
        workflowId: req.params.workflowId,
        version: req.params.version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to activate workflow version'
      });
    }
  }

  /**
   * Deactivate a specific version of a workflow
   * POST /api/workflows/:workflowId/versions/:version/deactivate
   */
  async deactivateVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, version } = req.params;
      const versionNumber = parseInt(version);
      const { reason } = req.body;

      this.logger.info('Deactivating workflow version', {
        workflowId,
        version: versionNumber,
        reason
      });

      const result = await this.versionManager.deactivateVersion(workflowId, versionNumber, reason);

      if (!result.success) {
        res.status(400).json({
          success: false,
          message: 'Failed to deactivate workflow version'
        });
        return;
      }

      res.json({
        success: true,
        data: result.deactivatedVersion,
        message: 'Workflow version deactivated successfully'
      });
    } catch (error) {
      this.logger.error('Failed to deactivate workflow version', {
        workflowId: req.params.workflowId,
        version: req.params.version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to deactivate workflow version'
      });
    }
  }

  /**
   * Rollback to a previous version
   * POST /api/workflows/:workflowId/versions/:version/rollback
   */
  async rollbackToVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, version } = req.params;
      const targetVersion = parseInt(version);
      const { reason, preserveData, rollbackStrategy } = req.body;

      this.logger.info('Rolling back workflow version', {
        workflowId,
        targetVersion,
        reason
      });

      const rollbackConfig = {
        targetVersion,
        reason: reason || 'Manual rollback',
        preserveData: preserveData !== false,
        rollbackStrategy: rollbackStrategy || 'immediate'
      };

      const result = await this.versionManager.rollbackToVersion(workflowId, rollbackConfig);

      if (!result.success) {
        res.status(400).json({
          success: false,
          message: 'Failed to rollback workflow version'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          rolledBackTo: result.rolledBackTo,
          previousVersion: result.previousVersion
        },
        message: 'Workflow rollback completed successfully'
      });
    } catch (error) {
      this.logger.error('Failed to rollback workflow version', {
        workflowId: req.params.workflowId,
        targetVersion: req.params.version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to rollback workflow version'
      });
    }
  }

  /**
   * Compare two versions of a workflow
   * GET /api/workflows/:workflowId/versions/:fromVersion/compare/:toVersion
   */
  async compareVersions(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, fromVersion, toVersion } = req.params;
      const fromVersionNumber = parseInt(fromVersion);
      const toVersionNumber = parseInt(toVersion);

      this.logger.debug('Comparing workflow versions', {
        workflowId,
        fromVersion: fromVersionNumber,
        toVersion: toVersionNumber
      });

      const comparison = await this.versionManager.compareVersions(
        workflowId,
        fromVersionNumber,
        toVersionNumber
      );

      res.json({
        success: true,
        data: comparison,
        message: 'Version comparison completed successfully'
      });
    } catch (error) {
      this.logger.error('Failed to compare workflow versions', {
        workflowId: req.params.workflowId,
        fromVersion: req.params.fromVersion,
        toVersion: req.params.toVersion,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to compare workflow versions'
      });
    }
  }

  /**
   * Delete a specific version (if not active)
   * DELETE /api/workflows/:workflowId/versions/:version
   */
  async deleteVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, version } = req.params;
      const versionNumber = parseInt(version);
      const force = req.query.force === 'true';

      this.logger.info('Deleting workflow version', {
        workflowId,
        version: versionNumber,
        force
      });

      const result = await this.versionManager.deleteVersion(workflowId, versionNumber, force);

      if (!result.success) {
        res.status(400).json({
          success: false,
          message: 'Failed to delete workflow version'
        });
        return;
      }

      res.json({
        success: true,
        data: result.deletedVersion,
        message: 'Workflow version deleted successfully'
      });
    } catch (error) {
      this.logger.error('Failed to delete workflow version', {
        workflowId: req.params.workflowId,
        version: req.params.version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to delete workflow version'
      });
    }
  }

  /**
   * Clone a version to create a new workflow
   * POST /api/workflows/:workflowId/versions/:version/clone
   */
  async cloneVersion(req: Request, res: Response): Promise<void> {
    try {
      const { workflowId, version } = req.params;
      const sourceVersion = parseInt(version);
      const { newWorkflowName } = req.body;
      const createdBy = (req.headers['x-user-id'] as string) || 'anonymous';

      this.logger.info('Cloning workflow version', {
        sourceWorkflowId: workflowId,
        sourceVersion,
        newWorkflowName,
        createdBy
      });

      const clonedVersion = await this.versionManager.cloneVersion(
        workflowId,
        sourceVersion,
        newWorkflowName,
        createdBy
      );

      res.status(201).json({
        success: true,
        data: clonedVersion,
        message: 'Workflow version cloned successfully'
      });
    } catch (error) {
      this.logger.error('Failed to clone workflow version', {
        workflowId: req.params.workflowId,
        version: req.params.version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to clone workflow version'
      });
    }
  }

  /**
   * Validate a workflow definition
   * POST /api/workflows/validate
   */
  async validateDefinition(req: Request, res: Response): Promise<void> {
    try {
      const { definition } = req.body;

      this.logger.debug('Validating workflow definition');

      const validation = await this.versionManager.validateDefinition(definition);

      res.json({
        success: true,
        data: validation,
        message: 'Workflow definition validation completed'
      });
    } catch (error) {
      this.logger.error('Failed to validate workflow definition', {
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        message: 'Failed to validate workflow definition'
      });
    }
  }
}
