import { Request, Response } from 'express';
import { inject, injectable } from 'inversify';
import * as jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { TYPES } from '../../types';
import type { ITenantManager } from '../../core/interfaces/ITenantManager';
import type { ITenantContext } from '../../core/interfaces/ITenantContext';
import { TenantUser } from '../../infrastructure/database/entities/TenantUser.entity';

interface LoginRequest {
  tenantName: string;
  username: string;
  password: string;
}

interface LoginResponse {
  token: string;
  user: TenantUser;
  tenant: any;
  expiresAt: string;
}

@injectable()
export class AuthController {
  constructor(
    @inject(TYPES.TenantManager) private tenantManager: ITenantManager,
    @inject(TYPES.TenantContext) private tenantContext: ITenantContext
  ) {}

  /**
   * Authenticate user and return JWT token
   */
  async login(req: Request, res: Response): Promise<void> {
    try {
      const { tenantName, username, password }: LoginRequest = req.body;

      if (!tenantName || !username || !password) {
        res.status(400).json({
          success: false,
          error: 'Tenant name, username, and password are required'
        });
        return;
      }

      // Get tenant by name
      const tenant = await this.tenantManager.getTenantByName(tenantName);
      if (!tenant) {
        res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
        return;
      }

      if (!tenant.enabled) {
        res.status(401).json({
          success: false,
          error: 'Tenant is disabled'
        });
        return;
      }

      // Authenticate user
      const user = await this.tenantManager.authenticateUser(tenant.id, username, password);
      if (!user) {
        res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
        return;
      }

      if (!user.enabled) {
        res.status(401).json({
          success: false,
          error: 'User account is disabled'
        });
        return;
      }

      // Generate JWT token
      const tokenPayload = {
        userId: user.id,
        tenantId: tenant.id,
        username: user.username,
        roles: user.roles
      };

      const secret = process.env.JWT_SECRET || 'your-secret-key';
      const expiresIn = process.env.JWT_EXPIRES_IN || '24h';

      const token = jwt.sign(tokenPayload, secret, { expiresIn } as jwt.SignOptions);

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24); // Default 24 hours

      const response: LoginResponse = {
        token,
        user,
        tenant,
        expiresAt: expiresAt.toISOString()
      };

      res.json({
        success: true,
        data: response,
        message: 'Login successful'
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Logout user (invalidate token)
   */
  async logout(req: Request, res: Response): Promise<void> {
    try {
      // In a real implementation, you might want to blacklist the token
      // For now, we'll just return success
      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Validate JWT token
   */
  async validateToken(req: Request, res: Response): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          error: 'No token provided'
        });
        return;
      }

      const token = authHeader.substring(7);
      const secret = process.env.JWT_SECRET || 'your-secret-key';

      try {
        const decoded = jwt.verify(token, secret) as any;

        // Verify user and tenant still exist and are enabled
        const tenant = await this.tenantManager.getTenant(decoded.tenantId);
        if (!tenant || !tenant.enabled) {
          res.status(401).json({
            success: false,
            error: 'Invalid token'
          });
          return;
        }

        const users = await this.tenantManager.getTenantUsers(decoded.tenantId, {
          page: 1,
          limit: 1000 // Get all users to find the specific one
        });

        const user = users.data.find((u) => u.id === decoded.userId);
        if (!user || !user.enabled) {
          res.status(401).json({
            success: false,
            error: 'Invalid token'
          });
          return;
        }

        res.json({
          success: true,
          data: {
            user,
            tenant,
            tokenValid: true
          }
        });
      } catch (jwtError) {
        res.status(401).json({
          success: false,
          error: 'Invalid token'
        });
      }
    } catch (error) {
      console.error('Token validation error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * Refresh JWT token
   */
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          error: 'No token provided'
        });
        return;
      }

      const token = authHeader.substring(7);
      const secret = process.env.JWT_SECRET || 'your-secret-key';

      try {
        const decoded = jwt.verify(token, secret, { ignoreExpiration: true }) as any;

        // Verify user and tenant still exist and are enabled
        const tenant = await this.tenantManager.getTenant(decoded.tenantId);
        if (!tenant || !tenant.enabled) {
          res.status(401).json({
            success: false,
            error: 'Invalid token'
          });
          return;
        }

        const users = await this.tenantManager.getTenantUsers(decoded.tenantId, {
          page: 1,
          limit: 1000
        });

        const user = users.data.find((u) => u.id === decoded.userId);
        if (!user || !user.enabled) {
          res.status(401).json({
            success: false,
            error: 'Invalid token'
          });
          return;
        }

        // Generate new token
        const tokenPayload = {
          userId: user.id,
          tenantId: tenant.id,
          username: user.username,
          roles: user.roles
        };

        const expiresIn = process.env.JWT_EXPIRES_IN || '24h';
        const newToken = jwt.sign(tokenPayload, secret, { expiresIn } as jwt.SignOptions);

        // Calculate expiration date
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + 24);

        res.json({
          success: true,
          data: {
            token: newToken,
            user,
            tenant,
            expiresAt: expiresAt.toISOString()
          }
        });
      } catch (jwtError) {
        res.status(401).json({
          success: false,
          error: 'Invalid token'
        });
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
}
