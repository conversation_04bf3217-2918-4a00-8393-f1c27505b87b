import { Router } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../types';
import { IDataSourceController } from '../../core/interfaces/IDataSourceController';

/**
 * Create data source routes
 * @param container Inversify container
 * @returns Express router
 */
export const createDataSourceRoutes = (container: Container): Router => {
  const router = Router();
  const controller = container.get<IDataSourceController>(TYPES.DataSourceController);

  // Get all data sources
  router.get('/', (req, res) => controller.getAll(req, res));

  // Get data source by ID
  router.get('/:id', (req, res) => controller.getById(req, res));

  // Create a new data source
  router.post('/', (req, res) => controller.create(req, res));

  // Update a data source
  router.put('/:id', (req, res) => controller.update(req, res));

  // Delete a data source
  router.delete('/:id', (req, res) => controller.delete(req, res));

  // Test a data source connection without saving
  router.post('/test', (req, res) => controller.testConnection(req, res));

  // Get available data source types
  router.get('/types', (req, res) => controller.getDataSourceTypes(req, res));

  return router;
};
