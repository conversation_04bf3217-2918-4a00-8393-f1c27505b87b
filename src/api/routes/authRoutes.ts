import { Router } from 'express';
import { Container } from 'inversify';
import { AuthController } from '../controllers/AuthController';
import { TYPES } from '../../types';

export function createAuthRoutes(container: Container): Router {
  const router = Router();
  const authController = container.get<AuthController>(AuthController);

  /**
   * @route POST /auth/login
   * @desc Authenticate user and return JWT token
   * @access Public
   */
  router.post('/login', async (req, res) => {
    await authController.login(req, res);
  });

  /**
   * @route POST /auth/logout
   * @desc Logout user (invalidate token)
   * @access Private
   */
  router.post('/logout', async (req, res) => {
    await authController.logout(req, res);
  });

  /**
   * @route GET /auth/validate
   * @desc Validate JWT token
   * @access Private
   */
  router.get('/validate', async (req, res) => {
    await authController.validateToken(req, res);
  });

  /**
   * @route POST /auth/refresh
   * @desc Refresh JWT token
   * @access Private
   */
  router.post('/refresh', async (req, res) => {
    await authController.refreshToken(req, res);
  });

  return router;
}
