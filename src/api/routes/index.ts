import { Router } from 'express';
import { Container } from 'inversify';
import { createWorkflowRoutes } from './workflowRoutes';
import { createNodeRoutes } from './nodeRoutes';
import { createDataSourceRoutes } from './dataSourceRoutes';
import { createTenantRoutes } from './tenantRoutes';
import { createFunctionRoutes } from './functionRoutes';

/**
 * Create all admin API routes
 * @param container Inversify container
 * @returns Express router
 */
export const createAdminRoutes = (container: Container): Router => {
  const router = Router();

  // Mount workflow routes
  router.use('/workflows', createWorkflowRoutes(container));

  // Mount function routes
  router.use('/functions', createFunctionRoutes(container));

  // Mount node routes
  router.use('/nodes', createNodeRoutes(container));

  // Mount data source routes
  router.use('/datasources', createDataSourceRoutes(container));

  // Mount tenant routes
  router.use('/tenants', createTenantRoutes(container));

  // Test route
  router.get('/test', (req, res) => {
    res.json({ message: 'Admin API is working' });
  });

  return router;
};
