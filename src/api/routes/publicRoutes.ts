import { Router, Request, Response, NextFunction } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../types';
import type { IWorkflowEngine } from '../../core/interfaces/IWorkflowEngine';
import type { IRepository } from '../../core/interfaces/IRepository';
import type { ILogger } from '../../core/interfaces/ILogger';
import { Workflow } from '../../infrastructure/database/entities/Workflow.entity';

/**
 * Create public routes for REST API triggered workflows
 * These routes bypass authentication middleware based on workflow configuration
 * @param container Inversify container
 * @returns Express router
 */
export const createPublicRoutes = (container: Container): Router => {
  const router = Router();
  const workflowEngine = container.get<IWorkflowEngine>(TYPES.WorkflowEngine);
  const workflowRepository = container.get<IRepository<Workflow>>(TYPES.WorkflowRepository);
  const logger = container.get<ILogger>(TYPES.Logger);

  // Handle all HTTP methods and paths
  // Use middleware approach without specific route pattern
  router.use(async (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.info(`Public API request: ${req.method} ${req.path}`);

      // Find workflow by REST API trigger configuration
      const workflows = await workflowRepository.findAll();
      const matchingWorkflow = workflows.find((workflow) => {
        // Find REST API trigger node in workflow
        const triggerNode = workflow.nodes_config.nodes?.find(
          (node: { id: string; type: string; name: string; config: Record<string, any> }) =>
            node.type === 'rest-api-trigger'
        );

        if (!triggerNode) return false;

        // Check if path and method match
        const pathMatch = triggerNode.config?.path === req.path;
        const methodMatch = triggerNode.config?.method?.toUpperCase() === req.method.toUpperCase();

        return pathMatch && methodMatch;
      });

      if (!matchingWorkflow) {
        logger.warn(`No workflow found for endpoint: ${req.method} ${req.path}`);
        res.status(404).json({
          success: false,
          error: 'Endpoint not found'
        });
        return;
      }

      // Check if workflow is enabled
      if (!matchingWorkflow.enabled) {
        logger.warn(`Workflow ${matchingWorkflow.id} is disabled`);
        res.status(503).json({
          success: false,
          error: 'Service temporarily unavailable'
        });
        return;
      }

      // Get trigger node configuration
      const triggerNode = matchingWorkflow.nodes_config.nodes.find(
        (node: { id: string; type: string; name: string; config: Record<string, any> }) =>
          node.type === 'rest-api-trigger'
      );

      // Check authentication if required by the trigger node
      if (triggerNode?.config?.authentication) {
        const authHeader = req.headers.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          logger.warn(`Authentication required for workflow ${matchingWorkflow.id}`);
          res.status(401).json({
            success: false,
            error: 'Authentication required'
          });
          return;
        }

        // TODO: Validate JWT token here if authentication is required
        // For now, we just check for the presence of the header
      }

      // Prepare workflow input matching the REST API trigger format
      const workflowInput = {
        trigger: 'rest-api',
        request: {
          method: req.method,
          path: req.path,
          headers: req.headers,
          query: req.query,
          body: req.body,
          params: req.params
        },
        timestamp: new Date().toISOString(),
        workflowId: matchingWorkflow.id,
        executionId: `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };

      logger.info(`Executing workflow ${matchingWorkflow.id} for public endpoint`);

      // Execute the workflow
      const result = await workflowEngine.executeWorkflow(matchingWorkflow.id, workflowInput);

      // Handle response based on workflow output
      if (result && typeof result === 'object') {
        // If workflow returns a response object with statusCode
        if ('statusCode' in result) {
          const statusCode = typeof result.statusCode === 'number' ? result.statusCode : 200;

          // Check for body property
          if ('body' in result) {
            res.status(statusCode).json(result.body);
            return;
          }

          // Return the entire result minus statusCode
          const { statusCode: _, ...responseBody } = result;
          res.status(statusCode).json(responseBody);
          return;
        }

        // If workflow returns headers
        if ('headers' in result && typeof result.headers === 'object') {
          Object.entries(result.headers).forEach(([key, value]) => {
            if (typeof value === 'string') {
              res.setHeader(key, value);
            }
          });
        }
      }

      // Default response
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Error in public API endpoint:', error);

      // Don't expose internal error details in production
      const isDevelopment = process.env.NODE_ENV === 'development';

      res.status(500).json({
        success: false,
        error: isDevelopment
          ? error instanceof Error
            ? error.message
            : 'Unknown error'
          : 'Internal server error'
      });
    }
  });

  return router;
};
