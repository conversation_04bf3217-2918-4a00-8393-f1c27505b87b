import { Router } from 'express';
import { Container } from 'inversify';
import { MCPFunctionController } from '../controllers/MCPFunctionController';
import { TYPES } from '../../types';

/**
 * Create MCP function routes
 * @param container Inversify container
 * @returns Express router
 */
export const createFunctionRoutes = (container: Container): Router => {
  const router = Router();
  const controller = container.get<MCPFunctionController>(TYPES.MCPFunctionController);

  /**
   * @swagger
   * /api/functions:
   *   get:
   *     summary: Get all MCP functions
   *     tags: [Functions]
   *     parameters:
   *       - in: query
   *         name: enabled
   *         schema:
   *           type: boolean
   *         description: Filter by enabled status
   *     responses:
   *       200:
   *         description: List of MCP functions
   */
  router.get('/', (req, res) => controller.getAll(req, res));

  /**
   * @swagger
   * /api/functions/{id}:
   *   get:
   *     summary: Get MCP function by ID
   *     tags: [Functions]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: MCP function details
   *       404:
   *         description: Function not found
   */
  router.get('/:id', (req, res) => controller.getById(req, res));

  /**
   * @swagger
   * /api/functions:
   *   post:
   *     summary: Create a new MCP function
   *     tags: [Functions]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               description:
   *                 type: string
   *               input_schema:
   *                 type: object
   *               handler_config:
   *                 type: object
   *               enabled:
   *                 type: boolean
   *     responses:
   *       201:
   *         description: Function created successfully
   */
  router.post('/', (req, res) => controller.create(req, res));

  /**
   * @swagger
   * /api/functions/{id}:
   *   put:
   *     summary: Update an MCP function
   *     tags: [Functions]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *     responses:
   *       200:
   *         description: Function updated successfully
   *       404:
   *         description: Function not found
   */
  router.put('/:id', (req, res) => controller.update(req, res));

  /**
   * @swagger
   * /api/functions/{id}:
   *   delete:
   *     summary: Delete an MCP function
   *     tags: [Functions]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Function deleted successfully
   *       404:
   *         description: Function not found
   */
  router.delete('/:id', (req, res) => controller.delete(req, res));

  /**
   * @swagger
   * /api/functions/test:
   *   post:
   *     summary: Test an MCP function without saving it
   *     tags: [Functions]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               function:
   *                 type: object
   *                 description: Function configuration to test
   *               input:
   *                 type: object
   *                 description: Input data for testing
   *     responses:
   *       200:
   *         description: Test completed successfully
   */
  router.post('/test', (req, res) => controller.test(req, res));

  /**
   * @swagger
   * /api/functions/{id}/toggle:
   *   patch:
   *     summary: Enable/disable an MCP function
   *     tags: [Functions]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               enabled:
   *                 type: boolean
   *     responses:
   *       200:
   *         description: Function status updated successfully
   *       404:
   *         description: Function not found
   */
  router.patch('/:id/toggle', (req, res) => controller.toggleEnabled(req, res));

  /**
   * @swagger
   * /api/functions/{id}/stats:
   *   get:
   *     summary: Get function execution statistics
   *     tags: [Functions]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Function statistics
   *       404:
   *         description: Function not found
   */
  router.get('/:id/stats', (req, res) => controller.getStats(req, res));

  return router;
};
