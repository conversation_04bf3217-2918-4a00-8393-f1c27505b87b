import { Router } from 'express';
import { Container } from 'inversify';
import { AdminController } from '../controllers/AdminController';
import { authMiddleware } from '../middleware/authMiddleware';
import { adminMiddleware } from '../middleware/adminMiddleware';

export function createAdminRoutes(container: Container): Router {
  const router = Router();
  const adminController = container.get<AdminController>(AdminController);

  // Apply authentication middleware to all admin routes
  router.use(authMiddleware);
  router.use(adminMiddleware);

  // Dashboard routes
  /**
   * @route GET /admin/dashboard/stats
   * @desc Get dashboard statistics
   * @access Admin
   */
  router.get('/dashboard/stats', async (req, res) => {
    await adminController.getDashboardStats(req, res);
  });

  /**
   * @route GET /admin/dashboard/tenant-metrics
   * @desc Get tenant metrics
   * @access Admin
   */
  router.get('/dashboard/tenant-metrics', async (req, res) => {
    await adminController.getTenantMetrics(req, res);
  });

  // System routes
  /**
   * @route GET /admin/system/health
   * @desc Get system health status
   * @access Admin
   */
  router.get('/system/health', async (req, res) => {
    await adminController.getSystemHealth(req, res);
  });

  // Tenant management routes
  /**
   * @route GET /admin/tenants
   * @desc Get all tenants with pagination
   * @access Admin
   */
  router.get('/tenants', async (req, res) => {
    await adminController.getTenants(req, res);
  });

  /**
   * @route GET /admin/tenants/:id
   * @desc Get single tenant by ID
   * @access Admin
   */
  router.get('/tenants/:id', async (req, res) => {
    await adminController.getTenant(req, res);
  });

  /**
   * @route POST /admin/tenants
   * @desc Create new tenant
   * @access Admin
   */
  router.post('/tenants', async (req, res) => {
    await adminController.createTenant(req, res);
  });

  /**
   * @route PUT /admin/tenants/:id
   * @desc Update tenant
   * @access Admin
   */
  router.put('/tenants/:id', async (req, res) => {
    await adminController.updateTenant(req, res);
  });

  /**
   * @route DELETE /admin/tenants/:id
   * @desc Delete tenant
   * @access Admin
   */
  router.delete('/tenants/:id', async (req, res) => {
    await adminController.deleteTenant(req, res);
  });

  /**
   * @route POST /admin/tenants/validate-settings
   * @desc Validate tenant settings
   * @access Admin
   */
  router.post('/tenants/validate-settings', async (req, res) => {
    await adminController.validateTenantSettings(req, res);
  });

  // Tenant user management routes
  /**
   * @route GET /admin/tenants/:tenantId/users
   * @desc Get all users for a tenant
   * @access Admin
   */
  router.get('/tenants/:tenantId/users', async (req, res) => {
    await adminController.getTenantUsers(req, res);
  });

  /**
   * @route POST /admin/tenants/:tenantId/users
   * @desc Create new user for a tenant
   * @access Admin
   */
  router.post('/tenants/:tenantId/users', async (req, res) => {
    await adminController.createTenantUser(req, res);
  });

  /**
   * @route PUT /admin/tenants/:tenantId/users/:userId
   * @desc Update tenant user
   * @access Admin
   */
  router.put('/tenants/:tenantId/users/:userId', async (req, res) => {
    await adminController.updateTenantUser(req, res);
  });

  /**
   * @route DELETE /admin/tenants/:tenantId/users/:userId
   * @desc Delete tenant user
   * @access Admin
   */
  router.delete('/tenants/:tenantId/users/:userId', async (req, res) => {
    await adminController.deleteTenantUser(req, res);
  });

  return router;
}
