import { Router } from 'express';
import { Container } from 'inversify';
import { TenantController } from '../controllers/TenantController';

/**
 * Create tenant management routes
 * @param container Inversify container
 * @returns Express router
 */
export const createTenantRoutes = (container: Container): Router => {
  const router = Router();
  const tenantController = container.get<TenantController>(TenantController);

  // Tenant management routes
  router.get('/', (req, res) => tenantController.getAllTenants(req, res));
  router.post('/', (req, res) => tenantController.createTenant(req, res));
  router.get('/:id', (req, res) => tenantController.getTenantById(req, res));
  router.put('/:id', (req, res) => tenantController.updateTenant(req, res));
  router.delete('/:id', (req, res) => tenantController.deleteTenant(req, res));

  // Tenant user management routes
  router.get('/:id/users', (req, res) => tenantController.getTenantUsers(req, res));
  router.post('/:id/users', (req, res) => tenantController.createTenantUser(req, res));
  router.put('/:id/users/:userId', (req, res) => tenantController.updateTenantUser(req, res));
  router.delete('/:id/users/:userId', (req, res) => tenantController.deleteTenantUser(req, res));

  // Authentication routes
  router.post('/:id/auth', (req, res) => tenantController.authenticateUser(req, res));

  // Utility routes
  router.post('/validate-settings', (req, res) => tenantController.validateSettings(req, res));

  return router;
};
