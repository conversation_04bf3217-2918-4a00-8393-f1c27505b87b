import { Router } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../types';
import { INodeController } from '../../core/interfaces/INodeController';

/**
 * Create node routes
 * @param container Inversify container
 * @returns Express router
 */
export const createNodeRoutes = (container: Container): Router => {
  const router = Router();
  const controller = container.get<INodeController>(TYPES.NodeController);

  // Get all node configurations
  router.get('/', (req, res) => controller.getAll(req, res));

  // Get node configuration by ID
  router.get('/:id', (req, res) => controller.getById(req, res));

  // Create a new node configuration
  router.post('/', (req, res) => controller.create(req, res));

  // Update a node configuration
  router.put('/:id', (req, res) => controller.update(req, res));

  // Delete a node configuration
  router.delete('/:id', (req, res) => controller.delete(req, res));

  // Test a node configuration without saving
  router.post('/test', (req, res) => controller.test(req, res));

  // Get available node types
  router.get('/types', (req, res) => controller.getNodeTypes(req, res));

  return router;
};
