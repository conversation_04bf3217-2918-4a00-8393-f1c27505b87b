import { Router } from 'express';
import { Container } from 'inversify';
import { WorkflowVersionController } from '../controllers/WorkflowVersionController';
import { TYPES } from '../../types';

/**
 * Create workflow version routes
 */
export function createWorkflowVersionRoutes(container: Container): Router {
  const router = Router();
  const controller = container.get<WorkflowVersionController>(WorkflowVersionController);

  // Workflow version management routes
  
  /**
   * @swagger
   * /api/workflows/{workflowId}/versions:
   *   post:
   *     summary: Create a new version of a workflow
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *         description: The workflow ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               definition:
   *                 type: object
   *                 description: The workflow definition
   *               changeDescription:
   *                 type: string
   *                 description: Description of changes
   *     responses:
   *       201:
   *         description: Version created successfully
   *       400:
   *         description: Invalid request
   */
  router.post('/:workflowId/versions', (req, res) => controller.createVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions:
   *   get:
   *     summary: Get version history for a workflow
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 50
   *       - in: query
   *         name: offset
   *         schema:
   *           type: integer
   *           default: 0
   *     responses:
   *       200:
   *         description: Version history retrieved successfully
   */
  router.get('/:workflowId/versions', (req, res) => controller.getVersionHistory(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/active:
   *   get:
   *     summary: Get the currently active version
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *     responses:
   *       200:
   *         description: Active version retrieved successfully
   *       404:
   *         description: No active version found
   */
  router.get('/:workflowId/versions/active', (req, res) => controller.getActiveVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{version}:
   *   get:
   *     summary: Get a specific version of a workflow
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: version
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: Version retrieved successfully
   *       404:
   *         description: Version not found
   */
  router.get('/:workflowId/versions/:version', (req, res) => controller.getVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{version}/activate:
   *   post:
   *     summary: Activate a specific version
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: version
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               deployment:
   *                 type: object
   *                 properties:
   *                   deploymentStrategy:
   *                     type: string
   *                     enum: [immediate, gradual, scheduled]
   *                   rolloutPercentage:
   *                     type: number
   *                   scheduledTime:
   *                     type: string
   *                     format: date-time
   *     responses:
   *       200:
   *         description: Version activated successfully
   *       400:
   *         description: Activation failed
   */
  router.post('/:workflowId/versions/:version/activate', (req, res) => controller.activateVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{version}/deactivate:
   *   post:
   *     summary: Deactivate a specific version
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: version
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               reason:
   *                 type: string
   *                 description: Reason for deactivation
   *     responses:
   *       200:
   *         description: Version deactivated successfully
   */
  router.post('/:workflowId/versions/:version/deactivate', (req, res) => controller.deactivateVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{version}/rollback:
   *   post:
   *     summary: Rollback to a previous version
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: version
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               reason:
   *                 type: string
   *                 description: Reason for rollback
   *               preserveData:
   *                 type: boolean
   *                 default: true
   *               rollbackStrategy:
   *                 type: string
   *                 enum: [immediate, gradual]
   *                 default: immediate
   *     responses:
   *       200:
   *         description: Rollback completed successfully
   */
  router.post('/:workflowId/versions/:version/rollback', (req, res) => controller.rollbackToVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{fromVersion}/compare/{toVersion}:
   *   get:
   *     summary: Compare two versions of a workflow
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: fromVersion
   *         required: true
   *         schema:
   *           type: integer
   *       - in: path
   *         name: toVersion
   *         required: true
   *         schema:
   *           type: integer
   *     responses:
   *       200:
   *         description: Version comparison completed
   */
  router.get('/:workflowId/versions/:fromVersion/compare/:toVersion', (req, res) => controller.compareVersions(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{version}:
   *   delete:
   *     summary: Delete a specific version
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: version
   *         required: true
   *         schema:
   *           type: integer
   *       - in: query
   *         name: force
   *         schema:
   *           type: boolean
   *           default: false
   *     responses:
   *       200:
   *         description: Version deleted successfully
   *       400:
   *         description: Cannot delete active version
   */
  router.delete('/:workflowId/versions/:version', (req, res) => controller.deleteVersion(req, res));

  /**
   * @swagger
   * /api/workflows/{workflowId}/versions/{version}/clone:
   *   post:
   *     summary: Clone a version to create a new workflow
   *     tags: [Workflow Versions]
   *     parameters:
   *       - in: path
   *         name: workflowId
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: version
   *         required: true
   *         schema:
   *           type: integer
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               newWorkflowName:
   *                 type: string
   *                 description: Name for the new workflow
   *     responses:
   *       201:
   *         description: Version cloned successfully
   */
  router.post('/:workflowId/versions/:version/clone', (req, res) => controller.cloneVersion(req, res));

  // Validation route (not workflow-specific)
  
  /**
   * @swagger
   * /api/workflows/validate:
   *   post:
   *     summary: Validate a workflow definition
   *     tags: [Workflow Versions]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               definition:
   *                 type: object
   *                 description: Workflow definition to validate
   *     responses:
   *       200:
   *         description: Validation completed
   */
  router.post('/validate', (req, res) => controller.validateDefinition(req, res));

  return router;
}
