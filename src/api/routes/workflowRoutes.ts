import { Router } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../types';
import { IWorkflowController } from '../../core/interfaces/IWorkflowController';

/**
 * Create workflow routes
 * @param container Inversify container
 * @returns Express router
 */
export const createWorkflowRoutes = (container: Container): Router => {
  const router = Router();
  const controller = container.get<IWorkflowController>(TYPES.WorkflowController);

  // Get all workflows
  router.get('/', (req, res) => controller.getAll(req, res));

  // Get workflow by ID
  router.get('/:id', (req, res) => controller.getById(req, res));

  // Create a new workflow
  router.post('/', (req, res) => controller.create(req, res));

  // Update a workflow
  router.put('/:id', (req, res) => controller.update(req, res));

  // Delete a workflow
  router.delete('/:id', (req, res) => controller.delete(req, res));

  // Test a workflow without saving
  router.post('/test', (req, res) => controller.test(req, res));

  // Get workflow execution history
  router.get('/:id/executions', (req, res) => controller.getExecutionHistory(req, res));

  // Get workflow metrics
  router.get('/:id/metrics', (req, res) => controller.getMetrics(req, res));

  // Execute a workflow
  router.post('/:id/execute', (req, res) => controller.execute(req, res));

  // Public trigger endpoint for workflows
  router.post('/:id/trigger', (req, res) => controller.trigger(req, res));

  return router;
};
