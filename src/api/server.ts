import express from 'express';
import cors from 'cors';
import { injectable, inject } from 'inversify';
import { Container } from 'inversify';
import type { ILogger } from '../core/interfaces/ILogger';
import type { IObservabilityManager } from '../core/interfaces/IObservabilityManager';
import { TYPES } from '../types';
import { getConfig } from '../config';
import { createAdminRoutes } from './routes';

/**
 * API server for the MCP server
 * Provides HTTP endpoints for metrics and admin API
 */
@injectable()
export class ApiServer {
  private app: express.Application;
  private server: any;

  /**
   * Constructor
   * @param logger Logger service
   * @param observabilityManager Observability manager service
   * @param container Inversify container
   */
  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.ObservabilityManager) private observabilityManager: IObservabilityManager,
    @inject(TYPES.Container) private container: Container
  ) {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Start the API server
   * @param port Port to listen on
   */
  public start(port: number): void {
    this.server = this.app.listen(port, () => {
      this.logger.info(`API server listening on port ${port}`);
    });
  }

  /**
   * Stop the API server
   */
  public stop(): void {
    if (this.server) {
      this.server.close();
    }
  }

  /**
   * Set up middleware
   */
  private setupMiddleware(): void {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`);
      next();
    });
  }

  /**
   * Set up routes
   */
  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({ status: 'ok' });
    });

    // Prometheus metrics endpoint
    this.app.get('/metrics', async (req, res) => {
      try {
        res.set('Content-Type', 'text/plain');
        const metrics = await this.observabilityManager.exportPrometheusMetrics();
        res.send(metrics);
      } catch (error) {
        this.logger.error(`Error exporting metrics: ${(error as Error).message}`);
        res.status(500).send('Error exporting metrics');
      }
    });

    // Get data retention configuration
    this.app.get('/admin/data/config', (req, res) => {
      const config = getConfig();
      res.status(200).json({
        retentionDays: config.data.retentionDays,
        cleanupIntervalMinutes: config.data.cleanupIntervalMinutes,
        cleanupBatchSize: config.data.cleanupBatchSize
      });
    });

    // Mount Admin API routes
    this.app.use('/admin', createAdminRoutes(this.container));
  }
}
