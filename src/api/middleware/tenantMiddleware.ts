import { Request, Response, NextFunction } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../types';
import { ITenantResolver } from '../../core/interfaces/ITenantResolver';
import { ITenantContext } from '../../core/interfaces/ITenantContext';
import { ILogger } from '../../core/interfaces/ILogger';

/**
 * Extended Request interface with tenant information
 */
export interface TenantRequest extends Request {
  tenant?: any;
  tenantId?: string;
}

/**
 * Middleware to resolve and set tenant context from request
 * @param container Inversify container
 * @returns Express middleware function
 */
export const createTenantMiddleware = (container: Container) => {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    try {
      const tenantResolver = container.get<ITenantResolver>(TYPES.TenantResolver);
      const tenantContext = container.get<ITenantContext>(TYPES.TenantContext);
      const logger = container.get<ILogger>(TYPES.Logger);

      // Resolve tenant from request
      const tenant = await tenantResolver.resolveTenant(req);

      if (tenant) {
        // Set tenant in context
        tenantContext.setCurrentTenant(tenant);
        
        // Add tenant info to request for convenience
        req.tenant = tenant;
        req.tenantId = tenant.id;

        logger.debug('Tenant resolved and set in context', { 
          tenantId: tenant.id, 
          tenantName: tenant.name 
        });
      } else {
        // Clear tenant context if no tenant resolved
        tenantContext.clearCurrentTenant();
        logger.debug('No tenant resolved from request');
      }

      next();
    } catch (error) {
      const logger = container.get<ILogger>(TYPES.Logger);
      logger.error('Error in tenant middleware', { error });
      
      // Continue without tenant context in case of error
      next();
    }
  };
};

/**
 * Middleware to require tenant context
 * Returns 400 if no tenant is resolved
 * @param container Inversify container
 * @returns Express middleware function
 */
export const createRequireTenantMiddleware = (container: Container) => {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    try {
      const tenantContext = container.get<ITenantContext>(TYPES.TenantContext);
      
      if (!tenantContext.hasTenantContext()) {
        return res.status(400).json({
          message: 'Tenant context is required. Please provide tenant information in request headers, subdomain, or path.'
        });
      }

      next();
    } catch (error) {
      const logger = container.get<ILogger>(TYPES.Logger);
      logger.error('Error in require tenant middleware', { error });
      
      return res.status(500).json({
        message: 'Internal server error'
      });
    }
  };
};

/**
 * Middleware to check tenant permissions
 * @param resource Resource name
 * @param action Action name
 * @param container Inversify container
 * @returns Express middleware function
 */
export const createTenantPermissionMiddleware = (
  resource: string, 
  action: string, 
  container: Container
) => {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    try {
      const tenantContext = container.get<ITenantContext>(TYPES.TenantContext);
      const logger = container.get<ILogger>(TYPES.Logger);

      // Check if user has permission
      if (!tenantContext.hasPermission(resource, action)) {
        const userId = tenantContext.getUserId() || 'anonymous';
        logger.warn('Permission denied', { 
          userId, 
          resource, 
          action,
          tenantId: tenantContext.getTenantId()
        });

        return res.status(403).json({
          message: `Permission denied: cannot ${action} on ${resource}`
        });
      }

      next();
    } catch (error) {
      const logger = container.get<ILogger>(TYPES.Logger);
      logger.error('Error in tenant permission middleware', { error });
      
      return res.status(500).json({
        message: 'Internal server error'
      });
    }
  };
};

/**
 * Middleware to log tenant-specific audit events
 * @param event Event name
 * @param container Inversify container
 * @returns Express middleware function
 */
export const createTenantAuditMiddleware = (event: string, container: Container) => {
  return async (req: TenantRequest, res: Response, next: NextFunction) => {
    try {
      const tenantContext = container.get<ITenantContext>(TYPES.TenantContext);
      const tenantIsolation = container.get<any>(TYPES.TenantIsolation); // ITenantIsolation
      
      const tenantId = tenantContext.getTenantId();
      if (tenantId) {
        const auditDetails = {
          method: req.method,
          path: req.path,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
          userId: tenantContext.getUserId(),
          timestamp: new Date()
        };

        // Log audit event (fire and forget)
        tenantIsolation.logAuditEvent(tenantId, event, auditDetails).catch((error: any) => {
          const logger = container.get<ILogger>(TYPES.Logger);
          logger.error('Error logging audit event', { error, tenantId, event });
        });
      }

      next();
    } catch (error) {
      const logger = container.get<ILogger>(TYPES.Logger);
      logger.error('Error in tenant audit middleware', { error });
      
      // Continue without audit logging in case of error
      next();
    }
  };
};
