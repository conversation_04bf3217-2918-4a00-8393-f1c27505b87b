import { Request, Response, NextFunction } from 'express';

/**
 * Admin authorization middleware
 * Checks if user has admin permissions
 */
export const adminMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    // Check if user has admin role
    const hasAdminRole = req.user.roles.some(role => 
      role.name === 'admin' || role.name === 'system_admin'
    );

    if (!hasAdminRole) {
      res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
      return;
    }

    next();

  } catch (error) {
    console.error('Admin middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

/**
 * Permission-based authorization middleware
 * Checks if user has specific permission
 */
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Check if user has the required permission
      const hasPermission = req.user.roles.some(role =>
        role.permissions.includes(permission) || 
        role.permissions.includes('*') // Wildcard permission
      );

      if (!hasPermission) {
        res.status(403).json({
          success: false,
          error: `Permission '${permission}' required`
        });
        return;
      }

      next();

    } catch (error) {
      console.error('Permission middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
};

/**
 * Tenant-specific authorization middleware
 * Checks if user belongs to the specified tenant
 */
export const requireTenantAccess = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    const tenantId = req.params.tenantId || req.body.tenantId || req.query.tenantId;

    if (!tenantId) {
      res.status(400).json({
        success: false,
        error: 'Tenant ID required'
      });
      return;
    }

    // Check if user belongs to the tenant or has admin role
    const hasAdminRole = req.user.roles.some(role => 
      role.name === 'admin' || role.name === 'system_admin'
    );

    if (!hasAdminRole && req.user.tenantId !== tenantId) {
      res.status(403).json({
        success: false,
        error: 'Access denied to this tenant'
      });
      return;
    }

    next();

  } catch (error) {
    console.error('Tenant access middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

/**
 * Role-based authorization middleware
 * Checks if user has one of the specified roles
 */
export const requireRole = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Check if user has any of the required roles
      const hasRequiredRole = req.user.roles.some(userRole =>
        roles.includes(userRole.name)
      );

      if (!hasRequiredRole) {
        res.status(403).json({
          success: false,
          error: `One of the following roles required: ${roles.join(', ')}`
        });
        return;
      }

      next();

    } catch (error) {
      console.error('Role middleware error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
};
