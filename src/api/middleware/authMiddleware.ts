import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';

// Extend Request interface to include user data
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
        tenantId: string;
        username: string;
        roles: Array<{
          name: string;
          permissions: string[];
        }>;
      };
    }
  }
}

/**
 * Authentication middleware
 * Verifies JWT token and adds user data to request
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // Skip authentication in development mode
    if (process.env.NODE_ENV === 'development') {
      req.user = {
        userId: 'dev-user-id',
        tenantId: 'default',
        username: 'dev-admin',
        roles: [{ name: 'admin', permissions: ['*'] }]
      };
      next();
      return;
    }

    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Access denied. No token provided.'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    const secret = process.env.JWT_SECRET || 'your-secret-key';

    try {
      const decoded = jwt.verify(token, secret) as any;

      // Add user data to request
      req.user = {
        userId: decoded.userId,
        tenantId: decoded.tenantId,
        username: decoded.username,
        roles: decoded.roles || []
      };

      next();
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        error: 'Invalid token.'
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

/**
 * Optional authentication middleware
 * Adds user data to request if token is present, but doesn't require it
 */
export const optionalAuthMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      next();
      return;
    }

    const token = authHeader.substring(7);
    const secret = process.env.JWT_SECRET || 'your-secret-key';

    try {
      const decoded = jwt.verify(token, secret) as any;

      req.user = {
        userId: decoded.userId,
        tenantId: decoded.tenantId,
        username: decoded.username,
        roles: decoded.roles || []
      };
    } catch (jwtError) {
      // Token is invalid, but we don't fail the request
      console.warn(
        'Invalid token in optional auth middleware:',
        jwtError instanceof Error ? jwtError.message : 'Unknown error'
      );
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    next(); // Continue even if there's an error
  }
};
