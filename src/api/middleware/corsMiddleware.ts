import { Request, Response, NextFunction } from 'express';

/**
 * CORS middleware for Admin UI
 * Allows frontend to communicate with backend API
 */
export const corsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Get allowed origins from environment or use defaults
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
    'http://localhost:7000', // MCP Server
    'http://localhost:7001', // API Server
    'http://localhost:7002', // Admin UI Server
    'http://localhost:7005', // Frontend React app
    'http://localhost:7174', // Vite preview port
    'http://127.0.0.1:7000',
    'http://127.0.0.1:7001',
    'http://127.0.0.1:7002',
    'http://127.0.0.1:7005',
    'http://127.0.0.1:7174'
  ];

  const origin = req.headers.origin;

  // Check if origin is allowed
  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    // Allow requests with no origin (e.g., mobile apps, Postman)
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  // Allow credentials
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Allow specific headers
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-Tenant-ID, X-User-ID'
  );

  // Allow specific methods
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.status(200).end();
    return;
  }

  next();
};

/**
 * Security headers middleware
 * Adds security-related headers
 */
export const securityHeadersMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');

  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Content Security Policy (basic)
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' ws: wss:;"
  );

  next();
};
