import express from 'express';
import path from 'path';
import { Container } from 'inversify';
import { createAuthRoutes } from './routes/authRoutes';
import { createAdminRoutes as createDashboardRoutes } from './routes/adminRoutes';
import { createAdminRoutes } from './routes/index';
import { createPublicRoutes } from './routes/publicRoutes';
import { corsMiddleware, securityHeadersMiddleware } from './middleware/corsMiddleware';
import { ILogger } from '../core/interfaces/ILogger';
import { TYPES } from '../types';

export class AdminServer {
  private app: express.Application;
  private container: Container;
  private logger: ILogger;
  private server: any;

  constructor(container: Container) {
    this.container = container;
    this.logger = container.get<ILogger>(TYPES.Logger);
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security headers
    this.app.use(securityHeadersMiddleware);

    // CORS
    this.app.use(corsMiddleware);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Dynamic MCP Server Admin API',
        version: process.env.npm_package_version || '1.0.0'
      });
    });

    // Public API routes (no authentication required)
    // This must come BEFORE authenticated routes to bypass auth middleware
    this.app.use('/api/public', createPublicRoutes(this.container));

    // Authenticated API routes
    this.app.use('/auth', createAuthRoutes(this.container));
    this.app.use('/admin', createDashboardRoutes(this.container));
    this.app.use('/api', createAdminRoutes(this.container));

    // Serve static files from frontend build
    const frontendDistPath = path.join(__dirname, '../../frontend/dist');
    this.app.use(express.static(frontendDistPath));

    // Catch-all handler for SPA routing (must be last)
    this.app.use((req: express.Request, res: express.Response) => {
      // Only handle GET requests for non-API routes
      if (
        req.method === 'GET' &&
        !req.path.startsWith('/admin') &&
        !req.path.startsWith('/auth') &&
        !req.path.startsWith('/api') &&
        !req.path.startsWith('/health')
      ) {
        res.sendFile(path.join(frontendDistPath, 'index.html'));
      } else {
        res.status(404).json({
          success: false,
          error: 'Endpoint not found',
          path: req.originalUrl,
          method: req.method
        });
      }
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use(
      (error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
        this.logger.error('Unhandled error in Admin API:', error);

        // Don't leak error details in production
        const isDevelopment = process.env.NODE_ENV === 'development';

        res.status(error.status || 500).json({
          success: false,
          error: isDevelopment ? error.message : 'Internal server error',
          ...(isDevelopment && { stack: error.stack }),
          timestamp: new Date().toISOString()
        });
      }
    );

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception in Admin API:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled rejection in Admin API:', {
        reason: reason instanceof Error ? reason.message : String(reason),
        stack: reason instanceof Error ? reason.stack : undefined
      });
      // Don't exit the process - just log the error
      // process.exit(1);
    });
  }

  public async start(port: number = 3001): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(port, () => {
          this.logger.info(`Admin API server started on port ${port}`);
          this.logger.info(`Health check: http://localhost:${port}/health`);
          this.logger.info(`Admin UI API: http://localhost:${port}/admin`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            this.logger.error(`Port ${port} is already in use`);
          } else {
            this.logger.error('Failed to start Admin API server:', error);
          }
          reject(error);
        });
      } catch (error) {
        this.logger.error('Error starting Admin API server:', error);
        reject(error);
      }
    });
  }

  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          this.logger.info('Admin API server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  public getApp(): express.Application {
    return this.app;
  }
}
