import { injectable } from 'inversify';
import { IWorkflowMemory } from '../../core/interfaces/IWorkflowMemory';
import { WorkflowContext } from '../../core/types/WorkflowContext';

/**
 * In-memory implementation of workflow memory
 */
@injectable()
export class InMemoryWorkflowMemory implements IWorkflowMemory {
  private contexts: Map<string, WorkflowContext> = new Map();

  /**
   * Get workflow context by execution ID
   * @param executionId Execution ID
   * @returns Workflow context
   */
  async getContext(executionId: string): Promise<WorkflowContext> {
    const context = this.contexts.get(executionId);
    if (!context) {
      throw new Error(`Workflow context for execution ID ${executionId} not found`);
    }
    return { ...context }; // Return a copy to prevent direct modification
  }

  /**
   * Update workflow context
   * @param executionId Execution ID
   * @param context Workflow context
   */
  async updateContext(executionId: string, context: WorkflowContext): Promise<void> {
    this.contexts.set(executionId, { ...context }); // Store a copy
  }

  /**
   * Delete workflow context
   * @param executionId Execution ID
   */
  async deleteContext(executionId: string): Promise<void> {
    this.contexts.delete(executionId);
  }
}
