import { injectable, inject } from 'inversify';
import { IWorkflowEngine } from '../../core/interfaces/IWorkflowEngine';
import type { INodeRegistry } from '../../core/interfaces/INodeRegistry';
import type { IWorkflowMemory } from '../../core/interfaces/IWorkflowMemory';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { IRepository } from '../../core/interfaces/IRepository';
import { Workflow } from '../../infrastructure/database/entities/Workflow.entity';
import { WorkflowExecution } from '../../infrastructure/database/entities/WorkflowExecution.entity';
import { WorkflowNodeExecution } from '../../infrastructure/database/entities/WorkflowNodeExecution.entity';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import { v4 as uuidv4 } from 'uuid';
import type { IDatabase } from '../../core/interfaces/IDatabase';
import type { IObservabilityManager } from '../../core/interfaces/IObservabilityManager';
import type { IJSONPathService } from '../../services/JSONPathService';
import { WebSocketService } from '../../services/WebSocketService';

/**
 * Workflow engine implementation
 */
@injectable()
export class WorkflowEngine implements IWorkflowEngine {
  private workflowRepository: IRepository<Workflow>;
  private workflowExecutionRepository: IRepository<WorkflowExecution>;
  private workflowNodeExecutionRepository: IRepository<WorkflowNodeExecution>;

  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.NodeRegistry) private nodeRegistry: INodeRegistry,
    @inject(TYPES.WorkflowMemory) private workflowMemory: IWorkflowMemory,
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.ObservabilityManager) private observabilityManager: IObservabilityManager,
    @inject(TYPES.JSONPathService) private jsonPathService: IJSONPathService,
    @inject(TYPES.WebSocketService) private webSocketService: WebSocketService
  ) {
    // Repositories will be initialized in the initialize method
  }

  /**
   * Initialize the workflow engine
   */
  async initialize(): Promise<void> {
    try {
      // Initialize database if not already initialized
      await this.database.initialize();

      // Get repositories
      this.workflowRepository = this.database.getRepository<Workflow>(Workflow);
      this.workflowExecutionRepository =
        this.database.getRepository<WorkflowExecution>(WorkflowExecution);
      this.workflowNodeExecutionRepository =
        this.database.getRepository<WorkflowNodeExecution>(WorkflowNodeExecution);

      this.logger.info('Workflow engine initialized');
    } catch (error) {
      this.logger.error('Error initializing workflow engine', error);
      throw error;
    }
  }

  /**
   * Execute a workflow with the given ID and input
   * @param workflowId Workflow ID
   * @param input Input data
   * @returns Workflow execution result
   */
  async executeWorkflow(workflowId: string, input: any): Promise<any> {
    // 1. Load workflow configuration from database
    const workflow = await this.getWorkflow(workflowId);
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // 2. Create execution record
    const executionId = uuidv4();
    const startTime = new Date();

    await this.workflowExecutionRepository.save({
      id: executionId,
      workflow_id: workflowId,
      status: 'RUNNING',
      input_data: input,
      started_at: startTime
    });

    // Record workflow execution start in observability manager
    await this.observabilityManager.recordWorkflowExecution(workflowId, executionId, {
      startTime,
      status: 'RUNNING'
    });

    // 3. Initialize workflow context
    const context: WorkflowContext = {
      executionId,
      workflowId,
      input,
      output: null,
      nodeResults: {},
      variables: {},
      startedAt: startTime,
      // JSONPath helper methods
      query: (path: string) => this.jsonPathService.query(context, path),
      queryAll: (path: string) => this.jsonPathService.queryAll(context, path),
      set: (path: string, value: any) => {
        const updatedContext = this.jsonPathService.set(context, path, value);
        Object.assign(context, updatedContext);
      },
      exists: (path: string) => this.jsonPathService.exists(context, path)
    };

    await this.workflowMemory.updateContext(executionId, context);

    // 4. Broadcast workflow start
    this.webSocketService.broadcastWorkflowStart(workflowId, executionId, input);

    try {
      // 5. Execute nodes in sequence
      const { nodes, edges } = workflow.nodes_config;

      // Find start node (node with no incoming edges)
      const startNodeId = this.findStartNode(nodes, edges);
      if (!startNodeId) {
        throw new Error('No start node found in workflow');
      }

      // Execute nodes in sequence
      let currentNodeId = startNodeId;
      let result = input;

      while (currentNodeId) {
        const node = nodes.find((n) => n.id === currentNodeId);
        if (!node) {
          throw new Error(`Node with ID ${currentNodeId} not found`);
        }

        // Log node execution start
        this.logger.info(`Executing node ${node.id} (${node.type}) in workflow ${workflowId}`);

        // Broadcast node start
        this.webSocketService.broadcastNodeStart(executionId, node.id, node.type, result);

        // Create node execution record
        const nodeExecutionId = uuidv4();
        const nodeStartTime = new Date();

        await this.workflowNodeExecutionRepository.save({
          id: nodeExecutionId,
          workflow_execution_id: executionId,
          node_id: node.id,
          node_type: node.type,
          status: 'RUNNING',
          input_data: result,
          started_at: nodeStartTime
        });

        // Record node execution start in observability manager
        await this.observabilityManager.recordNodeExecution(node.id, executionId, {
          nodeType: node.type,
          startTime: nodeStartTime,
          status: 'RUNNING'
        });

        try {
          // Get node implementation
          const nodeImpl = this.nodeRegistry.getNode(node.type, node.config);

          // Execute node
          const nodeContext = await this.workflowMemory.getContext(executionId);
          result = await nodeImpl.execute(result, nodeContext);

          // Update node results in context
          nodeContext.nodeResults[node.id] = result;
          await this.workflowMemory.updateContext(executionId, nodeContext);

          // Broadcast node completion and context update
          this.webSocketService.broadcastNodeComplete(executionId, node.id, node.type, result);
          this.webSocketService.broadcastContextUpdate(executionId, nodeContext, 'nodeResult');

          // Update node execution record
          const nodeEndTime = new Date();
          const nodeDurationMs = nodeEndTime.getTime() - nodeStartTime.getTime();

          await this.workflowNodeExecutionRepository.update(nodeExecutionId, {
            status: 'COMPLETED',
            output_data: result,
            completed_at: nodeEndTime,
            execution_time_ms: nodeDurationMs
          });

          // Record node execution completion in observability manager
          await this.observabilityManager.recordNodeExecution(node.id, executionId, {
            nodeType: node.type,
            startTime: nodeStartTime,
            endTime: nodeEndTime,
            durationMs: nodeDurationMs,
            status: 'COMPLETED'
          });
        } catch (error) {
          // Handle node execution error
          const nodeEndTime = new Date();
          const nodeDurationMs = nodeEndTime.getTime() - nodeStartTime.getTime();
          const errorMessage = (error as Error).message;

          await this.workflowNodeExecutionRepository.update(nodeExecutionId, {
            status: 'FAILED',
            error_details: JSON.stringify({
              message: errorMessage,
              stack: (error as Error).stack
            }),
            completed_at: nodeEndTime,
            execution_time_ms: nodeDurationMs
          });

          // Record node execution failure in observability manager
          await this.observabilityManager.recordNodeExecution(node.id, executionId, {
            nodeType: node.type,
            startTime: nodeStartTime,
            endTime: nodeEndTime,
            durationMs: nodeDurationMs,
            status: 'FAILED',
            error: errorMessage
          });

          throw error;
        }

        // Find next node
        const nextEdge = edges.find((e) => e.source === currentNodeId);
        currentNodeId = nextEdge ? nextEdge.target : '';
      }

      // 5. Update workflow execution record
      const endTime = new Date();
      const durationMs = endTime.getTime() - startTime.getTime();

      await this.workflowExecutionRepository.update(executionId, {
        status: 'COMPLETED',
        output_data: result,
        completed_at: endTime,
        execution_time_ms: durationMs
      });

      // Record workflow execution completion in observability manager
      await this.observabilityManager.recordWorkflowExecution(workflowId, executionId, {
        startTime,
        endTime,
        durationMs,
        status: 'COMPLETED'
      });

      // 6. Broadcast workflow completion
      this.webSocketService.broadcastWorkflowComplete(workflowId, executionId, result);

      // 7. Return final result
      return result;
    } catch (error) {
      // Handle workflow execution error
      const endTime = new Date();
      const durationMs = endTime.getTime() - startTime.getTime();
      const errorMessage = (error as Error).message;

      await this.workflowExecutionRepository.update(executionId, {
        status: 'FAILED',
        error_details: JSON.stringify({
          message: errorMessage,
          stack: (error as Error).stack
        }),
        completed_at: endTime,
        execution_time_ms: durationMs
      });

      // Record workflow execution failure in observability manager
      await this.observabilityManager.recordWorkflowExecution(workflowId, executionId, {
        startTime,
        endTime,
        durationMs,
        status: 'FAILED',
        error: errorMessage
      });

      // Broadcast workflow error
      this.webSocketService.broadcastWorkflowError(workflowId, executionId, error);

      throw error;
    } finally {
      // Clean up workflow context (optional for in-memory implementation)
      // await this.workflowMemory.deleteContext(executionId);
    }
  }

  /**
   * Get a workflow by ID
   * @param workflowId Workflow ID
   * @returns Workflow configuration or null if not found
   */
  async getWorkflow(workflowId: string): Promise<Workflow | null> {
    return this.workflowRepository.findOne({
      where: { id: workflowId, enabled: true }
    });
  }

  /**
   * Execute a workflow with the given configuration and input
   * @param workflowConfig Workflow configuration
   * @param input Input data
   * @returns Workflow execution result
   */
  async executeWorkflowConfig(workflowConfig: any, input: any): Promise<any> {
    // Create a temporary execution ID for testing
    const executionId = uuidv4();
    const startTime = new Date();

    // Initialize workflow context
    const context: WorkflowContext = {
      executionId,
      workflowId: 'test-workflow',
      input,
      output: null,
      nodeResults: {},
      variables: {},
      startedAt: startTime,
      // JSONPath helper methods
      query: (path: string) => this.jsonPathService.query(context, path),
      queryAll: (path: string) => this.jsonPathService.queryAll(context, path),
      set: (path: string, value: any) => {
        const updatedContext = this.jsonPathService.set(context, path, value);
        Object.assign(context, updatedContext);
      },
      exists: (path: string) => this.jsonPathService.exists(context, path)
    };

    await this.workflowMemory.updateContext(executionId, context);

    try {
      // Execute nodes in sequence
      const { nodes, edges } = workflowConfig.nodes_config;

      // Find start node (node with no incoming edges)
      const startNodeId = this.findStartNode(nodes, edges);
      if (!startNodeId) {
        throw new Error('No start node found in workflow');
      }

      // Execute nodes in sequence
      let currentNodeId = startNodeId;
      let result = input;

      while (currentNodeId) {
        const node = nodes.find((n: any) => n.id === currentNodeId);
        if (!node) {
          throw new Error(`Node with ID ${currentNodeId} not found`);
        }

        // Get node implementation
        const nodeImpl = this.nodeRegistry.getNode(node.type, node.config);

        // Execute node
        const nodeContext = await this.workflowMemory.getContext(executionId);
        result = await nodeImpl.execute(result, nodeContext);

        // Update node results in context
        nodeContext.nodeResults[node.id] = result;
        await this.workflowMemory.updateContext(executionId, nodeContext);

        // Find next node
        const nextEdge = edges.find((e: any) => e.source === currentNodeId);
        currentNodeId = nextEdge ? nextEdge.target : '';
      }

      return result;
    } finally {
      // Clean up workflow context
      // await this.workflowMemory.deleteContext(executionId);
    }
  }

  /**
   * Reload a workflow by ID
   * @param workflowId Workflow ID
   * @returns True if the workflow was reloaded, false if not found
   */
  async reloadWorkflow(workflowId: string): Promise<boolean> {
    try {
      const workflow = await this.workflowRepository.findById(workflowId);
      if (!workflow) {
        return false;
      }

      this.logger.info(`Reloaded workflow: ${workflow.name} (${workflowId})`);
      return true;
    } catch (error) {
      this.logger.error(`Error reloading workflow ${workflowId}`, error);
      return false;
    }
  }

  /**
   * Reload all workflows
   * @returns Number of workflows reloaded
   */
  async reloadAllWorkflows(): Promise<number> {
    try {
      const workflows = await this.workflowRepository.findAll();
      this.logger.info(`Reloaded ${workflows.length} workflows`);
      return workflows.length;
    } catch (error) {
      this.logger.error('Error reloading all workflows', error);
      return 0;
    }
  }

  /**
   * Remove a workflow by ID
   * @param workflowId Workflow ID
   * @returns True if the workflow was removed, false if not found
   */
  async removeWorkflow(workflowId: string): Promise<boolean> {
    try {
      const workflow = await this.workflowRepository.findById(workflowId);
      if (!workflow) {
        return false;
      }

      this.logger.info(`Removed workflow: ${workflow.name} (${workflowId})`);
      return true;
    } catch (error) {
      this.logger.error(`Error removing workflow ${workflowId}`, error);
      return false;
    }
  }

  /**
   * Get workflow execution history
   * @param workflowId Workflow ID
   * @param limit Maximum number of executions to return
   * @param offset Offset for pagination
   * @returns Workflow execution history
   */
  async getWorkflowExecutionHistory(workflowId: string, limit = 50, offset = 0): Promise<any[]> {
    try {
      // This would typically query the workflow execution repository
      // For now, return empty array as placeholder
      return [];
    } catch (error) {
      this.logger.error(`Error getting execution history for workflow ${workflowId}`, error);
      return [];
    }
  }

  /**
   * Find the start node in a workflow
   * @param nodes Workflow nodes
   * @param edges Workflow edges
   * @returns ID of the start node or null if not found
   */
  private findStartNode(nodes: any[], edges: any[]): string | null {
    // Find node that has no incoming edges
    const nodesWithIncomingEdges = new Set(edges.map((e) => e.target));
    const startNode = nodes.find((n) => !nodesWithIncomingEdges.has(n.id));
    return startNode ? startNode.id : null;
  }
}
