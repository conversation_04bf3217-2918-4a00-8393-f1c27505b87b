import { injectable, inject } from 'inversify';
import { IRedisWorkflowMemory } from '../../core/interfaces/IRedisWorkflowMemory';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import type { IRedisClient } from '../../core/interfaces/IRedisClient';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { getEnvNumber } from '../../utils/env';

/**
 * Redis-based implementation of workflow memory
 */
@injectable()
export class RedisWorkflowMemory implements IRedisWorkflowMemory {
  private readonly keyPrefix = 'workflow:context:';
  private readonly historyKeyPrefix = 'workflow:history:';
  private readonly defaultTTL: number;
  private readonly historyTTL: number;
  private readonly maxHistoryEntries: number;
  private cleanupInterval: NodeJS.Timeout | null = null;

  /**
   * Constructor
   * @param redisClient Redis client
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.RedisClient) private redisClient: IRedisClient,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    this.defaultTTL = getEnvNumber('WORKFLOW_CONTEXT_TTL', 3600); // Default: 1 hour
    this.historyTTL = getEnvNumber('WORKFLOW_HISTORY_TTL', 86400 * 7); // Default: 7 days
    this.maxHistoryEntries = getEnvNumber('WORKFLOW_MAX_HISTORY_ENTRIES', 100);

    // Start cleanup process
    this.startCleanupProcess();
  }

  /**
   * Get workflow context by execution ID
   * @param executionId Execution ID
   * @returns Workflow context
   */
  async getContext(executionId: string): Promise<WorkflowContext> {
    const key = this.getKey(executionId);
    const data = await this.redisClient.get(key);

    if (!data) {
      throw new Error(`Workflow context for execution ID ${executionId} not found`);
    }

    try {
      return JSON.parse(data) as WorkflowContext;
    } catch (error) {
      this.logger.error(`Failed to parse workflow context: ${(error as Error).message}`);
      throw new Error(`Failed to parse workflow context for execution ID ${executionId}`);
    }
  }

  /**
   * Update workflow context
   * @param executionId Execution ID
   * @param context Workflow context
   */
  async updateContext(executionId: string, context: WorkflowContext): Promise<void> {
    const key = this.getKey(executionId);

    try {
      // Serialize context to JSON
      const data = JSON.stringify(context);

      // Store in Redis
      await this.redisClient.set(key, data);

      // Set default TTL if not already set
      const ttl = await this.redisClient.ttl(key);
      if (ttl < 0) {
        await this.setContextTTL(executionId, this.defaultTTL);
      }
    } catch (error) {
      this.logger.error(`Failed to update workflow context: ${(error as Error).message}`);
      throw new Error(`Failed to update workflow context for execution ID ${executionId}`);
    }
  }

  /**
   * Delete workflow context
   * @param executionId Execution ID
   */
  async deleteContext(executionId: string): Promise<void> {
    const key = this.getKey(executionId);

    try {
      await this.redisClient.del(key);
    } catch (error) {
      this.logger.error(`Failed to delete workflow context: ${(error as Error).message}`);
      throw new Error(`Failed to delete workflow context for execution ID ${executionId}`);
    }
  }

  /**
   * Set TTL for workflow context
   * @param executionId Execution ID
   * @param ttlSeconds TTL in seconds
   */
  async setContextTTL(executionId: string, ttlSeconds: number): Promise<void> {
    const key = this.getKey(executionId);

    try {
      const result = await this.redisClient.expire(key, ttlSeconds);
      if (!result) {
        this.logger.warn(`Failed to set TTL for workflow context: key ${key} does not exist`);
      }
    } catch (error) {
      this.logger.error(`Failed to set TTL for workflow context: ${(error as Error).message}`);
      throw new Error(`Failed to set TTL for workflow context for execution ID ${executionId}`);
    }
  }

  /**
   * Get Redis key for workflow context
   * @param executionId Execution ID
   * @returns Redis key
   */
  private getKey(executionId: string): string {
    return `${this.keyPrefix}${executionId}`;
  }

  /**
   * Get the Redis key for context history
   * @param executionId Execution ID
   * @returns Redis key for history
   */
  private getHistoryKey(executionId: string): string {
    return `${this.historyKeyPrefix}${executionId}`;
  }

  /**
   * Save context to history before deletion
   * @param executionId Execution ID
   * @param context Workflow context
   */
  async saveToHistory(executionId: string, context: WorkflowContext): Promise<void> {
    try {
      const historyKey = this.getHistoryKey(executionId);
      const historyEntry = {
        context,
        savedAt: new Date().toISOString(),
        version: 1
      };

      // Add to history list
      await this.redisClient.lpush(historyKey, JSON.stringify(historyEntry));

      // Trim to max entries
      await this.redisClient.ltrim(historyKey, 0, this.maxHistoryEntries - 1);

      // Set TTL for history
      await this.redisClient.expire(historyKey, this.historyTTL);

      this.logger.debug(`Context saved to history for execution ${executionId}`);
    } catch (error) {
      this.logger.error(`Failed to save context to history for execution ${executionId}`, error);
    }
  }

  /**
   * Get context history for execution
   * @param executionId Execution ID
   * @returns Array of historical context entries
   */
  async getHistory(executionId: string): Promise<any[]> {
    try {
      const historyKey = this.getHistoryKey(executionId);
      const historyData = await this.redisClient.lrange(historyKey, 0, -1);

      return historyData.map((entry: string) => JSON.parse(entry));
    } catch (error) {
      this.logger.error(`Failed to get context history for execution ${executionId}`, error);
      return [];
    }
  }

  /**
   * Start automatic cleanup process
   */
  private startCleanupProcess(): void {
    // Run cleanup every hour
    const cleanupIntervalMs = 60 * 60 * 1000; // 1 hour

    this.cleanupInterval = setInterval(async () => {
      await this.performCleanup();
    }, cleanupIntervalMs);

    this.logger.info('Context cleanup process started');
  }

  /**
   * Stop cleanup process
   */
  stopCleanupProcess(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      this.logger.info('Context cleanup process stopped');
    }
  }

  /**
   * Perform cleanup of expired contexts
   */
  async performCleanup(): Promise<void> {
    try {
      this.logger.info('Starting context cleanup...');

      // Get all context keys
      const contextKeys = await this.redisClient.keys(`${this.keyPrefix}*`);
      let cleanedCount = 0;

      for (const key of contextKeys) {
        try {
          // Check if key has TTL
          const ttl = await this.redisClient.ttl(key);

          // If TTL is -1 (no expiration) or very low, check if we should clean it
          if (ttl === -1 || ttl < 60) {
            // Less than 1 minute
            const executionId = key.replace(this.keyPrefix, '');

            // Get context before deletion for history
            const contextData = await this.redisClient.get(key);
            if (contextData) {
              const context = JSON.parse(contextData);
              await this.saveToHistory(executionId, context);
            }

            // Delete the context
            await this.redisClient.del(key);
            cleanedCount++;
          }
        } catch (error) {
          this.logger.warn(`Failed to cleanup context key ${key}`, error);
        }
      }

      this.logger.info(`Context cleanup completed: ${cleanedCount} contexts cleaned`);
    } catch (error) {
      this.logger.error('Context cleanup failed', error);
    }
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats(): Promise<{
    activeContexts: number;
    historyEntries: number;
    totalMemoryUsage: string;
  }> {
    try {
      const contextKeys = await this.redisClient.keys(`${this.keyPrefix}*`);
      const historyKeys = await this.redisClient.keys(`${this.historyKeyPrefix}*`);

      return {
        activeContexts: contextKeys.length,
        historyEntries: historyKeys.length,
        totalMemoryUsage: '0 B' // Simplified for now
      };
    } catch (error) {
      this.logger.error('Failed to get cleanup stats', error);
      return {
        activeContexts: 0,
        historyEntries: 0,
        totalMemoryUsage: '0 B'
      };
    }
  }
}
