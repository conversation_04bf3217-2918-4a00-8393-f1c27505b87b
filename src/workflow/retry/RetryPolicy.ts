import { injectable, inject, optional } from 'inversify';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { CircuitBreaker, CircuitBreakerOptions, CircuitBreakerError } from './CircuitBreaker';

/**
 * Options for retry policy
 */
export interface RetryOptions {
  /**
   * Maximum number of retry attempts
   */
  maxRetries?: number;

  /**
   * Initial delay between retries in milliseconds
   */
  retryDelay?: number;

  /**
   * Multiplier for exponential backoff
   */
  retryBackoffMultiplier?: number;

  /**
   * List of error names or messages that are retryable
   */
  retryableErrors?: string[];

  /**
   * Circuit breaker options
   */
  circuitBreaker?: CircuitBreakerOptions | boolean;
}

/**
 * Retry policy for handling transient errors
 */
@injectable()
export class RetryPolicy {
  private maxRetries: number;
  private retryDelay: number;
  private retryBackoffMultiplier: number;
  private retryableErrors: string[];
  private circuitBreaker?: CircuitBreaker;

  /**
   * Constructor
   * @param options Retry options
   * @param logger Logger
   */
  constructor(
    options: RetryOptions = {},
    @inject(TYPES.Logger) @optional() private logger?: ILogger
  ) {
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.retryBackoffMultiplier = options.retryBackoffMultiplier || 2;
    this.retryableErrors = options.retryableErrors || [];

    // Initialize circuit breaker if enabled
    if (options.circuitBreaker) {
      const circuitBreakerOptions = options.circuitBreaker === true ? {} : options.circuitBreaker;
      this.circuitBreaker = new CircuitBreaker(circuitBreakerOptions, this.logger);
    }
  }

  /**
   * Execute a function with retry
   * @param fn Function to execute
   * @returns Function result
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    // If circuit breaker is enabled, execute with circuit breaker
    if (this.circuitBreaker) {
      return this.executeWithCircuitBreaker(fn);
    }

    // Otherwise, execute with retry only
    return this.executeWithRetry(fn);
  }

  /**
   * Execute a function with circuit breaker
   * @param fn Function to execute
   * @returns Function result
   */
  private async executeWithCircuitBreaker<T>(fn: () => Promise<T>): Promise<T> {
    try {
      // Execute with circuit breaker
      return await this.circuitBreaker!.execute(() => this.executeWithRetry(fn));
    } catch (error) {
      // If circuit breaker error, rethrow
      if (error instanceof CircuitBreakerError) {
        this.log('warn', `Circuit breaker error: ${error.message}`);
        throw error;
      }

      // Otherwise, rethrow the original error
      throw error;
    }
  }

  /**
   * Execute a function with retry
   * @param fn Function to execute
   * @returns Function result
   */
  private async executeWithRetry<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error = new Error('Unknown error');
    let delay = this.retryDelay;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        // Check if error is retryable
        if (!this.isRetryable(error as Error)) {
          throw error;
        }

        // If this was the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw error;
        }

        this.log(
          'debug',
          `Retry attempt ${attempt + 1}/${this.maxRetries} after error: ${lastError.message}`
        );

        // Wait before retrying
        await this.sleep(delay);

        // Increase delay for next attempt
        delay *= this.retryBackoffMultiplier;
      }
    }

    throw lastError;
  }

  /**
   * Check if an error is retryable
   * @param error Error to check
   * @returns Whether the error is retryable
   */
  private isRetryable(error: Error): boolean {
    // If no specific errors are defined, all errors are retryable
    if (this.retryableErrors.length === 0) {
      return true;
    }

    // Check if error name or message matches any retryable error
    return this.retryableErrors.some(
      (retryableError) => error.name === retryableError || error.message.includes(retryableError)
    );
  }

  /**
   * Sleep for a specified duration
   * @param ms Duration in milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Log a message
   * @param level Log level
   * @param message Message
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string): void {
    if (this.logger) {
      this.logger[level](message);
    }
  }
}
