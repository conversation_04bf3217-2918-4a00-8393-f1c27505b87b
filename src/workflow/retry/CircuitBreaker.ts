import { injectable, inject, optional } from 'inversify';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * Circuit breaker states
 */
export enum CircuitBreakerState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

/**
 * Options for circuit breaker
 */
export interface CircuitBreakerOptions {
  /**
   * Failure threshold to trip the circuit
   */
  failureThreshold?: number;

  /**
   * Success threshold to reset the circuit
   */
  successThreshold?: number;

  /**
   * Timeout in milliseconds before trying to half-open the circuit
   */
  resetTimeout?: number;

  /**
   * Timeout in milliseconds for monitoring window
   */
  monitorTimeout?: number;

  /**
   * Maximum number of concurrent requests in half-open state
   */
  maxHalfOpenConcurrency?: number;
}

/**
 * Circuit breaker error
 */
export class CircuitBreakerError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CircuitBreakerError';
  }
}

/**
 * Circuit breaker for handling service failures
 */
@injectable()
export class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private lastFailureTime: number = 0;
  private halfOpenConcurrency: number = 0;
  private monitorWindow: Map<number, boolean> = new Map();

  private failureThreshold: number;
  private successThreshold: number;
  private resetTimeout: number;
  private monitorTimeout: number;
  private maxHalfOpenConcurrency: number;

  /**
   * Constructor
   * @param options Circuit breaker options
   * @param logger Logger
   */
  constructor(
    options: CircuitBreakerOptions = {},
    @inject(TYPES.Logger) @optional() private logger?: ILogger
  ) {
    this.failureThreshold = options.failureThreshold || 5;
    this.successThreshold = options.successThreshold || 3;
    this.resetTimeout = options.resetTimeout || 30000; // 30 seconds
    this.monitorTimeout = options.monitorTimeout || 60000; // 60 seconds
    this.maxHalfOpenConcurrency = options.maxHalfOpenConcurrency || 1;
  }

  /**
   * Execute a function with circuit breaker protection
   * @param fn Function to execute
   * @returns Function result
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    // Check if circuit is open
    if (this.state === CircuitBreakerState.OPEN) {
      // Check if reset timeout has elapsed
      if (Date.now() - this.lastFailureTime >= this.resetTimeout) {
        this.halfOpen();
      } else {
        this.log('debug', `Circuit is OPEN, fast failing`);
        throw new CircuitBreakerError('Circuit is open');
      }
    }

    // Check if circuit is half-open and too many concurrent requests
    if (
      this.state === CircuitBreakerState.HALF_OPEN &&
      this.halfOpenConcurrency >= this.maxHalfOpenConcurrency
    ) {
      this.log('debug', `Circuit is HALF_OPEN with max concurrency, fast failing`);
      throw new CircuitBreakerError('Circuit is half-open with max concurrency');
    }

    // Increment half-open concurrency
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.halfOpenConcurrency++;
    }

    try {
      // Execute the function
      const result = await fn();

      // Record success
      this.recordSuccess();

      // Return the result
      return result;
    } catch (error) {
      // Record failure
      this.recordFailure();

      // Rethrow the error
      throw error;
    } finally {
      // Decrement half-open concurrency
      if (this.state === CircuitBreakerState.HALF_OPEN) {
        this.halfOpenConcurrency--;
      }
    }
  }

  /**
   * Record a success
   */
  private recordSuccess(): void {
    const now = Date.now();

    // Clean up old entries in monitor window
    this.cleanupMonitorWindow(now);

    // Add success to monitor window
    this.monitorWindow.set(now, true);

    // If circuit is half-open, increment success count
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.successCount++;

      // If success threshold is reached, close the circuit
      if (this.successCount >= this.successThreshold) {
        this.close();
      }
    }
  }

  /**
   * Record a failure
   */
  private recordFailure(): void {
    const now = Date.now();
    this.lastFailureTime = now;

    // Clean up old entries in monitor window
    this.cleanupMonitorWindow(now);

    // Add failure to monitor window
    this.monitorWindow.set(now, false);

    // If circuit is closed, check failure threshold
    if (this.state === CircuitBreakerState.CLOSED) {
      // Count failures in monitor window
      this.failureCount = Array.from(this.monitorWindow.values()).filter(
        (success) => !success
      ).length;

      // If failure threshold is reached, open the circuit
      if (this.failureCount >= this.failureThreshold) {
        this.open();
      }
    } else if (this.state === CircuitBreakerState.HALF_OPEN) {
      // If circuit is half-open, any failure opens the circuit
      this.open();
    }
  }

  /**
   * Clean up old entries in monitor window
   * @param now Current time
   */
  private cleanupMonitorWindow(now: number): void {
    const cutoff = now - this.monitorTimeout;

    // Remove entries older than monitor timeout
    for (const [timestamp] of this.monitorWindow) {
      if (timestamp < cutoff) {
        this.monitorWindow.delete(timestamp);
      }
    }
  }

  /**
   * Open the circuit
   */
  private open(): void {
    if (this.state !== CircuitBreakerState.OPEN) {
      this.log('info', `Circuit state changed from ${this.state} to OPEN`);
      this.state = CircuitBreakerState.OPEN;
      this.failureCount = 0;
      this.successCount = 0;
      this.halfOpenConcurrency = 0;
    }
  }

  /**
   * Half-open the circuit
   */
  private halfOpen(): void {
    if (this.state !== CircuitBreakerState.HALF_OPEN) {
      this.log('info', `Circuit state changed from ${this.state} to HALF_OPEN`);
      this.state = CircuitBreakerState.HALF_OPEN;
      this.failureCount = 0;
      this.successCount = 0;
      this.halfOpenConcurrency = 0;
    }
  }

  /**
   * Close the circuit
   */
  private close(): void {
    if (this.state !== CircuitBreakerState.CLOSED) {
      this.log('info', `Circuit state changed from ${this.state} to CLOSED`);
      this.state = CircuitBreakerState.CLOSED;
      this.failureCount = 0;
      this.successCount = 0;
      this.halfOpenConcurrency = 0;
      this.monitorWindow.clear();
    }
  }

  /**
   * Reset the circuit
   */
  public reset(): void {
    this.log('info', `Circuit manually reset from ${this.state} to CLOSED`);
    this.close();
  }

  /**
   * Trip the circuit
   */
  public trip(): void {
    this.log('info', `Circuit manually tripped from ${this.state} to OPEN`);
    this.open();
  }

  /**
   * Get the current state of the circuit
   * @returns Circuit state
   */
  public getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * Log a message
   * @param level Log level
   * @param message Message
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string): void {
    if (this.logger) {
      this.logger[level](message);
    }
  }
}
