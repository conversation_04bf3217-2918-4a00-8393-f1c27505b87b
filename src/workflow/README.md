# Workflow Engine

This directory contains the implementation of the workflow engine for the Dynamic MCP Server. The workflow engine allows for the execution of workflows defined in the database, with each workflow consisting of a series of nodes connected by edges.

## Components

### Engine

- **WorkflowEngine**: The main component responsible for executing workflows. It loads workflow definitions from the database, executes nodes in sequence, and tracks execution metrics.
- **WorkflowMemory**: Manages the execution context for workflows. For Phase 0, this is an in-memory implementation.

### Nodes

- **NodeRegistry**: Manages the available node types and their implementations.
- **BaseNode**: Base class for all node types.
- **JavaScriptNode**: Executes JavaScript code in a sandboxed environment.
- **HTTPNode**: Makes HTTP requests to external services.

### Tools

- **WorkflowBasedTool**: MCP tool implementation that executes a workflow.

## Usage

### Creating a Workflow

Workflows are defined in the database with the following structure:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "echo",
  "description": "Echo the input with a timestamp",
  "version": 1,
  "input_schema": {
    "type": "object",
    "properties": {
      "message": {
        "type": "string",
        "description": "Message to echo"
      }
    },
    "required": ["message"]
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "message": {
        "type": "string"
      },
      "timestamp": {
        "type": "string",
        "format": "date-time"
      }
    }
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "input",
        "type": "javascript",
        "name": "Process Input",
        "config": {
          "script": "return args;"
        }
      },
      {
        "id": "addTimestamp",
        "type": "javascript",
        "name": "Add Timestamp",
        "config": {
          "script": "return { message: args.message, timestamp: new Date().toISOString() };"
        }
      }
    ],
    "edges": [
      {
        "source": "input",
        "target": "addTimestamp"
      }
    ]
  },
  "enabled": true
}
```

### Creating a Workflow-Based Function

To create a function that executes a workflow, define a function with the following handler configuration:

```json
{
  "type": "workflow",
  "workflow_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### Available Node Types

#### JavaScript Node

Executes JavaScript code in a sandboxed environment.

Configuration:

```json
{
  "type": "javascript",
  "config": {
    "script": "// JavaScript code here"
  }
}
```

The script has access to:

- `input`: The input data (recommended)
- `args`: The input data (for backward compatibility)
- `logger`: The logger instance
- `workflowContext`: The workflow context (variables and node results)

#### HTTP Node

Makes HTTP requests to external services.

Configuration:

```json
{
  "type": "http",
  "config": {
    "url": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Content-Type": "application/json"
    },
    "params": {
      "key": "value"
    },
    "body": {
      "key": "value"
    },
    "timeout": 30000,
    "failOnError": true,
    "returnFullResponse": false
  }
}
```

### Limitations in Phase 0

- Only sequential workflow execution (no branching or parallel execution)
- Limited node types (only JavaScript and HTTP)
- In-memory workflow context (no Redis integration yet)
- No admin API for managing workflows (manual database insertion for testing)
- No advanced error handling or retries
- No performance optimizations

## Future Enhancements

In subsequent phases, the following enhancements will be added:

- Redis integration for workflow memory
- Conditional branching and parallel execution
- Additional node types (SQL, Redis, etc.)
- Error handling and retries
- Admin API for workflow management
- Performance optimizations
