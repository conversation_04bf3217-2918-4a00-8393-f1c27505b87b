import { injectable, inject } from 'inversify';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { IDatabase } from '../../core/interfaces/IDatabase';
import type { IWorkflowEngine } from '../../core/interfaces/IWorkflowEngine';
import type { IDynamicServiceLoader } from '../../core/interfaces/IDynamicServiceLoader';
import { TYPES } from '../../types';

/**
 * Configuration change notification payload
 */
interface ConfigChangeNotification {
  entity_type: string;
  id: string;
  name: string;
  action: 'INSERT' | 'UPDATE' | 'DELETE';
}

/**
 * Workflow configuration watcher
 * Watches for changes in workflow configurations and reloads them
 */
@injectable()
export class WorkflowConfigWatcher {
  private isWatching: boolean = false;
  private readonly NOTIFICATION_CHANNEL = 'mcp_config_updates';

  /**
   * Constructor
   * @param logger Logger
   * @param database Database
   * @param workflowEngine Workflow engine
   * @param serviceLoader Dynamic service loader
   */
  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.WorkflowEngine) private workflowEngine: IWorkflowEngine,
    @inject(TYPES.ServiceLoader) private serviceLoader: IDynamicServiceLoader
  ) {}

  /**
   * Start watching for configuration changes
   */
  public async startWatching(): Promise<void> {
    if (this.isWatching) {
      this.logger.warn('Config watcher is already running');
      return;
    }

    try {
      // Register notification callback
      this.database.onNotification(this.handleNotification.bind(this));

      // Start listening for notifications
      await this.database.listen(this.NOTIFICATION_CHANNEL);

      this.isWatching = true;
      this.logger.info('Started watching for configuration changes');
    } catch (error) {
      this.logger.error('Failed to start config watcher', error);
      throw error;
    }
  }

  /**
   * Stop watching for configuration changes
   */
  public async stopWatching(): Promise<void> {
    if (!this.isWatching) {
      this.logger.warn('Config watcher is not running');
      return;
    }

    try {
      // Stop listening for notifications
      await this.database.unlisten(this.NOTIFICATION_CHANNEL);

      this.isWatching = false;
      this.logger.info('Stopped watching for configuration changes');
    } catch (error) {
      this.logger.error('Failed to stop config watcher', error);
      throw error;
    }
  }

  /**
   * Handle notification
   * @param channel Notification channel
   * @param payload Notification payload
   */
  private handleNotification(channel: string, payload: string): void {
    if (channel !== this.NOTIFICATION_CHANNEL) {
      return;
    }

    try {
      const notification = JSON.parse(payload) as ConfigChangeNotification;
      this.logger.info('Received configuration change notification', notification);

      // Handle different entity types
      switch (notification.entity_type) {
        case 'mcp_workflows':
          this.handleWorkflowChange(notification);
          break;
        case 'mcp_node_configs':
          this.handleNodeConfigChange(notification);
          break;
        case 'mcp_data_sources':
          this.handleDataSourceChange(notification);
          break;
        default:
          this.logger.warn(`Unknown entity type: ${notification.entity_type}`);
      }
    } catch (error) {
      this.logger.error('Error handling notification', error);
    }
  }

  /**
   * Handle workflow change
   * @param notification Notification payload
   */
  private async handleWorkflowChange(notification: ConfigChangeNotification): Promise<void> {
    try {
      switch (notification.action) {
        case 'INSERT':
        case 'UPDATE':
          // Reload workflow
          await this.workflowEngine.reloadWorkflow(notification.id);
          this.logger.info(`Reloaded workflow: ${notification.name} (${notification.id})`);
          break;
        case 'DELETE':
          // Remove workflow
          await this.workflowEngine.removeWorkflow(notification.id);
          this.logger.info(`Removed workflow: ${notification.name} (${notification.id})`);
          break;
      }
    } catch (error) {
      this.logger.error(`Error handling workflow change for ${notification.id}`, error);
    }
  }

  /**
   * Handle node configuration change
   * @param notification Notification payload
   */
  private async handleNodeConfigChange(notification: ConfigChangeNotification): Promise<void> {
    try {
      // For node config changes, we need to reload all workflows that use this node config
      // This would typically be implemented in the workflow engine
      this.logger.info(`Node config changed: ${notification.name} (${notification.id})`);

      // Reload all workflows to pick up the node config change
      await this.workflowEngine.reloadAllWorkflows();
    } catch (error) {
      this.logger.error(`Error handling node config change for ${notification.id}`, error);
    }
  }

  /**
   * Handle data source change
   * @param notification Notification payload
   */
  private async handleDataSourceChange(notification: ConfigChangeNotification): Promise<void> {
    try {
      // For data source changes, we need to reload all workflows that use this data source
      // This would typically be implemented in the workflow engine
      this.logger.info(`Data source changed: ${notification.name} (${notification.id})`);

      // Reload all workflows to pick up the data source change
      await this.workflowEngine.reloadAllWorkflows();
    } catch (error) {
      this.logger.error(`Error handling data source change for ${notification.id}`, error);
    }
  }
}
