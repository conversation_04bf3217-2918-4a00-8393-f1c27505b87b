import { inject, injectable } from 'inversify';
import { IMCPTool } from '../../core/interfaces/IMCPTool';
import type { IWorkflowEngine } from '../../core/interfaces/IWorkflowEngine';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * MCP tool implementation that executes a workflow
 */
@injectable()
export class WorkflowBasedTool implements IMCPTool {
  /**
   * Get tool ID
   */
  get id(): string {
    return this.config.id;
  }

  /**
   * Constructor
   * @param config Tool configuration
   * @param workflowEngine Workflow engine
   * @param logger Logger
   */
  constructor(
    private config: {
      id: string;
      name: string;
      description: string;
      input_schema: any;
      workflow_id: string;
      enabled: boolean;
    },
    @inject(TYPES.WorkflowEngine) private workflowEngine: IWorkflowEngine,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Get tool name
   */
  get name(): string {
    return this.config.name;
  }

  /**
   * Get tool description
   */
  get description(): string {
    return this.config.description;
  }

  /**
   * Get tool input schema
   */
  get inputSchema(): any {
    return this.config.input_schema;
  }

  /**
   * Check if tool is enabled
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * Execute the tool
   * @param params Tool parameters
   * @returns Tool execution result
   */
  async execute(params: any): Promise<any> {
    this.logger.info(
      `Executing workflow-based tool ${this.config.name} with workflow ID ${this.config.workflow_id}`
    );

    try {
      // Execute the workflow
      const result = await this.workflowEngine.executeWorkflow(this.config.workflow_id, params);

      this.logger.info(`Workflow-based tool ${this.config.name} execution completed`);
      return result;
    } catch (error) {
      this.logger.error(
        `Workflow-based tool ${this.config.name} execution failed: ${(error as Error).message}`
      );
      throw error;
    }
  }
}

/**
 * Factory function for creating workflow-based tools
 * @param container Inversify container
 * @returns Tool factory function
 */
export const createWorkflowBasedToolFactory = (container: any) => {
  return (config: {
    id: string;
    name: string;
    description: string;
    input_schema: any;
    workflow_id: string;
    enabled: boolean;
  }) => {
    return new WorkflowBasedTool(
      config,
      container.get(TYPES.WorkflowEngine),
      container.get(TYPES.Logger)
    );
  };
};
