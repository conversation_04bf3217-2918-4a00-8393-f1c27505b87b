import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { Counter, Histogram, Gauge, register, collectDefaultMetrics } from 'prom-client';
import {
  IObservabilityManager,
  WorkflowMetrics,
  NodeMetrics,
  TimeRange,
  WorkflowMetricsSummary,
  NodeMetricsSummary
} from '../../core/interfaces/IObservabilityManager';
import type { ILogger } from '../../core/interfaces/ILogger';
import type { IDatabase } from '../../core/interfaces/IDatabase';
import { TYPES } from '../../types';
import { getConfig } from '../../config';
import os from 'os';

/**
 * Implementation of the observability manager
 * Provides methods for recording and retrieving metrics about workflow and node executions
 */
@injectable()
export class ObservabilityManager implements IObservabilityManager {
  // Prometheus metrics
  private workflowExecutionCounter: Counter;
  private workflowExecutionDuration: Histogram;
  private nodeExecutionCounter: Counter;
  private nodeExecutionDuration: Histogram;
  private activeWorkflows: Gauge;

  // Cleanup timer
  private cleanupTimer: NodeJS.Timeout | null = null;

  /**
   * Constructor
   * @param database Database service
   * @param logger Logger service
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Initialize Prometheus metrics
    this.initializeMetrics();

    // Start data cleanup job
    this.startDataCleanupJob();
  }

  /**
   * Start the data cleanup job
   * Runs periodically to clean up old data (metrics, workflow executions, node executions)
   */
  private startDataCleanupJob(): void {
    const config = getConfig();
    const intervalMs = config.data.cleanupIntervalMinutes * 60 * 1000;

    this.logger.info(
      `Starting data cleanup job with interval of ${config.data.cleanupIntervalMinutes} minutes`
    );

    // Clear any existing timer
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    // Flag to track if cleanup is currently running
    let isCleanupRunning = false;

    // Set up new timer
    this.cleanupTimer = setInterval(async () => {
      // Skip if cleanup is already running
      if (isCleanupRunning) {
        this.logger.info('Skipping scheduled cleanup as previous cleanup is still running');
        return;
      }

      isCleanupRunning = true;
      try {
        await this.cleanupOldData();
      } catch (error) {
        this.logger.error(`Error cleaning up old data: ${(error as Error).message}`);
      } finally {
        isCleanupRunning = false;
      }
    }, intervalMs);
  }

  /**
   * Clean up old data
   * Deletes metrics, workflow executions, and node executions older than the retention period
   * Uses chunked delete for better performance and to avoid database locks
   */
  public async cleanupOldData(): Promise<void> {
    const config = getConfig();
    const retentionDays = config.data.retentionDays;
    const batchSize = config.data.cleanupBatchSize;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    this.logger.info(
      `Cleaning up data older than ${cutoffDate.toISOString()} (${retentionDays} days retention)`
    );

    try {
      // Delete old workflow metrics in chunks
      await this.deleteOldDataInChunks(
        'workflow_metrics',
        'start_time',
        cutoffDate,
        batchSize,
        'workflow metrics'
      );

      // Delete old node metrics in chunks
      await this.deleteOldDataInChunks(
        'node_metrics',
        'start_time',
        cutoffDate,
        batchSize,
        'node metrics'
      );

      // Delete old workflow node executions in chunks
      await this.deleteOldDataInChunks(
        'mcp_workflow_node_executions',
        'started_at',
        cutoffDate,
        batchSize,
        'workflow node executions'
      );

      // Delete old workflow executions in chunks
      await this.deleteOldDataInChunks(
        'mcp_workflow_executions',
        'started_at',
        cutoffDate,
        batchSize,
        'workflow executions'
      );
    } catch (error) {
      this.logger.error(`Failed to clean up old data: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Delete old data from a table in chunks
   * @param tableName Table name
   * @param dateColumn Date column to filter on
   * @param cutoffDate Cutoff date
   * @param batchSize Batch size
   * @param logName Name to use in logs
   */
  private async deleteOldDataInChunks(
    tableName: string,
    dateColumn: string,
    cutoffDate: Date,
    batchSize: number,
    logName: string
  ): Promise<void> {
    let totalDeleted = 0;
    let deletedInBatch = 0;

    do {
      // Start a transaction for each batch
      await this.database.query('BEGIN');

      try {
        // PostgreSQL doesn't support LIMIT in DELETE statements directly
        // Use a subquery with LIMIT to get the IDs to delete
        const deleteQuery = `
          DELETE FROM ${tableName}
          WHERE id IN (
            SELECT id FROM ${tableName}
            WHERE ${dateColumn} < $1
            ORDER BY ${dateColumn} ASC
            LIMIT $2
          )
        `;

        const result = await this.database.query(deleteQuery, [cutoffDate, batchSize]);
        deletedInBatch = result?.rowCount || 0;
        totalDeleted += deletedInBatch;

        // Commit the transaction
        await this.database.query('COMMIT');

        if (deletedInBatch > 0) {
          this.logger.debug(
            `Deleted batch of ${deletedInBatch} ${logName} (total: ${totalDeleted})`
          );
        }

        // Small pause between batches to reduce database load
        if (deletedInBatch === batchSize) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      } catch (error) {
        // Rollback the transaction in case of error
        await this.database.query('ROLLBACK');
        this.logger.error(`Error deleting ${logName}: ${(error as Error).message}`);
        throw error;
      }
    } while (deletedInBatch === batchSize);

    if (totalDeleted > 0) {
      this.logger.info(`Deleted ${totalDeleted} ${logName}`);
    }
  }

  /**
   * Record workflow execution metrics
   * @param workflowId Workflow ID
   * @param executionId Execution ID
   * @param metrics Workflow metrics
   */
  async recordWorkflowExecution(
    workflowId: string,
    executionId: string,
    metrics: WorkflowMetrics
  ): Promise<void> {
    try {
      // Record in database
      await this.database.getRepository('workflow_metrics').save({
        id: uuidv4(),
        workflow_id: workflowId,
        execution_id: executionId,
        start_time: metrics.startTime,
        end_time: metrics.endTime,
        duration_ms: metrics.durationMs,
        status: metrics.status,
        error: metrics.error
      });

      // Update Prometheus metrics
      this.workflowExecutionCounter.inc({ workflow_id: workflowId, status: metrics.status });

      if (metrics.durationMs) {
        this.workflowExecutionDuration.observe({ workflow_id: workflowId }, metrics.durationMs);
      }

      if (metrics.status === 'RUNNING') {
        this.activeWorkflows.inc({ workflow_id: workflowId });
      } else {
        this.activeWorkflows.dec({ workflow_id: workflowId });
      }
    } catch (error) {
      this.logger.error(`Failed to record workflow execution metrics: ${(error as Error).message}`);
    }
  }

  /**
   * Record node execution metrics
   * @param nodeId Node ID
   * @param executionId Execution ID
   * @param metrics Node metrics
   */
  async recordNodeExecution(
    nodeId: string,
    executionId: string,
    metrics: NodeMetrics
  ): Promise<void> {
    try {
      // Record in database
      await this.database.getRepository('node_metrics').save({
        id: uuidv4(),
        node_id: nodeId,
        execution_id: executionId,
        node_type: metrics.nodeType,
        start_time: metrics.startTime,
        end_time: metrics.endTime,
        duration_ms: metrics.durationMs,
        status: metrics.status,
        error: metrics.error
      });

      // Update Prometheus metrics
      this.nodeExecutionCounter.inc({
        node_id: nodeId,
        node_type: metrics.nodeType,
        status: metrics.status
      });

      if (metrics.durationMs) {
        this.nodeExecutionDuration.observe(
          {
            node_id: nodeId,
            node_type: metrics.nodeType
          },
          metrics.durationMs
        );
      }
    } catch (error) {
      this.logger.error(`Failed to record node execution metrics: ${(error as Error).message}`);
    }
  }

  /**
   * Get workflow metrics
   * @param workflowId Workflow ID (optional)
   * @param timeRange Time range (optional)
   * @returns Workflow metrics summary
   */
  async getWorkflowMetrics(
    workflowId?: string,
    timeRange?: TimeRange
  ): Promise<WorkflowMetricsSummary[]> {
    try {
      // Build query
      let query = `
        SELECT
          workflow_id,
          COUNT(*) as total_executions,
          COUNT(*) FILTER (WHERE status = 'COMPLETED') as successful_executions,
          COUNT(*) FILTER (WHERE status = 'FAILED') as failed_executions,
          AVG(duration_ms) FILTER (WHERE duration_ms IS NOT NULL) as avg_duration_ms,
          MIN(duration_ms) FILTER (WHERE duration_ms IS NOT NULL) as min_duration_ms,
          MAX(duration_ms) FILTER (WHERE duration_ms IS NOT NULL) as max_duration_ms
        FROM workflow_metrics
        WHERE 1=1
      `;

      const params: any[] = [];

      if (workflowId) {
        query += ` AND workflow_id = $${params.length + 1}`;
        params.push(workflowId);
      }

      if (timeRange) {
        query += ` AND start_time >= $${params.length + 1} AND start_time <= $${params.length + 2}`;
        params.push(timeRange.startTime);
        params.push(timeRange.endTime);
      }

      query += ` GROUP BY workflow_id`;

      // Execute query
      const results = await this.database.query(query, params);

      // Transform results
      return results.map((row: any) => {
        return {
          workflowId: row.workflow_id,
          totalExecutions: parseInt(row.total_executions),
          successfulExecutions: parseInt(row.successful_executions),
          failedExecutions: parseInt(row.failed_executions),
          averageDurationMs: parseFloat(row.avg_duration_ms) || 0,
          minDurationMs: parseFloat(row.min_duration_ms) || 0,
          maxDurationMs: parseFloat(row.max_duration_ms) || 0,
          p95DurationMs: 0, // Would require more complex query
          p99DurationMs: 0, // Would require more complex query
          errorRate: parseInt(row.failed_executions) / parseInt(row.total_executions),
          metrics: [] // Detailed metrics would be fetched separately if needed
        } as WorkflowMetricsSummary;
      });
    } catch (error) {
      this.logger.error(`Failed to get workflow metrics: ${(error as Error).message}`);
      return [];
    }
  }

  /**
   * Get node metrics
   * @param nodeType Node type (optional)
   * @param timeRange Time range (optional)
   * @returns Node metrics summary
   */
  async getNodeMetrics(nodeType?: string, timeRange?: TimeRange): Promise<NodeMetricsSummary[]> {
    try {
      // Build query
      let query = `
        SELECT
          node_type,
          COUNT(*) as total_executions,
          COUNT(*) FILTER (WHERE status = 'COMPLETED') as successful_executions,
          COUNT(*) FILTER (WHERE status = 'FAILED') as failed_executions,
          AVG(duration_ms) FILTER (WHERE duration_ms IS NOT NULL) as avg_duration_ms,
          MIN(duration_ms) FILTER (WHERE duration_ms IS NOT NULL) as min_duration_ms,
          MAX(duration_ms) FILTER (WHERE duration_ms IS NOT NULL) as max_duration_ms
        FROM node_metrics
        WHERE 1=1
      `;

      const params: any[] = [];

      if (nodeType) {
        query += ` AND node_type = $${params.length + 1}`;
        params.push(nodeType);
      }

      if (timeRange) {
        query += ` AND start_time >= $${params.length + 1} AND start_time <= $${params.length + 2}`;
        params.push(timeRange.startTime);
        params.push(timeRange.endTime);
      }

      query += ` GROUP BY node_type`;

      // Execute query
      const results = await this.database.query(query, params);

      // Transform results
      return results.map((row: any) => {
        return {
          nodeType: row.node_type,
          totalExecutions: parseInt(row.total_executions),
          successfulExecutions: parseInt(row.successful_executions),
          failedExecutions: parseInt(row.failed_executions),
          averageDurationMs: parseFloat(row.avg_duration_ms) || 0,
          minDurationMs: parseFloat(row.min_duration_ms) || 0,
          maxDurationMs: parseFloat(row.max_duration_ms) || 0,
          p95DurationMs: 0, // Would require more complex query
          p99DurationMs: 0, // Would require more complex query
          errorRate: parseInt(row.failed_executions) / parseInt(row.total_executions),
          metrics: [] // Detailed metrics would be fetched separately if needed
        } as NodeMetricsSummary;
      });
    } catch (error) {
      this.logger.error(`Failed to get node metrics: ${(error as Error).message}`);
      return [];
    }
  }

  /**
   * Export Prometheus metrics
   * @returns Prometheus metrics in string format
   */
  async exportPrometheusMetrics(): Promise<string> {
    try {
      // Reset counters to avoid duplicates
      this.workflowExecutionCounter.reset();
      this.nodeExecutionCounter.reset();
      this.activeWorkflows.reset();

      // Load workflow metrics from database
      const workflowMetricsQuery = `
        SELECT
          workflow_id,
          status,
          COUNT(*) as count
        FROM workflow_metrics
        GROUP BY workflow_id, status
      `;

      const workflowMetrics = await this.database.query(workflowMetricsQuery);

      // Update workflow execution counter
      for (const metric of workflowMetrics) {
        this.workflowExecutionCounter.inc(
          { workflow_id: metric.workflow_id, status: metric.status },
          parseInt(metric.count)
        );

        // Update active workflows gauge
        if (metric.status === 'RUNNING') {
          this.activeWorkflows.set({ workflow_id: metric.workflow_id }, parseInt(metric.count));
        }
      }

      // Load node metrics from database
      const nodeMetricsQuery = `
        SELECT
          node_id,
          node_type,
          status,
          COUNT(*) as count
        FROM node_metrics
        GROUP BY node_id, node_type, status
      `;

      const nodeMetrics = await this.database.query(nodeMetricsQuery);

      // Update node execution counter
      for (const metric of nodeMetrics) {
        this.nodeExecutionCounter.inc(
          {
            node_id: metric.node_id,
            node_type: metric.node_type,
            status: metric.status
          },
          parseInt(metric.count)
        );
      }

      // Load workflow duration metrics
      const workflowDurationQuery = `
        SELECT
          workflow_id,
          duration_ms
        FROM workflow_metrics
        WHERE duration_ms IS NOT NULL
      `;

      const workflowDurations = await this.database.query(workflowDurationQuery);

      // Update workflow duration histogram
      for (const metric of workflowDurations) {
        this.workflowExecutionDuration.observe(
          { workflow_id: metric.workflow_id },
          parseInt(metric.duration_ms)
        );
      }

      // Load node duration metrics
      const nodeDurationQuery = `
        SELECT
          node_id,
          node_type,
          duration_ms
        FROM node_metrics
        WHERE duration_ms IS NOT NULL
      `;

      const nodeDurations = await this.database.query(nodeDurationQuery);

      // Update node duration histogram
      for (const metric of nodeDurations) {
        this.nodeExecutionDuration.observe(
          { node_id: metric.node_id, node_type: metric.node_type },
          parseInt(metric.duration_ms)
        );
      }

      // Update system metrics
      const systemMemoryGauge = register.getSingleMetric('system_memory_usage') as Gauge;
      if (systemMemoryGauge) {
        systemMemoryGauge.set({ type: 'total' }, os.totalmem());
        systemMemoryGauge.set({ type: 'free' }, os.freemem());
        systemMemoryGauge.set({ type: 'used' }, os.totalmem() - os.freemem());
      }

      const systemLoadGauge = register.getSingleMetric('system_load_average') as Gauge;
      if (systemLoadGauge) {
        systemLoadGauge.set({ period: '1m' }, os.loadavg()[0]);
        systemLoadGauge.set({ period: '5m' }, os.loadavg()[1]);
        systemLoadGauge.set({ period: '15m' }, os.loadavg()[2]);
      }

      const systemUptimeGauge = register.getSingleMetric('system_uptime_seconds') as Gauge;
      if (systemUptimeGauge) {
        systemUptimeGauge.set(os.uptime());
      }

      const nodeJsHeapGauge = register.getSingleMetric('nodejs_heap_size_bytes') as Gauge;
      if (nodeJsHeapGauge) {
        nodeJsHeapGauge.set({ type: 'total' }, process.memoryUsage().heapTotal);
        nodeJsHeapGauge.set({ type: 'used' }, process.memoryUsage().heapUsed);
        nodeJsHeapGauge.set({ type: 'external' }, process.memoryUsage().external);
      }

      // Get metrics from registry
      const metrics = await register.metrics();
      return metrics;
    } catch (error) {
      this.logger.error(`Failed to export Prometheus metrics: ${(error as Error).message}`);
      return 'Error exporting metrics';
    }
  }

  /**
   * Initialize Prometheus metrics
   */
  private initializeMetrics(): void {
    // Clear any existing metrics
    register.clear();

    // Collect default Node.js metrics with prefix and additional system metrics
    collectDefaultMetrics({
      register,
      prefix: 'nodejs_',
      labels: { app: 'dynamic_mcp_server' },
      gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5] // Garbage collection duration buckets
    });

    // Add custom system metrics
    const systemMemoryGauge = new Gauge({
      name: 'system_memory_usage',
      help: 'System memory usage statistics in bytes',
      labelNames: ['type']
    });

    const systemCpuGauge = new Gauge({
      name: 'system_cpu_usage',
      help: 'System CPU usage statistics',
      labelNames: ['cpu']
    });

    const systemLoadGauge = new Gauge({
      name: 'system_load_average',
      help: 'System load average',
      labelNames: ['period']
    });

    const systemUptimeGauge = new Gauge({
      name: 'system_uptime_seconds',
      help: 'System uptime in seconds'
    });

    // Update system metrics every 15 seconds
    setInterval(() => {
      // Memory metrics
      systemMemoryGauge.set({ type: 'total' }, os.totalmem());
      systemMemoryGauge.set({ type: 'free' }, os.freemem());
      systemMemoryGauge.set({ type: 'used' }, os.totalmem() - os.freemem());

      // CPU metrics
      const cpus = os.cpus();
      cpus.forEach((cpu, index) => {
        const total = Object.values(cpu.times).reduce((acc, time) => acc + time, 0);
        const idle = cpu.times.idle;
        const usage = 1 - idle / total;
        systemCpuGauge.set({ cpu: `cpu${index}` }, usage);
      });

      // Load average metrics
      const loadAvg = os.loadavg();
      systemLoadGauge.set({ period: '1m' }, loadAvg[0]);
      systemLoadGauge.set({ period: '5m' }, loadAvg[1]);
      systemLoadGauge.set({ period: '15m' }, loadAvg[2]);

      // Uptime metric
      systemUptimeGauge.set(os.uptime());
    }, 15000);

    // Initialize workflow metrics
    this.workflowExecutionCounter = new Counter({
      name: 'workflow_executions_total',
      help: 'Total number of workflow executions',
      labelNames: ['workflow_id', 'status']
    });

    this.workflowExecutionDuration = new Histogram({
      name: 'workflow_execution_duration_milliseconds',
      help: 'Duration of workflow executions in milliseconds',
      labelNames: ['workflow_id'],
      buckets: [10, 50, 100, 500, 1000, 5000, 10000, 30000, 60000]
    });

    this.nodeExecutionCounter = new Counter({
      name: 'node_executions_total',
      help: 'Total number of node executions',
      labelNames: ['node_id', 'node_type', 'status']
    });

    this.nodeExecutionDuration = new Histogram({
      name: 'node_execution_duration_milliseconds',
      help: 'Duration of node executions in milliseconds',
      labelNames: ['node_id', 'node_type'],
      buckets: [1, 5, 10, 50, 100, 500, 1000, 5000, 10000]
    });

    this.activeWorkflows = new Gauge({
      name: 'active_workflows',
      help: 'Number of currently active workflows',
      labelNames: ['workflow_id']
    });

    // Initialize with default values
    this.activeWorkflows.set({ workflow_id: 'test' }, 0);

    // Log that metrics have been initialized
    this.logger.info('Prometheus metrics initialized with default Node.js and system metrics');
  }
}
