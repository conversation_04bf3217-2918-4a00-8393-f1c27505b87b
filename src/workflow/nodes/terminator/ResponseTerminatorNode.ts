import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for Response Terminator Node
 */
export interface IResponseTerminatorConfig {
  statusCode: number;
  headers: Record<string, string>;
  body: string | Record<string, any>;
  contentType?: string;
  encoding?: string;
  compress?: boolean;
  cache?: {
    maxAge?: number;
    private?: boolean;
    noCache?: boolean;
  };
  // Backward compatibility - deprecated
  responseTemplate?: string | Record<string, any>;
}

/**
 * Response Terminator Node - returns HTTP response for REST API workflows
 */
@injectable()
export class ResponseTerminatorNode extends BaseNode {
  protected config: IResponseTerminatorConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IResponseTerminatorConfig, logger: ILogger) {
    super(config, logger);

    // Handle backward compatibility: if responseTemplate exists, migrate it to body
    if ((config as any).responseTemplate && !config.body) {
      this.logger.warn(
        'ResponseTerminatorNode: responseTemplate is deprecated, use body instead. Migrating automatically.'
      );
      config.body = (config as any).responseTemplate;
      delete (config as any).responseTemplate;
    }

    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (typeof this.config.statusCode !== 'number') {
      throw new Error('Response Terminator: statusCode must be a number');
    }

    if (this.config.statusCode < 100 || this.config.statusCode > 599) {
      throw new Error('Response Terminator: statusCode must be between 100 and 599');
    }

    if (this.config.headers && typeof this.config.headers !== 'object') {
      throw new Error('Response Terminator: headers must be an object');
    }

    if (this.config.body === undefined && !(this.config as any).responseTemplate) {
      throw new Error('Response Terminator: body is required');
    }

    // Validate cache configuration
    if (this.config.cache) {
      if (
        this.config.cache.maxAge &&
        (typeof this.config.cache.maxAge !== 'number' || this.config.cache.maxAge < 0)
      ) {
        throw new Error('Response Terminator: cache.maxAge must be a non-negative number');
      }
    }
  }

  /**
   * Execute the terminator node
   * @param input Input data
   * @param context Workflow context
   * @returns HTTP response data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Response Terminator node with input: ${JSON.stringify(input)}`);

    try {
      // Process response body with variable substitution
      const processedBody = this.processBody(this.config.body, input, context);

      // Process headers with variable substitution
      const processedHeaders = this.processHeaders(this.config.headers, input, context);

      // Add default headers
      const finalHeaders = this.addDefaultHeaders(processedHeaders, processedBody);

      // Add cache headers if configured
      if (this.config.cache) {
        this.addCacheHeaders(finalHeaders, this.config.cache);
      }

      // Prepare response data
      const response = {
        statusCode: this.config.statusCode,
        headers: finalHeaders,
        body: processedBody,
        metadata: {
          terminator: 'response',
          contentType: this.getContentType(processedBody),
          encoding: this.config.encoding || 'utf-8',
          compress: this.config.compress || false,
          timestamp: new Date().toISOString(),
          executionId: context.executionId,
          workflowId: context.workflowId
        }
      };

      this.logger.debug(
        `Response Terminator node execution completed with response: ${JSON.stringify(response)}`
      );
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Response Terminator node execution failed: ${errorMessage}`);
      throw new Error(`Response Terminator execution failed: ${errorMessage}`);
    }
  }

  /**
   * Process response body with variable substitution
   */
  private processBody(
    body: string | Record<string, any>,
    input: any,
    context: WorkflowContext
  ): any {
    if (typeof body === 'string') {
      return this.replaceVariables(body, input, context);
    }

    if (typeof body === 'object' && body !== null) {
      return this.processObjectWithVariables(body, input, context);
    }

    return body;
  }

  /**
   * Process headers with variable substitution
   */
  private processHeaders(
    headers: Record<string, string>,
    input: any,
    context: WorkflowContext
  ): Record<string, string> {
    const processedHeaders: Record<string, string> = {};

    for (const [key, value] of Object.entries(headers || {})) {
      processedHeaders[key] = this.replaceVariables(value, input, context);
    }

    return processedHeaders;
  }

  /**
   * Add default headers
   */
  private addDefaultHeaders(headers: Record<string, string>, body: any): Record<string, string> {
    const finalHeaders = { ...headers };

    // Set Content-Type if not already set
    if (!finalHeaders['Content-Type'] && !finalHeaders['content-type']) {
      finalHeaders['Content-Type'] = this.config.contentType || this.getContentType(body);
    }

    // Set Content-Length for string bodies
    if (
      typeof body === 'string' &&
      !finalHeaders['Content-Length'] &&
      !finalHeaders['content-length']
    ) {
      finalHeaders['Content-Length'] = Buffer.byteLength(
        body,
        (this.config.encoding as BufferEncoding) || 'utf-8'
      ).toString();
    }

    // Add server identification
    if (!finalHeaders['X-Powered-By'] && !finalHeaders['x-powered-by']) {
      finalHeaders['X-Powered-By'] = 'Dynamic MCP Server';
    }

    return finalHeaders;
  }

  /**
   * Add cache headers
   */
  private addCacheHeaders(
    headers: Record<string, string>,
    cache: NonNullable<IResponseTerminatorConfig['cache']>
  ): void {
    if (cache.noCache) {
      headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
      headers['Pragma'] = 'no-cache';
      headers['Expires'] = '0';
    } else if (cache.maxAge !== undefined) {
      const cacheControl = [`max-age=${cache.maxAge}`];

      if (cache.private) {
        cacheControl.push('private');
      } else {
        cacheControl.push('public');
      }

      headers['Cache-Control'] = cacheControl.join(', ');
    }
  }

  /**
   * Determine content type based on body
   */
  private getContentType(body: any): string {
    if (typeof body === 'string') {
      // Try to detect if it's JSON
      try {
        JSON.parse(body);
        return 'application/json';
      } catch {
        // Check if it looks like HTML
        if (body.trim().startsWith('<') && body.trim().endsWith('>')) {
          return 'text/html';
        }
        return 'text/plain';
      }
    }

    if (typeof body === 'object') {
      return 'application/json';
    }

    return 'text/plain';
  }

  /**
   * Replace variables in string
   */
  private replaceVariables(str: string, input: any, context: WorkflowContext): any {
    const variables = {
      ...context.variables,
      input,
      result: input, // Alias for input
      nodeResults: context.nodeResults,
      executionId: context.executionId,
      workflowId: context.workflowId
    };

    // Check if the entire string is a single variable reference
    const singleVarMatch = str.match(/^\${([^}]+)}$/);
    if (singleVarMatch) {
      const varName = singleVarMatch[1];
      const value = this.getNestedProperty(variables, varName);
      // Return the actual value (object, array, etc.) instead of stringifying it
      return value !== undefined ? value : str;
    }

    // For strings with multiple variables or mixed content, do string replacement
    return str.replace(/\${([^}]+)}/g, (match, varName) => {
      const value = this.getNestedProperty(variables, varName);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Process object recursively with variable substitution
   */
  private processObjectWithVariables(obj: any, input: any, context: WorkflowContext): any {
    if (typeof obj === 'string') {
      return this.replaceVariables(obj, input, context);
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.processObjectWithVariables(item, input, context));
    }

    if (typeof obj === 'object' && obj !== null) {
      const processedObj: any = {};
      for (const [key, value] of Object.entries(obj)) {
        processedObj[key] = this.processObjectWithVariables(value, input, context);
      }
      return processedObj;
    }

    return obj;
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get response statistics
   */
  public getResponseStats() {
    return {
      statusCode: this.config.statusCode,
      hasHeaders: Object.keys(this.config.headers || {}).length > 0,
      bodyType: typeof this.config.body,
      contentType: this.config.contentType,
      encoding: this.config.encoding || 'utf-8',
      compress: this.config.compress || false,
      hasCache: !!this.config.cache,
      cacheMaxAge: this.config.cache?.maxAge,
      cachePrivate: this.config.cache?.private,
      cacheNoCache: this.config.cache?.noCache
    };
  }

  /**
   * Check if response indicates success
   */
  public isSuccessResponse(): boolean {
    return this.config.statusCode >= 200 && this.config.statusCode < 300;
  }

  /**
   * Check if response indicates error
   */
  public isErrorResponse(): boolean {
    return this.config.statusCode >= 400;
  }
}
