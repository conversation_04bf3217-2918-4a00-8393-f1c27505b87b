import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for Error Terminator Node
 */
export interface IErrorTerminatorConfig {
  errorCode: string;
  message: string;
  statusCode: number;
  details?: Record<string, any>;
  logLevel?: 'error' | 'warn' | 'info' | 'debug';
  includeStackTrace?: boolean;
  notifyExternal?: boolean;
  retryable?: boolean;
}

/**
 * Error Terminator Node - handles and returns error responses
 */
@injectable()
export class ErrorTerminatorNode extends BaseNode {
  protected config: IErrorTerminatorConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IErrorTerminatorConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.errorCode) {
      throw new Error('Error Terminator: errorCode is required');
    }

    if (!this.config.message) {
      throw new Error('Error Terminator: message is required');
    }

    if (typeof this.config.statusCode !== 'number') {
      throw new Error('Error Terminator: statusCode must be a number');
    }

    if (this.config.statusCode < 400 || this.config.statusCode > 599) {
      throw new Error(
        'Error Terminator: statusCode must be between 400 and 599 for error responses'
      );
    }

    if (this.config.logLevel) {
      const validLevels = ['error', 'warn', 'info', 'debug'];
      if (!validLevels.includes(this.config.logLevel)) {
        throw new Error(
          `Error Terminator: invalid logLevel '${this.config.logLevel}'. Valid levels: ${validLevels.join(', ')}`
        );
      }
    }
  }

  /**
   * Execute the terminator node
   * @param input Input data (usually error information)
   * @param context Workflow context
   * @returns Error response data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Error Terminator node with input: ${JSON.stringify(input)}`);

    try {
      // Process error message with variable substitution
      const processedMessage = this.processMessage(this.config.message, input, context);

      // Process error details with variable substitution
      const processedDetails = this.processDetails(this.config.details, input, context);

      // Extract error information from input
      const errorInfo = this.extractErrorInfo(input);

      // Log error with appropriate level
      this.logError(processedMessage, errorInfo, this.config.logLevel || 'error');

      // Prepare error response
      const errorResponse = {
        error: {
          code: this.config.errorCode,
          message: processedMessage,
          statusCode: this.config.statusCode,
          details: processedDetails,
          timestamp: new Date().toISOString(),
          executionId: context.executionId,
          workflowId: context.workflowId,
          retryable: this.config.retryable || false
        },
        metadata: {
          terminator: 'error',
          logLevel: this.config.logLevel || 'error',
          includeStackTrace: this.config.includeStackTrace || false,
          notifyExternal: this.config.notifyExternal || false,
          originalInput: input,
          errorSource: errorInfo.source || 'workflow'
        }
      };

      // Add stack trace if configured and available
      if (this.config.includeStackTrace && errorInfo.stackTrace) {
        errorResponse.error.details = {
          ...errorResponse.error.details,
          stackTrace: errorInfo.stackTrace
        };
      }

      // Notify external systems if configured
      if (this.config.notifyExternal) {
        await this.notifyExternalSystems(errorResponse, context);
      }

      this.logger.debug(
        `Error Terminator node execution completed with response: ${JSON.stringify(errorResponse)}`
      );
      return errorResponse;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error Terminator node execution failed: ${errorMessage}`);

      // Return a fallback error response
      return this.createFallbackErrorResponse(errorMessage, context);
    }
  }

  /**
   * Process error message with variable substitution
   */
  private processMessage(message: string, input: any, context: WorkflowContext): string {
    return this.replaceVariables(message, input, context);
  }

  /**
   * Process error details with variable substitution
   */
  private processDetails(
    details: Record<string, any> | undefined,
    input: any,
    context: WorkflowContext
  ): Record<string, any> {
    if (!details) {
      return {};
    }

    return this.processObjectWithVariables(details, input, context);
  }

  /**
   * Extract error information from input
   */
  private extractErrorInfo(input: any): any {
    const errorInfo: any = {
      source: 'workflow',
      originalError: input
    };

    // Check if input is an Error object
    if (input instanceof Error) {
      errorInfo.name = input.name;
      errorInfo.message = input.message;
      errorInfo.stackTrace = input.stack;
      errorInfo.source = 'exception';
    }

    // Check if input has error-like properties
    if (input && typeof input === 'object') {
      if (input.error) {
        errorInfo.originalError = input.error;
        errorInfo.source = 'node-error';
      }

      if (input.message) {
        errorInfo.originalMessage = input.message;
      }

      if (input.code) {
        errorInfo.originalCode = input.code;
      }

      if (input.statusCode) {
        errorInfo.originalStatusCode = input.statusCode;
      }

      if (input.stack) {
        errorInfo.stackTrace = input.stack;
      }
    }

    return errorInfo;
  }

  /**
   * Log error with specified level
   */
  private logError(message: string, errorInfo: any, level: string): void {
    const logData = {
      message,
      errorCode: this.config.errorCode,
      statusCode: this.config.statusCode,
      errorInfo,
      timestamp: new Date().toISOString()
    };

    switch (level) {
      case 'error':
        this.logger.error(`Error Terminator: ${message}`, logData);
        break;
      case 'warn':
        this.logger.warn(`Error Terminator: ${message}`, logData);
        break;
      case 'info':
        this.logger.info(`Error Terminator: ${message}`, logData);
        break;
      case 'debug':
        this.logger.debug(`Error Terminator: ${message}`, logData);
        break;
    }
  }

  /**
   * Notify external systems about the error
   */
  private async notifyExternalSystems(errorResponse: any, context: WorkflowContext): Promise<void> {
    try {
      // This is a placeholder for external notification logic
      // In a real implementation, this could send notifications to:
      // - Error tracking services (Sentry, Rollbar, etc.)
      // - Monitoring systems (DataDog, New Relic, etc.)
      // - Alerting systems (PagerDuty, Slack, etc.)
      // - Logging aggregators (ELK, Splunk, etc.)

      this.logger.info('External error notification sent', {
        errorCode: errorResponse.error.code,
        executionId: context.executionId,
        workflowId: context.workflowId
      });
    } catch (notificationError) {
      const errorMessage =
        notificationError instanceof Error ? notificationError.message : 'Unknown error';
      this.logger.warn(`Failed to notify external systems: ${errorMessage}`);
    }
  }

  /**
   * Create fallback error response when Error Terminator itself fails
   */
  private createFallbackErrorResponse(errorMessage: string, context: WorkflowContext): any {
    return {
      error: {
        code: 'ERROR_TERMINATOR_FAILURE',
        message: 'Error Terminator node failed to process error',
        statusCode: 500,
        details: {
          originalError: errorMessage,
          fallback: true
        },
        timestamp: new Date().toISOString(),
        executionId: context.executionId,
        workflowId: context.workflowId,
        retryable: false
      },
      metadata: {
        terminator: 'error',
        fallback: true,
        logLevel: 'error'
      }
    };
  }

  /**
   * Replace variables in string
   */
  private replaceVariables(str: string, input: any, context: WorkflowContext): string {
    const variables = {
      ...context.variables,
      input,
      error: input, // Alias for input when it contains error data
      nodeResults: context.nodeResults,
      executionId: context.executionId,
      workflowId: context.workflowId
    };

    return str.replace(/\${([^}]+)}/g, (match, varName) => {
      const value = this.getNestedProperty(variables, varName);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Process object recursively with variable substitution
   */
  private processObjectWithVariables(obj: any, input: any, context: WorkflowContext): any {
    if (typeof obj === 'string') {
      return this.replaceVariables(obj, input, context);
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.processObjectWithVariables(item, input, context));
    }

    if (typeof obj === 'object' && obj !== null) {
      const processedObj: any = {};
      for (const [key, value] of Object.entries(obj)) {
        processedObj[key] = this.processObjectWithVariables(value, input, context);
      }
      return processedObj;
    }

    return obj;
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get error statistics
   */
  public getErrorStats() {
    return {
      errorCode: this.config.errorCode,
      statusCode: this.config.statusCode,
      logLevel: this.config.logLevel || 'error',
      includeStackTrace: this.config.includeStackTrace || false,
      notifyExternal: this.config.notifyExternal || false,
      retryable: this.config.retryable || false,
      hasDetails: Object.keys(this.config.details || {}).length > 0
    };
  }

  /**
   * Check if error is retryable
   */
  public isRetryable(): boolean {
    return this.config.retryable || false;
  }

  /**
   * Check if error is client error (4xx)
   */
  public isClientError(): boolean {
    return this.config.statusCode >= 400 && this.config.statusCode < 500;
  }

  /**
   * Check if error is server error (5xx)
   */
  public isServerError(): boolean {
    return this.config.statusCode >= 500;
  }
}
