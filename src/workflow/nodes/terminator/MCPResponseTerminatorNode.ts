import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for MCP Response Terminator Node
 */
export interface IMCPResponseTerminatorConfig {
  result: string | Record<string, any>;
  schema: Record<string, any>;
  metadata?: Record<string, any>;
  validateSchema?: boolean;
  errorOnValidationFailure?: boolean;
}

/**
 * MCP Response Terminator Node - returns response for MCP function workflows
 */
@injectable()
export class MCPResponseTerminatorNode extends BaseNode {
  protected config: IMCPResponseTerminatorConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IMCPResponseTerminatorConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (this.config.result === undefined) {
      throw new Error('MCP Response Terminator: result is required');
    }

    if (!this.config.schema || typeof this.config.schema !== 'object') {
      throw new Error('MCP Response Terminator: schema must be an object');
    }

    if (this.config.metadata && typeof this.config.metadata !== 'object') {
      throw new Error('MCP Response Terminator: metadata must be an object');
    }
  }

  /**
   * Execute the terminator node
   * @param input Input data
   * @param context Workflow context
   * @returns MCP response data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(
      `Executing MCP Response Terminator node with input: ${JSON.stringify(input)}`
    );

    try {
      // Process result with variable substitution
      const processedResult = this.processResult(this.config.result, input, context);

      // Process metadata with variable substitution
      const processedMetadata = this.processMetadata(this.config.metadata, input, context);

      // Validate result against schema if configured
      if (this.config.validateSchema !== false) {
        await this.validateResult(processedResult, this.config.schema);
      }

      // Prepare MCP response
      const mcpResponse = {
        content: [
          {
            type: 'text',
            text: this.formatResultAsText(processedResult)
          }
        ],
        isError: false,
        result: processedResult,
        schema: this.config.schema,
        metadata: {
          ...processedMetadata,
          terminator: 'mcp-response',
          timestamp: new Date().toISOString(),
          executionId: context.executionId,
          workflowId: context.workflowId,
          resultType: typeof processedResult,
          schemaValidated: this.config.validateSchema !== false
        }
      };

      this.logger.debug(
        `MCP Response Terminator node execution completed with response: ${JSON.stringify(mcpResponse)}`
      );
      return mcpResponse;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`MCP Response Terminator node execution failed: ${errorMessage}`);

      // Return error response if validation fails and errorOnValidationFailure is true
      if (this.config.errorOnValidationFailure !== false) {
        return this.createErrorResponse(errorMessage, context);
      }

      throw new Error(`MCP Response Terminator execution failed: ${errorMessage}`);
    }
  }

  /**
   * Process result with variable substitution
   */
  private processResult(
    result: string | Record<string, any>,
    input: any,
    context: WorkflowContext
  ): any {
    if (typeof result === 'string') {
      return this.replaceVariables(result, input, context);
    }

    if (typeof result === 'object' && result !== null) {
      return this.processObjectWithVariables(result, input, context);
    }

    return result;
  }

  /**
   * Process metadata with variable substitution
   */
  private processMetadata(
    metadata: Record<string, any> | undefined,
    input: any,
    context: WorkflowContext
  ): Record<string, any> {
    if (!metadata) {
      return {};
    }

    return this.processObjectWithVariables(metadata, input, context);
  }

  /**
   * Validate result against schema
   */
  private async validateResult(result: any, schema: Record<string, any>): Promise<void> {
    try {
      // Basic schema validation
      if (schema.type) {
        await this.validateType(result, schema.type);
      }

      if (schema.properties && typeof result === 'object' && result !== null) {
        await this.validateProperties(result, schema.properties);
      }

      if (schema.required && Array.isArray(schema.required)) {
        await this.validateRequired(result, schema.required);
      }

      if (schema.items && Array.isArray(result)) {
        await this.validateArrayItems(result, schema.items);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Schema validation failed: ${errorMessage}`);
    }
  }

  /**
   * Validate type
   */
  private async validateType(value: any, expectedType: string): Promise<void> {
    const actualType = Array.isArray(value) ? 'array' : typeof value;

    const typeMap: Record<string, string> = {
      string: 'string',
      number: 'number',
      integer: 'number',
      boolean: 'boolean',
      object: 'object',
      array: 'array',
      null: 'object'
    };

    if (expectedType === 'integer' && typeof value === 'number' && !Number.isInteger(value)) {
      throw new Error(`Expected integer, got decimal number`);
    }

    if (expectedType === 'null' && value !== null) {
      throw new Error(`Expected null, got ${actualType}`);
    }

    if (typeMap[expectedType] && typeMap[expectedType] !== actualType) {
      throw new Error(`Expected ${expectedType}, got ${actualType}`);
    }
  }

  /**
   * Validate object properties
   */
  private async validateProperties(
    obj: Record<string, any>,
    properties: Record<string, any>
  ): Promise<void> {
    for (const [propName, propSchema] of Object.entries(properties)) {
      if (propName in obj) {
        if (propSchema.type) {
          await this.validateType(obj[propName], propSchema.type);
        }
      }
    }
  }

  /**
   * Validate required properties
   */
  private async validateRequired(obj: any, required: string[]): Promise<void> {
    if (typeof obj !== 'object' || obj === null) {
      throw new Error('Required properties validation requires an object');
    }

    for (const requiredProp of required) {
      if (!(requiredProp in obj)) {
        throw new Error(`Missing required property: ${requiredProp}`);
      }
    }
  }

  /**
   * Validate array items
   */
  private async validateArrayItems(arr: any[], itemSchema: any): Promise<void> {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i];

      if (itemSchema.type) {
        try {
          await this.validateType(item, itemSchema.type);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          throw new Error(`Array item ${i}: ${errorMessage}`);
        }
      }
    }
  }

  /**
   * Format result as text for MCP content
   */
  private formatResultAsText(result: any): string {
    if (typeof result === 'string') {
      return result;
    }

    if (typeof result === 'object') {
      return JSON.stringify(result, null, 2);
    }

    return String(result);
  }

  /**
   * Create error response
   */
  private createErrorResponse(errorMessage: string, context: WorkflowContext): any {
    return {
      content: [
        {
          type: 'text',
          text: `Error: ${errorMessage}`
        }
      ],
      isError: true,
      error: {
        message: errorMessage,
        code: 'VALIDATION_ERROR',
        timestamp: new Date().toISOString()
      },
      metadata: {
        terminator: 'mcp-response',
        timestamp: new Date().toISOString(),
        executionId: context.executionId,
        workflowId: context.workflowId,
        resultType: 'error'
      }
    };
  }

  /**
   * Replace variables in string
   */
  private replaceVariables(str: string, input: any, context: WorkflowContext): string {
    const variables = {
      ...context.variables,
      input,
      result: input, // Alias for input
      nodeResults: context.nodeResults,
      executionId: context.executionId,
      workflowId: context.workflowId
    };

    return str.replace(/\${([^}]+)}/g, (match, varName) => {
      const value = this.getNestedProperty(variables, varName);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Process object recursively with variable substitution
   */
  private processObjectWithVariables(obj: any, input: any, context: WorkflowContext): any {
    if (typeof obj === 'string') {
      return this.replaceVariables(obj, input, context);
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.processObjectWithVariables(item, input, context));
    }

    if (typeof obj === 'object' && obj !== null) {
      const processedObj: any = {};
      for (const [key, value] of Object.entries(obj)) {
        processedObj[key] = this.processObjectWithVariables(value, input, context);
      }
      return processedObj;
    }

    return obj;
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get response statistics
   */
  public getResponseStats() {
    return {
      hasResult: this.config.result !== undefined,
      resultType: typeof this.config.result,
      hasSchema: Object.keys(this.config.schema || {}).length > 0,
      hasMetadata: Object.keys(this.config.metadata || {}).length > 0,
      validateSchema: this.config.validateSchema !== false,
      errorOnValidationFailure: this.config.errorOnValidationFailure !== false
    };
  }

  /**
   * Get schema information
   */
  public getSchemaInfo() {
    return {
      schema: this.config.schema,
      hasType: !!this.config.schema.type,
      hasProperties: !!this.config.schema.properties,
      hasRequired: Array.isArray(this.config.schema.required),
      requiredCount: Array.isArray(this.config.schema.required)
        ? this.config.schema.required.length
        : 0,
      propertyCount: this.config.schema.properties
        ? Object.keys(this.config.schema.properties).length
        : 0
    };
  }
}
