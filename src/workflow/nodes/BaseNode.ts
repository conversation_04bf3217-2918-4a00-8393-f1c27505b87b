import { INode } from '../../core/interfaces/INode';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import { ILogger } from '../../core/interfaces/ILogger';

/**
 * Base class for workflow nodes
 */
export abstract class BaseNode implements INode {
  protected config: Record<string, any>;
  protected logger: ILogger;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: Record<string, any>, logger: ILogger) {
    this.config = config;
    this.logger = logger;
    this.validateConfig();
  }

  /**
   * Execute the node with the given input and context
   * @param input Input data
   * @param context Workflow context
   * @returns Node execution result
   */
  abstract execute(input: any, context: WorkflowContext): Promise<any>;

  /**
   * Validate node configuration
   * @throws Error if configuration is invalid
   */
  protected validateConfig(): void {
    // Base validation logic
    if (!this.config) {
      throw new Error('Node configuration is required');
    }
  }
}
