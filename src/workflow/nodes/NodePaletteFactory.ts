import { Container } from 'inversify';
import { NodeFactory } from '../../core/interfaces/INodeRegistry';
import { TYPES } from '../../types';
import { ILogger } from '../../core/interfaces/ILogger';
import { IScriptEngine } from '../../core/interfaces/IScriptEngine';
import { IDatabase } from '../../core/interfaces/IDatabase';
import { IRedisClient } from '../../core/interfaces/IRedisClient';
import { IConditionalProcessor } from '../../core/interfaces/IConditionalProcessor';

// Trigger Nodes
import { RestAPITriggerNode, IRestAPITriggerConfig } from './trigger/RestAPITriggerNode';
import { MCPFunctionTriggerNode, IMCPFunctionTriggerConfig } from './trigger/MCPFunctionTriggerNode';
import { TimerTriggerNode, ITimerTriggerConfig } from './trigger/TimerTriggerNode';
import { WebhookTriggerNode, IWebhookTriggerConfig } from './trigger/WebhookTriggerNode';

// Action Nodes
import { JavaScriptActionNode, IJavaScriptActionConfig } from './action/JavaScriptActionNode';
import { SQLActionNode, ISQLActionConfig } from './action/SQLActionNode';
import { RedisActionNode, IRedisActionConfig } from './action/RedisActionNode';
import { HTTPActionNode, IHTTPActionConfig } from './action/HTTPActionNode';
import { LiteLLMActionNode, ILiteLLMActionConfig } from './action/LiteLLMActionNode';

// Logic Nodes
import { IfElseLogicNode, IIfElseLogicConfig } from './logic/IfElseLogicNode';
import { SwitchLogicNode, ISwitchLogicConfig } from './logic/SwitchLogicNode';
import { MergeLogicNode, IMergeLogicConfig } from './logic/MergeLogicNode';
import { LoopLogicNode, ILoopLogicConfig } from './logic/LoopLogicNode';

// Terminator Nodes
import { ResponseTerminatorNode, IResponseTerminatorConfig } from './terminator/ResponseTerminatorNode';
import { MCPResponseTerminatorNode, IMCPResponseTerminatorConfig } from './terminator/MCPResponseTerminatorNode';
import { ErrorTerminatorNode, IErrorTerminatorConfig } from './terminator/ErrorTerminatorNode';

/**
 * Factory functions for Trigger Nodes
 */

export function createRestAPITriggerFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new RestAPITriggerNode(config as IRestAPITriggerConfig, logger);
  };
}

export function createMCPFunctionTriggerFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new MCPFunctionTriggerNode(config as IMCPFunctionTriggerConfig, logger);
  };
}

export function createTimerTriggerFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new TimerTriggerNode(config as ITimerTriggerConfig, logger);
  };
}

export function createWebhookTriggerFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new WebhookTriggerNode(config as IWebhookTriggerConfig, logger);
  };
}

/**
 * Factory functions for Action Nodes
 */

export function createJavaScriptActionFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const scriptEngine = container.get<IScriptEngine>(TYPES.ScriptEngine);
    return new JavaScriptActionNode(config as IJavaScriptActionConfig, scriptEngine, logger);
  };
}

export function createSQLActionFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const database = container.get<IDatabase>(TYPES.Database);
    return new SQLActionNode(config as ISQLActionConfig, database, logger);
  };
}

export function createRedisActionFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const redisClient = container.get<IRedisClient>(TYPES.RedisClient);
    return new RedisActionNode(config as IRedisActionConfig, redisClient, logger);
  };
}

export function createHTTPActionFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new HTTPActionNode(config as IHTTPActionConfig, logger);
  };
}

export function createLiteLLMActionFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new LiteLLMActionNode(config as ILiteLLMActionConfig, logger);
  };
}

/**
 * Factory functions for Logic Nodes
 */

export function createIfElseLogicFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const conditionalProcessor = container.get<IConditionalProcessor>(TYPES.ConditionalProcessor);
    return new IfElseLogicNode(config as IIfElseLogicConfig, logger, conditionalProcessor);
  };
}

export function createSwitchLogicFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const conditionalProcessor = container.get<IConditionalProcessor>(TYPES.ConditionalProcessor);
    return new SwitchLogicNode(config as ISwitchLogicConfig, logger, conditionalProcessor);
  };
}

export function createMergeLogicFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new MergeLogicNode(config as IMergeLogicConfig, logger);
  };
}

export function createLoopLogicFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const conditionalProcessor = container.get<IConditionalProcessor>(TYPES.ConditionalProcessor);
    return new LoopLogicNode(config as ILoopLogicConfig, logger, conditionalProcessor);
  };
}

/**
 * Factory functions for Terminator Nodes
 */

export function createResponseTerminatorFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new ResponseTerminatorNode(config as IResponseTerminatorConfig, logger);
  };
}

export function createMCPResponseTerminatorFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new MCPResponseTerminatorNode(config as IMCPResponseTerminatorConfig, logger);
  };
}

export function createErrorTerminatorFactory(container: Container): NodeFactory {
  return (config: Record<string, any>) => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new ErrorTerminatorNode(config as IErrorTerminatorConfig, logger);
  };
}

/**
 * Register all NodePalette types with the node registry
 */
export function registerNodePaletteTypes(container: Container): void {
  const nodeRegistry = container.get(TYPES.NodeRegistry);

  // Register Trigger Nodes
  (nodeRegistry as any).registerNode('rest-api-trigger', createRestAPITriggerFactory(container));
  (nodeRegistry as any).registerNode('mcp-function-trigger', createMCPFunctionTriggerFactory(container));
  (nodeRegistry as any).registerNode('timer-trigger', createTimerTriggerFactory(container));
  (nodeRegistry as any).registerNode('webhook-trigger', createWebhookTriggerFactory(container));

  // Register Action Nodes
  (nodeRegistry as any).registerNode('javascript-action', createJavaScriptActionFactory(container));
  (nodeRegistry as any).registerNode('sql-action', createSQLActionFactory(container));
  (nodeRegistry as any).registerNode('redis-action', createRedisActionFactory(container));
  (nodeRegistry as any).registerNode('http-action', createHTTPActionFactory(container));
  (nodeRegistry as any).registerNode('litellm-action', createLiteLLMActionFactory(container));

  // Register Logic Nodes
  (nodeRegistry as any).registerNode('if-else-logic', createIfElseLogicFactory(container));
  (nodeRegistry as any).registerNode('switch-logic', createSwitchLogicFactory(container));
  (nodeRegistry as any).registerNode('merge-logic', createMergeLogicFactory(container));
  (nodeRegistry as any).registerNode('loop-logic', createLoopLogicFactory(container));

  // Register Terminator Nodes
  (nodeRegistry as any).registerNode('response-terminator', createResponseTerminatorFactory(container));
  (nodeRegistry as any).registerNode('mcp-response-terminator', createMCPResponseTerminatorFactory(container));
  (nodeRegistry as any).registerNode('error-terminator', createErrorTerminatorFactory(container));

  console.log('All NodePalette types registered successfully');
}
