import { injectable } from 'inversify';
import { INodeRegistry, NodeFactory } from '../../core/interfaces/INodeRegistry';
import { INode } from '../../core/interfaces/INode';

/**
 * Registry for workflow node types
 */
@injectable()
export class NodeRegistry implements INodeRegistry {
  private nodeFactories: Map<string, NodeFactory> = new Map();

  /**
   * Register a node type with a factory function
   * @param type Node type
   * @param nodeFactory Factory function for creating nodes of this type
   */
  registerNode(type: string, nodeFactory: NodeFactory): void {
    this.nodeFactories.set(type, nodeFactory);
  }

  /**
   * Get a node instance for the given type and configuration
   * @param type Node type
   * @param config Node configuration
   * @returns Node instance
   */
  getNode(type: string, config: Record<string, any>): INode {
    const factory = this.nodeFactories.get(type);
    if (!factory) {
      throw new Error(`Node type '${type}' is not registered`);
    }
    return factory(config);
  }

  /**
   * Get all registered node types
   * @returns Array of node types
   */
  getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodeFactories.keys());
  }
}
