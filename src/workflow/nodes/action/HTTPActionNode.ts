import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';
import fetch, { RequestInit } from 'node-fetch';

/**
 * Configuration interface for HTTP Action Node
 */
export interface IHTTPActionConfig {
  url: string;
  method: string;
  headers: Record<string, string>;
  body: string | Record<string, any>;
  timeout: number;
  followRedirects?: boolean;
  validateSSL?: boolean;
  retryCount?: number;
  retryDelay?: number;
  returnFullResponse?: boolean;
  failOnError?: boolean;
}

/**
 * HTTP Action Node - makes HTTP requests to external APIs
 */
@injectable()
export class HTTPActionNode extends BaseNode {
  protected config: IHTTPActionConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IHTTPActionConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.url) {
      throw new Error('HTTP Action: url is required');
    }
    
    // Validate URL format
    try {
      new URL(this.config.url);
    } catch {
      throw new Error(`HTTP Action: invalid URL format '${this.config.url}'`);
    }
    
    if (!this.config.method) {
      throw new Error('HTTP Action: method is required');
    }
    
    // Validate HTTP method
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    if (!validMethods.includes(this.config.method.toUpperCase())) {
      throw new Error(`HTTP Action: invalid method '${this.config.method}'. Valid methods: ${validMethods.join(', ')}`);
    }
    
    if (this.config.timeout && (typeof this.config.timeout !== 'number' || this.config.timeout <= 0)) {
      throw new Error('HTTP Action: timeout must be a positive number');
    }
    
    if (this.config.retryCount && (typeof this.config.retryCount !== 'number' || this.config.retryCount < 0)) {
      throw new Error('HTTP Action: retryCount must be a non-negative number');
    }
    
    if (this.config.retryDelay && (typeof this.config.retryDelay !== 'number' || this.config.retryDelay < 0)) {
      throw new Error('HTTP Action: retryDelay must be a non-negative number');
    }
  }

  /**
   * Execute the action node
   * @param input Input data
   * @param context Workflow context
   * @returns HTTP response data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing HTTP Action node with input: ${JSON.stringify(input)}`);

    try {
      // Process configuration with variable substitution
      const processedUrl = this.replaceVariables(this.config.url, input, context);
      const processedHeaders = this.processHeaders(this.config.headers, input, context);
      const processedBody = this.processBody(this.config.body, input, context);

      // Prepare request configuration
      const requestConfig: RequestInit = {
        method: this.config.method.toUpperCase(),
        headers: processedHeaders,
        timeout: this.config.timeout || 30000
      };

      // Add body for methods that support it
      if (['POST', 'PUT', 'PATCH'].includes(requestConfig.method as string) && processedBody) {
        requestConfig.body = processedBody;
      }

      // Execute request with retry logic
      const result = await this.executeWithRetry(processedUrl, requestConfig);

      this.logger.debug(`HTTP Action node execution completed with result: ${JSON.stringify(result)}`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`HTTP Action node execution failed: ${errorMessage}`);
      throw new Error(`HTTP Action execution failed: ${errorMessage}`);
    }
  }

  /**
   * Replace variables in string
   */
  private replaceVariables(str: string, input: any, context: WorkflowContext): string {
    const variables = { ...context.variables, input };
    
    return str.replace(/\${([^}]+)}/g, (match, varName) => {
      return (variables as any)[varName] !== undefined ? String((variables as any)[varName]) : match;
    });
  }

  /**
   * Process headers with variable substitution
   */
  private processHeaders(headers: Record<string, string>, input: any, context: WorkflowContext): Record<string, string> {
    const processedHeaders: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(headers || {})) {
      processedHeaders[key] = this.replaceVariables(value, input, context);
    }
    
    // Set default Content-Type if not specified and body is present
    if (!processedHeaders['Content-Type'] && !processedHeaders['content-type']) {
      if (['POST', 'PUT', 'PATCH'].includes(this.config.method.toUpperCase())) {
        processedHeaders['Content-Type'] = 'application/json';
      }
    }
    
    return processedHeaders;
  }

  /**
   * Process body with variable substitution
   */
  private processBody(body: string | Record<string, any> | undefined, input: any, context: WorkflowContext): string | undefined {
    if (!body) {
      return undefined;
    }
    
    if (typeof body === 'string') {
      return this.replaceVariables(body, input, context);
    }
    
    if (typeof body === 'object') {
      // Process object body with variable substitution
      const processedBody = this.processObjectWithVariables(body, input, context);
      return JSON.stringify(processedBody);
    }
    
    return String(body);
  }

  /**
   * Process object recursively with variable substitution
   */
  private processObjectWithVariables(obj: any, input: any, context: WorkflowContext): any {
    if (typeof obj === 'string') {
      return this.replaceVariables(obj, input, context);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.processObjectWithVariables(item, input, context));
    }
    
    if (typeof obj === 'object' && obj !== null) {
      const processedObj: any = {};
      for (const [key, value] of Object.entries(obj)) {
        processedObj[key] = this.processObjectWithVariables(value, input, context);
      }
      return processedObj;
    }
    
    return obj;
  }

  /**
   * Execute HTTP request with retry logic
   */
  private async executeWithRetry(url: string, requestConfig: RequestInit): Promise<any> {
    const maxRetries = this.config.retryCount || 0;
    const retryDelay = this.config.retryDelay || 1000;
    
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          this.logger.debug(`HTTP Action retry attempt ${attempt}/${maxRetries}`);
          await this.sleep(retryDelay * attempt);
        }
        
        return await this.executeRequest(url, requestConfig);
        
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on client errors (4xx)
        if (error instanceof Error && error.message.includes('4')) {
          break;
        }
        
        if (attempt === maxRetries) {
          break;
        }
      }
    }
    
    throw lastError || new Error('HTTP request failed after retries');
  }

  /**
   * Execute single HTTP request
   */
  private async executeRequest(url: string, requestConfig: RequestInit): Promise<any> {
    this.logger.debug(`Making ${requestConfig.method} request to ${url}`);
    
    const response = await fetch(url, requestConfig);
    
    // Check if response is OK
    if (!response.ok && this.config.failOnError !== false) {
      throw new Error(`HTTP request failed with status ${response.status}: ${response.statusText}`);
    }
    
    // Parse response
    let responseData;
    const contentType = response.headers.get('content-type');
    
    try {
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }
    } catch (parseError) {
      responseData = await response.text();
    }
    
    // Return full response or just data
    if (this.config.returnFullResponse) {
      return {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        data: responseData,
        url: response.url
      };
    }
    
    return responseData;
  }

  /**
   * Sleep utility function
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get request execution statistics
   */
  public getExecutionStats() {
    return {
      url: this.config.url,
      method: this.config.method,
      timeout: this.config.timeout || 30000,
      retryCount: this.config.retryCount || 0,
      retryDelay: this.config.retryDelay || 1000,
      hasHeaders: Object.keys(this.config.headers || {}).length > 0,
      hasBody: !!this.config.body,
      returnFullResponse: this.config.returnFullResponse || false,
      failOnError: this.config.failOnError !== false
    };
  }
}
