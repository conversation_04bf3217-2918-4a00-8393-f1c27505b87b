import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IScriptEngine } from '../../../core/interfaces/IScriptEngine';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';

/**
 * Configuration interface for JavaScript Action Node
 */
export interface IJavaScriptActionConfig {
  script: string;
  timeout: number;
  allowedModules: string[];
  variables?: Record<string, any>;
  returnVariable?: string;
}

/**
 * JavaScript Action Node - executes custom JavaScript code
 */
@injectable()
export class JavaScriptActionNode extends BaseNode {
  protected config: IJavaScriptActionConfig;
  private scriptEngine: IScriptEngine;

  /**
   * Constructor
   * @param config Node configuration
   * @param scriptEngine Script engine for executing JavaScript
   * @param logger Logger
   */
  constructor(
    config: IJavaScriptActionConfig,
    @inject(TYPES.ScriptEngine) scriptEngine: IScriptEngine,
    logger: ILogger
  ) {
    super(config, logger);
    this.config = config;
    this.scriptEngine = scriptEngine;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.script) {
      throw new Error('JavaScript Action: script is required');
    }

    if (typeof this.config.script !== 'string') {
      throw new Error('JavaScript Action: script must be a string');
    }

    if (
      this.config.timeout &&
      (typeof this.config.timeout !== 'number' || this.config.timeout <= 0)
    ) {
      throw new Error('JavaScript Action: timeout must be a positive number');
    }

    if (this.config.allowedModules && !Array.isArray(this.config.allowedModules)) {
      throw new Error('JavaScript Action: allowedModules must be an array');
    }
  }

  /**
   * Execute the action node
   * @param input Input data
   * @param context Workflow context
   * @returns Script execution result
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing JavaScript Action node with input: ${JSON.stringify(input)}`);

    try {
      // Prepare script execution context
      const scriptContext = {
        input: input,
        args: input, // Keep for backward compatibility
        logger: this.logger,
        workflowContext: {
          variables: { ...context.variables, ...this.config.variables },
          nodeResults: context.nodeResults,
          executionId: context.executionId,
          workflowId: context.workflowId
        },
        // Add utility functions
        utils: {
          now: () => new Date(),
          timestamp: () => Date.now(),
          uuid: () => require('crypto').randomUUID(),
          sleep: (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))
        }
      };

      // Set timeout
      const timeout = this.config.timeout || 30000;

      // Execute script with timeout
      const result = await Promise.race([
        this.scriptEngine.execute(this.config.script, scriptContext),
        this.createTimeoutPromise(timeout)
      ]);

      // Process result
      const processedResult = this.processResult(result, context);

      this.logger.debug(
        `JavaScript Action node execution completed with result: ${JSON.stringify(processedResult)}`
      );
      return processedResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`JavaScript Action node execution failed: ${errorMessage}`);
      throw new Error(`JavaScript Action execution failed: ${errorMessage}`);
    }
  }

  /**
   * Create timeout promise
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`JavaScript Action execution timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Process script execution result
   */
  private processResult(result: any, context: WorkflowContext): any {
    // If returnVariable is specified, extract that variable from result
    if (this.config.returnVariable && result && typeof result === 'object') {
      if (this.config.returnVariable in result) {
        return result[this.config.returnVariable];
      } else {
        this.logger.warn(`Return variable '${this.config.returnVariable}' not found in result`);
      }
    }

    // Return the full result
    return result;
  }

  /**
   * Get allowed modules for script execution
   */
  public getAllowedModules(): string[] {
    return this.config.allowedModules || [];
  }

  /**
   * Validate script syntax (static method for pre-validation)
   */
  public static validateScriptSyntax(script: string): { valid: boolean; error?: string } {
    try {
      // Basic syntax validation using Function constructor
      new Function(script);
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown syntax error'
      };
    }
  }

  /**
   * Get script execution statistics
   */
  public getExecutionStats() {
    return {
      scriptLength: this.config.script.length,
      timeout: this.config.timeout || 30000,
      allowedModules: this.config.allowedModules?.length || 0,
      hasVariables: Object.keys(this.config.variables || {}).length > 0,
      hasReturnVariable: !!this.config.returnVariable
    };
  }
}
