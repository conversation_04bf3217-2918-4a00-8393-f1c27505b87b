import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';

/**
 * Configuration interface for SQL Action Node
 */
export interface ISQLActionConfig {
  query: string;
  parameters: any[];
  connection: string;
  timeout?: number;
  transactional?: boolean;
  returnFormat?: 'rows' | 'count' | 'first' | 'scalar';
  maxRows?: number;
}

/**
 * SQL Action Node - executes SQL queries against database
 */
@injectable()
export class SQLActionNode extends BaseNode {
  protected config: ISQLActionConfig;
  private database: IDatabase;

  /**
   * Constructor
   * @param config Node configuration
   * @param database Database connection
   * @param logger Logger
   */
  constructor(
    config: ISQLActionConfig,
    @inject(TYPES.Database) database: IDatabase,
    logger: ILogger
  ) {
    super(config, logger);
    this.config = config;
    this.database = database;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.query) {
      throw new Error('SQL Action: query is required');
    }
    
    if (typeof this.config.query !== 'string') {
      throw new Error('SQL Action: query must be a string');
    }
    
    if (!this.config.connection) {
      throw new Error('SQL Action: connection is required');
    }
    
    if (this.config.parameters && !Array.isArray(this.config.parameters)) {
      throw new Error('SQL Action: parameters must be an array');
    }
    
    if (this.config.timeout && (typeof this.config.timeout !== 'number' || this.config.timeout <= 0)) {
      throw new Error('SQL Action: timeout must be a positive number');
    }
    
    if (this.config.returnFormat) {
      const validFormats = ['rows', 'count', 'first', 'scalar'];
      if (!validFormats.includes(this.config.returnFormat)) {
        throw new Error(`SQL Action: invalid returnFormat '${this.config.returnFormat}'. Valid formats: ${validFormats.join(', ')}`);
      }
    }
    
    if (this.config.maxRows && (typeof this.config.maxRows !== 'number' || this.config.maxRows <= 0)) {
      throw new Error('SQL Action: maxRows must be a positive number');
    }
  }

  /**
   * Execute the action node
   * @param input Input data
   * @param context Workflow context
   * @returns Query execution result
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing SQL Action node with input: ${JSON.stringify(input)}`);

    try {
      // Process query parameters
      const processedQuery = this.processQuery(this.config.query, input, context);
      const processedParameters = this.processParameters(this.config.parameters, input, context);

      // Validate query safety
      this.validateQuerySafety(processedQuery);

      // Execute query
      let result;
      if (this.config.transactional) {
        result = await this.executeTransactional(processedQuery, processedParameters);
      } else {
        result = await this.executeQuery(processedQuery, processedParameters);
      }

      // Format result based on returnFormat
      const formattedResult = this.formatResult(result);

      this.logger.debug(`SQL Action node execution completed with result: ${JSON.stringify(formattedResult)}`);
      return formattedResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`SQL Action node execution failed: ${errorMessage}`);
      throw new Error(`SQL Action execution failed: ${errorMessage}`);
    }
  }

  /**
   * Process query with variable substitution
   */
  private processQuery(query: string, input: any, context: WorkflowContext): string {
    let processedQuery = query;

    // Replace workflow variables
    const variables = { ...context.variables, input };
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `\${${key}}`;
      if (processedQuery.includes(placeholder)) {
        processedQuery = processedQuery.replace(
          new RegExp(`\\$\\{${key}\\}`, 'g'),
          String(value)
        );
      }
    }

    return processedQuery;
  }

  /**
   * Process parameters with variable substitution
   */
  private processParameters(parameters: any[], input: any, context: WorkflowContext): any[] {
    if (!parameters || parameters.length === 0) {
      return [];
    }

    const variables = { ...context.variables, input };
    
    return parameters.map(param => {
      if (typeof param === 'string' && param.startsWith('${') && param.endsWith('}')) {
        const varName = param.slice(2, -1);
        return (variables as any)[varName] !== undefined ? (variables as any)[varName] : param;
      }
      return param;
    });
  }

  /**
   * Validate query safety (basic SQL injection prevention)
   */
  private validateQuerySafety(query: string): void {
    const normalizedQuery = query.toLowerCase().trim();
    
    // Check for dangerous patterns
    const dangerousPatterns = [
      /;\s*(drop|delete|truncate|alter|create|insert|update)\s+/,
      /union\s+select/,
      /exec\s*\(/,
      /xp_cmdshell/,
      /sp_executesql/
    ];

    for (const pattern of dangerousPatterns) {
      if (pattern.test(normalizedQuery)) {
        throw new Error('SQL Action: potentially dangerous query detected');
      }
    }

    // Ensure query doesn't contain multiple statements (basic check)
    const statements = query.split(';').filter(s => s.trim().length > 0);
    if (statements.length > 1) {
      throw new Error('SQL Action: multiple statements not allowed');
    }
  }

  /**
   * Execute query without transaction
   */
  private async executeQuery(query: string, parameters: any[]): Promise<any> {
    const timeout = this.config.timeout || 30000;
    
    return await Promise.race([
      this.database.query(query, parameters),
      this.createTimeoutPromise(timeout)
    ]);
  }

  /**
   * Execute query within transaction
   */
  private async executeTransactional(query: string, parameters: any[]): Promise<any> {
    const timeout = this.config.timeout || 30000;
    
    return await Promise.race([
      this.database.transaction(async (trx) => {
        return await trx.query(query, parameters);
      }),
      this.createTimeoutPromise(timeout)
    ]);
  }

  /**
   * Create timeout promise
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`SQL Action execution timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Format result based on returnFormat configuration
   */
  private formatResult(result: any): any {
    if (!result) {
      return null;
    }

    const returnFormat = this.config.returnFormat || 'rows';
    
    switch (returnFormat) {
      case 'count':
        return Array.isArray(result) ? result.length : (result.rowCount || 0);
        
      case 'first':
        return Array.isArray(result) ? (result[0] || null) : result;
        
      case 'scalar':
        if (Array.isArray(result) && result.length > 0) {
          const firstRow = result[0];
          const firstKey = Object.keys(firstRow)[0];
          return firstRow[firstKey];
        }
        return null;
        
      case 'rows':
      default:
        // Apply maxRows limit if specified
        if (this.config.maxRows && Array.isArray(result)) {
          return result.slice(0, this.config.maxRows);
        }
        return result;
    }
  }

  /**
   * Get query execution statistics
   */
  public getExecutionStats() {
    return {
      queryLength: this.config.query.length,
      parameterCount: this.config.parameters?.length || 0,
      connection: this.config.connection,
      timeout: this.config.timeout || 30000,
      transactional: this.config.transactional || false,
      returnFormat: this.config.returnFormat || 'rows',
      maxRows: this.config.maxRows
    };
  }
}
