import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for LiteLLM Action Node
 */
export interface ILiteLLMActionConfig {
  model: string;
  prompt: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  apiKey?: string;
  baseUrl?: string;
  timeout?: number;
  stream?: boolean;
  stopSequences?: string[];
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

/**
 * LiteLLM Action Node - calls LLM models via LiteLLM
 */
@injectable()
export class LiteLLMActionNode extends BaseNode {
  protected config: ILiteLLMActionConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: ILiteLLMActionConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.model) {
      throw new Error('LiteLLM Action: model is required');
    }
    
    if (!this.config.prompt) {
      throw new Error('LiteLLM Action: prompt is required');
    }
    
    if (typeof this.config.temperature !== 'number' || this.config.temperature < 0 || this.config.temperature > 2) {
      throw new Error('LiteLLM Action: temperature must be a number between 0 and 2');
    }
    
    if (typeof this.config.maxTokens !== 'number' || this.config.maxTokens <= 0) {
      throw new Error('LiteLLM Action: maxTokens must be a positive number');
    }
    
    if (this.config.timeout && (typeof this.config.timeout !== 'number' || this.config.timeout <= 0)) {
      throw new Error('LiteLLM Action: timeout must be a positive number');
    }
    
    if (this.config.topP && (typeof this.config.topP !== 'number' || this.config.topP <= 0 || this.config.topP > 1)) {
      throw new Error('LiteLLM Action: topP must be a number between 0 and 1');
    }
    
    if (this.config.frequencyPenalty && (typeof this.config.frequencyPenalty !== 'number' || this.config.frequencyPenalty < -2 || this.config.frequencyPenalty > 2)) {
      throw new Error('LiteLLM Action: frequencyPenalty must be a number between -2 and 2');
    }
    
    if (this.config.presencePenalty && (typeof this.config.presencePenalty !== 'number' || this.config.presencePenalty < -2 || this.config.presencePenalty > 2)) {
      throw new Error('LiteLLM Action: presencePenalty must be a number between -2 and 2');
    }
  }

  /**
   * Execute the action node
   * @param input Input data
   * @param context Workflow context
   * @returns LLM response
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing LiteLLM Action node with input: ${JSON.stringify(input)}`);

    try {
      // Process prompt with variable substitution
      const processedPrompt = this.processPrompt(this.config.prompt, input, context);
      const processedSystemPrompt = this.config.systemPrompt 
        ? this.processPrompt(this.config.systemPrompt, input, context)
        : undefined;

      // Prepare LiteLLM request
      const requestPayload = this.buildRequestPayload(processedPrompt, processedSystemPrompt);

      // Execute LLM request
      const result = await this.executeLLMRequest(requestPayload);

      // Process and format response
      const formattedResult = this.formatResponse(result, input, context);

      this.logger.debug(`LiteLLM Action node execution completed with result: ${JSON.stringify(formattedResult)}`);
      return formattedResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`LiteLLM Action node execution failed: ${errorMessage}`);
      throw new Error(`LiteLLM Action execution failed: ${errorMessage}`);
    }
  }

  /**
   * Process prompt with variable substitution
   */
  private processPrompt(prompt: string, input: any, context: WorkflowContext): string {
    const variables = { 
      ...context.variables, 
      input,
      nodeResults: context.nodeResults,
      executionId: context.executionId,
      workflowId: context.workflowId
    };
    
    return prompt.replace(/\${([^}]+)}/g, (match, varName) => {
      // Support nested property access like ${input.data.value}
      const value = this.getNestedProperty(variables, varName);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Build LiteLLM request payload
   */
  private buildRequestPayload(prompt: string, systemPrompt?: string): any {
    const messages: any[] = [];
    
    // Add system message if provided
    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }
    
    // Add user message
    messages.push({
      role: 'user',
      content: prompt
    });

    const payload: any = {
      model: this.config.model,
      messages: messages,
      temperature: this.config.temperature,
      max_tokens: this.config.maxTokens,
      stream: this.config.stream || false
    };

    // Add optional parameters
    if (this.config.topP !== undefined) {
      payload.top_p = this.config.topP;
    }
    
    if (this.config.frequencyPenalty !== undefined) {
      payload.frequency_penalty = this.config.frequencyPenalty;
    }
    
    if (this.config.presencePenalty !== undefined) {
      payload.presence_penalty = this.config.presencePenalty;
    }
    
    if (this.config.stopSequences && this.config.stopSequences.length > 0) {
      payload.stop = this.config.stopSequences;
    }

    return payload;
  }

  /**
   * Execute LLM request via LiteLLM
   */
  private async executeLLMRequest(payload: any): Promise<any> {
    const timeout = this.config.timeout || 60000;
    
    // Prepare request configuration
    const requestConfig = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
      },
      body: JSON.stringify(payload),
      timeout: timeout
    };

    // Determine endpoint URL
    const baseUrl = this.config.baseUrl || 'http://localhost:4000'; // Default LiteLLM proxy URL
    const endpoint = `${baseUrl}/chat/completions`;

    // Execute request with timeout
    return await Promise.race([
      this.makeRequest(endpoint, requestConfig),
      this.createTimeoutPromise(timeout)
    ]);
  }

  /**
   * Make HTTP request to LiteLLM
   */
  private async makeRequest(url: string, config: any): Promise<any> {
    const fetch = (await import('node-fetch')).default;
    
    this.logger.debug(`Making LiteLLM request to ${url}`);
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`LiteLLM request failed with status ${response.status}: ${errorText}`);
    }
    
    return await response.json();
  }

  /**
   * Create timeout promise
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`LiteLLM Action execution timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Format LLM response
   */
  private formatResponse(response: any, input: any, context: WorkflowContext): any {
    // Extract content from response
    const content = response.choices?.[0]?.message?.content || '';
    const usage = response.usage || {};
    
    return {
      model: this.config.model,
      content: content,
      usage: {
        promptTokens: usage.prompt_tokens || 0,
        completionTokens: usage.completion_tokens || 0,
        totalTokens: usage.total_tokens || 0
      },
      metadata: {
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
        finishReason: response.choices?.[0]?.finish_reason,
        timestamp: new Date().toISOString(),
        executionId: context.executionId
      },
      rawResponse: response
    };
  }

  /**
   * Get execution statistics
   */
  public getExecutionStats() {
    return {
      model: this.config.model,
      promptLength: this.config.prompt.length,
      systemPromptLength: this.config.systemPrompt?.length || 0,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: this.config.timeout || 60000,
      stream: this.config.stream || false,
      hasStopSequences: (this.config.stopSequences?.length || 0) > 0,
      hasApiKey: !!this.config.apiKey,
      baseUrl: this.config.baseUrl || 'http://localhost:4000'
    };
  }
}
