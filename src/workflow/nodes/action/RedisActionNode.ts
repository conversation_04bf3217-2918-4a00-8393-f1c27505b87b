import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IRedisClient } from '../../../core/interfaces/IRedisClient';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';

/**
 * Configuration interface for Redis Action Node
 */
export interface IRedisActionConfig {
  operation: 'get' | 'set' | 'del' | 'exists' | 'incr' | 'decr' | 'lpush' | 'rpush' | 'lpop' | 'rpop' | 'llen' | 'hget' | 'hset' | 'hdel' | 'hgetall' | 'expire';
  key: string;
  value?: any;
  ttl?: number;
  field?: string; // for hash operations
  timeout?: number;
  keyPrefix?: string;
}

/**
 * Redis Action Node - performs Redis cache operations
 */
@injectable()
export class RedisActionNode extends BaseNode {
  protected config: IRedisActionConfig;
  private redisClient: IRedisClient;

  /**
   * Constructor
   * @param config Node configuration
   * @param redisClient Redis client
   * @param logger Logger
   */
  constructor(
    config: IRedisActionConfig,
    @inject(TYPES.RedisClient) redisClient: IRedisClient,
    logger: ILogger
  ) {
    super(config, logger);
    this.config = config;
    this.redisClient = redisClient;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.operation) {
      throw new Error('Redis Action: operation is required');
    }
    
    const validOperations = ['get', 'set', 'del', 'exists', 'incr', 'decr', 'lpush', 'rpush', 'lpop', 'rpop', 'llen', 'hget', 'hset', 'hdel', 'hgetall', 'expire'];
    if (!validOperations.includes(this.config.operation)) {
      throw new Error(`Redis Action: invalid operation '${this.config.operation}'. Valid operations: ${validOperations.join(', ')}`);
    }
    
    if (!this.config.key) {
      throw new Error('Redis Action: key is required');
    }
    
    // Validate operation-specific requirements
    this.validateOperationRequirements();
    
    if (this.config.timeout && (typeof this.config.timeout !== 'number' || this.config.timeout <= 0)) {
      throw new Error('Redis Action: timeout must be a positive number');
    }
    
    if (this.config.ttl && (typeof this.config.ttl !== 'number' || this.config.ttl <= 0)) {
      throw new Error('Redis Action: ttl must be a positive number');
    }
  }

  /**
   * Validate operation-specific requirements
   */
  private validateOperationRequirements(): void {
    const operation = this.config.operation;
    
    // Operations that require value
    if (['set', 'lpush', 'rpush', 'hset'].includes(operation) && this.config.value === undefined) {
      throw new Error(`Redis Action: value is required for operation '${operation}'`);
    }
    
    // Hash operations that require field
    if (['hget', 'hset', 'hdel'].includes(operation) && !this.config.field) {
      throw new Error(`Redis Action: field is required for hash operation '${operation}'`);
    }
    
    // Operations that require ttl
    if (operation === 'expire' && !this.config.ttl) {
      throw new Error('Redis Action: ttl is required for expire operation');
    }
  }

  /**
   * Execute the action node
   * @param input Input data
   * @param context Workflow context
   * @returns Redis operation result
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Redis Action node with input: ${JSON.stringify(input)}`);

    try {
      // Process key with variable substitution and prefix
      const processedKey = this.processKey(this.config.key, input, context);
      
      // Process value with variable substitution
      const processedValue = this.processValue(this.config.value, input, context);
      
      // Process field for hash operations
      const processedField = this.processField(this.config.field, input, context);

      // Execute Redis operation
      const result = await this.executeRedisOperation(
        this.config.operation,
        processedKey,
        processedValue,
        processedField
      );

      // Format result
      const formattedResult = this.formatResult(result);

      this.logger.debug(`Redis Action node execution completed with result: ${JSON.stringify(formattedResult)}`);
      return formattedResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Redis Action node execution failed: ${errorMessage}`);
      throw new Error(`Redis Action execution failed: ${errorMessage}`);
    }
  }

  /**
   * Process key with variable substitution and prefix
   */
  private processKey(key: string, input: any, context: WorkflowContext): string {
    let processedKey = this.replaceVariables(key, input, context);
    
    // Add prefix if configured
    if (this.config.keyPrefix) {
      processedKey = `${this.config.keyPrefix}:${processedKey}`;
    }
    
    return processedKey;
  }

  /**
   * Process value with variable substitution
   */
  private processValue(value: any, input: any, context: WorkflowContext): any {
    if (value === undefined || value === null) {
      return value;
    }
    
    if (typeof value === 'string') {
      return this.replaceVariables(value, input, context);
    }
    
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    
    return value;
  }

  /**
   * Process field for hash operations
   */
  private processField(field: string | undefined, input: any, context: WorkflowContext): string | undefined {
    if (!field) {
      return undefined;
    }
    
    return this.replaceVariables(field, input, context);
  }

  /**
   * Replace variables in string
   */
  private replaceVariables(str: string, input: any, context: WorkflowContext): string {
    const variables = { ...context.variables, input };
    
    return str.replace(/\${([^}]+)}/g, (match, varName) => {
      return (variables as any)[varName] !== undefined ? String((variables as any)[varName]) : match;
    });
  }

  /**
   * Execute Redis operation with timeout
   */
  private async executeRedisOperation(
    operation: string,
    key: string,
    value?: any,
    field?: string
  ): Promise<any> {
    const timeout = this.config.timeout || 5000;
    
    const operationPromise = this.performRedisOperation(operation, key, value, field);
    
    return await Promise.race([
      operationPromise,
      this.createTimeoutPromise(timeout)
    ]);
  }

  /**
   * Perform the actual Redis operation
   */
  private async performRedisOperation(
    operation: string,
    key: string,
    value?: any,
    field?: string
  ): Promise<any> {
    switch (operation) {
      case 'get':
        return await this.redisClient.get(key);
        
      case 'set':
        if (this.config.ttl) {
          return await this.redisClient.setex(key, this.config.ttl, value);
        }
        return await this.redisClient.set(key, value);
        
      case 'del':
        return await this.redisClient.del(key);
        
      case 'exists':
        return await this.redisClient.exists(key);
        
      case 'incr':
        return await this.redisClient.incr(key);
        
      case 'decr':
        return await this.redisClient.decr(key);
        
      case 'lpush':
        return await this.redisClient.lpush(key, value);
        
      case 'rpush':
        return await this.redisClient.rpush(key, value);
        
      case 'lpop':
        return await this.redisClient.lpop(key);
        
      case 'rpop':
        return await this.redisClient.rpop(key);
        
      case 'llen':
        return await this.redisClient.llen(key);
        
      case 'hget':
        return await this.redisClient.hget(key, field!);
        
      case 'hset':
        return await this.redisClient.hset(key, field!, value);
        
      case 'hdel':
        return await this.redisClient.hdel(key, field!);
        
      case 'hgetall':
        return await this.redisClient.hgetall(key);
        
      case 'expire':
        return await this.redisClient.expire(key, this.config.ttl!);
        
      default:
        throw new Error(`Unsupported Redis operation: ${operation}`);
    }
  }

  /**
   * Create timeout promise
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Redis Action execution timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Format result for consistent output
   */
  private formatResult(result: any): any {
    return {
      operation: this.config.operation,
      key: this.config.key,
      result: result,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get operation execution statistics
   */
  public getExecutionStats() {
    return {
      operation: this.config.operation,
      key: this.config.key,
      hasValue: this.config.value !== undefined,
      hasField: this.config.field !== undefined,
      hasTTL: this.config.ttl !== undefined,
      timeout: this.config.timeout || 5000,
      keyPrefix: this.config.keyPrefix
    };
  }
}
