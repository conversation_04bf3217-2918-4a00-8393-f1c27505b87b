import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for Timer Trigger Node
 */
export interface ITimerTriggerConfig {
  cronExpression: string;
  timezone: string;
  enabled: boolean;
  maxExecutions?: number;
  startDate?: string;
  endDate?: string;
}

/**
 * Timer Trigger Node - triggers workflow execution based on cron schedule
 */
@injectable()
export class TimerTriggerNode extends BaseNode {
  protected config: ITimerTriggerConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: ITimerTriggerConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.cronExpression) {
      throw new Error('Timer Trigger: cronExpression is required');
    }
    
    if (!this.config.timezone) {
      throw new Error('Timer Trigger: timezone is required');
    }
    
    // Validate cron expression format
    if (!this.isValidCronExpression(this.config.cronExpression)) {
      throw new Error(`Timer Trigger: invalid cron expression '${this.config.cronExpression}'`);
    }
    
    // Validate timezone
    if (!this.isValidTimezone(this.config.timezone)) {
      throw new Error(`Timer Trigger: invalid timezone '${this.config.timezone}'`);
    }
    
    // Validate dates if provided
    if (this.config.startDate && !this.isValidDate(this.config.startDate)) {
      throw new Error(`Timer Trigger: invalid startDate '${this.config.startDate}'`);
    }
    
    if (this.config.endDate && !this.isValidDate(this.config.endDate)) {
      throw new Error(`Timer Trigger: invalid endDate '${this.config.endDate}'`);
    }
  }

  /**
   * Execute the trigger node
   * @param input Input data (usually empty for timer triggers)
   * @param context Workflow context
   * @returns Timer execution data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Timer Trigger node with input: ${JSON.stringify(input)}`);

    try {
      // Check if trigger is enabled
      if (!this.config.enabled) {
        throw new Error('Timer Trigger is disabled');
      }

      // Get current time in specified timezone
      const now = new Date();
      const triggerTime = this.convertToTimezone(now, this.config.timezone);

      // Check if we're within the valid date range
      if (!this.isWithinDateRange(now)) {
        throw new Error('Timer Trigger execution is outside the valid date range');
      }

      // Prepare output data
      const result = {
        trigger: 'timer',
        schedule: {
          cronExpression: this.config.cronExpression,
          timezone: this.config.timezone,
          triggerTime: triggerTime.toISOString(),
          nextExecution: this.getNextExecutionTime()
        },
        timestamp: now.toISOString(),
        workflowId: context.workflowId,
        executionId: context.executionId,
        input: input || {}
      };

      this.logger.debug(`Timer Trigger node execution completed with result: ${JSON.stringify(result)}`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Timer Trigger node execution failed: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Validate cron expression format
   */
  private isValidCronExpression(expression: string): boolean {
    // Basic cron validation (5 or 6 fields)
    const parts = expression.trim().split(/\s+/);
    return parts.length === 5 || parts.length === 6;
  }

  /**
   * Validate timezone
   */
  private isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate date string
   */
  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * Convert date to specified timezone
   */
  private convertToTimezone(date: Date, timezone: string): Date {
    try {
      const formatter = new Intl.DateTimeFormat('en-US', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
      
      const parts = formatter.formatToParts(date);
      const year = parseInt(parts.find(p => p.type === 'year')?.value || '0');
      const month = parseInt(parts.find(p => p.type === 'month')?.value || '0') - 1;
      const day = parseInt(parts.find(p => p.type === 'day')?.value || '0');
      const hour = parseInt(parts.find(p => p.type === 'hour')?.value || '0');
      const minute = parseInt(parts.find(p => p.type === 'minute')?.value || '0');
      const second = parseInt(parts.find(p => p.type === 'second')?.value || '0');
      
      return new Date(year, month, day, hour, minute, second);
    } catch {
      return date;
    }
  }

  /**
   * Check if current time is within valid date range
   */
  private isWithinDateRange(now: Date): boolean {
    if (this.config.startDate) {
      const startDate = new Date(this.config.startDate);
      if (now < startDate) {
        return false;
      }
    }

    if (this.config.endDate) {
      const endDate = new Date(this.config.endDate);
      if (now > endDate) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get next execution time (placeholder implementation)
   */
  private getNextExecutionTime(): string {
    // TODO: Implement proper cron parsing to calculate next execution
    // For now, return a placeholder
    const nextHour = new Date();
    nextHour.setHours(nextHour.getHours() + 1);
    return nextHour.toISOString();
  }
}
