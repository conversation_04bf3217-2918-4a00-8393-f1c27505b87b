import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for MCP Function Trigger Node
 */
export interface IMCPFunctionTriggerConfig {
  functionName: string;
  inputSchema: Record<string, any>;
  outputSchema: Record<string, any>;
  description?: string;
  validation?: boolean;
}

/**
 * MCP Function Trigger Node - triggers workflow execution via MCP function call
 */
@injectable()
export class MCPFunctionTriggerNode extends BaseNode {
  protected config: IMCPFunctionTriggerConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IMCPFunctionTriggerConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.functionName) {
      throw new Error('MCP Function Trigger: functionName is required');
    }

    if (!this.config.inputSchema) {
      throw new Error('MCP Function Trigger: inputSchema is required');
    }

    if (!this.config.outputSchema) {
      throw new Error('MCP Function Trigger: outputSchema is required');
    }

    // Validate function name format
    if (!/^[a-zA-Z][a-zA-Z0-9_-]*$/.test(this.config.functionName)) {
      throw new Error(
        'MCP Function Trigger: functionName must start with letter and contain only letters, numbers, underscores, and hyphens'
      );
    }
  }

  /**
   * Execute the trigger node
   * @param input Input data from MCP function call
   * @param context Workflow context
   * @returns Processed input data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing MCP Function Trigger node with input: ${JSON.stringify(input)}`);

    try {
      // Extract MCP function call data
      const functionData = {
        functionName: this.config.functionName,
        arguments: input.arguments || input,
        metadata: input.metadata || {},
        callId: input.callId || context.executionId
      };

      // Validate input if validation is enabled
      if (this.config.validation !== false) {
        await this.validateInput(functionData.arguments);
      }

      // Prepare output data
      const result = {
        trigger: 'mcp-function',
        function: functionData,
        timestamp: new Date().toISOString(),
        workflowId: context.workflowId,
        executionId: context.executionId,
        schema: {
          input: this.config.inputSchema,
          output: this.config.outputSchema
        }
      };

      this.logger.debug(
        `MCP Function Trigger node execution completed with result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`MCP Function Trigger node execution failed: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Validate input arguments against schema
   */
  private async validateInput(args: any): Promise<void> {
    if (!this.config.inputSchema || !args) {
      return;
    }

    // Basic schema validation
    const schema = this.config.inputSchema;

    // Check required properties
    if (schema.required && Array.isArray(schema.required)) {
      for (const requiredField of schema.required) {
        if (!(requiredField in args)) {
          throw new Error(`Missing required argument: ${requiredField}`);
        }
      }
    }

    // Check property types
    if (schema.properties) {
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        if (propName in args) {
          await this.validateProperty(propName, args[propName], propSchema as any);
        }
      }
    }
  }

  /**
   * Validate individual property
   */
  private async validateProperty(name: string, value: any, schema: any): Promise<void> {
    if (!schema.type) {
      return;
    }

    const actualType = typeof value;
    const expectedType = schema.type;

    // Type mapping
    const typeMap: Record<string, string> = {
      string: 'string',
      number: 'number',
      integer: 'number',
      boolean: 'boolean',
      object: 'object',
      array: 'object'
    };

    if (expectedType === 'array' && !Array.isArray(value)) {
      throw new Error(`Argument '${name}' must be an array`);
    }

    if (expectedType !== 'array' && typeMap[expectedType] !== actualType) {
      throw new Error(`Argument '${name}' must be of type ${expectedType}, got ${actualType}`);
    }
  }

  /**
   * Get MCP function definition for registration
   */
  public getMCPFunctionDefinition() {
    return {
      name: this.config.functionName,
      description: this.config.description || `Workflow function: ${this.config.functionName}`,
      inputSchema: this.config.inputSchema,
      outputSchema: this.config.outputSchema
    };
  }
}
