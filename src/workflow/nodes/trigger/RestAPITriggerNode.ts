import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Configuration interface for REST API Trigger Node
 */
export interface IRestAPITriggerConfig {
  method: string;
  path: string;
  authentication: boolean;
  validation: boolean;
  headers?: Record<string, string>;
  queryParams?: Record<string, any>;
  bodySchema?: Record<string, any>;
}

/**
 * REST API Trigger Node - triggers workflow execution via HTTP endpoint
 */
@injectable()
export class RestAPITriggerNode extends BaseNode {
  protected config: IRestAPITriggerConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IRestAPITriggerConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.method) {
      throw new Error('REST API Trigger: method is required');
    }

    if (!this.config.path) {
      throw new Error('REST API Trigger: path is required');
    }

    // Validate HTTP method
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    if (!validMethods.includes(this.config.method.toUpperCase())) {
      throw new Error(
        `REST API Trigger: invalid method '${this.config.method}'. Valid methods: ${validMethods.join(', ')}`
      );
    }

    // Validate path format
    if (!this.config.path.startsWith('/')) {
      throw new Error('REST API Trigger: path must start with "/"');
    }
  }

  /**
   * Execute the trigger node
   * @param input Input data from HTTP request
   * @param context Workflow context
   * @returns Processed input data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing REST API Trigger node with input: ${JSON.stringify(input)}`);

    try {
      // Extract HTTP request data
      const requestData = {
        method: input.method || this.config.method,
        path: input.path || this.config.path,
        headers: input.headers || {},
        query: input.query || {},
        body: input.body || {},
        params: input.params || {}
      };

      // Validate request if validation is enabled
      if (this.config.validation) {
        await this.validateRequest(requestData);
      }

      // Process authentication if required
      if (this.config.authentication) {
        await this.authenticateRequest(requestData);
      }

      // Prepare output data
      const result = {
        trigger: 'rest-api',
        request: requestData,
        timestamp: new Date().toISOString(),
        workflowId: context.workflowId,
        executionId: context.executionId
      };

      this.logger.debug(
        `REST API Trigger node execution completed with result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`REST API Trigger node execution failed: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Validate incoming request
   */
  private async validateRequest(requestData: any): Promise<void> {
    // Validate method
    if (requestData.method.toUpperCase() !== this.config.method.toUpperCase()) {
      throw new Error(`Method mismatch: expected ${this.config.method}, got ${requestData.method}`);
    }

    // Validate body schema if provided
    if (this.config.bodySchema && requestData.body) {
      // Basic schema validation (can be extended with JSON Schema validator)
      const requiredFields = Object.keys(this.config.bodySchema);
      for (const field of requiredFields) {
        if (!(field in requestData.body)) {
          throw new Error(`Missing required field in body: ${field}`);
        }
      }
    }
  }

  /**
   * Authenticate incoming request
   */
  private async authenticateRequest(requestData: any): Promise<void> {
    // Basic authentication check
    const authHeader = requestData.headers.authorization || requestData.headers.Authorization;

    if (!authHeader) {
      throw new Error('Authentication required: missing Authorization header');
    }

    // TODO: Implement proper authentication logic
    // This is a placeholder for authentication validation
    this.logger.debug('Authentication check passed');
  }
}
