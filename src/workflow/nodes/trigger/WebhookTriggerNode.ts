import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';
import * as crypto from 'crypto';

/**
 * Configuration interface for Webhook Trigger Node
 */
export interface IWebhookTriggerConfig {
  url: string;
  secret: string;
  headers: Record<string, string>;
  signatureHeader?: string;
  signatureAlgorithm?: string;
  validateSignature?: boolean;
  allowedIPs?: string[];
}

/**
 * Webhook Trigger Node - triggers workflow execution via external webhook
 */
@injectable()
export class WebhookTriggerNode extends BaseNode {
  protected config: IWebhookTriggerConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IWebhookTriggerConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.url) {
      throw new Error('Webhook Trigger: url is required');
    }
    
    // Validate URL format
    try {
      new URL(this.config.url);
    } catch {
      throw new Error(`Webhook Trigger: invalid URL format '${this.config.url}'`);
    }
    
    // Validate signature algorithm if provided
    if (this.config.signatureAlgorithm) {
      const validAlgorithms = ['sha1', 'sha256', 'sha512'];
      if (!validAlgorithms.includes(this.config.signatureAlgorithm)) {
        throw new Error(`Webhook Trigger: invalid signature algorithm '${this.config.signatureAlgorithm}'. Valid algorithms: ${validAlgorithms.join(', ')}`);
      }
    }
  }

  /**
   * Execute the trigger node
   * @param input Input data from webhook request
   * @param context Workflow context
   * @returns Processed webhook data
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Webhook Trigger node with input: ${JSON.stringify(input)}`);

    try {
      // Extract webhook request data
      const webhookData = {
        url: this.config.url,
        headers: input.headers || {},
        body: input.body || {},
        query: input.query || {},
        method: input.method || 'POST',
        sourceIP: input.sourceIP || input.ip
      };

      // Validate source IP if allowed IPs are configured
      if (this.config.allowedIPs && this.config.allowedIPs.length > 0) {
        await this.validateSourceIP(webhookData.sourceIP);
      }

      // Validate webhook signature if configured
      if (this.config.validateSignature !== false && this.config.secret) {
        await this.validateSignature(webhookData);
      }

      // Process webhook headers
      const processedHeaders = this.processHeaders(webhookData.headers);

      // Prepare output data
      const result = {
        trigger: 'webhook',
        webhook: {
          url: this.config.url,
          method: webhookData.method,
          headers: processedHeaders,
          body: webhookData.body,
          query: webhookData.query,
          sourceIP: webhookData.sourceIP
        },
        timestamp: new Date().toISOString(),
        workflowId: context.workflowId,
        executionId: context.executionId
      };

      this.logger.debug(`Webhook Trigger node execution completed with result: ${JSON.stringify(result)}`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Webhook Trigger node execution failed: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Validate source IP address
   */
  private async validateSourceIP(sourceIP: string): Promise<void> {
    if (!sourceIP) {
      throw new Error('Webhook Trigger: source IP is required for IP validation');
    }

    if (!this.config.allowedIPs?.includes(sourceIP)) {
      throw new Error(`Webhook Trigger: source IP '${sourceIP}' is not in allowed IPs list`);
    }
  }

  /**
   * Validate webhook signature
   */
  private async validateSignature(webhookData: any): Promise<void> {
    const signatureHeader = this.config.signatureHeader || 'x-hub-signature-256';
    const algorithm = this.config.signatureAlgorithm || 'sha256';
    
    const receivedSignature = webhookData.headers[signatureHeader] || 
                             webhookData.headers[signatureHeader.toLowerCase()];

    if (!receivedSignature) {
      throw new Error(`Webhook Trigger: missing signature header '${signatureHeader}'`);
    }

    // Calculate expected signature
    const payload = typeof webhookData.body === 'string' 
      ? webhookData.body 
      : JSON.stringify(webhookData.body);
      
    const expectedSignature = this.calculateSignature(payload, this.config.secret, algorithm);

    // Compare signatures
    if (!this.compareSignatures(receivedSignature, expectedSignature)) {
      throw new Error('Webhook Trigger: signature validation failed');
    }
  }

  /**
   * Calculate HMAC signature
   */
  private calculateSignature(payload: string, secret: string, algorithm: string): string {
    const hmac = crypto.createHmac(algorithm, secret);
    hmac.update(payload);
    return `${algorithm}=${hmac.digest('hex')}`;
  }

  /**
   * Safely compare signatures to prevent timing attacks
   */
  private compareSignatures(received: string, expected: string): boolean {
    // Remove algorithm prefix if present
    const cleanReceived = received.replace(/^(sha1|sha256|sha512)=/, '');
    const cleanExpected = expected.replace(/^(sha1|sha256|sha512)=/, '');

    if (cleanReceived.length !== cleanExpected.length) {
      return false;
    }

    // Use crypto.timingSafeEqual for constant-time comparison
    try {
      return crypto.timingSafeEqual(
        Buffer.from(cleanReceived, 'hex'),
        Buffer.from(cleanExpected, 'hex')
      );
    } catch {
      return false;
    }
  }

  /**
   * Process and filter headers
   */
  private processHeaders(headers: Record<string, string>): Record<string, string> {
    const processedHeaders: Record<string, string> = {};
    
    // Copy configured headers
    for (const [key, value] of Object.entries(this.config.headers || {})) {
      processedHeaders[key.toLowerCase()] = value;
    }

    // Add relevant headers from request
    const relevantHeaders = [
      'content-type',
      'content-length',
      'user-agent',
      'x-forwarded-for',
      'x-real-ip'
    ];

    for (const [key, value] of Object.entries(headers)) {
      const lowerKey = key.toLowerCase();
      if (relevantHeaders.includes(lowerKey)) {
        processedHeaders[lowerKey] = value;
      }
    }

    return processedHeaders;
  }
}
