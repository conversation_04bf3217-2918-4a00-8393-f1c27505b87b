import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IConditionalProcessor } from '../../../core/interfaces/IConditionalProcessor';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';

/**
 * Switch case configuration
 */
export interface ISwitchCase {
  condition: string;
  output: any;
  label?: string;
}

/**
 * Configuration interface for Switch Logic Node
 */
export interface ISwitchLogicConfig {
  expression: string;
  cases: ISwitchCase[];
  defaultOutput?: any;
  evaluationType?: 'value' | 'condition';
  variables?: Record<string, any>;
}

/**
 * Switch Logic Node - multi-way branching based on expression evaluation
 */
@injectable()
export class SwitchLogicNode extends BaseNode {
  protected config: ISwitchLogicConfig;
  private conditionalProcessor: IConditionalProcessor;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   * @param conditionalProcessor Conditional processor for evaluating expressions
   */
  constructor(
    config: ISwitchLogicConfig,
    logger: ILogger,
    @inject(TYPES.ConditionalProcessor) conditionalProcessor: IConditionalProcessor
  ) {
    super(config, logger);
    this.config = config;
    this.conditionalProcessor = conditionalProcessor;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.expression) {
      throw new Error('Switch Logic: expression is required');
    }

    if (!this.config.cases || !Array.isArray(this.config.cases)) {
      throw new Error('Switch Logic: cases must be an array');
    }

    if (this.config.cases.length === 0) {
      throw new Error('Switch Logic: at least one case is required');
    }

    // Validate each case
    for (let i = 0; i < this.config.cases.length; i++) {
      const switchCase = this.config.cases[i];
      if (!switchCase.condition) {
        throw new Error(`Switch Logic: case ${i} condition is required`);
      }
      if (switchCase.output === undefined) {
        throw new Error(`Switch Logic: case ${i} output is required`);
      }
    }

    // Validate evaluation type
    if (this.config.evaluationType) {
      const validTypes = ['value', 'condition'];
      if (!validTypes.includes(this.config.evaluationType)) {
        throw new Error(
          `Switch Logic: invalid evaluationType '${this.config.evaluationType}'. Valid types: ${validTypes.join(', ')}`
        );
      }
    }
  }

  /**
   * Execute the logic node
   * @param input Input data
   * @param context Workflow context
   * @returns Result based on switch evaluation
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Switch Logic node with input: ${JSON.stringify(input)}`);

    try {
      // Prepare evaluation context
      const evaluationContext = this.prepareEvaluationContext(input, context);

      // Process expression with variable substitution
      const processedExpression = this.processExpression(this.config.expression, evaluationContext);

      // Evaluate expression
      const expressionValue = await this.evaluateExpression(processedExpression, evaluationContext);

      // Find matching case
      const matchedCase = await this.findMatchingCase(expressionValue, evaluationContext);

      // Get output (from matched case or default)
      const selectedOutput = matchedCase ? matchedCase.output : this.config.defaultOutput;

      if (selectedOutput === undefined) {
        throw new Error('Switch Logic: no matching case found and no default output specified');
      }

      // Process output with variable substitution
      const processedOutput = this.processOutput(selectedOutput, evaluationContext);

      // Prepare result
      const result = {
        expression: processedExpression,
        expressionValue: expressionValue,
        matchedCase: matchedCase
          ? {
              condition: matchedCase.condition,
              label: matchedCase.label,
              output: matchedCase.output
            }
          : null,
        output: processedOutput,
        metadata: {
          originalExpression: this.config.expression,
          evaluationType: this.config.evaluationType || 'value',
          totalCases: this.config.cases.length,
          hasDefault: this.config.defaultOutput !== undefined,
          timestamp: new Date().toISOString(),
          executionId: context.executionId
        }
      };

      this.logger.debug(
        `Switch Logic node execution completed with result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Switch Logic node execution failed: ${errorMessage}`);
      throw new Error(`Switch Logic execution failed: ${errorMessage}`);
    }
  }

  /**
   * Prepare evaluation context with all available variables
   */
  private prepareEvaluationContext(input: any, context: WorkflowContext): any {
    return {
      input: input,
      variables: { ...context.variables, ...this.config.variables },
      nodeResults: context.nodeResults,
      workflowContext: context,
      // Add utility functions
      utils: {
        isNull: (value: any) => value === null,
        isUndefined: (value: any) => value === undefined,
        isEmpty: (value: any) => value === null || value === undefined || value === '',
        isArray: (value: any) => Array.isArray(value),
        isObject: (value: any) =>
          typeof value === 'object' && value !== null && !Array.isArray(value),
        isString: (value: any) => typeof value === 'string',
        isNumber: (value: any) => typeof value === 'number',
        isBoolean: (value: any) => typeof value === 'boolean',
        toString: (value: any) => String(value),
        toNumber: (value: any) => Number(value),
        toLowerCase: (str: string) => (typeof str === 'string' ? str.toLowerCase() : str),
        toUpperCase: (str: string) => (typeof str === 'string' ? str.toUpperCase() : str)
      }
    };
  }

  /**
   * Process expression with variable substitution
   */
  private processExpression(expression: string, context: any): string {
    return expression.replace(/\${([^}]+)}/g, (match, varName) => {
      const value = this.getNestedProperty(context, varName);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Evaluate expression to get the switch value
   */
  private async evaluateExpression(expression: string, context: any): Promise<any> {
    try {
      // Try to get direct property value first
      const directValue = this.getNestedProperty(context, expression);
      if (directValue !== undefined) {
        return directValue;
      }

      // If not found, evaluate as expression
      return await this.conditionalProcessor.evaluate(expression, context);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.warn(`Expression evaluation failed, using literal value: ${errorMessage}`);
      return expression;
    }
  }

  /**
   * Find matching case based on evaluation type
   */
  private async findMatchingCase(expressionValue: any, context: any): Promise<ISwitchCase | null> {
    const evaluationType = this.config.evaluationType || 'value';

    for (const switchCase of this.config.cases) {
      const isMatch = await this.evaluateCase(switchCase, expressionValue, context, evaluationType);
      if (isMatch) {
        return switchCase;
      }
    }

    return null;
  }

  /**
   * Evaluate if a case matches based on evaluation type
   */
  private async evaluateCase(
    switchCase: ISwitchCase,
    expressionValue: any,
    context: any,
    evaluationType: string
  ): Promise<boolean> {
    const processedCondition = this.processExpression(switchCase.condition, context);

    if (evaluationType === 'value') {
      // Direct value comparison
      return this.compareValues(expressionValue, processedCondition);
    } else {
      // Condition evaluation
      const conditionContext = { ...context, switchValue: expressionValue };
      try {
        return await this.conditionalProcessor.evaluate(processedCondition, conditionContext);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.logger.warn(`Case condition evaluation failed: ${errorMessage}`);
        return false;
      }
    }
  }

  /**
   * Compare values for equality (with type coercion)
   */
  private compareValues(value1: any, value2: any): boolean {
    // Try exact equality first
    if (value1 === value2) {
      return true;
    }

    // Try loose equality
    if (value1 == value2) {
      return true;
    }

    // Try string comparison
    if (String(value1) === String(value2)) {
      return true;
    }

    return false;
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Process output with variable substitution
   */
  private processOutput(output: any, context: any): any {
    if (typeof output === 'string') {
      return output.replace(/\${([^}]+)}/g, (match, varName) => {
        const value = this.getNestedProperty(context, varName);
        return value !== undefined ? String(value) : match;
      });
    }

    if (typeof output === 'object' && output !== null) {
      return this.processObjectWithVariables(output, context);
    }

    return output;
  }

  /**
   * Process object recursively with variable substitution
   */
  private processObjectWithVariables(obj: any, context: any): any {
    if (typeof obj === 'string') {
      return this.processOutput(obj, context);
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.processObjectWithVariables(item, context));
    }

    if (typeof obj === 'object' && obj !== null) {
      const processedObj: any = {};
      for (const [key, value] of Object.entries(obj)) {
        processedObj[key] = this.processObjectWithVariables(value, context);
      }
      return processedObj;
    }

    return obj;
  }

  /**
   * Get execution statistics
   */
  public getExecutionStats() {
    return {
      expression: this.config.expression,
      caseCount: this.config.cases.length,
      hasDefault: this.config.defaultOutput !== undefined,
      evaluationType: this.config.evaluationType || 'value',
      hasVariables: Object.keys(this.config.variables || {}).length > 0
    };
  }
}
