import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IConditionalProcessor } from '../../../core/interfaces/IConditionalProcessor';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';

/**
 * Configuration interface for IF/ELSE Logic Node
 */
export interface IIfElseLogicConfig {
  condition: string;
  trueOutput: any;
  falseOutput: any;
  conditionType?: 'expression' | 'javascript' | 'simple';
  variables?: Record<string, any>;
}

/**
 * IF/ELSE Logic Node - conditional branching based on expression
 */
@injectable()
export class IfElseLogicNode extends BaseNode {
  protected config: IIfElseLogicConfig;
  private conditionalProcessor: IConditionalProcessor;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   * @param conditionalProcessor Conditional processor for evaluating expressions
   */
  constructor(
    config: IIfElseLogicConfig,
    logger: ILogger,
    @inject(TYPES.ConditionalProcessor) conditionalProcessor: IConditionalProcessor
  ) {
    super(config, logger);
    this.config = config;
    this.conditionalProcessor = conditionalProcessor;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.condition) {
      throw new Error('IF/ELSE Logic: condition is required');
    }

    if (typeof this.config.condition !== 'string') {
      throw new Error('IF/ELSE Logic: condition must be a string');
    }

    if (this.config.trueOutput === undefined) {
      throw new Error('IF/ELSE Logic: trueOutput is required');
    }

    if (this.config.falseOutput === undefined) {
      throw new Error('IF/ELSE Logic: falseOutput is required');
    }

    // Validate condition type
    if (this.config.conditionType) {
      const validTypes = ['expression', 'javascript', 'simple'];
      if (!validTypes.includes(this.config.conditionType)) {
        throw new Error(
          `IF/ELSE Logic: invalid conditionType '${this.config.conditionType}'. Valid types: ${validTypes.join(', ')}`
        );
      }
    }
  }

  /**
   * Execute the logic node
   * @param input Input data
   * @param context Workflow context
   * @returns Result based on condition evaluation
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing IF/ELSE Logic node with input: ${JSON.stringify(input)}`);

    try {
      // Prepare evaluation context
      const evaluationContext = this.prepareEvaluationContext(input, context);

      // Process condition with variable substitution
      const processedCondition = this.processCondition(this.config.condition, evaluationContext);

      // Evaluate condition
      const conditionResult = await this.evaluateCondition(processedCondition, evaluationContext);

      // Select output based on condition result
      const selectedOutput = conditionResult ? this.config.trueOutput : this.config.falseOutput;

      // Process output with variable substitution
      const processedOutput = this.processOutput(selectedOutput, evaluationContext);

      // Prepare result
      const result = {
        condition: processedCondition,
        conditionResult: conditionResult,
        selectedBranch: conditionResult ? 'true' : 'false',
        output: processedOutput,
        metadata: {
          originalCondition: this.config.condition,
          conditionType: this.config.conditionType || 'expression',
          timestamp: new Date().toISOString(),
          executionId: context.executionId
        }
      };

      this.logger.debug(
        `IF/ELSE Logic node execution completed with result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`IF/ELSE Logic node execution failed: ${errorMessage}`);
      throw new Error(`IF/ELSE Logic execution failed: ${errorMessage}`);
    }
  }

  /**
   * Prepare evaluation context with all available variables
   */
  private prepareEvaluationContext(input: any, context: WorkflowContext): any {
    return {
      input: input,
      variables: { ...context.variables, ...this.config.variables },
      nodeResults: context.nodeResults,
      workflowContext: context,
      // Add utility functions
      utils: {
        isNull: (value: any) => value === null,
        isUndefined: (value: any) => value === undefined,
        isEmpty: (value: any) => value === null || value === undefined || value === '',
        isArray: (value: any) => Array.isArray(value),
        isObject: (value: any) =>
          typeof value === 'object' && value !== null && !Array.isArray(value),
        isString: (value: any) => typeof value === 'string',
        isNumber: (value: any) => typeof value === 'number',
        isBoolean: (value: any) => typeof value === 'boolean',
        length: (value: any) => value?.length || 0,
        includes: (array: any[], item: any) => Array.isArray(array) && array.includes(item),
        startsWith: (str: string, prefix: string) =>
          typeof str === 'string' && str.startsWith(prefix),
        endsWith: (str: string, suffix: string) => typeof str === 'string' && str.endsWith(suffix),
        regex: (str: string, pattern: string, flags?: string) =>
          new RegExp(pattern, flags).test(str)
      }
    };
  }

  /**
   * Process condition with variable substitution
   */
  private processCondition(condition: string, context: any): string {
    return condition.replace(/\${([^}]+)}/g, (match, varName) => {
      const value = this.getNestedProperty(context, varName);
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Evaluate condition based on condition type
   */
  private async evaluateCondition(condition: string, context: any): Promise<boolean> {
    const conditionType = this.config.conditionType || 'expression';

    switch (conditionType) {
      case 'simple':
        return this.evaluateSimpleCondition(condition, context);

      case 'javascript':
        return this.evaluateJavaScriptCondition(condition, context);

      case 'expression':
      default:
        return this.conditionalProcessor.evaluate(condition, context);
    }
  }

  /**
   * Evaluate simple condition (basic comparisons)
   */
  private evaluateSimpleCondition(condition: string, context: any): boolean {
    // Simple condition patterns like "input.value > 10", "status == 'active'"
    const operators = ['>=', '<=', '!=', '==', '>', '<'];

    for (const op of operators) {
      if (condition.includes(op)) {
        const [left, right] = condition.split(op).map((s) => s.trim());
        const leftValue = this.getNestedProperty(context, left);
        const rightValue = this.parseValue(right, context);

        return this.compareValues(leftValue, rightValue, op);
      }
    }

    // If no operator found, treat as boolean expression
    const value = this.getNestedProperty(context, condition);
    return Boolean(value);
  }

  /**
   * Evaluate JavaScript condition
   */
  private evaluateJavaScriptCondition(condition: string, context: any): boolean {
    try {
      // Create a safe evaluation function
      const func = new Function(
        'context',
        `
        const { input, variables, nodeResults, utils } = context;
        return Boolean(${condition});
      `
      );

      return func(context);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.warn(`JavaScript condition evaluation failed: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Compare two values using the specified operator
   */
  private compareValues(left: any, right: any, operator: string): boolean {
    switch (operator) {
      case '==':
        return left == right;
      case '!=':
        return left != right;
      case '>':
        return left > right;
      case '<':
        return left < right;
      case '>=':
        return left >= right;
      case '<=':
        return left <= right;
      default:
        return false;
    }
  }

  /**
   * Parse value from string, considering context variables
   */
  private parseValue(value: string, context: any): any {
    // Remove quotes if present
    if (
      (value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))
    ) {
      return value.slice(1, -1);
    }

    // Check if it's a number
    if (!isNaN(Number(value))) {
      return Number(value);
    }

    // Check if it's a boolean
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value === 'null') return null;
    if (value === 'undefined') return undefined;

    // Try to get from context
    return this.getNestedProperty(context, value) || value;
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Process output with variable substitution
   */
  private processOutput(output: any, context: any): any {
    if (typeof output === 'string') {
      return output.replace(/\${([^}]+)}/g, (match, varName) => {
        const value = this.getNestedProperty(context, varName);
        return value !== undefined ? String(value) : match;
      });
    }

    if (typeof output === 'object' && output !== null) {
      return this.processObjectWithVariables(output, context);
    }

    return output;
  }

  /**
   * Process object recursively with variable substitution
   */
  private processObjectWithVariables(obj: any, context: any): any {
    if (typeof obj === 'string') {
      return this.processOutput(obj, context);
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.processObjectWithVariables(item, context));
    }

    if (typeof obj === 'object' && obj !== null) {
      const processedObj: any = {};
      for (const [key, value] of Object.entries(obj)) {
        processedObj[key] = this.processObjectWithVariables(value, context);
      }
      return processedObj;
    }

    return obj;
  }
}
