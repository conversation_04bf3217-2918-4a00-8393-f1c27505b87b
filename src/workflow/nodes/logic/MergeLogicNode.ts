import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { injectable } from 'inversify';

/**
 * Merge strategy configuration
 */
export interface IMergeStrategy {
  type: 'concat' | 'merge' | 'override' | 'custom';
  customFunction?: string; // JavaScript function for custom merge
}

/**
 * Configuration interface for Merge Logic Node
 */
export interface IMergeLogicConfig {
  inputs: string[]; // Array of input sources (node results, variables, etc.)
  strategy: IMergeStrategy;
  outputKey?: string;
  filterEmpty?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  variables?: Record<string, any>;
}

/**
 * Merge Logic Node - combines multiple inputs using specified strategy
 */
@injectable()
export class MergeLogicNode extends BaseNode {
  protected config: IMergeLogicConfig;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   */
  constructor(config: IMergeLogicConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.inputs || !Array.isArray(this.config.inputs)) {
      throw new Error('Merge Logic: inputs must be an array');
    }

    if (this.config.inputs.length === 0) {
      throw new Error('Merge Logic: at least one input is required');
    }

    if (!this.config.strategy || !this.config.strategy.type) {
      throw new Error('Merge Logic: strategy.type is required');
    }

    // Validate strategy type
    const validTypes = ['concat', 'merge', 'override', 'custom'];
    if (!validTypes.includes(this.config.strategy.type)) {
      throw new Error(
        `Merge Logic: invalid strategy type '${this.config.strategy.type}'. Valid types: ${validTypes.join(', ')}`
      );
    }

    // Validate custom function if strategy is custom
    if (this.config.strategy.type === 'custom' && !this.config.strategy.customFunction) {
      throw new Error('Merge Logic: customFunction is required for custom strategy');
    }

    // Validate sort order
    if (this.config.sortOrder && !['asc', 'desc'].includes(this.config.sortOrder)) {
      throw new Error('Merge Logic: sortOrder must be "asc" or "desc"');
    }
  }

  /**
   * Execute the logic node
   * @param input Input data
   * @param context Workflow context
   * @returns Merged result
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Merge Logic node with input: ${JSON.stringify(input)}`);

    try {
      // Prepare evaluation context
      const evaluationContext = this.prepareEvaluationContext(input, context);

      // Collect input values
      const inputValues = await this.collectInputValues(evaluationContext);

      // Filter empty values if configured
      const filteredValues = this.config.filterEmpty
        ? this.filterEmptyValues(inputValues)
        : inputValues;

      // Apply merge strategy
      const mergedResult = await this.applyMergeStrategy(filteredValues, evaluationContext);

      // Sort result if configured
      const sortedResult = this.config.sortBy
        ? this.sortResult(mergedResult, this.config.sortBy, this.config.sortOrder || 'asc')
        : mergedResult;

      // Prepare final result
      const result = {
        inputs: this.config.inputs,
        inputValues: inputValues,
        filteredValues: filteredValues,
        mergedResult: sortedResult,
        metadata: {
          strategy: this.config.strategy.type,
          inputCount: inputValues.length,
          filteredCount: filteredValues.length,
          filterEmpty: this.config.filterEmpty || false,
          sortBy: this.config.sortBy,
          sortOrder: this.config.sortOrder,
          timestamp: new Date().toISOString(),
          executionId: context.executionId
        }
      };

      // Return specific output key if configured
      if (this.config.outputKey) {
        return { [this.config.outputKey]: sortedResult };
      }

      this.logger.debug(
        `Merge Logic node execution completed with result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Merge Logic node execution failed: ${errorMessage}`);
      throw new Error(`Merge Logic execution failed: ${errorMessage}`);
    }
  }

  /**
   * Prepare evaluation context with all available variables
   */
  private prepareEvaluationContext(input: any, context: WorkflowContext): any {
    return {
      input: input,
      variables: { ...context.variables, ...this.config.variables },
      nodeResults: context.nodeResults,
      workflowContext: context
    };
  }

  /**
   * Get value from input source
   */
  private getInputValue(inputSource: string, context: any): any {
    // Process variable substitution
    const processedSource = inputSource.replace(/\${([^}]+)}/g, (match, varName) => {
      const value = this.getNestedProperty(context, varName);
      return value !== undefined ? String(value) : match;
    });

    // Get value from context
    return this.getNestedProperty(context, processedSource);
  }

  /**
   * Filter out empty values
   */
  private filterEmptyValues(values: any[]): any[] {
    return values.filter((value) => {
      if (value === null || value === undefined) return false;
      if (typeof value === 'string' && value.trim() === '') return false;
      if (Array.isArray(value) && value.length === 0) return false;
      if (typeof value === 'object' && Object.keys(value).length === 0) return false;
      return true;
    });
  }

  /**
   * Apply merge strategy to combine values
   */
  private async applyMergeStrategy(values: any[], context: any): Promise<any> {
    switch (this.config.strategy.type) {
      case 'concat':
        return this.concatStrategy(values);

      case 'merge':
        return this.mergeStrategy(values);

      case 'override':
        return this.overrideStrategy(values);

      case 'custom':
        return this.customStrategy(values, context);

      default:
        throw new Error(`Unsupported merge strategy: ${this.config.strategy.type}`);
    }
  }

  /**
   * Concatenation strategy - combines arrays or strings
   */
  private concatStrategy(values: any[]): any {
    const nonNullValues = values.filter((v) => v !== null && v !== undefined);

    if (nonNullValues.length === 0) {
      return [];
    }

    // If all values are arrays, concatenate them
    if (nonNullValues.every((v) => Array.isArray(v))) {
      return nonNullValues.reduce((acc, arr) => acc.concat(arr), []);
    }

    // If all values are strings, concatenate them
    if (nonNullValues.every((v) => typeof v === 'string')) {
      return nonNullValues.join('');
    }

    // Otherwise, create an array of all values
    return nonNullValues;
  }

  /**
   * Merge strategy - deep merge objects or combine arrays
   */
  private mergeStrategy(values: any[]): any {
    const nonNullValues = values.filter((v) => v !== null && v !== undefined);

    if (nonNullValues.length === 0) {
      return {};
    }

    // If all values are objects, deep merge them
    if (nonNullValues.every((v) => typeof v === 'object' && !Array.isArray(v))) {
      return this.deepMergeObjects(nonNullValues);
    }

    // If all values are arrays, merge unique values
    if (nonNullValues.every((v) => Array.isArray(v))) {
      const merged = nonNullValues.reduce((acc, arr) => acc.concat(arr), []);
      return [...new Set(merged)]; // Remove duplicates
    }

    // Otherwise, return as object with indexed keys
    const result: any = {};
    nonNullValues.forEach((value, index) => {
      result[`input_${index}`] = value;
    });
    return result;
  }

  /**
   * Override strategy - last non-null value wins
   */
  private overrideStrategy(values: any[]): any {
    for (let i = values.length - 1; i >= 0; i--) {
      if (values[i] !== null && values[i] !== undefined) {
        return values[i];
      }
    }
    return null;
  }

  /**
   * Custom strategy - execute custom JavaScript function
   */
  private customStrategy(values: any[], context: any): any {
    try {
      const customFunction = this.config.strategy.customFunction!;

      // Create a safe evaluation function
      const func = new Function(
        'values',
        'context',
        `
        const { input, variables, nodeResults } = context;
        return (${customFunction})(values, context);
      `
      );

      return func(values, context);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Custom merge function execution failed: ${errorMessage}`);
      throw new Error(`Custom merge function failed: ${errorMessage}`);
    }
  }

  /**
   * Collect values from all specified inputs
   */
  private async collectInputValues(context: any): Promise<any[]> {
    const values: any[] = [];

    for (const inputSource of this.config.inputs) {
      try {
        const value = this.getInputValue(inputSource, context);
        values.push(value);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.logger.warn(`Failed to collect input from '${inputSource}': ${errorMessage}`);
        values.push(null);
      }
    }

    return values;
  }

  /**
   * Deep merge objects
   */
  private deepMergeObjects(objects: any[]): any {
    const result: any = {};

    for (const obj of objects) {
      for (const [key, value] of Object.entries(obj)) {
        if (
          typeof value === 'object' &&
          value !== null &&
          !Array.isArray(value) &&
          typeof result[key] === 'object' &&
          result[key] !== null &&
          !Array.isArray(result[key])
        ) {
          result[key] = this.deepMergeObjects([result[key], value]);
        } else {
          result[key] = value;
        }
      }
    }

    return result;
  }

  /**
   * Sort result if it's an array
   */
  private sortResult(result: any, sortBy: string, sortOrder: 'asc' | 'desc'): any {
    if (!Array.isArray(result)) {
      return result;
    }

    return result.sort((a, b) => {
      const aValue = this.getNestedProperty({ item: a }, `item.${sortBy}`) || a;
      const bValue = this.getNestedProperty({ item: b }, `item.${sortBy}`) || b;

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get execution statistics
   */
  public getExecutionStats() {
    return {
      inputCount: this.config.inputs.length,
      strategy: this.config.strategy.type,
      hasCustomFunction: this.config.strategy.type === 'custom',
      filterEmpty: this.config.filterEmpty || false,
      hasSorting: !!this.config.sortBy,
      sortBy: this.config.sortBy,
      sortOrder: this.config.sortOrder,
      outputKey: this.config.outputKey,
      hasVariables: Object.keys(this.config.variables || {}).length > 0
    };
  }
}
