import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IConditionalProcessor } from '../../../core/interfaces/IConditionalProcessor';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';

/**
 * Configuration interface for Loop Logic Node
 */
export interface ILoopLogicConfig {
  type: 'forEach' | 'while' | 'for';
  condition?: string; // For while loops
  array?: string; // For forEach loops - path to array
  start?: number; // For for loops
  end?: number; // For for loops
  step?: number; // For for loops
  maxIterations: number;
  itemVariable?: string; // Variable name for current item (forEach)
  indexVariable?: string; // Variable name for current index
  breakCondition?: string; // Optional break condition
  continueCondition?: string; // Optional continue condition
  variables?: Record<string, any>;
}

/**
 * Loop Logic Node - iterates over arrays or repeats execution
 */
@injectable()
export class LoopLogicNode extends BaseNode {
  protected config: ILoopLogicConfig;
  private conditionalProcessor: IConditionalProcessor;

  /**
   * Constructor
   * @param config Node configuration
   * @param logger Logger
   * @param conditionalProcessor Conditional processor for evaluating expressions
   */
  constructor(
    config: ILoopLogicConfig,
    logger: ILogger,
    @inject(TYPES.ConditionalProcessor) conditionalProcessor: IConditionalProcessor
  ) {
    super(config, logger);
    this.config = config;
    this.conditionalProcessor = conditionalProcessor;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.type) {
      throw new Error('Loop Logic: type is required');
    }

    const validTypes = ['forEach', 'while', 'for'];
    if (!validTypes.includes(this.config.type)) {
      throw new Error(
        `Loop Logic: invalid type '${this.config.type}'. Valid types: ${validTypes.join(', ')}`
      );
    }

    if (typeof this.config.maxIterations !== 'number' || this.config.maxIterations <= 0) {
      throw new Error('Loop Logic: maxIterations must be a positive number');
    }

    // Type-specific validation
    this.validateTypeSpecificConfig();
  }

  /**
   * Validate type-specific configuration
   */
  private validateTypeSpecificConfig(): void {
    switch (this.config.type) {
      case 'forEach':
        if (!this.config.array) {
          throw new Error('Loop Logic: array is required for forEach type');
        }
        break;

      case 'while':
        if (!this.config.condition) {
          throw new Error('Loop Logic: condition is required for while type');
        }
        break;

      case 'for':
        if (typeof this.config.start !== 'number') {
          throw new Error('Loop Logic: start is required for for type');
        }
        if (typeof this.config.end !== 'number') {
          throw new Error('Loop Logic: end is required for for type');
        }
        if (this.config.step && typeof this.config.step !== 'number') {
          throw new Error('Loop Logic: step must be a number');
        }
        break;
    }
  }

  /**
   * Execute the logic node
   * @param input Input data
   * @param context Workflow context
   * @returns Loop execution results
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Loop Logic node with input: ${JSON.stringify(input)}`);

    try {
      // Prepare evaluation context
      const evaluationContext = this.prepareEvaluationContext(input, context);

      // Execute loop based on type
      const loopResults = await this.executeLoop(evaluationContext);

      // Prepare final result
      const result = {
        loopType: this.config.type,
        iterations: loopResults.iterations,
        results: loopResults.results,
        completed: loopResults.completed,
        breakReason: loopResults.breakReason,
        metadata: {
          maxIterations: this.config.maxIterations,
          actualIterations: loopResults.iterations.length,
          executionTime: loopResults.executionTime,
          timestamp: new Date().toISOString(),
          executionId: context.executionId
        }
      };

      this.logger.debug(
        `Loop Logic node execution completed with result: ${JSON.stringify(result)}`
      );
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Loop Logic node execution failed: ${errorMessage}`);
      throw new Error(`Loop Logic execution failed: ${errorMessage}`);
    }
  }

  /**
   * Prepare evaluation context with all available variables
   */
  private prepareEvaluationContext(input: any, context: WorkflowContext): any {
    return {
      input: input,
      variables: { ...context.variables, ...this.config.variables },
      nodeResults: context.nodeResults,
      workflowContext: context
    };
  }

  /**
   * Execute loop based on configured type
   */
  private async executeLoop(context: any): Promise<any> {
    const startTime = Date.now();

    switch (this.config.type) {
      case 'forEach':
        return await this.executeForEachLoop(context, startTime);

      case 'while':
        return await this.executeWhileLoop(context, startTime);

      case 'for':
        return await this.executeForLoop(context, startTime);

      default:
        throw new Error(`Unsupported loop type: ${this.config.type}`);
    }
  }

  /**
   * Execute forEach loop
   */
  private async executeForEachLoop(context: any, startTime: number): Promise<any> {
    const arrayPath = this.config.array!;
    const array = this.getNestedProperty(context, arrayPath);

    if (!Array.isArray(array)) {
      throw new Error(`Loop Logic: array at path '${arrayPath}' is not an array`);
    }

    const iterations: any[] = [];
    const results: any[] = [];
    let breakReason: string | null = null;

    for (let i = 0; i < array.length && i < this.config.maxIterations; i++) {
      const item = array[i];

      // Create iteration context
      const iterationContext = {
        ...context,
        [this.config.itemVariable || 'item']: item,
        [this.config.indexVariable || 'index']: i
      };

      // Check continue condition
      if (this.config.continueCondition) {
        const shouldContinue = await this.evaluateCondition(
          this.config.continueCondition,
          iterationContext
        );
        if (!shouldContinue) {
          iterations.push({ index: i, item, skipped: true });
          continue;
        }
      }

      // Execute iteration
      const iterationResult = await this.executeIteration(iterationContext, i);
      iterations.push({ index: i, item, result: iterationResult });
      results.push(iterationResult);

      // Check break condition
      if (this.config.breakCondition) {
        const shouldBreak = await this.evaluateCondition(
          this.config.breakCondition,
          iterationContext
        );
        if (shouldBreak) {
          breakReason = 'break condition met';
          break;
        }
      }
    }

    return {
      iterations,
      results,
      completed: breakReason === null && iterations.length === array.length,
      breakReason,
      executionTime: Date.now() - startTime
    };
  }

  /**
   * Execute while loop
   */
  private async executeWhileLoop(context: any, startTime: number): Promise<any> {
    const iterations: any[] = [];
    const results: any[] = [];
    let breakReason: string | null = null;
    let iterationCount = 0;

    while (iterationCount < this.config.maxIterations) {
      // Create iteration context
      const iterationContext = {
        ...context,
        [this.config.indexVariable || 'index']: iterationCount
      };

      // Check while condition
      const shouldContinue = await this.evaluateCondition(this.config.condition!, iterationContext);
      if (!shouldContinue) {
        breakReason = 'while condition false';
        break;
      }

      // Check continue condition
      if (this.config.continueCondition) {
        const shouldContinueIteration = await this.evaluateCondition(
          this.config.continueCondition,
          iterationContext
        );
        if (!shouldContinueIteration) {
          iterations.push({ index: iterationCount, skipped: true });
          iterationCount++;
          continue;
        }
      }

      // Execute iteration
      const iterationResult = await this.executeIteration(iterationContext, iterationCount);
      iterations.push({ index: iterationCount, result: iterationResult });
      results.push(iterationResult);

      // Check break condition
      if (this.config.breakCondition) {
        const shouldBreak = await this.evaluateCondition(
          this.config.breakCondition,
          iterationContext
        );
        if (shouldBreak) {
          breakReason = 'break condition met';
          break;
        }
      }

      iterationCount++;
    }

    if (iterationCount >= this.config.maxIterations) {
      breakReason = 'max iterations reached';
    }

    return {
      iterations,
      results,
      completed: breakReason === 'while condition false',
      breakReason,
      executionTime: Date.now() - startTime
    };
  }

  /**
   * Execute for loop
   */
  private async executeForLoop(context: any, startTime: number): Promise<any> {
    const start = this.config.start!;
    const end = this.config.end!;
    const step = this.config.step || 1;

    const iterations: any[] = [];
    const results: any[] = [];
    let breakReason: string | null = null;
    let iterationCount = 0;

    for (
      let i = start;
      (step > 0 ? i < end : i > end) && iterationCount < this.config.maxIterations;
      i += step
    ) {
      // Create iteration context
      const iterationContext = {
        ...context,
        [this.config.indexVariable || 'index']: i,
        iteration: iterationCount
      };

      // Check continue condition
      if (this.config.continueCondition) {
        const shouldContinue = await this.evaluateCondition(
          this.config.continueCondition,
          iterationContext
        );
        if (!shouldContinue) {
          iterations.push({ index: i, iteration: iterationCount, skipped: true });
          iterationCount++;
          continue;
        }
      }

      // Execute iteration
      const iterationResult = await this.executeIteration(iterationContext, i);
      iterations.push({ index: i, iteration: iterationCount, result: iterationResult });
      results.push(iterationResult);

      // Check break condition
      if (this.config.breakCondition) {
        const shouldBreak = await this.evaluateCondition(
          this.config.breakCondition,
          iterationContext
        );
        if (shouldBreak) {
          breakReason = 'break condition met';
          break;
        }
      }

      iterationCount++;
    }

    if (iterationCount >= this.config.maxIterations) {
      breakReason = 'max iterations reached';
    }

    return {
      iterations,
      results,
      completed: breakReason === null,
      breakReason,
      executionTime: Date.now() - startTime
    };
  }

  /**
   * Execute single iteration
   */
  private async executeIteration(context: any, index: number): Promise<any> {
    // For now, just return the context data
    // In a real implementation, this would execute child nodes or workflow steps
    return {
      index,
      timestamp: new Date().toISOString(),
      context: {
        variables: context.variables,
        input: context.input
      }
    };
  }

  /**
   * Evaluate condition
   */
  private async evaluateCondition(condition: string, context: any): Promise<boolean> {
    try {
      const processedCondition = condition.replace(/\${([^}]+)}/g, (match, varName) => {
        const value = this.getNestedProperty(context, varName);
        return value !== undefined ? String(value) : match;
      });

      return await this.conditionalProcessor.evaluate(processedCondition, context);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.warn(`Condition evaluation failed: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Get nested property from object using dot notation
   */
  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get execution statistics
   */
  public getExecutionStats() {
    return {
      type: this.config.type,
      maxIterations: this.config.maxIterations,
      hasCondition: !!this.config.condition,
      hasArray: !!this.config.array,
      hasBreakCondition: !!this.config.breakCondition,
      hasContinueCondition: !!this.config.continueCondition,
      itemVariable: this.config.itemVariable || 'item',
      indexVariable: this.config.indexVariable || 'index',
      hasVariables: Object.keys(this.config.variables || {}).length > 0
    };
  }
}
