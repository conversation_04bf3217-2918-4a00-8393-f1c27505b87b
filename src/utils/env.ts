import * as dotenv from 'dotenv';
import * as path from 'path';

// Determine environment
const nodeEnv = process.env.NODE_ENV || 'development';

// Load environment variables from .env file
// First load default .env file
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

// Then load environment-specific .env file (e.g. .env.development)
dotenv.config({
  path: path.resolve(process.cwd(), `.env.${nodeEnv}`),
  override: true // Override any variables already set
});

/**
 * Get environment variable
 * @param key Environment variable key
 * @param defaultValue Default value if not found
 */
export function getEnv(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is not defined`);
  }
  return value;
}

/**
 * Get environment variable as number
 * @param key Environment variable key
 * @param defaultValue Default value if not found
 */
export function getEnvNumber(key: string, defaultValue?: number): number {
  const value = getEnv(key, defaultValue?.toString());
  const num = Number(value);
  if (isNaN(num)) {
    throw new Error(`Environment variable ${key} is not a number`);
  }
  return num;
}

/**
 * Get environment variable as boolean
 * @param key Environment variable key
 * @param defaultValue Default value if not found
 */
export function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = getEnv(key, defaultValue?.toString());
  return value.toLowerCase() === 'true';
}
