import { injectable } from 'inversify';
import pino from 'pino';
import { ILogger } from '../core/interfaces/ILogger';

/**
 * Pino-based logger implementation
 */
@injectable()
export class PinoLogger implements ILogger {
  private logger: pino.Logger;

  constructor() {
    this.logger = pino({
      level: process.env.LOG_LEVEL || 'info',
      transport: process.env.NODE_ENV !== 'production'
        ? { target: 'pino-pretty' }
        : undefined,
    });
  }

  debug(message: string, ...args: any[]): void {
    this.logger.debug(args.length ? { args } : {}, message);
  }

  info(message: string, ...args: any[]): void {
    this.logger.info(args.length ? { args } : {}, message);
  }

  warn(message: string, ...args: any[]): void {
    this.logger.warn(args.length ? { args } : {}, message);
  }

  error(message: string, ...args: any[]): void {
    this.logger.error(args.length ? { args } : {}, message);
  }

  fatal(message: string, ...args: any[]): void {
    this.logger.fatal(args.length ? { args } : {}, message);
  }

  trace(message: string, ...args: any[]): void {
    this.logger.trace(args.length ? { args } : {}, message);
  }

  child(bindings: Record<string, any>): ILogger {
    const childLogger = new PinoLogger();
    childLogger.logger = this.logger.child(bindings);
    return childLogger;
  }
}
