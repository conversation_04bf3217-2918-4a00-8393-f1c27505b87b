/**
 * Dependency injection types
 */
export const TYPES = {
  // Container
  Container: Symbol.for('Container'),

  // Core services
  Logger: Symbol.for('Logger'),
  Database: Symbol.for('Database'),
  ScriptEngine: Symbol.for('ScriptEngine'),

  // MCP services
  MCPServer: Symbol.for('MCPServer'),
  MessageRouter: Symbol.for('MessageRouter'),
  ServiceLoader: Symbol.for('ServiceLoader'),
  DynamicServiceLoader: Symbol.for('DynamicServiceLoader'),

  // Workflow engine
  WorkflowEngine: Symbol.for('WorkflowEngine'),
  NodeRegistry: Symbol.for('NodeRegistry'),
  WorkflowMemory: Symbol.for('WorkflowMemory'),
  RedisWorkflowMemory: Symbol.for('RedisWorkflowMemory'),

  // Phase 2: Advanced workflow components
  ExecutionCoordinator: Symbol.for('ExecutionCoordinator'),
  ParallelExecutor: Symbol.for('ParallelExecutor'),
  ConditionalProcessor: Symbol.for('ConditionalProcessor'),
  DataTransformer: Symbol.for('DataTransformer'),
  WorkflowVersionManager: Symbol.for('WorkflowVersionManager'),

  // External services
  RedisClient: Symbol.for('RedisClient'),
  ObservabilityManager: Symbol.for('ObservabilityManager'),
  ErrorHandler: Symbol.for('ErrorHandler'),
  JSONPathService: Symbol.for('JSONPathService'),
  WebSocketService: Symbol.for('WebSocketService'),

  // Repositories
  WorkflowRepository: Symbol.for('WorkflowRepository'),
  WorkflowExecutionRepository: Symbol.for('WorkflowExecutionRepository'),
  WorkflowNodeExecutionRepository: Symbol.for('WorkflowNodeExecutionRepository'),
  NodeConfigRepository: Symbol.for('NodeConfigRepository'),
  DataSourceRepository: Symbol.for('DataSourceRepository'),
  MCPFunctionRepository: Symbol.for('MCPFunctionRepository'),

  // Controllers
  WorkflowController: Symbol.for('WorkflowController'),
  NodeController: Symbol.for('NodeController'),
  DataSourceController: Symbol.for('DataSourceController'),
  MCPFunctionController: Symbol.for('MCPFunctionController'),

  // Validators
  WorkflowValidator: Symbol.for('WorkflowValidator'),
  NodeValidator: Symbol.for('NodeValidator'),
  DataSourceValidator: Symbol.for('DataSourceValidator'),
  MCPFunctionValidator: Symbol.for('MCPFunctionValidator'),

  // Config Watcher
  WorkflowConfigWatcher: Symbol.for('WorkflowConfigWatcher'),

  // Admin API
  AdminAPI: Symbol.for('AdminAPI'),

  // Phase 3: Multi-tenancy
  TenantManager: Symbol.for('TenantManager'),
  TenantContext: Symbol.for('TenantContext'),
  TenantResolver: Symbol.for('TenantResolver'),
  TenantIsolation: Symbol.for('TenantIsolation'),
  TenantRepository: Symbol.for('TenantRepository'),
  TenantUserRepository: Symbol.for('TenantUserRepository'),
  TenantController: Symbol.for('TenantController'),
  TenantValidator: Symbol.for('TenantValidator'),

  // Phase 4: Production Readiness
  HistoryTracker: Symbol.for('HistoryTracker'),
  AlertManager: Symbol.for('AlertManager')
};
