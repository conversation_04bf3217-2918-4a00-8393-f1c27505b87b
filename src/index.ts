import 'reflect-metadata';
import { getConfig } from './config';
import { configureContainer } from './inversify.config';
import { TYPES } from './types';
import { DynamicMcpServer } from './server/DynamicMcpServer';
import { ILogger } from './core/interfaces/ILogger';
import { MCPServerImpl } from './server/MCPServer';
import { ApiServer } from './api/server';
import { AdminServer } from './api/adminServer';
import { WorkflowConfigWatcher } from './workflow/config/WorkflowConfigWatcher';

async function main(): Promise<void> {
  try {
    // Get configuration
    const config = getConfig();

    // Configure dependency injection
    const container = configureContainer();

    // Get logger
    const logger = container.get<ILogger>(TYPES.Logger);

    logger.info('Starting Dynamic MCP Server...');
    logger.info(`Environment: ${config.environment}`);

    // Get servers
    const mcpServer = container.get<DynamicMcpServer>(DynamicMcpServer);
    const apiServer = container.get<ApiServer>(ApiServer);
    const adminServer = new AdminServer(container);
    const configWatcher = container.get<WorkflowConfigWatcher>(TYPES.WorkflowConfigWatcher);

    // Start servers
    await mcpServer.start(config);
    apiServer.start(config.apiPort);

    // Start Admin API server on a different port
    try {
      const adminPort = parseInt(process.env.ADMIN_PORT || '7002');
      await adminServer.start(adminPort);
      logger.info(`Admin UI API started on port ${adminPort}`);
    } catch (error) {
      logger.error('Failed to start Admin API server:', error);
      // Continue without Admin API for now
    }

    // Start config watcher
    await configWatcher.startWatching();
    logger.info('Workflow config watcher started');
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

main();
