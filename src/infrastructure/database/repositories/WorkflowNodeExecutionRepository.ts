import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { IRepository } from '../../../core/interfaces/IRepository';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TYPES } from '../../../types';
import { WorkflowNodeExecution } from '../entities/WorkflowNodeExecution.entity';

/**
 * Repository for workflow node executions
 */
@injectable()
export class WorkflowNodeExecutionRepository implements IRepository<WorkflowNodeExecution> {
  private repository: Repository<WorkflowNodeExecution>;

  /**
   * Constructor
   * @param database Database
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<WorkflowNodeExecution> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(WorkflowNodeExecution);
    }
    return this.repository;
  }

  /**
   * Find all workflow node executions
   * @returns All workflow node executions
   */
  public async findAll(): Promise<WorkflowNodeExecution[]> {
    return this.getRepository().find();
  }

  /**
   * Find workflow node execution by ID
   * @param id Workflow node execution ID
   * @returns Workflow node execution
   */
  public async findById(id: string): Promise<WorkflowNodeExecution | null> {
    return this.getRepository().findOneBy({ id });
  }

  /**
   * Find workflow node executions by workflow execution ID
   * @param workflowExecutionId Workflow execution ID
   * @returns Workflow node executions
   */
  public async findByWorkflowExecutionId(
    workflowExecutionId: string
  ): Promise<WorkflowNodeExecution[]> {
    return this.getRepository().findBy({ workflow_execution_id: workflowExecutionId });
  }

  /**
   * Find workflow node executions by status
   * @param status Execution status
   * @returns Workflow node executions
   */
  public async findByStatus(
    status: 'RUNNING' | 'COMPLETED' | 'FAILED'
  ): Promise<WorkflowNodeExecution[]> {
    return this.getRepository().findBy({ status });
  }

  /**
   * Find workflow node executions by node type
   * @param nodeType Node type
   * @returns Workflow node executions
   */
  public async findByNodeType(nodeType: string): Promise<WorkflowNodeExecution[]> {
    return this.getRepository().findBy({ node_type: nodeType });
  }

  /**
   * Create a new workflow node execution
   * @param data Workflow node execution data
   * @returns Created workflow node execution
   */
  public async create(data: Partial<WorkflowNodeExecution>): Promise<WorkflowNodeExecution> {
    const workflowNodeExecution = this.getRepository().create(data);
    return this.getRepository().save(workflowNodeExecution);
  }

  /**
   * Update a workflow node execution
   * @param id Workflow node execution ID
   * @param data Workflow node execution data
   * @returns Updated workflow node execution
   */
  public async update(
    id: string,
    data: Partial<WorkflowNodeExecution>
  ): Promise<WorkflowNodeExecution> {
    await this.getRepository().update(id, data);
    const updatedWorkflowNodeExecution = await this.findById(id);
    if (!updatedWorkflowNodeExecution) {
      throw new Error(`Workflow node execution with ID ${id} not found`);
    }
    return updatedWorkflowNodeExecution;
  }

  /**
   * Delete a workflow node execution
   * @param id Workflow node execution ID
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.getRepository().delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  public async findBy(criteria: Partial<WorkflowNodeExecution>): Promise<WorkflowNodeExecution[]> {
    return this.getRepository().findBy(criteria);
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  public async findOne(criteria: any): Promise<WorkflowNodeExecution | null> {
    return this.getRepository().findOne(criteria);
  }

  /**
   * Save an entity
   * @param entity Entity data
   */
  public async save(entity: Partial<WorkflowNodeExecution>): Promise<WorkflowNodeExecution> {
    return this.getRepository().save(entity);
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  public async transaction<R>(
    callback: (repository: IRepository<WorkflowNodeExecution>) => Promise<R>
  ): Promise<R> {
    return this.database.getConnection().transaction(async (manager: any) => {
      const transactionRepository = manager.getRepository(WorkflowNodeExecution);
      const repo = new WorkflowNodeExecutionRepository(this.database, this.logger);
      repo.repository = transactionRepository;
      return callback(repo);
    });
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    return this.database.getConnection().query(sql, params);
  }
}
