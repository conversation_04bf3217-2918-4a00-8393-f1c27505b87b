import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { IRepository } from '../../../core/interfaces/IRepository';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TYPES } from '../../../types';
import { WorkflowExecution } from '../entities/WorkflowExecution.entity';

/**
 * Repository for workflow executions
 */
@injectable()
export class WorkflowExecutionRepository implements IRepository<WorkflowExecution> {
  private repository: Repository<WorkflowExecution>;

  /**
   * Constructor
   * @param database Database
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<WorkflowExecution> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(WorkflowExecution);
    }
    return this.repository;
  }

  /**
   * Find all workflow executions
   * @returns All workflow executions
   */
  public async findAll(): Promise<WorkflowExecution[]> {
    return this.getRepository().find();
  }

  /**
   * Find workflow execution by ID
   * @param id Workflow execution ID
   * @returns Workflow execution
   */
  public async findById(id: string): Promise<WorkflowExecution | null> {
    return this.getRepository().findOneBy({ id });
  }

  /**
   * Find workflow executions by workflow ID
   * @param workflowId Workflow ID
   * @returns Workflow executions
   */
  public async findByWorkflowId(workflowId: string): Promise<WorkflowExecution[]> {
    return this.getRepository().findBy({ workflow_id: workflowId });
  }

  /**
   * Find workflow executions by status
   * @param status Execution status
   * @returns Workflow executions
   */
  public async findByStatus(
    status: 'RUNNING' | 'COMPLETED' | 'FAILED'
  ): Promise<WorkflowExecution[]> {
    return this.getRepository().findBy({ status });
  }

  /**
   * Create a new workflow execution
   * @param data Workflow execution data
   * @returns Created workflow execution
   */
  public async create(data: Partial<WorkflowExecution>): Promise<WorkflowExecution> {
    const workflowExecution = this.getRepository().create(data);
    return this.getRepository().save(workflowExecution);
  }

  /**
   * Update a workflow execution
   * @param id Workflow execution ID
   * @param data Workflow execution data
   * @returns Updated workflow execution
   */
  public async update(id: string, data: Partial<WorkflowExecution>): Promise<WorkflowExecution> {
    await this.getRepository().update(id, data);
    const updatedWorkflowExecution = await this.findById(id);
    if (!updatedWorkflowExecution) {
      throw new Error(`Workflow execution with ID ${id} not found`);
    }
    return updatedWorkflowExecution;
  }

  /**
   * Delete a workflow execution
   * @param id Workflow execution ID
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.getRepository().delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  public async findBy(criteria: Partial<WorkflowExecution>): Promise<WorkflowExecution[]> {
    return this.getRepository().findBy(criteria);
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  public async findOne(criteria: any): Promise<WorkflowExecution | null> {
    return this.getRepository().findOne(criteria);
  }

  /**
   * Save an entity
   * @param entity Entity data
   */
  public async save(entity: Partial<WorkflowExecution>): Promise<WorkflowExecution> {
    return this.getRepository().save(entity);
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  public async transaction<R>(
    callback: (repository: IRepository<WorkflowExecution>) => Promise<R>
  ): Promise<R> {
    return this.database.getConnection().transaction(async (manager: any) => {
      const transactionRepository = manager.getRepository(WorkflowExecution);
      const repo = new WorkflowExecutionRepository(this.database, this.logger);
      repo.repository = transactionRepository;
      return callback(repo);
    });
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    return this.database.getConnection().query(sql, params);
  }
}
