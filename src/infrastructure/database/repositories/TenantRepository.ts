import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { TYPES } from '../../../types';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { Tenant } from '../entities/Tenant.entity';

/**
 * Repository for Tenant entity
 * Provides custom methods for tenant operations
 */
@injectable()
export class TenantRepository {
  private repository: Repository<Tenant>;

  constructor(@inject(TYPES.Database) private database: IDatabase) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<Tenant> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(Tenant);
    }
    return this.repository;
  }

  /**
   * Find tenant by name (case-insensitive)
   */
  async findByName(name: string): Promise<Tenant | null> {
    return this.getRepository().findOne({
      where: { name: name.toLowerCase() },
      relations: ['users']
    });
  }

  /**
   * Find enabled tenants only
   */
  async findEnabled(): Promise<Tenant[]> {
    return this.getRepository().find({
      where: { enabled: true },
      relations: ['users']
    });
  }

  /**
   * Find tenants created by specific user
   */
  async findByCreator(createdBy: string): Promise<Tenant[]> {
    return this.getRepository().find({
      where: { createdBy },
      relations: ['users']
    });
  }

  /**
   * Count total tenants
   */
  async countTotal(): Promise<number> {
    return this.getRepository().count();
  }

  /**
   * Count enabled tenants
   */
  async countEnabled(): Promise<number> {
    return this.getRepository().count({ where: { enabled: true } });
  }

  // Standard repository methods
  async findOne(options: any): Promise<Tenant | null> {
    return this.getRepository().findOne(options);
  }

  async find(options?: any): Promise<Tenant[]> {
    return this.getRepository().find(options);
  }

  async findAndCount(options?: any): Promise<[Tenant[], number]> {
    return this.getRepository().findAndCount(options);
  }

  async save(entity: Tenant): Promise<Tenant> {
    return this.getRepository().save(entity);
  }

  create(data: Partial<Tenant>): Tenant {
    return this.getRepository().create(data);
  }

  async remove(entity: Tenant): Promise<Tenant> {
    return this.getRepository().remove(entity);
  }

  async count(options?: any): Promise<number> {
    return this.getRepository().count(options);
  }
}
