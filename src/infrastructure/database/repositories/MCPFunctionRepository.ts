import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { IRepository } from '../../../core/interfaces/IRepository';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TYPES } from '../../../types';
import { MCPFunction } from '../entities/MCPFunction.entity';

/**
 * Repository for MCP Function entities
 */
@injectable()
export class MCPFunctionRepository implements IRepository<MCPFunction> {
  private repository: Repository<MCPFunction>;

  /**
   * Constructor
   * @param database Database
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<MCPFunction> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(MCPFunction);
    }
    return this.repository;
  }

  /**
   * Find all functions
   * @returns All functions
   */
  public async findAll(): Promise<MCPFunction[]> {
    return this.getRepository().find();
  }

  /**
   * Find function by id
   * @param id Function ID
   */
  public async findById(id: string): Promise<MCPFunction | null> {
    const func = await this.getRepository().findOne({
      where: { id }
    });
    return func || null;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  public async findBy(criteria: Partial<MCPFunction>): Promise<MCPFunction[]> {
    return this.getRepository().findBy(criteria);
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  public async findOne(criteria: any): Promise<MCPFunction | null> {
    return this.getRepository().findOne(criteria);
  }

  /**
   * Create a new function
   * @param entity Function data
   */
  public async create(entity: Partial<MCPFunction>): Promise<MCPFunction> {
    const func = this.getRepository().create(entity);
    return this.getRepository().save(func);
  }

  /**
   * Save a function
   * @param entity Function data
   */
  public async save(entity: Partial<MCPFunction>): Promise<MCPFunction> {
    return this.getRepository().save(entity);
  }

  /**
   * Update an existing function
   * @param id Function ID
   * @param entity Function data
   */
  public async update(id: string, entity: Partial<MCPFunction>): Promise<MCPFunction> {
    await this.getRepository().update(id, entity);
    const updated = await this.findById(id);
    if (!updated) {
      throw new Error(`Function with id ${id} not found after update`);
    }
    return updated;
  }

  /**
   * Delete a function
   * @param id Function ID
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.getRepository().delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  public async transaction<R>(
    callback: (repository: IRepository<MCPFunction>) => Promise<R>
  ): Promise<R> {
    return this.database.getConnection().transaction(async (manager: any) => {
      const transactionRepository = manager.getRepository(MCPFunction);
      const repo = new MCPFunctionRepository(this.database, this.logger);
      repo.repository = transactionRepository;
      return callback(repo);
    });
  }

  /**
   * Find functions by enabled status
   * @param enabled Whether the function is enabled
   * @returns Array of functions
   */
  async findByEnabled(enabled: boolean): Promise<MCPFunction[]> {
    return this.getRepository().find({
      where: { enabled }
    });
  }

  /**
   * Find function by name
   * @param name Function name
   * @returns Function or null
   */
  async findByName(name: string): Promise<MCPFunction | null> {
    return this.getRepository().findOne({
      where: { name }
    });
  }

  /**
   * Check if function name exists
   * @param name Function name
   * @param excludeId Optional ID to exclude from check
   * @returns True if name exists
   */
  async nameExists(name: string, excludeId?: string): Promise<boolean> {
    const query = this.getRepository()
      .createQueryBuilder('func')
      .where('func.name = :name', { name });

    if (excludeId) {
      query.andWhere('func.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Get functions with pagination and filtering
   * @param options Query options
   * @returns Paginated functions
   */
  async findWithPagination(options: {
    page?: number;
    limit?: number;
    enabled?: boolean;
    search?: string;
  }): Promise<{ data: MCPFunction[]; total: number; page: number; limit: number }> {
    const page = options.page || 1;
    const limit = options.limit || 10;
    const offset = (page - 1) * limit;

    const query = this.getRepository().createQueryBuilder('func');

    // Filter by enabled status
    if (options.enabled !== undefined) {
      query.andWhere('func.enabled = :enabled', { enabled: options.enabled });
    }

    // Search by name or description
    if (options.search) {
      query.andWhere('(func.name ILIKE :search OR func.description ILIKE :search)', {
        search: `%${options.search}%`
      });
    }

    // Get total count
    const total = await query.getCount();

    // Apply pagination and get results
    const data = await query.orderBy('func.created_at', 'DESC').skip(offset).take(limit).getMany();

    return {
      data,
      total,
      page,
      limit
    };
  }
}
