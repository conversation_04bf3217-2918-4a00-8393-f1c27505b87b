import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { TYPES } from '../../../types';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TenantUser } from '../entities/TenantUser.entity';

/**
 * Repository for TenantUser entity
 * Provides custom methods for tenant user operations
 */
@injectable()
export class TenantUserRepository {
  private repository: Repository<TenantUser>;

  constructor(@inject(TYPES.Database) private database: IDatabase) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<TenantUser> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(TenantUser);
    }
    return this.repository;
  }

  /**
   * Find user by tenant and username
   */
  async findByTenantAndUsername(tenantId: string, username: string): Promise<TenantUser | null> {
    return this.getRepository().findOne({
      where: { tenantId, username },
      relations: ['tenant']
    });
  }

  /**
   * Find user by tenant and email
   */
  async findByTenantAndEmail(tenantId: string, email: string): Promise<TenantUser | null> {
    return this.getRepository().findOne({
      where: { tenantId, email },
      relations: ['tenant']
    });
  }

  /**
   * Find enabled users for tenant
   */
  async findEnabledByTenant(tenantId: string): Promise<TenantUser[]> {
    return this.getRepository().find({
      where: { tenantId, enabled: true },
      relations: ['tenant']
    });
  }

  /**
   * Find users with specific role
   */
  async findByRole(tenantId: string, roleName: string): Promise<TenantUser[]> {
    return this.getRepository()
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.tenant', 'tenant')
      .where('user.tenantId = :tenantId', { tenantId })
      .andWhere('JSON_EXTRACT(user.roles, "$[*].name") LIKE :roleName', {
        roleName: `%${roleName}%`
      })
      .getMany();
  }

  /**
   * Count users in tenant
   */
  async countByTenant(tenantId: string): Promise<number> {
    return this.getRepository().count({ where: { tenantId } });
  }

  /**
   * Count enabled users in tenant
   */
  async countEnabledByTenant(tenantId: string): Promise<number> {
    return this.getRepository().count({ where: { tenantId, enabled: true } });
  }

  /**
   * Update last login time
   */
  async updateLastLogin(userId: string): Promise<void> {
    await this.getRepository().update(userId, { lastLoginAt: new Date() });
  }

  // Standard repository methods
  async findOne(options: any): Promise<TenantUser | null> {
    return this.getRepository().findOne(options);
  }

  async find(options?: any): Promise<TenantUser[]> {
    return this.getRepository().find(options);
  }

  async findAndCount(options?: any): Promise<[TenantUser[], number]> {
    return this.getRepository().findAndCount(options);
  }

  async save(entity: TenantUser): Promise<TenantUser> {
    return this.getRepository().save(entity);
  }

  create(data: Partial<TenantUser>): TenantUser {
    return this.getRepository().create(data);
  }

  async remove(entity: TenantUser): Promise<TenantUser> {
    return this.getRepository().remove(entity);
  }

  async count(options?: any): Promise<number> {
    return this.getRepository().count(options);
  }

  async update(criteria: any, partialEntity: any): Promise<any> {
    return this.getRepository().update(criteria, partialEntity);
  }
}
