import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { IRepository } from '../../../core/interfaces/IRepository';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TYPES } from '../../../types';
import { DataSource as DataSourceEntity } from '../entities/DataSource.entity';

/**
 * Repository for data sources
 */
@injectable()
export class DataSourceRepository implements IRepository<DataSourceEntity> {
  private repository: Repository<DataSourceEntity>;

  /**
   * Constructor
   * @param database Database
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<DataSourceEntity> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(DataSourceEntity);
    }
    return this.repository;
  }

  /**
   * Find all data sources
   * @returns All data sources
   */
  public async findAll(): Promise<DataSourceEntity[]> {
    return this.getRepository().find();
  }

  /**
   * Find data source by ID
   * @param id Data source ID
   * @returns Data source
   */
  public async findById(id: string): Promise<DataSourceEntity | null> {
    return this.getRepository().findOneBy({ id });
  }

  /**
   * Find data source by name
   * @param name Data source name
   * @returns Data source
   */
  public async findByName(name: string): Promise<DataSourceEntity | null> {
    return this.getRepository().findOneBy({ name });
  }

  /**
   * Find data sources by type
   * @param type Data source type
   * @returns Data sources
   */
  public async findByType(type: string): Promise<DataSourceEntity[]> {
    return this.getRepository().findBy({ type });
  }

  /**
   * Create a new data source
   * @param data Data source data
   * @returns Created data source
   */
  public async create(data: Partial<DataSourceEntity>): Promise<DataSourceEntity> {
    const dataSource = this.getRepository().create(data);
    return this.getRepository().save(dataSource);
  }

  /**
   * Update a data source
   * @param id Data source ID
   * @param data Data source data
   * @returns Updated data source
   */
  public async update(id: string, data: Partial<DataSourceEntity>): Promise<DataSourceEntity> {
    await this.getRepository().update(id, data);
    const updatedDataSource = await this.findById(id);
    if (!updatedDataSource) {
      throw new Error(`Data source with ID ${id} not found`);
    }
    return updatedDataSource;
  }

  /**
   * Delete a data source
   * @param id Data source ID
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.getRepository().delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  public async findBy(criteria: Partial<DataSourceEntity>): Promise<DataSourceEntity[]> {
    return this.getRepository().findBy(criteria);
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  public async findOne(criteria: any): Promise<DataSourceEntity | null> {
    return this.getRepository().findOne(criteria);
  }

  /**
   * Save an entity
   * @param entity Entity data
   */
  public async save(entity: Partial<DataSourceEntity>): Promise<DataSourceEntity> {
    return this.getRepository().save(entity);
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  public async transaction<R>(
    callback: (repository: IRepository<DataSourceEntity>) => Promise<R>
  ): Promise<R> {
    return this.database.getConnection().transaction(async (manager: any) => {
      const transactionRepository = manager.getRepository(DataSourceEntity);
      const repo = new DataSourceRepository(this.database, this.logger);
      repo.repository = transactionRepository;
      return callback(repo);
    });
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    return this.database.getConnection().query(sql, params);
  }
}
