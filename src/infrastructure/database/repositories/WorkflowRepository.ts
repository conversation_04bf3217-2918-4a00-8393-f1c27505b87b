import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { IRepository } from '../../../core/interfaces/IRepository';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TYPES } from '../../../types';
import { Workflow } from '../entities/Workflow.entity';

/**
 * Repository for workflows
 */
@injectable()
export class WorkflowRepository implements IRepository<Workflow> {
  private repository: Repository<Workflow>;

  /**
   * Constructor
   * @param database Database
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<Workflow> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(Workflow);
    }
    return this.repository;
  }

  /**
   * Find all workflows
   * @returns All workflows
   */
  public async findAll(): Promise<Workflow[]> {
    return this.getRepository().find();
  }

  /**
   * Find workflow by ID
   * @param id Workflow ID
   * @returns Workflow
   */
  public async findById(id: string): Promise<Workflow | null> {
    return this.getRepository().findOneBy({ id });
  }

  /**
   * Find workflow by name
   * @param name Workflow name
   * @returns Workflow
   */
  public async findByName(name: string): Promise<Workflow | null> {
    return this.getRepository().findOneBy({ name });
  }

  /**
   * Find workflows by enabled status
   * @param enabled Enabled status
   * @returns Workflows
   */
  public async findByEnabled(enabled: boolean): Promise<Workflow[]> {
    return this.getRepository().findBy({ enabled });
  }

  /**
   * Create a new workflow
   * @param data Workflow data
   * @returns Created workflow
   */
  public async create(data: Partial<Workflow>): Promise<Workflow> {
    const workflow = this.getRepository().create(data);
    return this.getRepository().save(workflow);
  }

  /**
   * Update a workflow
   * @param id Workflow ID
   * @param data Workflow data
   * @returns Updated workflow
   */
  public async update(id: string, data: Partial<Workflow>): Promise<Workflow> {
    await this.getRepository().update(id, data);
    const updatedWorkflow = await this.findById(id);
    if (!updatedWorkflow) {
      throw new Error(`Workflow with ID ${id} not found`);
    }
    return updatedWorkflow;
  }

  /**
   * Delete a workflow
   * @param id Workflow ID
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.getRepository().delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  public async findBy(criteria: Partial<Workflow>): Promise<Workflow[]> {
    return this.getRepository().findBy(criteria);
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  public async findOne(criteria: any): Promise<Workflow | null> {
    return this.getRepository().findOne(criteria);
  }

  /**
   * Save an entity
   * @param entity Entity data
   */
  public async save(entity: Partial<Workflow>): Promise<Workflow> {
    return this.getRepository().save(entity);
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  public async transaction<R>(
    callback: (repository: IRepository<Workflow>) => Promise<R>
  ): Promise<R> {
    return this.database.getConnection().transaction(async (manager: any) => {
      const transactionRepository = manager.getRepository(Workflow);
      const repo = new WorkflowRepository(this.database, this.logger);
      repo.repository = transactionRepository;
      return callback(repo);
    });
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    return this.database.getConnection().query(sql, params);
  }
}
