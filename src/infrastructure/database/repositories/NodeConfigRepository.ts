import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { IRepository } from '../../../core/interfaces/IRepository';
import type { ILogger } from '../../../core/interfaces/ILogger';
import type { IDatabase } from '../../../core/interfaces/IDatabase';
import { TYPES } from '../../../types';
import { NodeConfig } from '../entities/NodeConfig.entity';

/**
 * Repository for node configurations
 */
@injectable()
export class NodeConfigRepository implements IRepository<NodeConfig> {
  private repository: Repository<NodeConfig>;

  /**
   * Constructor
   * @param database Database
   * @param logger Logger
   */
  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Repository will be initialized lazily
  }

  /**
   * Get the repository instance, initializing it if necessary
   */
  private getRepository(): Repository<NodeConfig> {
    if (!this.repository) {
      this.repository = this.database.getConnection().getRepository(NodeConfig);
    }
    return this.repository;
  }

  /**
   * Find all node configurations
   * @returns All node configurations
   */
  public async findAll(): Promise<NodeConfig[]> {
    return this.getRepository().find();
  }

  /**
   * Find node configuration by ID
   * @param id Node configuration ID
   * @returns Node configuration
   */
  public async findById(id: string): Promise<NodeConfig | null> {
    return this.getRepository().findOneBy({ id });
  }

  /**
   * Find node configurations by type
   * @param type Node type
   * @returns Node configurations
   */
  public async findByType(type: string): Promise<NodeConfig[]> {
    return this.getRepository().findBy({ node_type: type });
  }

  /**
   * Create a new node configuration
   * @param data Node configuration data
   * @returns Created node configuration
   */
  public async create(data: Partial<NodeConfig>): Promise<NodeConfig> {
    const nodeConfig = this.getRepository().create(data);
    return this.getRepository().save(nodeConfig);
  }

  /**
   * Update a node configuration
   * @param id Node configuration ID
   * @param data Node configuration data
   * @returns Updated node configuration
   */
  public async update(id: string, data: Partial<NodeConfig>): Promise<NodeConfig> {
    await this.getRepository().update(id, data);
    const updatedNodeConfig = await this.findById(id);
    if (!updatedNodeConfig) {
      throw new Error(`Node configuration with ID ${id} not found`);
    }
    return updatedNodeConfig;
  }

  /**
   * Delete a node configuration
   * @param id Node configuration ID
   */
  public async delete(id: string): Promise<boolean> {
    const result = await this.getRepository().delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  public async findBy(criteria: Partial<NodeConfig>): Promise<NodeConfig[]> {
    return this.getRepository().findBy(criteria);
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  public async findOne(criteria: any): Promise<NodeConfig | null> {
    return this.getRepository().findOne(criteria);
  }

  /**
   * Save an entity
   * @param entity Entity data
   */
  public async save(entity: Partial<NodeConfig>): Promise<NodeConfig> {
    return this.getRepository().save(entity);
  }

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  public async transaction<R>(
    callback: (repository: IRepository<NodeConfig>) => Promise<R>
  ): Promise<R> {
    return this.database.getConnection().transaction(async (manager: any) => {
      const transactionRepository = manager.getRepository(NodeConfig);
      const repo = new NodeConfigRepository(this.database, this.logger);
      repo.repository = transactionRepository;
      return callback(repo);
    });
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  public async query(sql: string, params?: any[]): Promise<any> {
    return this.database.getConnection().query(sql, params);
  }
}
