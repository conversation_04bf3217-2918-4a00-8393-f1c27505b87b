import {
  DataSource,
  EntityTarget,
  FindOptionsWhere,
  ObjectLiteral,
  QueryRunner,
  Repository
} from 'typeorm';
import { injectable, inject } from 'inversify';
import { IDatabase, NotificationCallback } from '../../core/interfaces/IDatabase';
import { IRepository } from '../../core/interfaces/IRepository';
import type { ILogger } from '../../core/interfaces/ILogger';
import { typeOrmConfig } from './config';
import { DatabaseInitializer } from './DatabaseInitializer';
import { TYPES } from '../../types';

/**
 * PostgreSQL database implementation
 */
@injectable()
export class PostgresDatabase implements IDatabase {
  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  async query(sql: string, params?: any[]): Promise<any> {
    return this.dataSource.query(sql, params || []);
  }
  private dataSource: DataSource;
  private initialized: boolean = false;
  private notificationCallbacks: NotificationCallback[] = [];
  private databaseInitializer: DatabaseInitializer;

  constructor(@inject(TYPES.Logger) private logger: ILogger) {
    this.dataSource = new DataSource(typeOrmConfig);
    this.databaseInitializer = new DatabaseInitializer(logger);
  }

  /**
   * Initialize the database connection
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // First, ensure the database exists
      await this.databaseInitializer.initializeDatabase();

      // Then connect to the application database
      await this.dataSource.initialize();
      this.initialized = true;
      this.logger.info('Database connection initialized');
      this.logger.info(
        'Database migrations will run automatically via TypeORM migrationsRun: true'
      );

      // Set up notification listener
      this.setupNotificationListener();
    } catch (error) {
      this.logger.error('Error initializing database connection', error);
      throw error;
    }
  }

  /**
   * Set up notification listener
   * This method sets up the event listener for PostgreSQL notifications
   */
  private setupNotificationListener(): void {
    try {
      // For now, we'll skip the notification listener setup
      // This can be implemented later when needed for hot-reload functionality
      this.logger.debug('Notification listener setup skipped - will be implemented when needed');
    } catch (error) {
      this.logger.error('Error setting up notification listener', error);
      // Don't throw the error to prevent startup failure
    }
  }

  /**
   * Get a repository for a specific entity
   * @param entity Entity class
   */
  getRepository<T extends ObjectLiteral>(entity: EntityTarget<T>): IRepository<T> {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }

    return new TypeOrmRepository<T>(entity, this.dataSource);
  }

  /**
   * Close the database connection
   */
  async close(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      await this.dataSource.destroy();
      this.initialized = false;
      this.logger.info('Database connection closed');
    } catch (error) {
      this.logger.error('Error closing database connection', error);
      throw error;
    }
  }

  /**
   * Listen for notifications on a channel
   * @param channel Channel name
   * @returns Promise that resolves when the listen command is executed
   */
  async listen(channel: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      await this.query(`LISTEN ${channel}`);
      this.logger.debug(`Listening for notifications on channel: ${channel}`);
    } catch (error) {
      this.logger.error(`Error listening on channel ${channel}`, error);
      throw error;
    }
  }

  /**
   * Stop listening for notifications on a channel
   * @param channel Channel name
   * @returns Promise that resolves when the unlisten command is executed
   */
  async unlisten(channel: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      await this.query(`UNLISTEN ${channel}`);
      this.logger.debug(`Stopped listening for notifications on channel: ${channel}`);
    } catch (error) {
      this.logger.error(`Error unlistening on channel ${channel}`, error);
      throw error;
    }
  }

  /**
   * Register a callback for notifications
   * @param callback Callback function
   */
  onNotification(callback: NotificationCallback): void {
    this.notificationCallbacks.push(callback);
  }

  /**
   * Send a notification
   * @param channel Channel name
   * @param payload Notification payload
   * @returns Promise that resolves when the notify command is executed
   */
  async notify(channel: string, payload: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }

    try {
      await this.query(`SELECT pg_notify($1, $2)`, [channel, payload]);
      this.logger.debug(`Sent notification on channel: ${channel}`, { payload });
    } catch (error) {
      this.logger.error(`Error sending notification on channel ${channel}`, error);
      throw error;
    }
  }

  /**
   * Get the TypeORM DataSource
   * @returns TypeORM DataSource
   */
  getConnection(): DataSource {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }
    return this.dataSource;
  }

  /**
   * Get the TypeORM DataSource (alias for getConnection)
   * @returns TypeORM DataSource
   */
  async getDataSource(): Promise<DataSource> {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }
    return this.dataSource;
  }

  /**
   * Execute operations within a transaction
   * @param operation Function that performs database operations
   * @returns Result of the operation
   */
  async transaction<T>(operation: (trx: any) => Promise<T>): Promise<T> {
    if (!this.initialized) {
      throw new Error('Database not initialized');
    }
    return await this.dataSource.transaction(async (manager) => {
      return await operation(manager);
    });
  }
}

/**
 * TypeORM repository implementation
 */
class TypeOrmRepository<T extends ObjectLiteral> implements IRepository<T> {
  private repository: Repository<T>;

  constructor(
    private entity: EntityTarget<T>,
    private dataSource: DataSource
  ) {
    this.repository = dataSource.getRepository(entity);
  }

  /**
   * Find all entities
   */
  async findAll(): Promise<T[]> {
    return this.repository.find();
  }

  /**
   * Find entity by id
   * @param id Entity id
   */
  async findById(id: string): Promise<T | null> {
    const entity = await this.repository.findOne({
      where: { id } as unknown as FindOptionsWhere<T>
    });
    return entity || null;
  }

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  async findBy(criteria: Partial<T>): Promise<T[]> {
    return this.repository.find({
      where: criteria as unknown as FindOptionsWhere<T>
    });
  }

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  async findOne(criteria: any): Promise<T | null> {
    const entity = await this.repository.findOne(criteria);
    return entity || null;
  }

  /**
   * Create a new entity
   * @param entity Entity data
   */
  async create(entity: Partial<T>): Promise<T> {
    const newEntity = this.repository.create(entity as any);
    const saved = await this.repository.save(newEntity);
    return saved as unknown as T;
  }

  /**
   * Save an entity
   * @param entity Entity data
   */
  async save(entity: Partial<T>): Promise<T> {
    return this.repository.save(entity as any);
  }

  /**
   * Update an existing entity
   * @param id Entity id
   * @param entity Entity data
   */
  async update(id: string, entity: Partial<T>): Promise<T> {
    await this.repository.update(id, entity as any);
    const updated = await this.findById(id);
    if (!updated) {
      throw new Error(`Entity with id ${id} not found`);
    }
    return updated;
  }

  /**
   * Delete an entity
   * @param id Entity id
   */
  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== undefined && (result.affected ?? 0) > 0;
  }

  async transaction<R>(callback: (repository: IRepository<T>) => Promise<R>): Promise<R> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const transactionRepository = new TransactionRepository<T>(this.entity, queryRunner);

      const result = await callback(transactionRepository);

      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}

/**
 * Transaction repository implementation
 */
class TransactionRepository<T extends ObjectLiteral> implements IRepository<T> {
  private repository: Repository<T>;

  constructor(
    entity: EntityTarget<T>,
    private queryRunner: QueryRunner
  ) {
    this.repository = queryRunner.manager.getRepository(entity);
  }

  async findAll(): Promise<T[]> {
    return this.repository.find();
  }

  async findById(id: string): Promise<T | null> {
    const entity = await this.repository.findOne({
      where: { id } as unknown as FindOptionsWhere<T>
    });
    return entity || null;
  }

  async findBy(criteria: Partial<T>): Promise<T[]> {
    return this.repository.find({
      where: criteria as unknown as FindOptionsWhere<T>
    });
  }

  async findOne(criteria: any): Promise<T | null> {
    const entity = await this.repository.findOne(criteria);
    return entity || null;
  }

  async create(entity: Partial<T>): Promise<T> {
    const newEntity = this.repository.create(entity as any);
    const saved = await this.repository.save(newEntity);
    return saved as unknown as T;
  }

  async save(entity: Partial<T>): Promise<T> {
    return this.repository.save(entity as any);
  }

  async update(id: string, entity: Partial<T>): Promise<T> {
    await this.repository.update(id, entity as any);
    const updated = await this.findById(id);
    if (!updated) {
      throw new Error(`Entity with id ${id} not found`);
    }
    return updated;
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return result.affected !== undefined && (result.affected || 0) > 0;
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  async query(sql: string, params?: any[]): Promise<any> {
    return this.queryRunner.query(sql, params);
  }

  async transaction<R>(): Promise<R> {
    throw new Error('Nested transactions are not supported');
  }
}
