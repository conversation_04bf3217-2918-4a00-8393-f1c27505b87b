import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateTenantTables1700000000001 implements MigrationInterface {
  name = 'CreateTenantTables1700000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create mcp_tenants table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_tenants',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isUnique: true,
          },
          {
            name: 'display_name',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'settings',
            type: 'jsonb',
            default: "'{}'",
          },
          {
            name: 'encryption_key_id',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
        ],
      }),
      true
    );

    // Create mcp_tenant_users table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_tenant_users',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'tenant_id',
            type: 'uuid',
          },
          {
            name: 'username',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'email',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'password_hash',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'roles',
            type: 'jsonb',
            default: "'[]'",
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'last_login_at',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create indexes for mcp_tenants
    await queryRunner.createIndex(
      'mcp_tenants',
      new TableIndex({
        name: 'IDX_mcp_tenants_name',
        columnNames: ['name'],
      })
    );

    await queryRunner.createIndex(
      'mcp_tenants',
      new TableIndex({
        name: 'IDX_mcp_tenants_enabled',
        columnNames: ['enabled'],
      })
    );

    // Create indexes for mcp_tenant_users
    await queryRunner.createIndex(
      'mcp_tenant_users',
      new TableIndex({
        name: 'IDX_mcp_tenant_users_tenant_id',
        columnNames: ['tenant_id'],
      })
    );

    await queryRunner.createIndex(
      'mcp_tenant_users',
      new TableIndex({
        name: 'IDX_mcp_tenant_users_username',
        columnNames: ['tenant_id', 'username'],
        isUnique: true,
      })
    );

    await queryRunner.createIndex(
      'mcp_tenant_users',
      new TableIndex({
        name: 'IDX_mcp_tenant_users_email',
        columnNames: ['tenant_id', 'email'],
        isUnique: true,
      })
    );

    // Create foreign key for mcp_tenant_users
    await queryRunner.createForeignKey(
      'mcp_tenant_users',
      new TableForeignKey({
        columnNames: ['tenant_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'mcp_tenants',
        onDelete: 'CASCADE',
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop mcp_tenant_users table
    await queryRunner.dropTable('mcp_tenant_users', true);

    // Drop mcp_tenants table
    await queryRunner.dropTable('mcp_tenants', true);
  }
}
