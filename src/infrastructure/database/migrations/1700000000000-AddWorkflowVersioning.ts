import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeign<PERSON>ey } from 'typeorm';

export class AddWorkflowVersioning1700000000000 implements MigrationInterface {
  name = 'AddWorkflowVersioning1700000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add versioning columns to existing workflows table
    await queryRunner.query(`
      ALTER TABLE mcp_workflows
      ADD COLUMN current_version INTEGER DEFAULT 1,
      ADD COLUMN is_versioned BOOLEAN DEFAULT true,
      ADD COLUMN tenant_id VARCHAR(255)
    `);

    // Create workflow_versions table
    await queryRunner.createTable(
      new Table({
        name: 'workflow_versions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()'
          },
          {
            name: 'workflow_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'version',
            type: 'integer',
            isNullable: false
          },
          {
            name: 'is_active',
            type: 'boolean',
            default: false
          },
          {
            name: 'definition',
            type: 'jsonb',
            isNullable: false
          },
          {
            name: 'created_by',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'change_type',
            type: 'varchar',
            length: '50',
            isNullable: false
          },
          {
            name: 'change_description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true
          },
          {
            name: 'tenant_id',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP'
          }
        ]
      }),
      true
    );

    // Create workflow_version_history table
    await queryRunner.createTable(
      new Table({
        name: 'workflow_version_history',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'gen_random_uuid()'
          },
          {
            name: 'version_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'workflow_id',
            type: 'uuid',
            isNullable: false
          },
          {
            name: 'version',
            type: 'integer',
            isNullable: false
          },
          {
            name: 'change_type',
            type: 'varchar',
            length: '50',
            isNullable: false
          },
          {
            name: 'change_description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'changed_by',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'previous_version',
            type: 'integer',
            isNullable: true
          },
          {
            name: 'rollback_reason',
            type: 'text',
            isNullable: true
          },
          {
            name: 'change_details',
            type: 'jsonb',
            isNullable: true
          },
          {
            name: 'metadata',
            type: 'jsonb',
            isNullable: true
          },
          {
            name: 'tenant_id',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'CURRENT_TIMESTAMP'
          }
        ]
      }),
      true
    );

    // Create indexes for workflow_versions
    await queryRunner.createIndex(
      'workflow_versions',
      new TableIndex({
        name: 'IDX_workflow_versions_workflow_version',
        columnNames: ['workflow_id', 'version'],
        isUnique: true
      })
    );

    await queryRunner.createIndex(
      'workflow_versions',
      new TableIndex({
        name: 'IDX_workflow_versions_workflow_active',
        columnNames: ['workflow_id', 'is_active']
      })
    );

    await queryRunner.createIndex(
      'workflow_versions',
      new TableIndex({ name: 'IDX_workflow_versions_created_at', columnNames: ['created_at'] })
    );

    // Create indexes for workflow_version_history
    await queryRunner.createIndex(
      'workflow_version_history',
      new TableIndex({
        name: 'IDX_workflow_version_history_version_created',
        columnNames: ['version_id', 'created_at']
      })
    );

    await queryRunner.createIndex(
      'workflow_version_history',
      new TableIndex({
        name: 'IDX_workflow_version_history_workflow_created',
        columnNames: ['workflow_id', 'created_at']
      })
    );

    await queryRunner.createIndex(
      'workflow_version_history',
      new TableIndex({
        name: 'IDX_workflow_version_history_change_type',
        columnNames: ['change_type']
      })
    );

    // Create foreign keys
    await queryRunner.createForeignKey(
      'workflow_versions',
      new TableForeignKey({
        columnNames: ['workflow_id'],
        referencedTableName: 'mcp_workflows',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'workflow_version_history',
      new TableForeignKey({
        columnNames: ['version_id'],
        referencedTableName: 'workflow_versions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE'
      })
    );

    // Create trigger for updated_at on workflow_versions
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await queryRunner.query(`
      CREATE TRIGGER update_workflow_versions_updated_at
      BEFORE UPDATE ON workflow_versions
      FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);

    // Create initial versions for existing workflows
    await queryRunner.query(`
      INSERT INTO workflow_versions (workflow_id, version, is_active, definition, created_by, change_type, change_description, created_at)
      SELECT
        id,
        version,
        true,
        jsonb_build_object(
          'id', id,
          'name', name,
          'version', version,
          'nodes', nodes_config->'nodes',
          'edges', nodes_config->'edges',
          'metadata', jsonb_build_object(
            'inputSchema', input_schema,
            'outputSchema', output_schema,
            'description', description
          )
        ),
        'system',
        'create',
        'Initial version from migration',
        created_at
      FROM mcp_workflows
      WHERE enabled = true;
    `);

    // Create history entries for initial versions
    await queryRunner.query(`
      INSERT INTO workflow_version_history (version_id, workflow_id, version, change_type, changed_by, change_description, created_at)
      SELECT
        wv.id,
        wv.workflow_id,
        wv.version,
        'create',
        'system',
        'Initial version created during migration',
        wv.created_at
      FROM workflow_versions wv;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop triggers
    await queryRunner.query(
      'DROP TRIGGER IF EXISTS update_workflow_versions_updated_at ON workflow_versions;'
    );
    await queryRunner.query('DROP FUNCTION IF EXISTS update_updated_at_column();');

    // Drop foreign keys
    const workflowVersionsTable = await queryRunner.getTable('workflow_versions');
    if (workflowVersionsTable) {
      const workflowForeignKey = workflowVersionsTable.foreignKeys.find(
        (fk) => fk.columnNames.indexOf('workflow_id') !== -1
      );
      if (workflowForeignKey) {
        await queryRunner.dropForeignKey('workflow_versions', workflowForeignKey);
      }
    }

    const historyTable = await queryRunner.getTable('workflow_version_history');
    if (historyTable) {
      const versionForeignKey = historyTable.foreignKeys.find(
        (fk) => fk.columnNames.indexOf('version_id') !== -1
      );
      if (versionForeignKey) {
        await queryRunner.dropForeignKey('workflow_version_history', versionForeignKey);
      }
    }

    // Drop tables
    await queryRunner.dropTable('workflow_version_history', true);
    await queryRunner.dropTable('workflow_versions', true);

    // Remove versioning columns from workflows table
    await queryRunner.query(`
      ALTER TABLE mcp_workflows
      DROP COLUMN IF EXISTS current_version,
      DROP COLUMN IF EXISTS is_versioned,
      DROP COLUMN IF EXISTS tenant_id
    `);
  }
}
