import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to alter node_metrics table to use varchar for node_id
 */
export class AlterNodeMetricsTable1716560000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing indexes that reference node_id
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_node_metrics_node_id;
    `);

    // Alter node_id column to use varchar instead of uuid
    await queryRunner.query(`
      ALTER TABLE node_metrics ALTER COLUMN node_id TYPE VARCHAR(100);
    `);

    // Recreate the index
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_node_metrics_node_id ON node_metrics(node_id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration cannot be safely reversed as it would require converting
    // varchar values back to UUIDs, which might not be possible for all values.
    // If needed, a custom down migration would need to be implemented.
  }
}
