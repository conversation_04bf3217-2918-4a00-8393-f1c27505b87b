import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

/**
 * Migration to create workflow-related tables
 */
export class CreateWorkflowTables1621500000001 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create UUID extension if it doesn't exist
    await queryRunner.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');

    // Create workflows table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_workflows',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'version',
            type: 'int',
            default: 1,
          },
          {
            name: 'input_schema',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'output_schema',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'nodes_config',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'now()',
          },
        ],
      }),
      true
    );

    // Create workflow executions table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_workflow_executions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'workflow_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '50',
            isNullable: false,
          },
          {
            name: 'input_data',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'output_data',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'error_details',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'started_at',
            type: 'timestamptz',
            isNullable: false,
          },
          {
            name: 'completed_at',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'execution_time_ms',
            type: 'int',
            isNullable: true,
          },
        ],
      }),
      true
    );

    // Create workflow node executions table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_workflow_node_executions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'workflow_execution_id',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'node_id',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'node_type',
            type: 'varchar',
            length: '50',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'varchar',
            length: '50',
            isNullable: false,
          },
          {
            name: 'input_data',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'output_data',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'error_details',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'started_at',
            type: 'timestamptz',
            isNullable: false,
          },
          {
            name: 'completed_at',
            type: 'timestamptz',
            isNullable: true,
          },
          {
            name: 'execution_time_ms',
            type: 'int',
            isNullable: true,
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.createIndex(
      'mcp_workflows',
      new TableIndex({
        name: 'IDX_MCP_WORKFLOWS_NAME',
        columnNames: ['name'],
      })
    );

    await queryRunner.createIndex(
      'mcp_workflows',
      new TableIndex({
        name: 'IDX_MCP_WORKFLOWS_ENABLED',
        columnNames: ['enabled'],
      })
    );

    await queryRunner.createIndex(
      'mcp_workflow_executions',
      new TableIndex({
        name: 'IDX_MCP_WORKFLOW_EXECUTIONS_WORKFLOW_ID',
        columnNames: ['workflow_id'],
      })
    );

    await queryRunner.createIndex(
      'mcp_workflow_executions',
      new TableIndex({
        name: 'IDX_MCP_WORKFLOW_EXECUTIONS_STATUS',
        columnNames: ['status'],
      })
    );

    await queryRunner.createIndex(
      'mcp_workflow_node_executions',
      new TableIndex({
        name: 'IDX_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID',
        columnNames: ['workflow_execution_id'],
      })
    );

    await queryRunner.createIndex(
      'mcp_workflow_node_executions',
      new TableIndex({
        name: 'IDX_MCP_WORKFLOW_NODE_EXECUTIONS_STATUS',
        columnNames: ['status'],
      })
    );

    // Create foreign keys
    await queryRunner.createForeignKey(
      'mcp_workflow_executions',
      new TableForeignKey({
        name: 'FK_MCP_WORKFLOW_EXECUTIONS_WORKFLOW_ID',
        columnNames: ['workflow_id'],
        referencedTableName: 'mcp_workflows',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      })
    );

    await queryRunner.createForeignKey(
      'mcp_workflow_node_executions',
      new TableForeignKey({
        name: 'FK_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID',
        columnNames: ['workflow_execution_id'],
        referencedTableName: 'mcp_workflow_executions',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys
    await queryRunner.dropForeignKey(
      'mcp_workflow_node_executions',
      'FK_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID'
    );
    await queryRunner.dropForeignKey(
      'mcp_workflow_executions',
      'FK_MCP_WORKFLOW_EXECUTIONS_WORKFLOW_ID'
    );

    // Drop tables
    await queryRunner.dropTable('mcp_workflow_node_executions');
    await queryRunner.dropTable('mcp_workflow_executions');
    await queryRunner.dropTable('mcp_workflows');
  }
}
