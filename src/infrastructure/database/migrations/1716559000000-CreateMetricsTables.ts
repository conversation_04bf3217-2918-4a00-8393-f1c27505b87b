import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to create metrics tables
 */
export class CreateMetricsTables1716559000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create workflow_metrics table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS workflow_metrics (
        id UUID PRIMARY KEY,
        workflow_id UUID NOT NULL,
        execution_id UUID NOT NULL,
        start_time TIMESTAMPTZ NOT NULL,
        end_time TIMESTAMPTZ,
        duration_ms INTEGER,
        status VARCHAR(20) NOT NULL,
        error TEXT,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      );
    `);

    // Create node_metrics table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS node_metrics (
        id UUID PRIMARY KEY,
        node_id UUID NOT NULL,
        execution_id UUID NOT NULL,
        node_type VARCHAR(50) NOT NULL,
        start_time TIMESTAMPTZ NOT NULL,
        end_time TIMESTAMPTZ,
        duration_ms INTEGER,
        status VARCHAR(20) NOT NULL,
        error TEXT,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      );
    `);

    // Create indexes for faster queries
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_metrics_workflow_id ON workflow_metrics(workflow_id);
      CREATE INDEX IF NOT EXISTS idx_workflow_metrics_execution_id ON workflow_metrics(execution_id);
      CREATE INDEX IF NOT EXISTS idx_workflow_metrics_status ON workflow_metrics(status);
      CREATE INDEX IF NOT EXISTS idx_workflow_metrics_start_time ON workflow_metrics(start_time);
      
      CREATE INDEX IF NOT EXISTS idx_node_metrics_node_id ON node_metrics(node_id);
      CREATE INDEX IF NOT EXISTS idx_node_metrics_execution_id ON node_metrics(execution_id);
      CREATE INDEX IF NOT EXISTS idx_node_metrics_node_type ON node_metrics(node_type);
      CREATE INDEX IF NOT EXISTS idx_node_metrics_status ON node_metrics(status);
      CREATE INDEX IF NOT EXISTS idx_node_metrics_start_time ON node_metrics(start_time);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_workflow_metrics_workflow_id;
      DROP INDEX IF EXISTS idx_workflow_metrics_execution_id;
      DROP INDEX IF EXISTS idx_workflow_metrics_status;
      DROP INDEX IF EXISTS idx_workflow_metrics_start_time;
      
      DROP INDEX IF EXISTS idx_node_metrics_node_id;
      DROP INDEX IF EXISTS idx_node_metrics_execution_id;
      DROP INDEX IF EXISTS idx_node_metrics_node_type;
      DROP INDEX IF EXISTS idx_node_metrics_status;
      DROP INDEX IF EXISTS idx_node_metrics_start_time;
    `);

    // Drop tables
    await queryRunner.query(`DROP TABLE IF EXISTS node_metrics;`);
    await queryRunner.query(`DROP TABLE IF EXISTS workflow_metrics;`);
  }
}
