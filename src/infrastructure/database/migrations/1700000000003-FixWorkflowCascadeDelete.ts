import { MigrationInterface, QueryRunner, TableFore<PERSON><PERSON><PERSON> } from 'typeorm';

export class FixWorkflowCascadeDelete1700000000003 implements MigrationInterface {
  name = 'FixWorkflowCascadeDelete1700000000003';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing foreign key constraint that doesn't have CASCADE
    await queryRunner.dropForeignKey('mcp_workflow_executions', 'FK_4a7d2bc315e54aeebb56220950b');

    // Recreate with CASCADE delete
    await queryRunner.createForeignKey(
      'mcp_workflow_executions',
      new TableForeignKey({
        name: 'FK_MCP_WORKFLOW_EXECUTIONS_WORKFLOW_ID_CASCADE',
        columnNames: ['workflow_id'],
        referencedTableName: 'mcp_workflows',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      })
    );

    // Also check and fix workflow_node_executions if needed
    try {
      await queryRunner.dropForeignKey('mcp_workflow_node_executions', 'FK_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID');
      
      await queryRunner.createForeignKey(
        'mcp_workflow_node_executions',
        new TableForeignKey({
          name: 'FK_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID_CASCADE',
          columnNames: ['workflow_execution_id'],
          referencedTableName: 'mcp_workflow_executions',
          referencedColumnNames: ['id'],
          onDelete: 'CASCADE',
        })
      );
    } catch (error) {
      // Foreign key might already be correct or not exist
      console.log('Note: workflow_node_executions foreign key might already be correct');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop CASCADE foreign keys
    await queryRunner.dropForeignKey('mcp_workflow_executions', 'FK_MCP_WORKFLOW_EXECUTIONS_WORKFLOW_ID_CASCADE');

    // Recreate original foreign key without CASCADE
    await queryRunner.createForeignKey(
      'mcp_workflow_executions',
      new TableForeignKey({
        name: 'FK_4a7d2bc315e54aeebb56220950b',
        columnNames: ['workflow_id'],
        referencedTableName: 'mcp_workflows',
        referencedColumnNames: ['id'],
        onDelete: 'RESTRICT', // Original behavior
      })
    );

    try {
      await queryRunner.dropForeignKey('mcp_workflow_node_executions', 'FK_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID_CASCADE');
      
      await queryRunner.createForeignKey(
        'mcp_workflow_node_executions',
        new TableForeignKey({
          name: 'FK_MCP_WORKFLOW_NODE_EXECUTIONS_WORKFLOW_EXECUTION_ID',
          columnNames: ['workflow_execution_id'],
          referencedTableName: 'mcp_workflow_executions',
          referencedColumnNames: ['id'],
          onDelete: 'RESTRICT',
        })
      );
    } catch (error) {
      console.log('Note: workflow_node_executions foreign key rollback might not be needed');
    }
  }
}
