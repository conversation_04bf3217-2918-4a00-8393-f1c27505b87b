import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to add time-based indexes to execution tables for better performance
 * and efficient cleanup of old execution data
 */
export class AddExecutionTimeIndexes1716562000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add index on started_at for workflow executions
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_executions_started_at ON mcp_workflow_executions(started_at);
    `);

    // Add index on started_at for workflow node executions
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_node_executions_started_at ON mcp_workflow_node_executions(started_at);
    `);
    
    // Add index on completed_at for workflow executions (for completed executions queries)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_executions_completed_at ON mcp_workflow_executions(completed_at);
    `);
    
    // Add index on completed_at for workflow node executions (for completed executions queries)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_node_executions_completed_at ON mcp_workflow_node_executions(completed_at);
    `);
    
    // Add index on status for workflow executions (for filtering by status)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON mcp_workflow_executions(status);
    `);
    
    // Add index on status for workflow node executions (for filtering by status)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_node_executions_status ON mcp_workflow_node_executions(status);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_workflow_executions_started_at;
      DROP INDEX IF EXISTS idx_workflow_node_executions_started_at;
      DROP INDEX IF EXISTS idx_workflow_executions_completed_at;
      DROP INDEX IF EXISTS idx_workflow_node_executions_completed_at;
      DROP INDEX IF EXISTS idx_workflow_executions_status;
      DROP INDEX IF EXISTS idx_workflow_node_executions_status;
    `);
  }
}
