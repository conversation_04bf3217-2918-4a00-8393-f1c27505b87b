import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to add hot-reload trigger for mcp_functions table
 */
export class AddMCPFunctionsTrigger1716700000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create or replace the notification function if it doesn't exist
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION notify_config_change()
      RETURNS TRIGGER AS $$
      BEGIN
          PERFORM pg_notify('mcp_config_updates', json_build_object(
              'entity_type', TG_TABLE_NAME,
              'id', COALESCE(NEW.id, OLD.id),
              'name', COALESCE(NEW.name, OLD.name),
              'action', TG_OP
          )::text);
          RETURN COALESCE(NEW, OLD);
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Add trigger for mcp_functions table
    await queryRunner.query(`
      CREATE TRIGGER mcp_functions_notify
      AFTER INSERT OR UPDATE OR DELETE ON mcp_functions
      FOR EACH ROW EXECUTE FUNCTION notify_config_change();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the trigger
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS mcp_functions_notify ON mcp_functions;
    `);
  }
}
