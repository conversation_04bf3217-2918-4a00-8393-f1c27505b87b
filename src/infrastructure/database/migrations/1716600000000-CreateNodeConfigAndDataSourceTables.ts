import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

/**
 * Migration to create node config and data source tables
 */
export class CreateNodeConfigAndDataSourceTables1716600000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create node_configs table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_node_configs',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'node_type',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'config',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'now()',
          },
        ],
      }),
      true
    );

    // Create indexes for node_configs table
    await queryRunner.createIndex(
      'mcp_node_configs',
      new TableIndex({
        name: 'IDX_NODE_CONFIGS_NAME',
        columnNames: ['name'],
      })
    );

    await queryRunner.createIndex(
      'mcp_node_configs',
      new TableIndex({
        name: 'IDX_NODE_CONFIGS_TYPE',
        columnNames: ['node_type'],
      })
    );

    await queryRunner.createIndex(
      'mcp_node_configs',
      new TableIndex({
        name: 'IDX_NODE_CONFIGS_ENABLED',
        columnNames: ['enabled'],
      })
    );

    // Create data_sources table
    await queryRunner.createTable(
      new Table({
        name: 'mcp_data_sources',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'type',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'connection_config_encrypted',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'now()',
          },
        ],
      }),
      true
    );

    // Create indexes for data_sources table
    await queryRunner.createIndex(
      'mcp_data_sources',
      new TableIndex({
        name: 'IDX_DATA_SOURCES_NAME',
        columnNames: ['name'],
        isUnique: true,
      })
    );

    await queryRunner.createIndex(
      'mcp_data_sources',
      new TableIndex({
        name: 'IDX_DATA_SOURCES_TYPE',
        columnNames: ['type'],
      })
    );

    await queryRunner.createIndex(
      'mcp_data_sources',
      new TableIndex({
        name: 'IDX_DATA_SOURCES_ENABLED',
        columnNames: ['enabled'],
      })
    );

    // Create database triggers for hot-reload mechanism
    await queryRunner.query(`
      -- Function to notify about configuration changes
      CREATE OR REPLACE FUNCTION notify_config_change()
      RETURNS TRIGGER AS $$
      BEGIN
          PERFORM pg_notify('mcp_config_updates', json_build_object(
              'entity_type', TG_TABLE_NAME,
              'id', NEW.id,
              'name', NEW.name,
              'action', TG_OP
          )::text);
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      -- Trigger for mcp_node_configs
      CREATE TRIGGER mcp_node_configs_notify
      AFTER INSERT OR UPDATE OR DELETE ON mcp_node_configs
      FOR EACH ROW EXECUTE FUNCTION notify_config_change();

      -- Trigger for mcp_data_sources
      CREATE TRIGGER mcp_data_sources_notify
      AFTER INSERT OR UPDATE OR DELETE ON mcp_data_sources
      FOR EACH ROW EXECUTE FUNCTION notify_config_change();
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop triggers
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS mcp_node_configs_notify ON mcp_node_configs;
      DROP TRIGGER IF EXISTS mcp_data_sources_notify ON mcp_data_sources;
    `);

    // Drop function
    await queryRunner.query(`
      DROP FUNCTION IF EXISTS notify_config_change();
    `);

    // Drop tables
    await queryRunner.dropTable('mcp_data_sources', true);
    await queryRunner.dropTable('mcp_node_configs', true);
  }
}
