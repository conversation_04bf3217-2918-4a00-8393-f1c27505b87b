import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

/**
 * Migration to create the mcp_functions table
 */
export class CreateMCPFunctionsTable1621500000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'mcp_functions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255',
            isUnique: true,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'input_schema',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'handler_config',
            type: 'jsonb',
            isNullable: false,
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'now()',
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'now()',
          },
        ],
      }),
      true
    );

    // Create indexes
    await queryRunner.createIndex(
      'mcp_functions',
      new TableIndex({
        name: 'IDX_MCP_FUNCTIONS_NAME',
        columnNames: ['name'],
      })
    );

    await queryRunner.createIndex(
      'mcp_functions',
      new TableIndex({
        name: 'IDX_MCP_FUNCTIONS_ENABLED',
        columnNames: ['enabled'],
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('mcp_functions');
  }
}
