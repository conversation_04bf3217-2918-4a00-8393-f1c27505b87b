import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration to add time-based indexes to metrics tables for better performance
 * and efficient cleanup of old metrics
 */
export class AddMetricsTimeIndexes1716561000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add index on start_time for workflow_metrics
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_metrics_start_time_cleanup ON workflow_metrics(start_time);
    `);

    // Add index on start_time for node_metrics
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_node_metrics_start_time_cleanup ON node_metrics(start_time);
    `);
    
    // Add index on end_time for workflow_metrics (for completed metrics queries)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_workflow_metrics_end_time ON workflow_metrics(end_time);
    `);
    
    // Add index on end_time for node_metrics (for completed metrics queries)
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_node_metrics_end_time ON node_metrics(end_time);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`
      DROP INDEX IF EXISTS idx_workflow_metrics_start_time_cleanup;
      DROP INDEX IF EXISTS idx_node_metrics_start_time_cleanup;
      DROP INDEX IF EXISTS idx_workflow_metrics_end_time;
      DROP INDEX IF EXISTS idx_node_metrics_end_time;
    `);
  }
}
