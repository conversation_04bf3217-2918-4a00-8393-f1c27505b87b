import { DataSource, DataSourceOptions } from 'typeorm';
import { MCPFunction } from './entities/MCPFunction.entity';
import { Workflow } from './entities/Workflow.entity';
import { WorkflowExecution } from './entities/WorkflowExecution.entity';
import { WorkflowNodeExecution } from './entities/WorkflowNodeExecution.entity';
import { WorkflowMetrics } from './entities/WorkflowMetrics.entity';
import { NodeMetrics } from './entities/NodeMetrics.entity';
import { NodeConfig } from './entities/NodeConfig.entity';
import { DataSource as DataSourceEntity } from './entities/DataSource.entity';
import { Tenant } from './entities/Tenant.entity';
import { TenantUser } from './entities/TenantUser.entity';
import { CreateMCPFunctionsTable1621500000000 } from './migrations/1621500000000-CreateMCPFunctionsTable';
import { CreateWorkflowTables1621500000001 } from './migrations/1621500000001-CreateWorkflowTables';
import { CreateMetricsTables1716559000000 } from './migrations/1716559000000-CreateMetricsTables';
import { AlterNodeMetricsTable1716560000000 } from './migrations/1716560000000-AlterNodeMetricsTable';
import { AddMetricsTimeIndexes1716561000000 } from './migrations/1716561000000-AddMetricsTimeIndexes';
import { AddExecutionTimeIndexes1716562000000 } from './migrations/1716562000000-AddExecutionTimeIndexes';
import { CreateNodeConfigAndDataSourceTables1716600000000 } from './migrations/1716600000000-CreateNodeConfigAndDataSourceTables';
import { CreateTenantTables1700000000001 } from './migrations/1700000000001-CreateTenantTables';
import { getEnv, getEnvNumber, getEnvBoolean } from '../../utils/env';

/**
 * TypeORM configuration
 */
export const typeOrmConfig: DataSourceOptions = {
  type: 'postgres',
  host: getEnv('DB_HOST', 'localhost'),
  port: getEnvNumber('DB_PORT', 5432),
  migrationsRun: true,
  username: getEnv('DB_USERNAME', 'postgres'),
  password: getEnv('DB_PASSWORD', 'postgres'),
  database: getEnv('DB_DATABASE', 'mcp_server'),
  synchronize: getEnv('NODE_ENV', 'development') !== 'production',
  logging: getEnv('NODE_ENV', 'development') !== 'production',
  entities: [
    MCPFunction,
    Workflow,
    WorkflowExecution,
    WorkflowNodeExecution,
    WorkflowMetrics,
    NodeMetrics,
    NodeConfig,
    DataSourceEntity,
    Tenant,
    TenantUser
  ],
  migrations: [
    CreateMCPFunctionsTable1621500000000,
    CreateWorkflowTables1621500000001,
    CreateMetricsTables1716559000000,
    AlterNodeMetricsTable1716560000000,
    AddMetricsTimeIndexes1716561000000,
    AddExecutionTimeIndexes1716562000000,
    CreateNodeConfigAndDataSourceTables1716600000000,
    CreateTenantTables1700000000001
  ],
  subscribers: []
};

// Create and export a DataSource instance
export default new DataSource(typeOrmConfig);
