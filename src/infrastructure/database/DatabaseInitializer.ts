import { DataSource } from 'typeorm';
import { injectable, inject } from 'inversify';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { getEnv, getEnvNumber } from '../../utils/env';

/**
 * Database initializer that ensures database exists before connecting
 */
@injectable()
export class DatabaseInitializer {
  constructor(@inject(TYPES.Logger) private logger: ILogger) {}

  /**
   * Initialize database - create if not exists
   */
  async initializeDatabase(): Promise<void> {
    const dbHost = getEnv('DB_HOST', 'localhost');
    const dbPort = getEnvNumber('DB_PORT', 5432);
    const dbUsername = getEnv('DB_USERNAME', 'postgres');
    const dbPassword = getEnv('DB_PASSWORD', 'postgres');
    const dbName = getEnv('DB_DATABASE', 'mcp_server');

    this.logger.info('Initializing database...');

    // First, connect to the default 'postgres' database to check if our database exists
    const adminDataSource = new DataSource({
      type: 'postgres',
      host: dbHost,
      port: dbPort,
      username: dbUsername,
      password: dbPassword,
      database: 'postgres', // Connect to default postgres database
      logging: false
    });

    try {
      await adminDataSource.initialize();
      this.logger.info('Connected to PostgreSQL server');

      // Check if our database exists
      const result = await adminDataSource.query(
        'SELECT 1 FROM pg_database WHERE datname = $1',
        [dbName]
      );

      if (result.length === 0) {
        // Database doesn't exist, create it
        this.logger.info(`Database '${dbName}' does not exist, creating...`);
        await adminDataSource.query(`CREATE DATABASE "${dbName}"`);
        this.logger.info(`Database '${dbName}' created successfully`);
      } else {
        this.logger.info(`Database '${dbName}' already exists`);
      }

      await adminDataSource.destroy();
      this.logger.info('Database initialization completed');
    } catch (error) {
      this.logger.error('Error during database initialization', error);
      await adminDataSource.destroy();
      throw error;
    }
  }
}
