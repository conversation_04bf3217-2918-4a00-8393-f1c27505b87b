import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';

/**
 * Entity representing a workflow stored in the database
 */
@Entity('mcp_workflows')
export class Workflow {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255, unique: true })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'int', default: 1 })
  version: number;

  @Column({ type: 'jsonb' })
  input_schema: any;

  @Column({ type: 'jsonb', nullable: true })
  output_schema: any;

  @Column({ type: 'jsonb' })
  nodes_config: {
    nodes: Array<{
      id: string;
      type: string;
      name: string;
      config: Record<string, any>;
    }>;
    edges: Array<{
      source: string;
      target: string;
    }>;
  };

  @Column({ default: true })
  @Index()
  enabled: boolean;

  @Column({ name: 'current_version', type: 'integer', default: 1 })
  currentVersion: number;

  @Column({ name: 'is_versioned', type: 'boolean', default: true })
  isVersioned: boolean;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  tenantId?: string;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;
}
