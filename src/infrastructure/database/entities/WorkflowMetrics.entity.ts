import { <PERSON><PERSON>ty, Column, PrimaryColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Workflow metrics entity
 * Stores metrics about workflow executions
 */
@Entity('workflow_metrics')
export class WorkflowMetrics {
  /**
   * Unique ID for the metrics record
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * Workflow ID
   */
  @Column('uuid')
  workflow_id: string;

  /**
   * Execution ID
   */
  @Column('uuid')
  execution_id: string;

  /**
   * Start time of the workflow execution
   */
  @Column('timestamp with time zone')
  start_time: Date;

  /**
   * End time of the workflow execution
   */
  @Column('timestamp with time zone', { nullable: true })
  end_time: Date;

  /**
   * Duration of the workflow execution in milliseconds
   */
  @Column('integer', { nullable: true })
  duration_ms: number;

  /**
   * Status of the workflow execution
   */
  @Column('varchar', { length: 20 })
  status: string;

  /**
   * Error details if the workflow execution failed
   */
  @Column('text', { nullable: true })
  error: string;

  /**
   * Creation timestamp
   */
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  /**
   * Last update timestamp
   */
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
