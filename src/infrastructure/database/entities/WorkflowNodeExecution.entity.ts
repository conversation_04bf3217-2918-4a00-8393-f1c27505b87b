import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, <PERSON>To<PERSON>ne, Jo<PERSON><PERSON><PERSON>umn, Index } from 'typeorm';
import { WorkflowExecution } from './WorkflowExecution.entity';

/**
 * Entity representing a workflow node execution stored in the database
 */
@Entity('mcp_workflow_node_executions')
export class WorkflowNodeExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  workflow_execution_id: string;

  @ManyToOne(() => WorkflowExecution, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workflow_execution_id' })
  workflow_execution: WorkflowExecution;

  @Column({ length: 255 })
  node_id: string;

  @Column({ length: 50 })
  node_type: string;

  @Column({ length: 50 })
  @Index()
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';

  @Column({ type: 'jsonb', nullable: true })
  input_data: any;

  @Column({ type: 'jsonb', nullable: true })
  output_data: any;

  @Column({ type: 'jsonb', nullable: true })
  error_details: any;

  @Column({ type: 'timestamptz' })
  started_at: Date;

  @Column({ type: 'timestamptz', nullable: true })
  completed_at: Date;

  @Column({ type: 'int', nullable: true })
  execution_time_ms: number;
}
