import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Tenant } from './Tenant.entity';

/**
 * User role interface
 */
export interface UserRole {
  name: string;
  permissions: Permission[];
}

/**
 * Permission interface
 */
export interface Permission {
  resource: string; // 'workflows', 'functions', 'data_sources', etc.
  actions: string[]; // 'read', 'write', 'delete', 'execute'
  conditions?: Record<string, any>; // Additional conditions
}

/**
 * Entity representing a user within a tenant
 */
@Entity('mcp_tenant_users')
@Index(['tenantId', 'username'], { unique: true })
@Index(['tenantId', 'email'], { unique: true })
export class TenantUser {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id', type: 'uuid' })
  @Index()
  tenantId: string;

  @Column({ length: 255 })
  username: string;

  @Column({ length: 255 })
  email: string;

  @Column({ name: 'password_hash', length: 255, nullable: true })
  passwordHash?: string;

  @Column({ type: 'jsonb', default: [] })
  roles: UserRole[];

  @Column({ default: true })
  enabled: boolean;

  @Column({ name: 'last_login_at', type: 'timestamptz', nullable: true })
  lastLoginAt?: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @ManyToOne(() => Tenant, tenant => tenant.users, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  /**
   * Check if user has permission for a specific resource and action
   */
  hasPermission(resource: string, action: string): boolean {
    return this.roles.some(role =>
      role.permissions.some(permission =>
        permission.resource === resource && permission.actions.includes(action)
      )
    );
  }

  /**
   * Get default admin role
   */
  static getAdminRole(): UserRole {
    return {
      name: 'admin',
      permissions: [
        {
          resource: '*',
          actions: ['read', 'write', 'delete', 'execute']
        }
      ]
    };
  }

  /**
   * Get default user role
   */
  static getUserRole(): UserRole {
    return {
      name: 'user',
      permissions: [
        {
          resource: 'workflows',
          actions: ['read', 'execute']
        },
        {
          resource: 'functions',
          actions: ['read', 'execute']
        }
      ]
    };
  }
}
