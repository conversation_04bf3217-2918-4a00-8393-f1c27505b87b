import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedC<PERSON>umn, ManyToOne, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { Workflow } from './Workflow.entity';

/**
 * Entity representing a workflow execution stored in the database
 */
@Entity('mcp_workflow_executions')
export class WorkflowExecution {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  workflow_id: string;

  @ManyToOne(() => Workflow, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workflow_id' })
  workflow: Workflow;

  @Column({ length: 50 })
  @Index()
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';

  @Column({ type: 'jsonb', nullable: true })
  input_data: any;

  @Column({ type: 'jsonb', nullable: true })
  output_data: any;

  @Column({ type: 'jsonb', nullable: true })
  error_details: any;

  @Column({ type: 'timestamptz' })
  started_at: Date;

  @Column({ type: 'timestamptz', nullable: true })
  completed_at: Date;

  @Column({ type: 'int', nullable: true })
  execution_time_ms: number;
}
