import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  Index
} from 'typeorm';

/**
 * Entity for storing user action history for audit trail
 */
@Entity('user_action_history')
@Index(['userId', 'timestamp'])
@Index(['tenantId', 'timestamp'])
@Index(['action', 'timestamp'])
@Index(['resourceType', 'resourceId'])
@Index(['correlationId'])
export class UserActionHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id', type: 'varchar', length: 255 })
  @Index()
  userId: string;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  tenantId?: string;

  @Column({ type: 'varchar', length: 100 })
  @Index()
  action: string;

  @Column({ name: 'resource_type', type: 'varchar', length: 100 })
  @Index()
  resourceType: string;

  @Column({ name: 'resource_id', type: 'varchar', length: 255 })
  @Index()
  resourceId: string;

  @Column({ type: 'timestamp with time zone' })
  @Index()
  timestamp: Date;

  @Column({ name: 'ip_address', type: 'varchar', length: 45, nullable: true })
  ipAddress?: string;

  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent?: string;

  @Column({ type: 'jsonb', nullable: true })
  details?: Record<string, any>;

  @Column({ name: 'correlation_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  correlationId?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
