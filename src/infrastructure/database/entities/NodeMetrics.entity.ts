import { <PERSON>tity, Column, PrimaryColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Node metrics entity
 * Stores metrics about node executions
 */
@Entity('node_metrics')
export class NodeMetrics {
  /**
   * Unique ID for the metrics record
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * Node ID
   */
  @Column('varchar', { length: 100 })
  node_id: string;

  /**
   * Execution ID
   */
  @Column('uuid')
  execution_id: string;

  /**
   * Node type
   */
  @Column('varchar', { length: 50 })
  node_type: string;

  /**
   * Start time of the node execution
   */
  @Column('timestamp with time zone')
  start_time: Date;

  /**
   * End time of the node execution
   */
  @Column('timestamp with time zone', { nullable: true })
  end_time: Date;

  /**
   * Duration of the node execution in milliseconds
   */
  @Column('integer', { nullable: true })
  duration_ms: number;

  /**
   * Status of the node execution
   */
  @Column('varchar', { length: 20 })
  status: string;

  /**
   * Error details if the node execution failed
   */
  @Column('text', { nullable: true })
  error: string;

  /**
   * Creation timestamp
   */
  @CreateDateColumn({ type: 'timestamp with time zone' })
  created_at: Date;

  /**
   * Last update timestamp
   */
  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updated_at: Date;
}
