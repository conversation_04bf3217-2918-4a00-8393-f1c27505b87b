import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany
} from 'typeorm';
import { Alert } from './Alert.entity';

/**
 * Entity for storing alert rules
 */
@Entity('alert_rules')
@Index(['tenantId', 'enabled'])
@Index(['enabled', 'evaluationInterval'])
@Index(['priority', 'enabled'])
@Index(['createdBy'])
export class AlertRule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'boolean', default: true })
  @Index()
  enabled: boolean;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  tenantId?: string;

  @Column({ type: 'jsonb' })
  conditions: any[];

  @Column({ type: 'varchar', length: 10, default: 'AND' })
  operator: string;

  @Column({ type: 'jsonb' })
  notifications: any[];

  @Column({ type: 'jsonb', nullable: true })
  escalation?: any;

  @Column({ name: 'evaluation_interval', type: 'integer', default: 60 })
  @Index()
  evaluationInterval: number;

  @Column({ name: 'cooldown_period', type: 'integer', default: 300 })
  cooldownPeriod: number;

  @Column({ type: 'simple-array', nullable: true })
  tags?: string[];

  @Column({ type: 'varchar', length: 20, default: 'medium' })
  @Index()
  priority: string;

  @Column({ name: 'created_by', type: 'varchar', length: 255, nullable: true })
  @Index()
  createdBy?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => Alert, alert => alert.alertRule)
  alerts: Alert[];
}
