import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany
} from 'typeorm';
import { TenantUser } from './TenantUser.entity';

/**
 * Tenant settings interface
 */
export interface TenantSettings {
  // Resource limits
  max_concurrent_workflows: number;
  max_script_execution_time: number;
  max_memory_usage: number;
  max_storage_usage: number;

  // Feature flags
  allowed_node_types: string[];
  allowed_data_sources: string[];
  enable_custom_functions: boolean;
  enable_external_apis: boolean;

  // Security settings
  require_2fa: boolean;
  session_timeout: number;
  allowed_ip_ranges?: string[];

  // Monitoring settings
  enable_detailed_logging: boolean;
  metrics_retention_days: number;
  alert_thresholds: Record<string, number>;
}

/**
 * Entity representing a tenant in the multi-tenant system
 */
@Entity('mcp_tenants')
export class Tenant {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255, unique: true })
  @Index()
  name: string;

  @Column({ name: 'display_name', length: 255 })
  displayName: string;

  @Column({ type: 'jsonb', default: {} })
  settings: TenantSettings;

  @Column({ name: 'encryption_key_id', length: 255, nullable: true })
  encryptionKeyId?: string;

  @Column({ default: true })
  @Index()
  enabled: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ name: 'created_by', length: 255, nullable: true })
  createdBy?: string;

  @OneToMany(() => TenantUser, tenantUser => tenantUser.tenant)
  users: TenantUser[];

  /**
   * Get default tenant settings
   */
  static getDefaultSettings(): TenantSettings {
    return {
      max_concurrent_workflows: 10,
      max_script_execution_time: 30000, // 30 seconds
      max_memory_usage: 512 * 1024 * 1024, // 512MB
      max_storage_usage: 1024 * 1024 * 1024, // 1GB
      allowed_node_types: ['javascript', 'http', 'sql', 'redis', 'litellm'],
      allowed_data_sources: ['postgresql', 'redis', 'http'],
      enable_custom_functions: true,
      enable_external_apis: true,
      require_2fa: false,
      session_timeout: 3600, // 1 hour
      enable_detailed_logging: true,
      metrics_retention_days: 30,
      alert_thresholds: {
        cpu_usage: 80,
        memory_usage: 80,
        error_rate: 5
      }
    };
  }
}
