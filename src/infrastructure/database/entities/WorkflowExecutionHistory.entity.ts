import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Workflow } from './Workflow.entity';

/**
 * Entity for storing workflow execution history
 */
@Entity('workflow_execution_history')
@Index(['workflowId', 'startTime'])
@Index(['tenantId', 'startTime'])
@Index(['status', 'startTime'])
@Index(['correlationId'])
@Index(['userId', 'startTime'])
export class WorkflowExecutionHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'workflow_id', type: 'uuid' })
  @Index()
  workflowId: string;

  @Column({ name: 'execution_id', type: 'uuid' })
  @Index()
  executionId: string;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  tenantId?: string;

  @Column({ type: 'varchar', length: 50 })
  @Index()
  status: string;

  @Column({ name: 'start_time', type: 'timestamp with time zone' })
  @Index()
  startTime: Date;

  @Column({ name: 'end_time', type: 'timestamp with time zone', nullable: true })
  endTime?: Date;

  @Column({ type: 'integer', nullable: true })
  duration?: number;

  @Column({ name: 'input_data', type: 'jsonb', nullable: true })
  inputData?: any;

  @Column({ name: 'output_data', type: 'jsonb', nullable: true })
  outputData?: any;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ name: 'node_executions', type: 'jsonb', nullable: true })
  nodeExecutions?: any[];

  @Column({ name: 'correlation_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  correlationId?: string;

  @Column({ name: 'user_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  userId?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Workflow, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workflow_id' })
  workflow: Workflow;
}
