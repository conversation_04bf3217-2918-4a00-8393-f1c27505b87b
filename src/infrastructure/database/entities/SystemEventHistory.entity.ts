import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  Index
} from 'typeorm';

/**
 * Entity for storing system events for monitoring and audit
 */
@Entity('system_event_history')
@Index(['eventType', 'timestamp'])
@Index(['eventCategory', 'timestamp'])
@Index(['severity', 'timestamp'])
@Index(['tenantId', 'timestamp'])
@Index(['correlationId'])
@Index(['source', 'timestamp'])
export class SystemEventHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'event_type', type: 'varchar', length: 100 })
  @Index()
  eventType: string;

  @Column({ name: 'event_category', type: 'varchar', length: 50 })
  @Index()
  eventCategory: string;

  @Column({ type: 'varchar', length: 20 })
  @Index()
  severity: string;

  @Column({ type: 'timestamp with time zone' })
  @Index()
  timestamp: Date;

  @Column({ type: 'varchar', length: 255 })
  @Index()
  source: string;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'jsonb', nullable: true })
  details?: Record<string, any>;

  @Column({ name: 'correlation_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  correlationId?: string;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  tenantId?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
