import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { WorkflowVersion } from './WorkflowVersion.entity';

@Entity('workflow_version_history')
@Index(['versionId', 'createdAt'])
@Index(['workflowId', 'createdAt'])
@Index(['changeType'])
export class WorkflowVersionHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'version_id', type: 'uuid' })
  versionId: string;

  @Column({ name: 'workflow_id', type: 'uuid' })
  workflowId: string;

  @Column({ type: 'integer' })
  version: number;

  @Column({ name: 'change_type', type: 'varchar', length: 50 })
  changeType: string;

  @Column({ name: 'change_description', type: 'text', nullable: true })
  changeDescription?: string;

  @Column({ name: 'changed_by', type: 'varchar', length: 255 })
  changedBy: string;

  @Column({ name: 'previous_version', type: 'integer', nullable: true })
  previousVersion?: number;

  @Column({ name: 'rollback_reason', type: 'text', nullable: true })
  rollbackReason?: string;

  @Column({ type: 'jsonb', nullable: true })
  changeDetails?: any;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: any;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  tenantId?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => WorkflowVersion, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'version_id' })
  workflowVersion: WorkflowVersion;
}
