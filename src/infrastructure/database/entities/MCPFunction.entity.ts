import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';

/**
 * Entity representing an MCP function stored in the database
 */
@Entity('mcp_functions')
export class MCPFunction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255, unique: true })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'jsonb' })
  input_schema: any;

  @Column({ type: 'jsonb' })
  handler_config: {
    type: 'script' | 'workflow';
    script_content?: string;
    workflow_id?: string;
  };

  @Column({ default: true })
  @Index()
  enabled: boolean;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;
}
