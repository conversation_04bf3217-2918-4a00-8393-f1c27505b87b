import { <PERSON><PERSON>ty, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * Entity representing a data source stored in the database
 * These are connection configurations for external systems like databases, APIs, etc.
 * Sensitive connection information is stored in encrypted format
 */
@Entity('mcp_data_sources')
export class DataSource {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255, unique: true })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 50 })
  @Index()
  type: string;

  @Column({ type: 'text' })
  connection_config_encrypted: string;

  @Column({ default: true })
  @Index()
  enabled: boolean;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;
}
