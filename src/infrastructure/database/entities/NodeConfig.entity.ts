import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * Entity representing a node configuration stored in the database
 * These are reusable node configurations that can be used in workflows
 */
@Entity('mcp_node_configs')
export class NodeConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 50 })
  @Index()
  node_type: string;

  @Column({ type: 'jsonb' })
  config: Record<string, any>;

  @Column({ default: true })
  @Index()
  enabled: boolean;

  @CreateDateColumn({ type: 'timestamptz' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updated_at: Date;
}
