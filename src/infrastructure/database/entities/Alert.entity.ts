import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { AlertRule } from './AlertRule.entity';

/**
 * Entity for storing alerts
 */
@Entity('alerts')
@Index(['ruleId', 'status'])
@Index(['tenantId', 'status'])
@Index(['severity', 'triggeredAt'])
@Index(['status', 'triggeredAt'])
@Index(['correlationId'])
@Index(['acknowledgedBy'])
@Index(['resolvedBy'])
export class Alert {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'rule_id', type: 'uuid' })
  @Index()
  ruleId: string;

  @Column({ name: 'rule_name', type: 'varchar', length: 255 })
  ruleName: string;

  @Column({ name: 'tenant_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  tenantId?: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'varchar', length: 20 })
  @Index()
  severity: string;

  @Column({ type: 'varchar', length: 20, default: 'active' })
  @Index()
  status: string;

  @Column({ name: 'triggered_at', type: 'timestamp with time zone' })
  @Index()
  triggeredAt: Date;

  @Column({ name: 'acknowledged_at', type: 'timestamp with time zone', nullable: true })
  acknowledgedAt?: Date;

  @Column({ name: 'resolved_at', type: 'timestamp with time zone', nullable: true })
  resolvedAt?: Date;

  @Column({ type: 'jsonb' })
  conditions: any[];

  @Column({ name: 'actual_values', type: 'jsonb' })
  actualValues: Record<string, any>;

  @Column({ name: 'correlation_id', type: 'varchar', length: 255, nullable: true })
  @Index()
  correlationId?: string;

  @Column({ name: 'escalation_level', type: 'integer', nullable: true })
  escalationLevel?: number;

  @Column({ name: 'escalated_at', type: 'timestamp with time zone', nullable: true })
  escalatedAt?: Date;

  @Column({ name: 'acknowledged_by', type: 'varchar', length: 255, nullable: true })
  @Index()
  acknowledgedBy?: string;

  @Column({ name: 'acknowledgment_note', type: 'text', nullable: true })
  acknowledgmentNote?: string;

  @Column({ name: 'resolved_by', type: 'varchar', length: 255, nullable: true })
  @Index()
  resolvedBy?: string;

  @Column({ name: 'resolution_note', type: 'text', nullable: true })
  resolutionNote?: string;

  @Column({ name: 'auto_resolved', type: 'boolean', default: false })
  autoResolved?: boolean;

  @Column({ type: 'simple-array', nullable: true })
  tags?: string[];

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => AlertRule, alertRule => alertRule.alerts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'rule_id' })
  alertRule: AlertRule;
}
