import { injectable, inject } from 'inversify';
import { createClient, RedisClientType } from 'redis';
import { IRedisClient } from '../../core/interfaces/IRedisClient';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { getEnv, getEnvNumber } from '../../utils/env';

/**
 * Redis client implementation
 */
@injectable()
export class RedisClient implements IRedisClient {
  private client: RedisClientType | null = null;
  private subscribers: Map<string, (message: string) => void> = new Map();
  private pubSubClient: RedisClientType | null = null;

  /**
   * Constructor
   * @param logger Logger
   */
  constructor(@inject(TYPES.Logger) private logger: ILogger) {}

  /**
   * Initialize the Redis client
   */
  async initialize(): Promise<void> {
    if (this.client) {
      return;
    }

    const host = getEnv('REDIS_HOST', 'localhost');
    const port = getEnvNumber('REDIS_PORT', 6379);
    const password = getEnv('REDIS_PASSWORD', '');
    const url = `redis://${password ? `:${password}@` : ''}${host}:${port}`;

    this.logger.debug(`Initializing Redis client with URL: ${url}`);

    try {
      this.client = createClient({ url });

      this.client.on('error', (err) => {
        this.logger.error(`Redis client error: ${err.message}`);
      });

      await this.client.connect();
      this.logger.info('Redis client connected');
    } catch (error) {
      this.logger.error(`Failed to initialize Redis client: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Close the Redis client connection
   */
  async close(): Promise<void> {
    if (this.pubSubClient) {
      await this.pubSubClient.quit();
      this.pubSubClient = null;
    }

    if (this.client) {
      await this.client.quit();
      this.client = null;
    }
  }

  /**
   * Get a value from Redis
   * @param key Key
   * @returns Value or null if not found
   */
  async get(key: string): Promise<string | null> {
    await this.ensureConnection();
    return await this.client!.get(key);
  }

  /**
   * Set a value in Redis
   * @param key Key
   * @param value Value
   * @param ttl TTL in seconds (optional)
   */
  async set(key: string, value: string, ttl?: number): Promise<void> {
    await this.ensureConnection();

    if (ttl) {
      await this.client!.set(key, value, { EX: ttl });
    } else {
      await this.client!.set(key, value);
    }
  }

  /**
   * Delete a key from Redis
   * @param key Key
   * @returns Number of keys deleted
   */
  async del(key: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.del(key);
  }

  /**
   * Check if a key exists in Redis
   * @param key Key
   * @returns Whether the key exists
   */
  async exists(key: string): Promise<boolean> {
    await this.ensureConnection();
    const result = await this.client!.exists(key);
    return result ? true : false;
  }

  /**
   * Set expiration time for a key
   * @param key Key
   * @param ttl TTL in seconds
   * @returns Whether the operation was successful
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    await this.ensureConnection();
    const result = await this.client!.expire(key, ttl);
    return result ? true : false;
  }

  /**
   * Get TTL for a key
   * @param key Key
   * @returns TTL in seconds, -1 if no expiration, -2 if key does not exist
   */
  async ttl(key: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.ttl(key);
  }

  /**
   * Execute a Redis command
   * @param command Command
   * @param args Command arguments
   * @returns Command result
   */
  async executeCommand(command: string, args: any[]): Promise<any> {
    await this.ensureConnection();

    // @ts-ignore - Redis client has a dynamic command interface
    if (typeof this.client![command] !== 'function') {
      throw new Error(`Unknown Redis command: ${command}`);
    }

    try {
      // @ts-ignore - Redis client has a dynamic command interface
      return await this.client![command](...args);
    } catch (error) {
      this.logger.error(`Redis command execution failed: ${(error as Error).message}`);
      throw error;
    }
  }

  /**
   * Publish a message to a channel
   * @param channel Channel
   * @param message Message
   * @returns Number of clients that received the message
   */
  async publish(channel: string, message: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.publish(channel, message);
  }

  /**
   * Subscribe to a channel
   * @param channel Channel
   * @param callback Callback function
   */
  async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
    await this.ensurePubSubConnection();

    this.subscribers.set(channel, callback);
    await this.pubSubClient!.subscribe(channel, (message) => {
      const cb = this.subscribers.get(channel);
      if (cb) {
        cb(message);
      }
    });
  }

  /**
   * Unsubscribe from a channel
   * @param channel Channel
   */
  async unsubscribe(channel: string): Promise<void> {
    if (!this.pubSubClient) {
      return;
    }

    await this.pubSubClient.unsubscribe(channel);
    this.subscribers.delete(channel);
  }

  /**
   * Set a value with expiration time
   * @param key Key
   * @param ttl TTL in seconds
   * @param value Value
   */
  async setex(key: string, ttl: number, value: string): Promise<void> {
    await this.ensureConnection();
    await this.client!.setEx(key, ttl, value);
  }

  /**
   * Increment a numeric value
   * @param key Key
   * @returns New value after increment
   */
  async incr(key: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.incr(key);
  }

  /**
   * Decrement a numeric value
   * @param key Key
   * @returns New value after decrement
   */
  async decr(key: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.decr(key);
  }

  /**
   * Push element to the left of a list
   * @param key List key
   * @param value Value to push
   * @returns Length of the list after the push operation
   */
  async lpush(key: string, value: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.lPush(key, value);
  }

  /**
   * Push element to the right of a list
   * @param key List key
   * @param value Value to push
   * @returns Length of the list after the push operation
   */
  async rpush(key: string, value: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.rPush(key, value);
  }

  /**
   * Pop element from the left of a list
   * @param key List key
   * @returns Popped element or null if list is empty
   */
  async lpop(key: string): Promise<string | null> {
    await this.ensureConnection();
    return await this.client!.lPop(key);
  }

  /**
   * Pop element from the right of a list
   * @param key List key
   * @returns Popped element or null if list is empty
   */
  async rpop(key: string): Promise<string | null> {
    await this.ensureConnection();
    return await this.client!.rPop(key);
  }

  /**
   * Get the length of a list
   * @param key List key
   * @returns Length of the list
   */
  async llen(key: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.lLen(key);
  }

  /**
   * Get a field value from a hash
   * @param key Hash key
   * @param field Field name
   * @returns Field value or null if not found
   */
  async hget(key: string, field: string): Promise<string | null> {
    await this.ensureConnection();
    const result = await this.client!.hGet(key, field);
    return result || null;
  }

  /**
   * Set a field value in a hash
   * @param key Hash key
   * @param field Field name
   * @param value Field value
   * @returns Number of fields that were added
   */
  async hset(key: string, field: string, value: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.hSet(key, field, value);
  }

  /**
   * Delete a field from a hash
   * @param key Hash key
   * @param field Field name
   * @returns Number of fields that were removed
   */
  async hdel(key: string, field: string): Promise<number> {
    await this.ensureConnection();
    return await this.client!.hDel(key, field);
  }

  /**
   * Get all fields and values from a hash
   * @param key Hash key
   * @returns Object with all field-value pairs
   */
  async hgetall(key: string): Promise<Record<string, string>> {
    await this.ensureConnection();
    return await this.client!.hGetAll(key);
  }

  /**
   * Trim a list to the specified range
   * @param key List key
   * @param start Start index
   * @param stop Stop index
   */
  async ltrim(key: string, start: number, stop: number): Promise<void> {
    await this.ensureConnection();
    await this.client!.lTrim(key, start, stop);
  }

  /**
   * Get a range of elements from a list
   * @param key List key
   * @param start Start index
   * @param stop Stop index
   * @returns Array of elements
   */
  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    await this.ensureConnection();
    return await this.client!.lRange(key, start, stop);
  }

  /**
   * Find all keys matching a pattern
   * @param pattern Pattern to match
   * @returns Array of matching keys
   */
  async keys(pattern: string): Promise<string[]> {
    await this.ensureConnection();
    return await this.client!.keys(pattern);
  }

  /**
   * Ensure that the Redis client is connected
   */
  private async ensureConnection(): Promise<void> {
    if (!this.client) {
      await this.initialize();
    } else if (!this.client.isOpen) {
      await this.client.connect();
    }
  }

  /**
   * Ensure that the pub/sub client is connected
   */
  private async ensurePubSubConnection(): Promise<void> {
    if (!this.pubSubClient) {
      const host = getEnv('REDIS_HOST', 'localhost');
      const port = getEnvNumber('REDIS_PORT', 6379);
      const password = getEnv('REDIS_PASSWORD', '');
      const url = `redis://${password ? `:${password}@` : ''}${host}:${port}`;

      this.pubSubClient = createClient({ url });

      this.pubSubClient.on('error', (err) => {
        this.logger.error(`Redis pub/sub client error: ${err.message}`);
      });

      await this.pubSubClient.connect();
    } else if (!this.pubSubClient.isOpen) {
      await this.pubSubClient.connect();
    }
  }
}
