import { VM, VMScript } from 'vm2';
import { injectable, inject } from 'inversify';
import { IScriptEngine, ScriptExecutionOptions } from '../../core/interfaces/IScriptEngine';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * VM2-based script engine for secure JavaScript execution
 */
@injectable()
export class VM2ScriptEngine implements IScriptEngine {
  constructor(@inject(TYPES.Logger) private logger: ILogger) {}

  /**
   * Execute JavaScript code in a sandbox
   * @param code JavaScript code to execute
   * @param context Context variables available to the code
   * @param options Additional execution options
   */
  async execute<T = any>(
    code: string,
    context: Record<string, any> = {},
    options: ScriptExecutionOptions = {}
  ): Promise<T> {
    const vm = new VM({
      timeout: options.timeout || 5000,
      sandbox: {},
      eval: false,
      wasm: false,
      allowAsync: true
    });

    // Add context variables to the sandbox
    Object.entries(context).forEach(([key, value]) => {
      vm.freeze(value, key);
    });

    try {
      // Wrap the code in a function to properly handle return statements
      const wrappedCode = `
        (async function() {
          try {
            ${code}
          } catch (error) {
            return { error: error.message };
          }
        })()
      `;

      // Create a script with the provided code
      const script = new VMScript(wrappedCode);

      // Execute the script
      const result = await vm.run(script);

      // Check if there was an error
      if (result && typeof result === 'object' && 'error' in result) {
        throw new Error(result.error);
      }

      return result as T;
    } catch (error) {
      this.logger.error('Error executing script', error);
      throw error;
    }
  }
}
