import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import express from 'express';
import { z } from 'zod';
import { ServerConfig } from '../config';
import Logger from '../utils/logger';
// Import package.json for name and version
import pkg from '../../package.json';

export class VoicebotMcpServer {
  private server: McpServer;
  private app: express.Application;
  private logger: Logger;
  private config: ServerConfig;

  constructor(config: ServerConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.app = express();
    this.server = new McpServer({
      name: pkg.name,
      version: pkg.version
    });

    this.setupMiddleware();
    this.registerTools();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());

    // Add basic request logging
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`);
      next();
    });

    // Add health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({ status: 'ok' });
    });

    // Add MCP Inspector in development mode
    if (this.config.environment === 'development') {
      this.logger.info('Development mode detected, MCP Inspector will be available');
      // Inspector will be added in a future update
    }
  }

  private registerTools(): void {
    // Register getDateTime tool
    this.server.tool(
      'getDateTime',
      { format: z.enum(['ISO', 'UTC', 'local']).optional().default('ISO') },
      async ({ format }) => {
        this.logger.debug('getDateTime tool called with format:', format);

        const now = new Date();
        let result: string;

        switch (format) {
          case 'UTC':
            result = now.toUTCString();
            break;
          case 'local':
            result = now.toString();
            break;
          case 'ISO':
          default:
            result = now.toISOString();
            break;
        }

        return {
          content: [{ type: 'text', text: result }]
        };
      }
    );

    this.logger.info('Registered getDateTime tool');
  }

  public async start(): Promise<void> {
    try {
      // Create HTTP server
      const server = this.app.listen(this.config.port, this.config.host, () => {
        this.logger.info(`MCP Server listening on ${this.config.host}:${this.config.port}`);
      });

      // Setup Streamable HTTP transport
      const transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => Math.random().toString(36).substring(2, 15)
      });

      // Mount MCP server to Express app
      this.app.use('/mcp', async (req, res) => {
        try {
          await transport.handleRequest(req, res, req.body);
        } catch (error) {
          this.logger.error('Error handling MCP request', error as Error);
          if (!res.headersSent) {
            res.status(500).json({
              jsonrpc: '2.0',
              error: {
                code: -32603,
                message: 'Internal server error'
              },
              id: null
            });
          }
        }
      });

      // Connect MCP server to transport
      await this.server.connect(transport);

      this.logger.info('MCP Server started successfully');

      // Handle graceful shutdown
      process.on('SIGTERM', () => this.shutdown(server));
      process.on('SIGINT', () => this.shutdown(server));
    } catch (error) {
      this.logger.error('Failed to start MCP Server', error as Error);
      throw error;
    }
  }

  private async shutdown(server: any): Promise<void> {
    this.logger.info('Shutting down MCP Server...');

    try {
      // Close HTTP server
      server.close();
      this.logger.info('MCP Server shut down successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('Error during shutdown', error as Error);
      process.exit(1);
    }
  }
}

export default VoicebotMcpServer;
