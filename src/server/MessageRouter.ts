import { injectable, inject } from 'inversify';
import { Request, Response } from 'express';
import { MCPServerImpl } from './MCPServer';
import type { ILogger } from '../core/interfaces/ILogger';
import { TYPES } from '../types';

/**
 * Router for MCP messages
 */
@injectable()
export class MessageRouter {
  constructor(
    @inject(TYPES.MCPServer) private mcpServer: MCPServerImpl,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Handle POST requests for client-to-server communication
   * @param req Express request
   * @param res Express response
   */
  async handlePost(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug('Handling POST request', {
        method: req.method,
        path: req.path,
        sessionId: req.headers['mcp-session-id']
      });

      const transport = this.mcpServer.getTransport();
      if (!transport) {
        this.logger.error('Transport not initialized');
        res.status(500).json({
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: 'Server transport not initialized'
          },
          id: req.body?.id || null
        });
        return;
      }

      // Check if this is a tools/list request
      if (req.body && req.body.method === 'tools/list') {
        this.logger.info('Intercepting tools/list request');

        try {
          // Create our own tools/list response directly
          const tools = this.mcpServer.getServer().getTools();
          const toolsList = Array.from(tools.values()).map((tool) => ({
            name: tool.name,
            inputSchema: tool.schema,
            description: tool.schema.description || ''
          }));

          // Send the response
          res.status(200).json({
            jsonrpc: '2.0',
            result: {
              tools: toolsList
            },
            id: req.body.id || null
          });

          this.logger.info('Custom tools/list response sent');
        } catch (error) {
          this.logger.error('Error handling tools/list request', error);
          if (!res.headersSent) {
            res.status(500).json({
              jsonrpc: '2.0',
              error: {
                code: -32603,
                message: 'Error handling tools/list request'
              },
              id: req.body.id || null
            });
          }
        }
      } else {
        // For other requests, let the SDK transport handle it directly
        try {
          await transport.handleRequest(req, res, req.body);
        } catch (error) {
          // If we get a "Server already initialized" error, handle it gracefully
          const err = error as Error;
          if (err.message && err.message.includes('Server already initialized')) {
            this.logger.warn(
              'Detected "Server already initialized" error - creating new transport'
            );

            // Create a new transport to handle the reconnection
            this.mcpServer.createNewTransport();

            // Try again with the new transport
            try {
              const newTransport = this.mcpServer.getTransport();
              if (newTransport) {
                await newTransport.handleRequest(req, res, req.body);
                return; // Successfully handled with new transport
              }
            } catch (retryError) {
              this.logger.error('Error retrying request with new transport', retryError);
              // Fall through to error handling below
            }

            // If retry failed, send a generic success response
            if (!res.headersSent) {
              res.status(200).json({
                jsonrpc: '2.0',
                result: {},
                id: req.body.id || null
              });
            }
          } else {
            // Log and send error for other errors
            this.logger.error('Error handling request', error);
            if (!res.headersSent) {
              res.status(500).json({
                jsonrpc: '2.0',
                error: {
                  code: -32603,
                  message: 'Internal server error'
                },
                id: req.body.id || null
              });
            }
          }
        }
      }

      this.logger.debug('POST request handled successfully');
    } catch (error) {
      this.logger.error('Error in handlePost method', error);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: 'Internal server error'
          },
          id: req.body?.id || null
        });
      }
    }
  }

  /**
   * Handle GET requests for server-to-client notifications via SSE
   * @param req Express request
   * @param res Express response
   */
  async handleGet(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug('Handling GET request', {
        method: req.method,
        path: req.path,
        sessionId: req.headers['mcp-session-id']
      });

      const transport = this.mcpServer.getTransport();
      if (!transport) {
        this.logger.error('Transport not initialized');
        res.status(500).send('Server transport not initialized');
        return;
      }

      // Let the SDK transport handle the request
      await transport.handleRequest(req, res);

      this.logger.debug('GET request handled successfully');
    } catch (error) {
      this.logger.error('Error handling GET request', error);
      if (!res.headersSent) {
        res.status(500).send('Internal server error');
      }
    }
  }

  /**
   * Handle DELETE requests for session termination
   * @param req Express request
   * @param res Express response
   */
  async handleDelete(req: Request, res: Response): Promise<void> {
    try {
      this.logger.debug('Handling DELETE request', {
        method: req.method,
        path: req.path,
        sessionId: req.headers['mcp-session-id']
      });

      const transport = this.mcpServer.getTransport();
      if (!transport) {
        this.logger.error('Transport not initialized');
        res.status(500).send('Server transport not initialized');
        return;
      }

      // Let the SDK transport handle the request
      await transport.handleRequest(req, res);

      this.logger.debug('DELETE request handled successfully');
    } catch (error) {
      this.logger.error('Error handling DELETE request', error);
      if (!res.headersSent) {
        res.status(500).send('Internal server error');
      }
    }
  }
}
