import express from 'express';
import cors from 'cors';
import { inject, injectable } from 'inversify';
import { TYPES } from '../types';
import type { ILogger } from '../core/interfaces/ILogger';
import { MCPServerImpl } from './MCPServer';
import { MessageRouter } from './MessageRouter';
import { ServerConfig } from '../config';

import { createWorkflowVersionRoutes } from '../api/routes/workflowVersions';
import { PerformanceMonitor } from '../workflows/performance/PerformanceMonitor';
import { WorkflowOptimizer } from '../workflows/performance/WorkflowOptimizer';
import { DynamicServiceLoader } from '../services/DynamicServiceLoader';
import type { Container } from 'inversify';
import pkg from '../../package.json';

/**
 * Dynamic MCP Server implementation
 */
@injectable()
export class DynamicMcpServer {
  private app: express.Application;
  private server: any;

  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.MCPServer) private mcpServer: MCPServerImpl,
    @inject(TYPES.MessageRouter) private messageRouter: MessageRouter,
    @inject(TYPES.Container) private container: Container,
    private performanceMonitor: PerformanceMonitor,
    private workflowOptimizer: WorkflowOptimizer
  ) {
    this.app = express();
    this.setupMiddleware();
    this.setupPhase2Routes();
  }

  /**
   * Set up Express middleware
   */
  private setupMiddleware(): void {
    // Enable CORS for all routes
    this.app.use(cors());

    // Parse JSON request bodies
    this.app.use(express.json());

    // Add basic request logging
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`);
      next();
    });

    // Add health check endpoint
    this.app.get('/health', (req, res) => {
      res.status(200).json({
        status: 'ok',
        version: pkg.version,
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Add performance metrics endpoint
    this.app.get('/metrics', (req, res) => {
      const workflowId = req.query.workflowId as string;
      const stats = this.performanceMonitor.getAggregatedStats(workflowId);

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    });

    // Add inspector endpoint
    this.app.get('/inspector', (req, res) => {
      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>MCP Inspector</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .info { background: #e7f3ff; padding: 20px; border-radius: 5px; }
          </style>
        </head>
        <body>
          <h1>Dynamic MCP Server Inspector</h1>
          <div class="info">
            <h3>Server Information</h3>
            <p><strong>Name:</strong> ${pkg.name}</p>
            <p><strong>Version:</strong> ${pkg.version}</p>
            <p><strong>Uptime:</strong> ${Math.floor(process.uptime())} seconds</p>
          </div>
          <div class="info" style="margin-top: 20px;">
            <h3>Available Endpoints</h3>
            <ul>
              <li><a href="/health">Health Check</a></li>
              <li><a href="/metrics">Performance Metrics</a></li>
              <li><a href="/api/workflows">Workflow Management</a></li>
              <li><a href="/api/admin">Admin API</a></li>
              <li><a href="/mcp">MCP Protocol Endpoint</a></li>
            </ul>
          </div>
        </body>
        </html>
      `);
    });
  }

  /**
   * Setup Phase 2 API routes
   */
  private setupPhase2Routes(): void {
    // API base path
    const apiRouter = express.Router();

    // Workflow version management routes
    const workflowVersionRoutes = createWorkflowVersionRoutes(this.container);
    apiRouter.use('/workflows', workflowVersionRoutes);

    // Admin API routes are now handled by separate AdminServer

    // Performance optimization endpoint
    apiRouter.post('/workflows/:workflowId/optimize', async (req, res) => {
      try {
        const { workflowId } = req.params;
        const { definition, performanceHistory } = req.body;

        this.logger.info('Optimizing workflow', { workflowId });

        const optimizedWorkflow = await this.workflowOptimizer.optimizeWorkflow(
          definition,
          performanceHistory || [],
          []
        );

        res.json({
          success: true,
          data: optimizedWorkflow,
          message: 'Workflow optimization completed'
        });
      } catch (error) {
        this.logger.error('Workflow optimization failed', {
          workflowId: req.params.workflowId,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        });

        res.status(400).json({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error occurred',
          message: 'Workflow optimization failed'
        });
      }
    });

    // Mount API router
    this.app.use('/api', apiRouter);

    this.logger.info('Phase 2 API routes configured successfully');
  }

  /**
   * Start the server
   * @param config Server configuration
   */
  public async start(config: ServerConfig): Promise<void> {
    try {
      // Initialize MCP server
      await this.mcpServer.initialize();

      // Set up callback for function reloading
      const serviceLoader = this.container.get<DynamicServiceLoader>(TYPES.DynamicServiceLoader);
      serviceLoader.setOnFunctionsReloadedCallback((functions) => {
        this.logger.info('Functions reloaded, registering with MCP server');
        this.mcpServer.registerNewFunctions(functions);
      });

      // Configure routes with special handling for tools/list
      this.app.post('/mcp', (req, res) => {
        this.logger.info('Received /mcp POST body:', req.body);
        // Check if this is a tools/list request
        if (req.body && req.body.method === 'tools/list') {
          this.logger.info('Direct handling of tools/list request');

          try {
            // Get tools from the server
            const tools = this.mcpServer.getServer().getTools();
            const toolsList = Array.from(tools.values()).map((tool) => ({
              name: tool.name,
              inputSchema: tool.schema,
              description: tool.schema.description || ''
            }));

            // Send the response
            res.status(200).json({
              jsonrpc: '2.0',
              result: {
                tools: toolsList
              },
              id: req.body?.id || null
            });

            this.logger.info('Direct tools/list response sent');
          } catch (error) {
            this.logger.error('Error handling direct tools/list request', error);
            res.status(500).json({
              jsonrpc: '2.0',
              error: {
                code: -32603,
                message: 'Internal server error'
              },
              id: req.body?.id || null
            });
          }
        } else {
          // For other requests, use the message router
          this.messageRouter.handlePost(req, res);
        }
      });

      this.app.get('/mcp', (req, res) => this.messageRouter.handleGet(req, res));
      this.app.delete('/mcp', (req, res) => this.messageRouter.handleDelete(req, res));

      // Start HTTP server
      this.server = this.app.listen(config.port, config.host, () => {
        this.logger.info(`Dynamic MCP Server listening on ${config.host}:${config.port}`);
      });

      // Start MCP server with HTTP transport
      await this.mcpServer.start(config.port);

      this.logger.info('Dynamic MCP Server started successfully with Phase 2 enhancements');

      // Start performance monitoring cleanup
      setInterval(
        () => {
          this.performanceMonitor.cleanup(24); // Cleanup metrics older than 24 hours
        },
        60 * 60 * 1000
      ); // Run every hour

      // Handle graceful shutdown
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());
    } catch (error) {
      this.logger.error('Failed to start Dynamic MCP Server', error);
      throw error;
    }
  }

  /**
   * Shutdown the server
   */
  private async shutdown(): Promise<void> {
    this.logger.info('Shutting down Dynamic MCP Server...');

    try {
      // Stop MCP server
      await this.mcpServer.stop();

      // Close HTTP server
      if (this.server) {
        this.server.close();
      }

      // Cleanup performance monitoring
      this.performanceMonitor.cleanup(0); // Clean all metrics

      this.logger.info('Dynamic MCP Server shut down successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('Error during shutdown', error);
      process.exit(1);
    }
  }
}
