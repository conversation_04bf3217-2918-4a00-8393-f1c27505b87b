import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import {
  ListToolsRequestSchema,
  CallToolRequestSchema
} from '@modelcontextprotocol/sdk/types.js';
import { DynamicServiceLoader } from '../services/DynamicServiceLoader';
import type { ILogger } from '../core/interfaces/ILogger';
import type { IWorkflowEngine } from '../core/interfaces/IWorkflowEngine';
import { injectable, inject } from 'inversify';
import { TYPES } from '../types';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { IMCPTool } from '../core/interfaces/IMCPTool';

/**
 * MCP server implementation using the official SDK with standard request handlers
 */
@injectable()
export class MCPServerImpl {
  private server: Server;
  private transport: StreamableHTTPServerTransport | null = null;
  private initialized: boolean = false;
  private tools: Map<string, IMCPTool> = new Map();

  constructor(
    @inject(TYPES.ServiceLoader) private serviceLoader: DynamicServiceLoader,
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.WorkflowEngine) private workflowEngine: IWorkflowEngine
  ) {
    // Get package.json for server name and version
    const packageJsonPath = path.resolve(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    // Create standard MCP server with capabilities
    this.server = new Server(
      {
        name: packageJson.name,
        version: packageJson.version
      },
      {
        capabilities: {
          tools: {}
        }
      }
    );

    // Set up standard request handlers
    this.setupRequestHandlers();
  }

  /**
   * Set up standard MCP request handlers
   */
  private setupRequestHandlers(): void {
    // Handle tools/list requests
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      this.logger.info('Handling tools/list request via standard SDK');

      const toolsList = Array.from(this.tools.values())
        .filter(tool => tool.isEnabled())
        .map(tool => ({
          name: tool.name,
          description: tool.description || tool.inputSchema.description || '',
          inputSchema: tool.inputSchema
        }));

      this.logger.info(`Returning ${toolsList.length} tools`);
      return { tools: toolsList };
    });

    // Handle tools/call requests
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      this.logger.info(`Handling tools/call request for tool: ${name} via standard SDK`);

      const tool = this.tools.get(name);
      if (!tool) {
        this.logger.error(`Tool not found: ${name}`);
        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `Tool not found: ${name}`
            }
          ]
        };
      }

      if (!tool.isEnabled()) {
        this.logger.error(`Tool is disabled: ${name}`);
        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `Tool is disabled: ${name}`
            }
          ]
        };
      }

      try {
        this.logger.info(`Executing tool ${name} with params: ${JSON.stringify(args)}`);
        const result = await tool.execute(args || {});

        // If result is already in the correct format, return it directly
        if (result && typeof result === 'object' && 'content' in result) {
          return result;
        }

        // Otherwise, format the result according to MCP protocol
        return {
          content: [
            {
              type: 'text',
              text: typeof result === 'string' ? result : JSON.stringify(result)
            }
          ]
        };
      } catch (error) {
        this.logger.error(`Error executing tool ${name}:`, error);
        return {
          isError: true,
          content: [
            {
              type: 'text',
              text: `Error executing tool ${name}: ${error instanceof Error ? error.message : 'Unknown error'}`
            }
          ]
        };
      }
    });

    this.logger.info('Standard MCP request handlers configured');
  }

  /**
   * Initialize the server
   */
  async initialize(): Promise<void> {
    try {
      // Skip if already initialized
      if (this.initialized) {
        this.logger.info('MCP server already initialized, skipping');
        return;
      }

      // Initialize workflow engine
      await this.workflowEngine.initialize();

      // Initialize service loader
      await this.serviceLoader.initialize();

      // Get all active functions and store them
      this.tools = this.serviceLoader.getActiveFunctions();

      this.initialized = true;
      this.logger.info(`MCP server initialized with ${this.tools.size} tools`);
    } catch (error) {
      this.logger.error('Error initializing MCP server', error);
      throw error;
    }
  }

  /**
   * Check if the server is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Start the server with HTTP transport
   * @param port HTTP port
   */
  async start(port: number): Promise<void> {
    try {
      // Create HTTP transport with session ID generation
      this.createNewTransport();

      this.logger.info(`MCP server started on port ${port}`);
    } catch (error) {
      this.logger.error('Error starting MCP server', error);
      throw error;
    }
  }

  /**
   * Create a new transport instance
   * This allows us to handle reconnections properly
   */
  createNewTransport(): void {
    // Close existing transport if it exists
    if (this.transport) {
      try {
        this.transport.close();
      } catch (error) {
        this.logger.warn('Error closing existing transport', error);
      }
    }

    // Create a new transport
    this.transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => crypto.randomUUID()
    });

    // Connect to the MCP server
    this.server.connect(this.transport);

    this.logger.info('Created new MCP transport');
  }

  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    try {
      if (this.transport) {
        this.transport.close();
        this.transport = null;
      }
      this.logger.info('MCP server stopped');
    } catch (error) {
      this.logger.error('Error stopping MCP server', error);
      throw error;
    }
  }

  /**
   * Register new functions with the MCP server
   * @param functions Map of functions to register (includes built-in functions)
   */
  registerNewFunctions(functions: Map<string, IMCPTool>): void {
    if (!this.initialized) {
      this.logger.warn('MCP server not initialized, cannot register new functions');
      return;
    }

    this.logger.info(`Re-registering ${functions.size} functions with MCP server`);

    // Update the tools map - the request handlers will use this updated map
    this.tools = functions;

    this.logger.info('Successfully updated tools map for MCP server');
  }



  /**
   * Get the HTTP transport
   */
  getTransport(): StreamableHTTPServerTransport | null {
    return this.transport;
  }

  /**
   * Get the MCP server instance
   */
  getServer(): Server {
    return this.server;
  }

  /**
   * Get the tools map for compatibility with existing code
   */
  getTools(): Map<string, { name: string; schema: any; handler?: Function }> {
    const toolsMap = new Map();

    for (const [name, tool] of this.tools.entries()) {
      if (tool.isEnabled()) {
        toolsMap.set(name, {
          name: tool.name,
          schema: tool.inputSchema,
          handler: async (params: Record<string, any>) => {
            const result = await tool.execute(params);
            // Format result according to MCP protocol
            if (result && typeof result === 'object' && 'content' in result) {
              return result;
            }
            return {
              content: [
                {
                  type: 'text',
                  text: typeof result === 'string' ? result : JSON.stringify(result)
                }
              ]
            };
          }
        });
      }
    }

    return toolsMap;
  }
}
