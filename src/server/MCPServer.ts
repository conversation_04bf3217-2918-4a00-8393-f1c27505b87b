import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { DynamicServiceLoader } from '../services/DynamicServiceLoader';
import type { ILogger } from '../core/interfaces/ILogger';
import type { IWorkflowEngine } from '../core/interfaces/IWorkflowEngine';
import { injectable, inject } from 'inversify';
import { TYPES } from '../types';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { IMCPTool } from '../core/interfaces/IMCPTool';
import { MCPError, MCPErrorCode } from '../core/errors/MCPError';
import { CustomMcpServer } from '../mcp/sdk/CustomMcpServer';

/**
 * MCP server implementation using the official SDK
 */
@injectable()
export class MCPServerImpl {
  private server: CustomMcpServer;
  private transport: StreamableHTTPServerTransport | null = null;
  private initialized: boolean = false;

  constructor(
    @inject(TYPES.ServiceLoader) private serviceLoader: DynamicServiceLoader,
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.WorkflowEngine) private workflowEngine: IWorkflowEngine
  ) {
    // Get package.json for server name and version
    const packageJsonPath = path.resolve(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    // Create custom MCP server
    this.server = new CustomMcpServer(packageJson.name, packageJson.version, this.logger);
  }

  /**
   * Initialize the server
   */
  async initialize(): Promise<void> {
    try {
      // Skip if already initialized
      if (this.initialized) {
        this.logger.info('MCP server already initialized, skipping');
        return;
      }

      // Initialize workflow engine
      await this.workflowEngine.initialize();

      // Initialize service loader
      await this.serviceLoader.initialize();

      // Get all active functions
      const tools = this.serviceLoader.getActiveFunctions();

      // Register tools with the MCP server
      for (const [, tool] of tools.entries()) {
        if (tool.isEnabled()) {
          try {
            this.registerTool(tool);
          } catch (error) {
            this.logger.error(`Error registering tool ${tool.name}:`, {
              error: error instanceof Error ? error.message : String(error),
              stack: error instanceof Error ? error.stack : undefined,
              toolName: tool.name,
              toolId: tool.id,
              inputSchema: tool.inputSchema
            });
            // Continue with next tool instead of stopping
          }
        }
      }

      this.initialized = true;
      this.logger.info(`MCP server initialized with ${tools.size} tools`);
    } catch (error) {
      this.logger.error('Error initializing MCP server', error);
      throw error;
    }
  }

  /**
   * Check if the server is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Start the server with HTTP transport
   * @param port HTTP port
   */
  async start(port: number): Promise<void> {
    try {
      // Create HTTP transport with session ID generation
      this.createNewTransport();

      this.logger.info(`MCP server started on port ${port}`);
    } catch (error) {
      this.logger.error('Error starting MCP server', error);
      throw error;
    }
  }

  /**
   * Create a new transport instance
   * This allows us to handle reconnections properly
   */
  createNewTransport(): void {
    // Close existing transport if it exists
    if (this.transport) {
      try {
        this.transport.close();
      } catch (error) {
        this.logger.warn('Error closing existing transport', error);
      }
    }

    // Create a new transport
    this.transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => crypto.randomUUID()
    });

    // Connect to the MCP server
    this.server.connect(this.transport);

    this.logger.info('Created new MCP transport');
  }

  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    try {
      if (this.transport) {
        this.transport.close();
        this.transport = null;
      }
      this.logger.info('MCP server stopped');
    } catch (error) {
      this.logger.error('Error stopping MCP server', error);
      throw error;
    }
  }

  /**
   * Register new functions with the MCP server
   * @param functions Map of functions to register (includes built-in functions)
   */
  registerNewFunctions(functions: Map<string, IMCPTool>): void {
    if (!this.initialized) {
      this.logger.warn('MCP server not initialized, cannot register new functions');
      return;
    }

    this.logger.info(`Re-registering ${functions.size} functions with MCP server`);

    // Clear all existing tools first to avoid conflicts
    // This creates a new server instance, so we need to reconnect transport
    this.server.clearTools();

    // Register each enabled function (this includes both built-in and database functions)
    for (const [, tool] of functions.entries()) {
      if (tool.isEnabled()) {
        try {
          this.registerTool(tool);
        } catch (error) {
          this.logger.error(`Error registering tool ${tool.name}:`, {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            toolName: tool.name,
            toolId: tool.id,
            inputSchema: tool.inputSchema
          });
          // Continue with next tool instead of stopping
        }
      }
    }

    // Reconnect transport to new server instance
    if (this.transport) {
      // Close old transport
      try {
        this.transport.close();
      } catch (error) {
        this.logger.warn('Error closing old transport', error);
      }

      // Create new transport and connect
      this.createNewTransport();
    }

    this.logger.info('Successfully re-registered all functions with MCP server');
  }

  /**
   * Register a tool with the MCP server
   * @param tool MCP tool
   */
  private registerTool(tool: IMCPTool): void {
    this.logger.info(`Registering tool: ${tool.name}`);

    try {
      // Validate tool before registration
      if (!tool.name) {
        throw new Error('Tool name is required');
      }

      if (!tool.inputSchema) {
        throw new Error('Tool input schema is required');
      }

      // Register the tool with our custom wrapper
      this.server.tool(tool.name, tool.inputSchema, async (params: Record<string, any>) => {
        try {
          this.logger.info(`Executing tool ${tool.name} with params: ${JSON.stringify(params)}`);

          // Extract actual parameters from MCP SDK format
          // The SDK might include metadata that we don't want to pass to the tool
          const actualParams: Record<string, any> = {};

          // Copy all parameters except for internal ones
          Object.keys(params).forEach((key) => {
            if (
              !key.startsWith('_') &&
              key !== 'signal' &&
              key !== 'sessionId' &&
              key !== 'requestId'
            ) {
              actualParams[key] = params[key];
            }
          });

          // If no actual parameters were found, use the original params
          // This ensures backward compatibility with tools that expect the raw params
          const paramsToUse = Object.keys(actualParams).length > 0 ? actualParams : params;

          const result = await tool.execute(paramsToUse);

          // If result is already in the correct format, return it directly
          if (result && typeof result === 'object' && 'content' in result) {
            return result;
          }

          // Otherwise, format the result according to MCP protocol
          return {
            content: [
              {
                type: 'text',
                text: typeof result === 'string' ? result : JSON.stringify(result)
              }
            ]
          };
        } catch (error) {
          this.logger.error(`Error executing tool ${tool.name}`, error);
          throw new MCPError(
            `Error executing tool ${tool.name}: ${(error as Error).message}`,
            MCPErrorCode.TOOL_EXECUTION_ERROR
          );
        }
      });
    } catch (error) {
      this.logger.error(`Failed to register tool ${tool.name}:`, {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error; // Re-throw so the caller can handle it
    }
  }

  /**
   * Get the HTTP transport
   */
  getTransport(): StreamableHTTPServerTransport | null {
    return this.transport;
  }

  /**
   * Get the MCP server instance
   */
  getServer(): CustomMcpServer {
    return this.server;
  }
}
