import { Container } from 'inversify';
import { TYPES } from './types';

// Interfaces
import { ILogger } from './core/interfaces/ILogger';
import { IDatabase } from './core/interfaces/IDatabase';
import { IScriptEngine } from './core/interfaces/IScriptEngine';
import { IWorkflowEngine } from './core/interfaces/IWorkflowEngine';
import { INodeRegistry } from './core/interfaces/INodeRegistry';
import { IWorkflowMemory } from './core/interfaces/IWorkflowMemory';
import { IRedisClient } from './core/interfaces/IRedisClient';
import { IRedisWorkflowMemory } from './core/interfaces/IRedisWorkflowMemory';
import { IObservabilityManager } from './core/interfaces/IObservabilityManager';
import { IWorkflowController } from './core/interfaces/IWorkflowController';
import { INodeController } from './core/interfaces/INodeController';
import { IDataSourceController } from './core/interfaces/IDataSourceController';
import { IValidator } from './core/interfaces/IValidator';
import { IRepository } from './core/interfaces/IRepository';

// Implementations
import { PinoLogger } from './utils/PinoLogger';
import { PostgresDatabase } from './infrastructure/database/PostgresDatabase';
import { VM2ScriptEngine } from './infrastructure/script-engine/VM2ScriptEngine';
import { DynamicServiceLoader } from './services/DynamicServiceLoader';
import { MCPServerImpl } from './server/MCPServer';
import { MessageRouter } from './server/MessageRouter';
import { DynamicMcpServer } from './server/DynamicMcpServer';
import { WorkflowEngine } from './workflow/engine/WorkflowEngine';
import { NodeRegistry } from './workflow/nodes/NodeRegistry';
import { InMemoryWorkflowMemory } from './workflow/engine/WorkflowMemory';
import { RedisClient } from './infrastructure/redis/RedisClient';
import { RedisWorkflowMemory } from './workflow/engine/RedisWorkflowMemory';
import { ErrorHandler } from './core/errors/ErrorHandler';
// NodePalette factory imports
import { registerNodePaletteTypes } from './workflow/nodes/NodePaletteFactory';
import { ObservabilityManager } from './workflow/observability/ObservabilityManager';
import { ApiServer } from './api/server';
import { getEnvBoolean } from './utils/env';

// Admin API
import { WorkflowController } from './api/controllers/WorkflowController';
import { NodeController } from './api/controllers/NodeController';
import { DataSourceController } from './api/controllers/DataSourceController';
import { MCPFunctionController } from './api/controllers/MCPFunctionController';
import { WorkflowValidator } from './api/validators/WorkflowValidator';
import { NodeValidator } from './api/validators/NodeValidator';
import { DataSourceValidator } from './api/validators/DataSourceValidator';
import { MCPFunctionValidator } from './api/validators/MCPFunctionValidator';
import { NodeConfigRepository } from './infrastructure/database/repositories/NodeConfigRepository';
import { DataSourceRepository } from './infrastructure/database/repositories/DataSourceRepository';
import { WorkflowRepository } from './infrastructure/database/repositories/WorkflowRepository';
import { WorkflowExecutionRepository } from './infrastructure/database/repositories/WorkflowExecutionRepository';
import { WorkflowNodeExecutionRepository } from './infrastructure/database/repositories/WorkflowNodeExecutionRepository';
import { MCPFunctionRepository } from './infrastructure/database/repositories/MCPFunctionRepository';
import { WorkflowConfigWatcher } from './workflow/config/WorkflowConfigWatcher';

// Phase 2: Advanced workflow components
import { IExecutionCoordinator } from './core/interfaces/IExecutionCoordinator';
import { IParallelExecutor } from './core/interfaces/IParallelExecutor';
import { IConditionalProcessor } from './core/interfaces/IConditionalProcessor';
import { IDataTransformer } from './core/interfaces/IDataTransformer';
import { IWorkflowVersionManager } from './core/interfaces/IWorkflowVersionManager';
import { ExecutionCoordinator } from './workflows/engine/ExecutionCoordinator';
import { ParallelExecutor } from './workflows/engine/ParallelExecutor';
import { ConditionalProcessor } from './workflows/engine/ConditionalProcessor';
import { DataTransformer } from './workflows/engine/DataTransformer';
import { WorkflowVersionManager } from './workflows/versioning/WorkflowVersionManager';

// Performance monitoring
import { PerformanceMonitor } from './workflows/performance/PerformanceMonitor';
import { WorkflowOptimizer } from './workflows/performance/WorkflowOptimizer';

// API Controllers
import { WorkflowVersionController } from './api/controllers/WorkflowVersionController';

// Phase 3: Multi-tenancy
import { ITenantManager } from './core/interfaces/ITenantManager';
import { ITenantContext } from './core/interfaces/ITenantContext';
import { ITenantResolver } from './core/interfaces/ITenantResolver';
import { ITenantIsolation } from './core/interfaces/ITenantIsolation';
import { TenantManager } from './services/TenantManager';
import { TenantContext } from './services/TenantContext';
import { TenantResolver } from './services/TenantResolver';
import { TenantIsolation } from './services/TenantIsolation';
import { TenantRepository } from './infrastructure/database/repositories/TenantRepository';
import { TenantUserRepository } from './infrastructure/database/repositories/TenantUserRepository';
import { TenantController } from './api/controllers/TenantController';
import { AuthController } from './api/controllers/AuthController';
import { AdminController } from './api/controllers/AdminController';

// Phase 5: Additional services
import { JSONPathService } from './services/JSONPathService';
import { IJSONPathService } from './services/JSONPathService';
import { WebSocketService } from './services/WebSocketService';

/**
 * Configure dependency injection container
 */
export function configureContainer(): Container {
  const container = new Container();

  // Bind container to itself for factory functions
  container.bind<Container>(TYPES.Container).toConstantValue(container);

  // Core services
  container.bind<ILogger>(TYPES.Logger).to(PinoLogger).inSingletonScope();
  container.bind<IDatabase>(TYPES.Database).to(PostgresDatabase).inSingletonScope();
  container.bind<IScriptEngine>(TYPES.ScriptEngine).to(VM2ScriptEngine).inSingletonScope();
  container.bind<IRedisClient>(TYPES.RedisClient).to(RedisClient).inSingletonScope();
  container.bind<ErrorHandler>(TYPES.ErrorHandler).to(ErrorHandler).inSingletonScope();
  container
    .bind<IObservabilityManager>(TYPES.ObservabilityManager)
    .to(ObservabilityManager)
    .inSingletonScope();
  container.bind<IJSONPathService>(TYPES.JSONPathService).to(JSONPathService).inSingletonScope();
  container.bind<WebSocketService>(TYPES.WebSocketService).to(WebSocketService).inSingletonScope();

  // Workflow engine
  container.bind<INodeRegistry>(TYPES.NodeRegistry).to(NodeRegistry).inSingletonScope();

  // Use Redis workflow memory if enabled, otherwise use in-memory
  const useRedisWorkflowMemory = getEnvBoolean('USE_REDIS_WORKFLOW_MEMORY', false);
  if (useRedisWorkflowMemory) {
    container
      .bind<IWorkflowMemory>(TYPES.WorkflowMemory)
      .to(RedisWorkflowMemory)
      .inSingletonScope();
    container
      .bind<IRedisWorkflowMemory>(TYPES.RedisWorkflowMemory)
      .to(RedisWorkflowMemory)
      .inSingletonScope();
  } else {
    container
      .bind<IWorkflowMemory>(TYPES.WorkflowMemory)
      .to(InMemoryWorkflowMemory)
      .inSingletonScope();
  }

  container.bind<IWorkflowEngine>(TYPES.WorkflowEngine).to(WorkflowEngine).inSingletonScope();

  // Phase 2: Advanced workflow components
  container
    .bind<IExecutionCoordinator>(TYPES.ExecutionCoordinator)
    .to(ExecutionCoordinator)
    .inSingletonScope();
  container.bind<IParallelExecutor>(TYPES.ParallelExecutor).to(ParallelExecutor).inSingletonScope();
  container
    .bind<IConditionalProcessor>(TYPES.ConditionalProcessor)
    .to(ConditionalProcessor)
    .inSingletonScope();
  container.bind<IDataTransformer>(TYPES.DataTransformer).to(DataTransformer).inSingletonScope();
  container
    .bind<IWorkflowVersionManager>(TYPES.WorkflowVersionManager)
    .to(WorkflowVersionManager)
    .inSingletonScope();

  // Performance monitoring
  container.bind<PerformanceMonitor>(PerformanceMonitor).toSelf().inSingletonScope();
  container.bind<WorkflowOptimizer>(WorkflowOptimizer).toSelf().inSingletonScope();

  // API Controllers
  container.bind<WorkflowVersionController>(WorkflowVersionController).toSelf().inSingletonScope();

  // Register NodePalette node factories
  const nodeRegistry = container.get<NodeRegistry>(TYPES.NodeRegistry);

  // Register all NodePalette types (Trigger, Action, Logic, Terminator)
  registerNodePaletteTypes(container);

  // MCP services
  container
    .bind<DynamicServiceLoader>(TYPES.ServiceLoader)
    .to(DynamicServiceLoader)
    .inSingletonScope();
  container
    .bind<DynamicServiceLoader>(TYPES.DynamicServiceLoader)
    .to(DynamicServiceLoader)
    .inSingletonScope();
  container.bind<MCPServerImpl>(TYPES.MCPServer).to(MCPServerImpl).inSingletonScope();
  container.bind<MessageRouter>(TYPES.MessageRouter).to(MessageRouter).inSingletonScope();
  container.bind<DynamicMcpServer>(DynamicMcpServer).toSelf().inSingletonScope();
  container.bind<ApiServer>(ApiServer).toSelf().inSingletonScope();

  // Admin API controllers
  container
    .bind<IWorkflowController>(TYPES.WorkflowController)
    .to(WorkflowController)
    .inSingletonScope();
  container.bind<INodeController>(TYPES.NodeController).to(NodeController).inSingletonScope();
  container
    .bind<IDataSourceController>(TYPES.DataSourceController)
    .to(DataSourceController)
    .inSingletonScope();
  container
    .bind<MCPFunctionController>(TYPES.MCPFunctionController)
    .to(MCPFunctionController)
    .inSingletonScope();

  // Validators
  container.bind<IValidator<any>>(TYPES.WorkflowValidator).to(WorkflowValidator).inSingletonScope();
  container.bind<IValidator<any>>(TYPES.NodeValidator).to(NodeValidator).inSingletonScope();
  container
    .bind<IValidator<any>>(TYPES.DataSourceValidator)
    .to(DataSourceValidator)
    .inSingletonScope();
  container
    .bind<IValidator<any>>(TYPES.MCPFunctionValidator)
    .to(MCPFunctionValidator)
    .inSingletonScope();

  // Repositories
  container
    .bind<IRepository<any>>(TYPES.WorkflowRepository)
    .to(WorkflowRepository)
    .inSingletonScope();
  container
    .bind<IRepository<any>>(TYPES.WorkflowExecutionRepository)
    .to(WorkflowExecutionRepository)
    .inSingletonScope();
  container
    .bind<IRepository<any>>(TYPES.WorkflowNodeExecutionRepository)
    .to(WorkflowNodeExecutionRepository)
    .inSingletonScope();
  container
    .bind<IRepository<any>>(TYPES.NodeConfigRepository)
    .to(NodeConfigRepository)
    .inSingletonScope();
  container
    .bind<IRepository<any>>(TYPES.DataSourceRepository)
    .to(DataSourceRepository)
    .inSingletonScope();
  container
    .bind<IRepository<any>>(TYPES.MCPFunctionRepository)
    .to(MCPFunctionRepository)
    .inSingletonScope();

  // Config Watcher
  container
    .bind<WorkflowConfigWatcher>(TYPES.WorkflowConfigWatcher)
    .to(WorkflowConfigWatcher)
    .inSingletonScope();

  // Phase 3: Multi-tenancy services
  container.bind<ITenantManager>(TYPES.TenantManager).to(TenantManager).inSingletonScope();
  container.bind<ITenantContext>(TYPES.TenantContext).to(TenantContext).inSingletonScope();
  container.bind<ITenantResolver>(TYPES.TenantResolver).to(TenantResolver).inSingletonScope();
  container.bind<ITenantIsolation>(TYPES.TenantIsolation).to(TenantIsolation).inSingletonScope();

  // Multi-tenancy repositories
  container.bind<TenantRepository>(TYPES.TenantRepository).to(TenantRepository).inSingletonScope();
  container
    .bind<TenantUserRepository>(TYPES.TenantUserRepository)
    .to(TenantUserRepository)
    .inSingletonScope();

  // Multi-tenancy controllers
  container.bind<TenantController>(TenantController).toSelf().inSingletonScope();
  container.bind<AuthController>(AuthController).toSelf().inSingletonScope();
  container.bind<AdminController>(AdminController).toSelf().inSingletonScope();

  return container;
}
