import { IMCPTool } from '../../core/interfaces/IMCPTool';
import { DynamicFunctionConfig } from '../../core/types/DynamicFunctionConfig';

/**
 * Base class for MCP tools
 */
export abstract class BaseMCPTool implements IMCPTool {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly inputSchema: any;
  private enabled: boolean;

  /**
   * Create a new MCP tool
   * @param config Tool configuration
   */
  constructor(config: DynamicFunctionConfig) {
    this.id = config.id;
    this.name = config.name;
    this.description = config.description;
    this.inputSchema = config.input_schema;
    this.enabled = config.enabled;
  }

  /**
   * Execute the tool with provided parameters
   * @param params Tool parameters
   */
  abstract execute(params: Record<string, any>): Promise<any>;

  /**
   * Check if the tool is enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Enable the tool
   */
  enable(): void {
    this.enabled = true;
  }

  /**
   * Disable the tool
   */
  disable(): void {
    this.enabled = false;
  }
}
