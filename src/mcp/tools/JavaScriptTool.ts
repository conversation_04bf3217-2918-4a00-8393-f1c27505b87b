import { BaseMCPTool } from './BaseMCPTool';
import { IScriptEngine } from '../../core/interfaces/IScriptEngine';
import { DynamicFunctionConfig } from '../../core/types/DynamicFunctionConfig';

/**
 * MCP tool that executes JavaScript code
 */
export class JavaScriptTool extends BaseMCPTool {
  private scriptContent: string;

  /**
   * Create a new JavaScript tool
   * @param config Tool configuration
   * @param scriptEngine Script engine for executing JavaScript
   * @param scriptContent JavaScript code to execute
   */
  constructor(
    config: DynamicFunctionConfig,
    private scriptEngine: IScriptEngine,
    scriptContent: string
  ) {
    super(config);
    this.scriptContent = scriptContent;
  }

  /**
   * Execute the tool with provided parameters
   * @param params Tool parameters
   */
  async execute(params: Record<string, any>): Promise<any> {
    // Extract actual parameters from MCP SDK format
    // The SDK wraps parameters with metadata
    const actualParams: Record<string, any> = {};

    // Copy all parameters except for internal ones that start with _
    Object.keys(params).forEach((key) => {
      if (!key.startsWith('_') && key !== 'signal' && key !== 'sessionId' && key !== 'requestId') {
        actualParams[key] = params[key];
      }
    });

    // Create a context with the parameters
    const context = {
      params: actualParams,
      console: {
        log: (...args: any[]) => console.log(`[${this.name}]`, ...args),
        error: (...args: any[]) => console.error(`[${this.name}]`, ...args),
        warn: (...args: any[]) => console.warn(`[${this.name}]`, ...args),
        info: (...args: any[]) => console.info(`[${this.name}]`, ...args)
      }
    };

    try {
      // Execute the script with the context
      const result = await this.scriptEngine.execute(this.scriptContent, context, {
        timeout: 10000 // 10 seconds timeout
      });

      return result;
    } catch (error) {
      console.error(`Error executing script for tool ${this.name}:`, error);
      throw error;
    }
  }
}
