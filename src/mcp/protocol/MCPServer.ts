import { injectable, inject } from 'inversify';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { DynamicServiceLoader } from '../../services/DynamicServiceLoader';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { IMCPTool } from '../../core/interfaces/IMCPTool';
import { MCPErrorCode, MCPError } from '../../core/errors/MCPError';
import * as fs from 'fs';
import * as path from 'path';

/**
 * MCP server implementation
 */
@injectable()
export class MCPServerImpl {
  private server: McpServer;
  private transport: StreamableHTTPServerTransport | null = null;
  private tools: Map<string, IMCPTool> = new Map();

  constructor(
    @inject(TYPES.ServiceLoader) private serviceLoader: DynamicServiceLoader,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Get package.json for server name and version
    const packageJsonPath = path.resolve(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    // Create MCP server
    this.server = new McpServer({
      name: packageJson.name,
      version: packageJson.version
    });
  }

  /**
   * Initialize the server
   */
  async initialize(): Promise<void> {
    try {
      // Initialize service loader
      await this.serviceLoader.initialize();

      // Get all active functions
      this.tools = this.serviceLoader.getActiveFunctions();

      // Register tools with the MCP server
      for (const [name, tool] of this.tools.entries()) {
        if (tool.isEnabled()) {
          this.registerTool(tool);
        }
      }

      this.logger.info(`MCP server initialized with ${this.tools.size} tools`);
    } catch (error) {
      this.logger.error('Error initializing MCP server', error);
      throw error;
    }
  }

  /**
   * Start the server with HTTP transport
   * @param port HTTP port
   */
  async start(port: number): Promise<void> {
    try {
      // Create HTTP transport
      this.transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => crypto.randomUUID()
      });

      // Connect to the MCP server
      await this.server.connect(this.transport);

      this.logger.info(`MCP server started on port ${port}`);
    } catch (error) {
      this.logger.error('Error starting MCP server', error);
      throw error;
    }
  }

  /**
   * Stop the server
   */
  async stop(): Promise<void> {
    try {
      if (this.transport) {
        this.transport.close();
        this.transport = null;
      }
      this.logger.info('MCP server stopped');
    } catch (error) {
      this.logger.error('Error stopping MCP server', error);
      throw error;
    }
  }

  /**
   * Register a tool with the MCP server
   * @param tool MCP tool
   */
  private registerTool(tool: IMCPTool): void {
    this.server.tool(tool.name, tool.inputSchema, async (params: Record<string, any>) => {
      try {
        const result = await tool.execute(params);
        return {
          content: [{ type: 'text' as const, text: JSON.stringify(result) }]
        };
      } catch (error) {
        this.logger.error(`Error executing tool ${tool.name}`, error);
        throw new MCPError(
          `Error executing tool ${tool.name}: ${(error as Error).message}`,
          MCPErrorCode.TOOL_EXECUTION_ERROR
        );
      }
    });
  }

  /**
   * Get the HTTP transport
   */
  getTransport(): StreamableHTTPServerTransport | null {
    return this.transport;
  }
}
