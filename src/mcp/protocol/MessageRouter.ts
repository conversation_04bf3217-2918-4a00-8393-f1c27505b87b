import { injectable, inject } from 'inversify';
import { Request, Response } from 'express';
import { MCPServerImpl } from './MCPServer';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';

// Custom function to check if a request is an initialize request
function isInitializeRequest(body: any): boolean {
  return (
    body &&
    body.jsonrpc === '2.0' &&
    body.method === 'initialize' &&
    body.params &&
    body.params.protocolVersion &&
    body.params.clientName &&
    body.params.clientVersion
  );
}

/**
 * Router for MCP messages
 */
@injectable()
export class MessageRouter {
  private transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

  constructor(
    @inject(TYPES.MCPServer) private mcpServer: MCPServerImpl,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Handle POST requests for client-to-server communication
   * @param req Express request
   * @param res Express response
   */
  async handlePost(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('Handling POST request', {
        method: req.method,
        path: req.path,
        headers: req.headers,
        sessionId: req.headers['mcp-session-id'],
        isInitRequest: isInitializeRequest(req.body),
        bodyMethod: req.body?.method,
        bodyId: req.body?.id
      });

      // Check for existing session ID
      const sessionId = req.headers['mcp-session-id'] as string | undefined;
      let transport: StreamableHTTPServerTransport | null;

      // Get the main transport
      const mainTransport = this.mcpServer.getTransport();
      if (!mainTransport) {
        this.logger.error('Main transport not initialized');
        res.status(500).json({
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: 'Server transport not initialized'
          },
          id: req.body?.id || null
        });
        return;
      }

      // Log active sessions
      this.logger.info(`Active sessions: ${Object.keys(this.transports).join(', ') || 'none'}`);

      if (sessionId && this.transports[sessionId]) {
        // Reuse existing transport
        this.logger.info(`Using existing transport for session ${sessionId}`);
        transport = this.transports[sessionId];
      } else if (isInitializeRequest(req.body)) {
        // New initialization request
        this.logger.info('Processing initialize request');
        transport = mainTransport;

        // For initialization requests, we'll get the session ID after handling the request
        // We'll store the transport in the map in the next request when the session ID is available
      } else {
        // If we have a session ID but no matching transport, or no session ID and not an initialize request
        this.logger.warn('Invalid request - no valid session ID or not an initialize request', {
          sessionId,
          hasSession: !!sessionId,
          knownSession: sessionId ? !!this.transports[sessionId] : false,
          isInitRequest: isInitializeRequest(req.body)
        });
        res.status(400).json({
          jsonrpc: '2.0',
          error: {
            code: -32000,
            message: 'Bad Request: No valid session ID provided'
          },
          id: req.body?.id || null
        });
        return;
      }

      // Handle the request
      this.logger.info('Forwarding request to transport handler', {
        transportSessionId: transport.sessionId,
        requestSessionId: sessionId
      });

      await transport.handleRequest(req, res, req.body);

      // If this was an initialization request, the transport now has a session ID
      // Store it in our map for future requests
      if (
        isInitializeRequest(req.body) &&
        transport.sessionId &&
        !this.transports[transport.sessionId]
      ) {
        this.logger.info(`New session initialized: ${transport.sessionId}`);
        this.transports[transport.sessionId] = transport;

        // Clean up transport when closed
        transport.onclose = () => {
          if (transport.sessionId) {
            delete this.transports[transport.sessionId];
            this.logger.info(`Session ${transport.sessionId} closed`);
          }
        };
      }

      this.logger.info('Request handled successfully', {
        transportSessionId: transport.sessionId,
        activeSessions: Object.keys(this.transports).join(', ')
      });
    } catch (error) {
      this.logger.error('Error handling POST request', error);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: 'Internal server error'
          },
          id: req.body?.id || null
        });
      }
    }
  }

  /**
   * Handle GET requests for server-to-client notifications via SSE
   * @param req Express request
   * @param res Express response
   */
  async handleGet(req: Request, res: Response): Promise<void> {
    try {
      await this.handleSessionRequest(req, res);
    } catch (error) {
      this.logger.error('Error handling GET request', error);
      if (!res.headersSent) {
        res.status(500).end('Internal server error');
      }
    }
  }

  /**
   * Handle DELETE requests for session termination
   * @param req Express request
   * @param res Express response
   */
  async handleDelete(req: Request, res: Response): Promise<void> {
    try {
      await this.handleSessionRequest(req, res);
    } catch (error) {
      this.logger.error('Error handling DELETE request', error);
      if (!res.headersSent) {
        res.status(500).end('Internal server error');
      }
    }
  }

  /**
   * Handle session-based requests (GET and DELETE)
   * @param req Express request
   * @param res Express response
   */
  private async handleSessionRequest(req: Request, res: Response): Promise<void> {
    this.logger.info(`Handling ${req.method} request`, {
      method: req.method,
      path: req.path,
      headers: req.headers,
      sessionId: req.headers['mcp-session-id']
    });

    const sessionId = req.headers['mcp-session-id'] as string | undefined;

    // Log active sessions
    this.logger.info(`Active sessions: ${Object.keys(this.transports).join(', ') || 'none'}`);

    if (!sessionId || !this.transports[sessionId]) {
      this.logger.warn('Invalid or missing session ID', {
        sessionId,
        hasSession: !!sessionId,
        knownSession: sessionId ? !!this.transports[sessionId] : false
      });
      res.status(400).send('Invalid or missing session ID');
      return;
    }

    this.logger.info(`Using existing transport for session ${sessionId}`);
    const transport = this.transports[sessionId];
    await transport.handleRequest(req, res);

    this.logger.info(`${req.method} request handled successfully`);
  }
}
