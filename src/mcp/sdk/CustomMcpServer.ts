import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { ILogger } from '../../core/interfaces/ILogger';

/**
 * Custom MCP server that properly handles schemas
 */
export class CustomMcpServer {
  private server: McpServer;
  private logger: ILogger;
  private tools: Map<string, { name: string; schema: any; handler: Function }> = new Map();
  private isConnected: boolean = false;
  private serverName: string;
  private serverVersion: string;

  /**
   * Create a new custom MCP server
   * @param name Server name
   * @param version Server version
   * @param logger Logger
   */
  constructor(name: string, version: string, logger: ILogger) {
    this.serverName = name;
    this.serverVersion = version;
    this.logger = logger;
    this.createNewServer();
  }

  /**
   * Create a new MCP server instance
   */
  private createNewServer(): void {
    this.server = new McpServer({
      name: this.serverName,
      version: this.serverVersion
    });
    this.isConnected = false;
  }

  /**
   * Register a tool with the server
   * @param name Tool name
   * @param schema Tool schema
   * @param handler Tool handler
   */
  tool(name: string, schema: any, handler: (params: Record<string, any>) => Promise<any>): void {
    // Store the tool for our custom tools/list implementation
    this.tools.set(name, { name, schema, handler });

    // Register the tool with the original server
    this.server.tool(name, schema, handler);
  }

  /**
   * Connect the server to a transport
   * @param transport Transport to connect to
   */
  connect(transport: StreamableHTTPServerTransport): void {
    // Always connect - new server instance needs to be connected
    // Monkey patch the transport to handle reconnections
    this.monkeyPatchTransport(transport);

    // Connect to the transport
    this.server.connect(transport);
    this.isConnected = true;
    this.logger.info('MCP server connected to transport');
  }

  /**
   * Monkey patch the transport to handle reconnections
   * This is a workaround for the "Server already initialized" error
   * @param transport Transport to patch
   */
  private monkeyPatchTransport(transport: any): void {
    // Store the original handlePostRequest method
    const originalHandlePostRequest = transport.handlePostRequest;

    // Replace it with our patched version
    transport.handlePostRequest = async function (req: any, res: any, parsedBody: any) {
      // Reset the _initialized flag for initialization requests
      if (
        parsedBody &&
        (parsedBody.method === 'initialize' ||
          (Array.isArray(parsedBody) && parsedBody.some((msg) => msg.method === 'initialize')))
      ) {
        this._initialized = false;
      }

      // Call the original method
      return originalHandlePostRequest.call(this, req, res, parsedBody);
    };

    this.logger.info('Transport monkey patched to handle reconnections');
  }

  /**
   * Get the underlying MCP server
   */
  getServer(): McpServer {
    return this.server;
  }

  /**
   * Get all registered tools
   */
  getTools(): Map<string, { name: string; schema: any; handler: Function }> {
    return this.tools;
  }

  /**
   * Clear all registered tools by creating a new server instance
   * This will disconnect existing clients, who should reconnect automatically
   */
  clearTools(): void {
    this.tools.clear();
    this.createNewServer();
    this.logger.info('All tools cleared - new server instance created (clients will reconnect)');
  }

  /**
   * Remove a specific tool
   */
  removeTool(name: string): boolean {
    const removed = this.tools.delete(name);
    if (removed) {
      this.logger.info(`Tool ${name} removed from MCP server`);
    }
    return removed;
  }
}
