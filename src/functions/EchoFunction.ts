import { z } from 'zod';
import { injectable } from 'inversify';
import { MCPFunction } from '../core/interfaces/MCPFunction';

/**
 * Simple echo function for testing
 */
@injectable()
export class EchoFunction implements MCPFunction {
  id: string = 'echo';
  name: string = 'echo';
  description: string = 'Simple echo function that returns the input message';
  inputSchema = {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        description: 'Message to echo back'
      }
    },
    required: ['message']
  };
  private enabled: boolean = true;

  /**
   * Execute the function
   * @param params Function parameters
   * @returns Function result in MCP format
   */
  async execute(params: Record<string, any>): Promise<any> {
    // Extract message from params
    const message = params.message || 'No message provided';

    console.log(`Echo function received params: ${JSON.stringify(params)}`);
    console.log(`Echo function extracted message: ${message}`);

    return {
      content: [
        {
          type: 'text',
          text: `Echo: ${message}`
        }
      ]
    };
  }

  /**
   * Check if the function is enabled
   * @returns True if enabled
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Enable the function
   */
  enable(): void {
    this.enabled = true;
  }

  /**
   * Disable the function
   */
  disable(): void {
    this.enabled = false;
  }
}
