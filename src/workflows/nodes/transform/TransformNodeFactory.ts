import { Container } from 'inversify';
import { INode } from '../../../core/interfaces/INode';
import { ILogger } from '../../../core/interfaces/ILogger';
import { IDataTransformer } from '../../../core/interfaces/IDataTransformer';
import { TYPES } from '../../../types';

// TODO: Phase 2 - These transform nodes use old naming convention
// They should be updated to use NodePalette naming convention:
// - 'data-mapping' -> 'data-mapping-transform'
// - 'aggregation' -> 'aggregation-transform'
// For now, they are commented out to maintain Phase 6 clean migration approach

/*
import { DataMappingNode, DataMappingNodeConfig } from './DataMappingNode';
import { AggregationNode, AggregationNodeConfig } from './AggregationNode';

/**
 * Factory function for creating Data Mapping nodes
 */
/*
export function createDataMappingNodeFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const dataTransformer = container.get<IDataTransformer>(TYPES.DataTransformer);

    return new DataMappingNode(config as DataMappingNodeConfig, logger, dataTransformer);
  };
}

/**
 * Factory function for creating Aggregation nodes
 */
/*
export function createAggregationNodeFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);
    const dataTransformer = container.get<IDataTransformer>(TYPES.DataTransformer);

    return new AggregationNode(config as AggregationNodeConfig, logger, dataTransformer);
  };
}
*/

/**
 * Register all transform node factories with the node registry
 */
export function registerTransformNodes(container: Container): void {
  // TODO: Phase 2 - Re-enable transform nodes with NodePalette naming convention
  // const nodeRegistry = container.get(TYPES.NodeRegistry);

  // Register transform nodes with NodePalette naming:
  // (nodeRegistry as any).registerNode('data-mapping-transform', createDataMappingNodeFactory(container));
  // (nodeRegistry as any).registerNode('aggregation-transform', createAggregationNodeFactory(container));

  console.log('Phase 2 transform nodes temporarily disabled for clean NodePalette migration');
}
