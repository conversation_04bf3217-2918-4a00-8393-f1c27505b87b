import { injectable, inject } from 'inversify';
import { BaseNode } from '../../../workflow/nodes/BaseNode';
import { IAggregationNode } from '../../../core/interfaces/IExtendedActionNodes';
import {
  type IDataTransformer,
  AggregationOperation
} from '../../../core/interfaces/IDataTransformer';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';

/**
 * Configuration for Aggregation node
 */
export interface AggregationNodeConfig {
  operations: Array<{
    type: 'sum' | 'count' | 'average' | 'min' | 'max' | 'distinct';
    field: string;
    alias?: string;
    filter?: {
      field: string;
      operator: string;
      value: any;
    };
  }>;
  groupBy?: string[];
  options?: {
    includeRawData?: boolean;
    sortResults?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  };
}

/**
 * Aggregation node for data aggregation operations
 */
@injectable()
export class AggregationNode extends BaseNode implements IAggregationNode {
  public readonly operations: Array<{
    type: 'sum' | 'count' | 'average' | 'min' | 'max' | 'distinct';
    field: string;
    alias?: string;
  }>;
  public readonly groupBy?: string[];

  constructor(
    config: AggregationNodeConfig,
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.DataTransformer) private dataTransformer: IDataTransformer
  ) {
    super(config, logger);
    this.operations = config.operations;
    this.groupBy = config.groupBy;
  }

  /**
   * Execute the Aggregation node
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info('Executing Aggregation node', {
      executionId: context.executionId,
      nodeId: this.config.id || 'unknown',
      operationCount: this.operations.length,
      groupByFields: this.groupBy?.length || 0,
      inputType: typeof input,
      isArray: Array.isArray(input)
    });

    try {
      if (!Array.isArray(input)) {
        throw new Error('Aggregation node requires array input');
      }

      const config = this.config as AggregationNodeConfig;

      // Apply filters if specified
      let filteredData = input;
      if (this.hasFilters()) {
        filteredData = this.applyFilters(input);
      }

      // Perform aggregation
      const aggregatedData = await this.aggregateData(filteredData, context);

      // Apply sorting if specified
      let result = aggregatedData;
      if (config.options?.sortResults) {
        result = this.sortResults(result, config.options.sortResults);
      }

      // Apply pagination if specified
      if (config.options?.limit || config.options?.offset) {
        result = this.paginateResults(result, config.options);
      }

      // Include raw data if requested
      if (config.options?.includeRawData) {
        result = {
          aggregated: result,
          rawData: filteredData,
          metadata: {
            originalCount: input.length,
            filteredCount: filteredData.length,
            operationCount: this.operations.length,
            timestamp: new Date()
          }
        };
      }

      this.logger.info('Aggregation completed successfully', {
        executionId: context.executionId,
        inputRecords: input.length,
        filteredRecords: filteredData.length,
        resultType: typeof result
      });

      return result;
    } catch (error) {
      this.logger.error('Aggregation node execution failed', {
        executionId: context.executionId,
        nodeId: this.config.id || 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      throw error;
    }
  }

  /**
   * Transform input data
   */
  async transformData(data: any, context: WorkflowContext): Promise<any> {
    return this.aggregateData(data, context);
  }

  /**
   * Aggregate data based on operations
   */
  async aggregateData(data: any[], context: WorkflowContext): Promise<any> {
    this.logger.debug('Aggregating data', {
      executionId: context.executionId,
      recordCount: data.length,
      operationCount: this.operations.length,
      hasGroupBy: !!this.groupBy?.length
    });

    const aggregationOps: AggregationOperation[] = this.operations.map((op) => ({
      type: op.type,
      field: op.field,
      alias: op.alias
    }));

    // Use the data transformer to perform aggregation
    const result = await this.dataTransformer.aggregateData(data, aggregationOps, this.groupBy);

    return result;
  }

  /**
   * Group data by specified fields
   */
  async groupData(data: any[], groupFields: string[]): Promise<Record<string, any[]>> {
    this.logger.debug('Grouping data', {
      recordCount: data.length,
      groupFields
    });

    const groups: Record<string, any[]> = {};

    for (const item of data) {
      const groupKey = groupFields.map((field) => this.getNestedValue(item, field)).join('|');

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }

      groups[groupKey].push(item);
    }

    return groups;
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    const config = this.config as AggregationNodeConfig;

    if (!config.operations || !Array.isArray(config.operations)) {
      throw new Error('Aggregation node requires operations array');
    }

    if (config.operations.length === 0) {
      throw new Error('Aggregation node requires at least one operation');
    }

    // Validate each operation
    for (let i = 0; i < config.operations.length; i++) {
      const operation = config.operations[i];

      if (!operation.type) {
        throw new Error(`Operation ${i}: type is required`);
      }

      const validTypes = ['sum', 'count', 'average', 'min', 'max', 'distinct'];
      if (!validTypes.includes(operation.type)) {
        throw new Error(
          `Operation ${i}: invalid type '${operation.type}'. Must be one of: ${validTypes.join(', ')}`
        );
      }

      if (!operation.field) {
        throw new Error(`Operation ${i}: field is required`);
      }

      // Validate filter if specified
      if (operation.filter) {
        if (!operation.filter.field || !operation.filter.operator) {
          throw new Error(`Operation ${i}: filter requires field and operator`);
        }
      }
    }

    // Validate groupBy if specified
    if (config.groupBy) {
      if (!Array.isArray(config.groupBy)) {
        throw new Error('groupBy must be an array');
      }

      if (config.groupBy.length === 0) {
        throw new Error('groupBy array cannot be empty');
      }
    }

    // Validate options
    if (config.options) {
      if (config.options.sortResults) {
        if (!config.options.sortResults.field) {
          throw new Error('sortResults requires field');
        }

        const validDirections = ['asc', 'desc'];
        if (!validDirections.includes(config.options.sortResults.direction)) {
          throw new Error('sortResults direction must be asc or desc');
        }
      }

      if (
        config.options.limit &&
        (typeof config.options.limit !== 'number' || config.options.limit <= 0)
      ) {
        throw new Error('limit must be a positive number');
      }

      if (
        config.options.offset &&
        (typeof config.options.offset !== 'number' || config.options.offset < 0)
      ) {
        throw new Error('offset must be a non-negative number');
      }
    }
  }

  /**
   * Check if any operations have filters
   */
  private hasFilters(): boolean {
    const config = this.config as AggregationNodeConfig;
    return config.operations.some((op) => op.filter);
  }

  /**
   * Apply filters to data
   */
  private applyFilters(data: any[]): any[] {
    const config = this.config as AggregationNodeConfig;

    return data.filter((item) => {
      return config.operations.every((operation) => {
        if (!operation.filter) return true;

        const fieldValue = this.getNestedValue(item, operation.filter.field);
        return this.evaluateFilter(fieldValue, operation.filter.operator, operation.filter.value);
      });
    });
  }

  /**
   * Evaluate filter condition
   */
  private evaluateFilter(fieldValue: any, operator: string, compareValue: any): boolean {
    switch (operator) {
      case 'equals':
      case '=':
      case '==':
        return fieldValue === compareValue;
      case 'not_equals':
      case '!=':
        return fieldValue !== compareValue;
      case 'greater_than':
      case '>':
        return Number(fieldValue) > Number(compareValue);
      case 'less_than':
      case '<':
        return Number(fieldValue) < Number(compareValue);
      case 'greater_than_or_equal':
      case '>=':
        return Number(fieldValue) >= Number(compareValue);
      case 'less_than_or_equal':
      case '<=':
        return Number(fieldValue) <= Number(compareValue);
      case 'contains':
        return String(fieldValue).includes(String(compareValue));
      case 'starts_with':
        return String(fieldValue).startsWith(String(compareValue));
      case 'ends_with':
        return String(fieldValue).endsWith(String(compareValue));
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue);
      case 'not_in':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
      default:
        this.logger.warn('Unknown filter operator', { operator });
        return true;
    }
  }

  /**
   * Sort aggregation results
   */
  private sortResults(data: any, sortConfig: { field: string; direction: 'asc' | 'desc' }): any {
    if (Array.isArray(data)) {
      return [...data].sort((a, b) => {
        const aVal = this.getNestedValue(a, sortConfig.field);
        const bVal = this.getNestedValue(b, sortConfig.field);

        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    } else if (typeof data === 'object' && data !== null) {
      // Sort grouped results
      const sortedEntries = Object.entries(data).sort(([, a], [, b]) => {
        const aVal = this.getNestedValue(a, sortConfig.field);
        const bVal = this.getNestedValue(b, sortConfig.field);

        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });

      return Object.fromEntries(sortedEntries);
    }

    return data;
  }

  /**
   * Apply pagination to results
   */
  private paginateResults(data: any, options: { limit?: number; offset?: number }): any {
    if (!Array.isArray(data)) {
      return data; // Cannot paginate non-array results
    }

    const offset = options.offset || 0;
    const limit = options.limit;

    if (limit) {
      return data.slice(offset, offset + limit);
    } else if (offset > 0) {
      return data.slice(offset);
    }

    return data;
  }

  /**
   * Get nested value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    if (!obj || typeof obj !== 'object') {
      return undefined;
    }

    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get node configuration summary for debugging
   */
  getConfigSummary(): any {
    const config = this.config as AggregationNodeConfig;

    return {
      type: 'aggregation',
      operations: this.operations.map((op) => ({
        type: op.type,
        field: op.field,
        alias: op.alias || `${op.type}_${op.field}`
      })),
      groupBy: this.groupBy || [],
      options: {
        includeRawData: config.options?.includeRawData || false,
        hasSorting: !!config.options?.sortResults,
        hasPagination: !!(config.options?.limit || config.options?.offset)
      },
      description: this.config.description
    };
  }

  /**
   * Get estimated execution time for planning purposes
   */
  getEstimatedExecutionTime(): number {
    // Base time + time per operation + grouping overhead
    const baseTime = 100;
    const operationTime = this.operations.length * 20;
    const groupingTime = this.groupBy?.length ? 50 : 0;

    return baseTime + operationTime + groupingTime; // milliseconds
  }

  /**
   * Get resource requirements for this node
   */
  getResourceRequirements(): {
    memory: number;
    cpu: number;
    timeout: number;
  } {
    const config = this.config as AggregationNodeConfig;

    return {
      memory: 1024 * 1024 * 5, // 5MB for aggregation operations
      cpu: 15 + this.operations.length * 5, // CPU percentage
      timeout: (config as any).timeout || 15000 // 15 seconds default
    };
  }
}
