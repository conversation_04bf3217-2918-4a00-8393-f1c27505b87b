import { injectable, inject } from 'inversify';
import { BaseNode } from '../../../workflow/nodes/BaseNode';
import { IDataMappingNode } from '../../../core/interfaces/IExtendedActionNodes';
import {
  type IDataTransformer,
  FieldMapping,
  ValidationRule
} from '../../../core/interfaces/IDataTransformer';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';

/**
 * Configuration for Data Mapping node
 */
export interface DataMappingNodeConfig {
  timeout?: number;
  mappings: Array<{
    source: string;
    target: string;
    transform?: string;
    defaultValue?: any;
    required?: boolean;
    validation?: ValidationRule;
  }>;
  validation?: {
    required?: string[];
    schema?: any;
    strictMode?: boolean;
  };
  options?: {
    preserveOriginal?: boolean;
    allowAdditionalFields?: boolean;
    skipNullValues?: boolean;
    skipUndefinedValues?: boolean;
  };
}

/**
 * Data mapping node for schema transformations
 */
@injectable()
export class DataMappingNode extends BaseNode implements IDataMappingNode {
  public readonly mappings: Array<{
    source: string;
    target: string;
    transform?: string;
    defaultValue?: any;
    required?: boolean;
    validation?: ValidationRule;
  }>;
  public readonly validation?: {
    required?: string[];
    schema?: any;
  };

  constructor(
    config: DataMappingNodeConfig,
    @inject(TYPES.Logger) logger: ILogger,
    @inject(TYPES.DataTransformer) private dataTransformer: IDataTransformer
  ) {
    super(config, logger);
    this.mappings = config.mappings;
    this.validation = config.validation;
  }

  /**
   * Execute the Data Mapping node
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info('Executing Data Mapping node', {
      executionId: context.executionId,
      nodeId: this.config.id || 'unknown',
      mappingCount: this.mappings.length,
      inputType: typeof input,
      isArray: Array.isArray(input)
    });

    try {
      const config = this.config as DataMappingNodeConfig;

      // Transform the data using mappings
      const mappedData = await this.mapData(input, context);

      // Validate the mapped data if validation is configured
      if (this.validation) {
        const validationResult = await this.validateMappedData(mappedData);
        if (!validationResult.valid) {
          throw new Error(`Data validation failed: ${validationResult.errors.join(', ')}`);
        }
      }

      // Prepare result based on options
      let result = mappedData;
      if (config.options?.preserveOriginal) {
        result = {
          original: input,
          mapped: mappedData,
          metadata: {
            mappingCount: this.mappings.length,
            timestamp: new Date()
          }
        };
      }

      this.logger.info('Data mapping completed successfully', {
        executionId: context.executionId,
        inputRecords: Array.isArray(input) ? input.length : 1,
        outputRecords: Array.isArray(mappedData) ? mappedData.length : 1
      });

      return result;
    } catch (error) {
      this.logger.error('Data Mapping node execution failed', {
        executionId: context.executionId,
        nodeId: this.config.id || 'unknown',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      throw error;
    }
  }

  /**
   * Transform input data
   */
  async transformData(data: any, context: WorkflowContext): Promise<any> {
    return this.mapData(data, context);
  }

  /**
   * Map data from source to target schema
   */
  async mapData(data: any, context: WorkflowContext): Promise<any> {
    this.logger.debug('Mapping data', {
      executionId: context.executionId,
      dataType: typeof data,
      isArray: Array.isArray(data),
      mappingCount: this.mappings.length
    });

    const fieldMappings: FieldMapping[] = this.mappings.map((mapping) => ({
      source: mapping.source,
      target: mapping.target,
      transform: mapping.transform,
      defaultValue: mapping.defaultValue,
      required: mapping.required,
      validation: mapping.validation
    }));

    // Use the data transformer to perform the mapping
    const result = await this.dataTransformer.mapFields(data, fieldMappings, context);

    return result;
  }

  /**
   * Validate mapped data
   */
  async validateMappedData(data: any): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    if (!this.validation) {
      return { valid: true, errors: [] };
    }

    // Check required fields
    if (this.validation.required) {
      for (const field of this.validation.required) {
        if (this.getNestedValue(data, field) === undefined) {
          errors.push(`Required field '${field}' is missing`);
        }
      }
    }

    // Validate against schema if provided
    if (this.validation.schema) {
      const schemaValidation = await this.dataTransformer.validateData(
        data,
        this.validation.schema
      );
      if (!schemaValidation.valid) {
        errors.push(...schemaValidation.errors);
      }
    }

    // Custom validation for each mapping
    for (const mapping of this.mappings) {
      if (mapping.validation) {
        const fieldValue = this.getNestedValue(data, mapping.target);
        const fieldErrors = this.validateField(fieldValue, mapping.validation, mapping.target);
        errors.push(...fieldErrors);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    const config = this.config as DataMappingNodeConfig;

    if (!config.mappings || !Array.isArray(config.mappings)) {
      throw new Error('Data Mapping node requires mappings array');
    }

    if (config.mappings.length === 0) {
      throw new Error('Data Mapping node requires at least one mapping');
    }

    // Validate each mapping
    for (let i = 0; i < config.mappings.length; i++) {
      const mapping = config.mappings[i];

      if (!mapping.source) {
        throw new Error(`Mapping ${i}: source field is required`);
      }

      if (!mapping.target) {
        throw new Error(`Mapping ${i}: target field is required`);
      }

      // Validate transform function if specified
      if (mapping.transform) {
        const availableTransforms = this.dataTransformer.getAvailableTransformations();
        const builtInTransforms = ['uppercase', 'lowercase', 'trim', 'number', 'string', 'boolean'];

        if (
          !availableTransforms.includes(mapping.transform) &&
          !builtInTransforms.includes(mapping.transform)
        ) {
          this.logger.warn(`Unknown transform function: ${mapping.transform}`, {
            mapping: i,
            availableTransforms
          });
        }
      }
    }

    // Validate validation configuration
    if (config.validation) {
      if (config.validation.required && !Array.isArray(config.validation.required)) {
        throw new Error('validation.required must be an array');
      }
    }
  }

  /**
   * Validate individual field value
   */
  private validateField(value: any, validation: any, fieldName: string): string[] {
    const errors: string[] = [];

    if (validation.required && (value === undefined || value === null)) {
      errors.push(`Field '${fieldName}' is required`);
      return errors; // Skip other validations if required field is missing
    }

    if (value === undefined || value === null) {
      return errors; // Skip validations for optional null/undefined values
    }

    // Type validation
    if (validation.type) {
      const actualType = Array.isArray(value) ? 'array' : typeof value;
      if (actualType !== validation.type) {
        errors.push(`Field '${fieldName}' expected type ${validation.type}, got ${actualType}`);
      }
    }

    // Pattern validation (for strings)
    if (validation.pattern && typeof value === 'string') {
      try {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(value)) {
          errors.push(`Field '${fieldName}' does not match pattern ${validation.pattern}`);
        }
      } catch (e) {
        errors.push(`Field '${fieldName}' has invalid pattern: ${validation.pattern}`);
      }
    }

    // Numeric range validation
    if (typeof value === 'number') {
      if (validation.min !== undefined && value < validation.min) {
        errors.push(`Field '${fieldName}' value ${value} is less than minimum ${validation.min}`);
      }
      if (validation.max !== undefined && value > validation.max) {
        errors.push(
          `Field '${fieldName}' value ${value} is greater than maximum ${validation.max}`
        );
      }
    }

    // String length validation
    if (typeof value === 'string') {
      if (validation.min !== undefined && value.length < validation.min) {
        errors.push(
          `Field '${fieldName}' length ${value.length} is less than minimum ${validation.min}`
        );
      }
      if (validation.max !== undefined && value.length > validation.max) {
        errors.push(
          `Field '${fieldName}' length ${value.length} is greater than maximum ${validation.max}`
        );
      }
    }

    // Array length validation
    if (Array.isArray(value)) {
      if (validation.min !== undefined && value.length < validation.min) {
        errors.push(
          `Field '${fieldName}' array length ${value.length} is less than minimum ${validation.min}`
        );
      }
      if (validation.max !== undefined && value.length > validation.max) {
        errors.push(
          `Field '${fieldName}' array length ${value.length} is greater than maximum ${validation.max}`
        );
      }
    }

    return errors;
  }

  /**
   * Get nested value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    if (!obj || typeof obj !== 'object') {
      return undefined;
    }

    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Get node configuration summary for debugging
   */
  getConfigSummary(): any {
    const config = this.config as DataMappingNodeConfig;

    return {
      type: 'data_mapping',
      mappings: this.mappings.map((m) => ({
        source: m.source,
        target: m.target,
        hasTransform: !!m.transform,
        hasDefault: m.defaultValue !== undefined,
        required: !!m.required
      })),
      validation: {
        hasRequiredFields: !!this.validation?.required?.length,
        hasSchema: !!this.validation?.schema,
        requiredFieldCount: this.validation?.required?.length || 0
      },
      options: config.options || {},
      description: this.config.description
    };
  }

  /**
   * Get estimated execution time for planning purposes
   */
  getEstimatedExecutionTime(): number {
    // Base time + time per mapping
    return 50 + this.mappings.length * 5; // milliseconds
  }

  /**
   * Get resource requirements for this node
   */
  getResourceRequirements(): {
    memory: number;
    cpu: number;
    timeout: number;
  } {
    const config = this.config as DataMappingNodeConfig;

    return {
      memory: 1024 * 1024 * 2, // 2MB base + mapping overhead
      cpu: 10 + this.mappings.length * 2, // CPU percentage
      timeout: (config as any).timeout || 10000 // 10 seconds default
    };
  }
}
