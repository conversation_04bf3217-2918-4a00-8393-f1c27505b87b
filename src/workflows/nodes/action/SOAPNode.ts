import { injectable, inject } from 'inversify';
import { BaseNode } from '../../../workflow/nodes/BaseNode';
import { ISOAPNode, SOAPAuthentication } from '../../../core/interfaces/IExtendedActionNodes';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';

/**
 * Configuration for SOAP node
 */
export interface SOAPNodeConfig {
  wsdl: string;
  operation: string;
  parameters: Record<string, any>;
  authentication?: SOAPAuthentication;
  timeout?: number;
  retryAttempts?: number;
  soapVersion?: '1.1' | '1.2';
  namespace?: string;
  soapAction?: string;
}

/**
 * SOAP node for calling web services
 */
@injectable()
export class SOAPNode extends BaseNode implements ISOAPNode {
  public readonly wsdl: string;
  public readonly operation: string;
  public readonly parameters: Record<string, any>;
  public readonly authentication?: SOAPAuthentication;
  public readonly timeout?: number;
  private readonly soapVersion: '1.1' | '1.2';
  private readonly namespace?: string;
  private readonly soapAction?: string;

  constructor(config: SOAPNodeConfig, @inject(TYPES.Logger) logger: ILogger) {
    super(config, logger);
    this.wsdl = config.wsdl;
    this.operation = config.operation;
    this.parameters = config.parameters;
    this.authentication = config.authentication;
    this.timeout = config.timeout;
    this.soapVersion = config.soapVersion || '1.1';
    this.namespace = config.namespace;
    this.soapAction = config.soapAction;
  }

  /**
   * Execute the SOAP node
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info('Executing SOAP node', {
      executionId: context.executionId,
      nodeId: this.config.id || 'unknown',
      operation: this.operation,
      wsdl: this.wsdl
    });

    try {
      const result = await this.callService(context);

      this.logger.info('SOAP service call completed', {
        executionId: context.executionId,
        operation: this.operation,
        statusCode: result.statusCode,
        executionTime: result.executionTime
      });

      return {
        ...input,
        soapResult: result,
        metadata: {
          operation: this.operation,
          wsdl: this.wsdl,
          statusCode: result.statusCode,
          executionTime: result.executionTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      this.logger.error('SOAP node execution failed', {
        executionId: context.executionId,
        nodeId: this.config.id || 'unknown',
        operation: this.operation,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      throw error;
    }
  }

  /**
   * Call SOAP web service
   */
  async callService(context: WorkflowContext): Promise<{
    result: any;
    headers?: Record<string, string>;
    statusCode: number;
    executionTime: number;
  }> {
    const startTime = Date.now();

    this.logger.debug('Calling SOAP service', {
      executionId: context.executionId,
      operation: this.operation,
      wsdl: this.wsdl
    });

    try {
      // Build SOAP envelope
      const envelope = await this.buildEnvelope(this.operation, this.resolveParameters(context));

      // Prepare headers
      const headers = this.buildHeaders();

      // Make HTTP request
      const response = await this.makeSOAPRequest(envelope, headers);

      // Parse response
      const parsedResponse = await this.parseResponse(response.body);

      const executionTime = Date.now() - startTime;

      return {
        result: parsedResponse.data,
        headers: response.headers,
        statusCode: response.status,
        executionTime
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;

      this.logger.error('SOAP service call failed', {
        executionId: context.executionId,
        operation: this.operation,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        executionTime
      });

      throw error;
    }
  }

  /**
   * Parse WSDL and get available operations
   */
  async parseWSDL(wsdlUrl: string): Promise<{
    services: Array<{
      name: string;
      ports: Array<{
        name: string;
        binding: string;
        address: string;
      }>;
    }>;
    operations: Array<{
      name: string;
      input: any;
      output: any;
      documentation?: string;
    }>;
  }> {
    this.logger.debug('Parsing WSDL', { wsdlUrl });

    try {
      // Fetch WSDL
      const response = await fetch(wsdlUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch WSDL: ${response.statusText}`);
      }

      const wsdlContent = await response.text();

      // Parse WSDL XML (simplified implementation)
      const services = this.extractServices(wsdlContent);
      const operations = this.extractOperations(wsdlContent);

      return {
        services,
        operations
      };
    } catch (error) {
      this.logger.error('WSDL parsing failed', {
        wsdlUrl,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Build SOAP envelope for operation
   */
  async buildEnvelope(operation: string, parameters: Record<string, any>): Promise<string> {
    this.logger.debug('Building SOAP envelope', {
      operation,
      parameterCount: Object.keys(parameters).length
    });

    const soapNamespace =
      this.soapVersion === '1.2'
        ? 'http://www.w3.org/2003/05/soap-envelope'
        : 'http://schemas.xmlsoap.org/soap/envelope/';

    const targetNamespace = this.namespace || 'http://tempuri.org/';

    let envelope = `<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="${soapNamespace}" xmlns:tns="${targetNamespace}">
  <soap:Header>`;

    // Add authentication to header if needed
    if (this.authentication?.type === 'wsse') {
      envelope += this.buildWSSecurityHeader();
    }

    envelope += `
  </soap:Header>
  <soap:Body>
    <tns:${operation}>`;

    // Add parameters
    for (const [key, value] of Object.entries(parameters)) {
      envelope += `
      <tns:${key}>${this.escapeXml(value)}</tns:${key}>`;
    }

    envelope += `
    </tns:${operation}>
  </soap:Body>
</soap:Envelope>`;

    return envelope;
  }

  /**
   * Parse SOAP response
   */
  async parseResponse(responseXml: string): Promise<{
    data: any;
    fault?: {
      code: string;
      message: string;
      detail?: any;
    };
  }> {
    this.logger.debug('Parsing SOAP response');

    try {
      // Check for SOAP fault
      if (responseXml.includes('soap:Fault') || responseXml.includes('Fault')) {
        const fault = this.extractFault(responseXml);
        return { data: null, fault };
      }

      // Extract response data (simplified XML parsing)
      const data = this.extractResponseData(responseXml);

      return { data };
    } catch (error) {
      this.logger.error('SOAP response parsing failed', {
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    const config = this.config as SOAPNodeConfig;

    if (!config.wsdl) {
      throw new Error('SOAP WSDL URL is required');
    }

    if (!config.operation) {
      throw new Error('SOAP operation is required');
    }

    if (!config.parameters) {
      throw new Error('SOAP parameters are required');
    }

    try {
      new URL(config.wsdl);
    } catch {
      throw new Error('Invalid WSDL URL');
    }

    if (config.soapVersion && !['1.1', '1.2'].includes(config.soapVersion)) {
      throw new Error('Invalid SOAP version. Must be 1.1 or 1.2');
    }
  }

  /**
   * Resolve parameters with context values
   */
  private resolveParameters(context: WorkflowContext): Record<string, any> {
    const resolved: Record<string, any> = {};

    for (const [key, value] of Object.entries(this.parameters)) {
      if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
        const path = value.slice(2, -2).trim();
        resolved[key] = this.getContextValue(path, context);
      } else {
        resolved[key] = value;
      }
    }

    return resolved;
  }

  /**
   * Get value from context using dot notation
   */
  private getContextValue(path: string, context: WorkflowContext): any {
    const parts = path.split('.');
    let value: any = {
      input: context.input,
      variables: context.variables,
      nodeResults: context.nodeResults
    };

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Build HTTP headers for SOAP request
   */
  private buildHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type':
        this.soapVersion === '1.2'
          ? 'application/soap+xml; charset=utf-8'
          : 'text/xml; charset=utf-8'
    };

    // Add SOAPAction header for SOAP 1.1
    if (this.soapVersion === '1.1' && this.soapAction) {
      headers['SOAPAction'] = `"${this.soapAction}"`;
    }

    // Add authentication headers
    if (this.authentication) {
      this.addAuthenticationHeaders(headers);
    }

    return headers;
  }

  /**
   * Add authentication headers
   */
  private addAuthenticationHeaders(headers: Record<string, string>): void {
    if (!this.authentication) return;

    switch (this.authentication.type) {
      case 'basic':
        if (this.authentication.username && this.authentication.password) {
          const credentials = Buffer.from(
            `${this.authentication.username}:${this.authentication.password}`
          ).toString('base64');
          headers['Authorization'] = `Basic ${credentials}`;
        }
        break;
      case 'digest':
        // Digest authentication would require more complex implementation
        this.logger.warn('Digest authentication not fully implemented');
        break;
      case 'custom':
        if (this.authentication.customHeaders) {
          Object.assign(headers, this.authentication.customHeaders);
        }
        break;
    }
  }

  /**
   * Build WS-Security header
   */
  private buildWSSecurityHeader(): string {
    if (!this.authentication || this.authentication.type !== 'wsse') {
      return '';
    }

    const username = this.authentication.username || '';
    const password = this.authentication.password || '';
    const nonce = Buffer.from(Math.random().toString()).toString('base64');
    const created = new Date().toISOString();

    return `
    <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
      <wsse:UsernameToken>
        <wsse:Username>${username}</wsse:Username>
        <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${password}</wsse:Password>
        <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">${nonce}</wsse:Nonce>
        <wsu:Created xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">${created}</wsu:Created>
      </wsse:UsernameToken>
    </wsse:Security>`;
  }

  /**
   * Make HTTP request to SOAP endpoint
   */
  private async makeSOAPRequest(
    envelope: string,
    headers: Record<string, string>
  ): Promise<{
    body: string;
    headers: Record<string, string>;
    status: number;
  }> {
    // Extract endpoint from WSDL (simplified - would normally parse WSDL)
    const endpoint = this.wsdl.replace('?wsdl', '').replace('?WSDL', '');

    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: envelope
    });

    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });

    return {
      body: await response.text(),
      headers: responseHeaders,
      status: response.status
    };
  }

  /**
   * Extract services from WSDL
   */
  private extractServices(wsdlContent: string): Array<{
    name: string;
    ports: Array<{
      name: string;
      binding: string;
      address: string;
    }>;
  }> {
    // Simplified WSDL parsing - in production, use a proper XML parser
    const services: Array<any> = [];

    // This is a very basic implementation
    // Real implementation would use proper XML parsing
    const serviceMatches = wsdlContent.match(/<wsdl:service[^>]*name="([^"]*)"[^>]*>/g);

    if (serviceMatches) {
      for (const match of serviceMatches) {
        const nameMatch = match.match(/name="([^"]*)"/);
        if (nameMatch) {
          services.push({
            name: nameMatch[1],
            ports: [] // Would extract ports from WSDL
          });
        }
      }
    }

    return services;
  }

  /**
   * Extract operations from WSDL
   */
  private extractOperations(wsdlContent: string): Array<{
    name: string;
    input: any;
    output: any;
    documentation?: string;
  }> {
    // Simplified operation extraction
    const operations: Array<any> = [];

    const operationMatches = wsdlContent.match(/<wsdl:operation[^>]*name="([^"]*)"[^>]*>/g);

    if (operationMatches) {
      for (const match of operationMatches) {
        const nameMatch = match.match(/name="([^"]*)"/);
        if (nameMatch) {
          operations.push({
            name: nameMatch[1],
            input: {}, // Would extract input schema
            output: {}, // Would extract output schema
            documentation: '' // Would extract documentation
          });
        }
      }
    }

    return operations;
  }

  /**
   * Extract SOAP fault from response
   */
  private extractFault(responseXml: string): {
    code: string;
    message: string;
    detail?: any;
  } {
    // Simplified fault extraction
    const codeMatch = responseXml.match(/<faultcode[^>]*>([^<]*)<\/faultcode>/i);
    const messageMatch = responseXml.match(/<faultstring[^>]*>([^<]*)<\/faultstring>/i);

    return {
      code: codeMatch ? codeMatch[1] : 'Unknown',
      message: messageMatch ? messageMatch[1] : 'Unknown SOAP fault',
      detail: null // Would extract fault detail
    };
  }

  /**
   * Extract response data from SOAP response
   */
  private extractResponseData(responseXml: string): any {
    // Simplified data extraction - would use proper XML parsing
    // Remove SOAP envelope and extract body content
    const bodyMatch = responseXml.match(/<soap:Body[^>]*>(.*)<\/soap:Body>/s);
    if (bodyMatch) {
      // Convert XML to simple object (very basic implementation)
      return this.xmlToObject(bodyMatch[1]);
    }

    return {};
  }

  /**
   * Simple XML to object conversion
   */
  private xmlToObject(xml: string): any {
    // Very basic XML parsing - in production, use a proper XML parser
    const result: any = {};

    const elementMatches = xml.match(/<([^>\/\s]+)[^>]*>([^<]*)<\/\1>/g);
    if (elementMatches) {
      for (const match of elementMatches) {
        const elementMatch = match.match(/<([^>\/\s]+)[^>]*>([^<]*)<\/\1>/);
        if (elementMatch) {
          result[elementMatch[1]] = elementMatch[2];
        }
      }
    }

    return result;
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(value: any): string {
    return String(value)
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }
}
