import { injectable, inject } from 'inversify';
import { BaseNode } from '../../../workflow/nodes/BaseNode';
import { IGraphQLNode } from '../../../core/interfaces/IExtendedActionNodes';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';

/**
 * Configuration for GraphQL node
 */
export interface GraphQLNodeConfig {
  endpoint: string;
  query: string;
  variables?: Record<string, any>;
  headers?: Record<string, string>;
  operationType: 'query' | 'mutation' | 'subscription';
  timeout?: number;
  retryAttempts?: number;
  authentication?: {
    type: 'bearer' | 'basic' | 'apikey';
    token?: string;
    username?: string;
    password?: string;
    apiKey?: string;
    headerName?: string;
  };
}

/**
 * GraphQL node for executing queries and mutations
 */
@injectable()
export class GraphQLNode extends BaseNode implements IGraphQLNode {
  public readonly endpoint: string;
  public readonly query: string;
  public readonly variables?: Record<string, any>;
  public readonly headers?: Record<string, string>;
  public readonly operationType: 'query' | 'mutation' | 'subscription';

  constructor(config: GraphQLNodeConfig, @inject(TYPES.Logger) logger: ILogger) {
    super(config, logger);
    this.endpoint = config.endpoint;
    this.query = config.query;
    this.variables = config.variables;
    this.headers = config.headers;
    this.operationType = config.operationType;
  }

  /**
   * Execute the GraphQL node
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info('Executing GraphQL node', {
      executionId: context.executionId,
      nodeId: this.config.id || 'unknown',
      operationType: this.operationType,
      endpoint: this.endpoint
    });

    try {
      let result: any;

      switch (this.operationType) {
        case 'query':
          result = await this.executeQuery(context);
          break;
        case 'mutation':
          result = await this.executeMutation(context);
          break;
        case 'subscription':
          throw new Error('Subscriptions not yet implemented');
        default:
          throw new Error(`Unsupported GraphQL operation: ${this.operationType}`);
      }

      this.logger.info('GraphQL operation completed', {
        executionId: context.executionId,
        operationType: this.operationType,
        hasErrors: !!result.errors
      });

      return {
        ...input,
        graphqlResult: result,
        metadata: {
          operationType: this.operationType,
          endpoint: this.endpoint,
          timestamp: new Date(),
          hasErrors: !!result.errors
        }
      };
    } catch (error) {
      this.logger.error('GraphQL node execution failed', {
        executionId: context.executionId,
        nodeId: this.config.id || 'unknown',
        operationType: this.operationType,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      throw error;
    }
  }

  /**
   * Execute GraphQL query
   */
  async executeQuery(context: WorkflowContext): Promise<{
    data?: any;
    errors?: Array<{
      message: string;
      locations?: Array<{ line: number; column: number }>;
      path?: string[];
    }>;
    extensions?: Record<string, any>;
  }> {
    this.logger.debug('Executing GraphQL query', {
      executionId: context.executionId,
      endpoint: this.endpoint
    });

    const requestBody = {
      query: this.resolveQuery(context),
      variables: this.resolveVariables(context),
      operationName: this.extractOperationName()
    };

    const response = await this.makeGraphQLRequest(requestBody, context);
    return response;
  }

  /**
   * Execute GraphQL mutation
   */
  async executeMutation(context: WorkflowContext): Promise<{
    data?: any;
    errors?: Array<{
      message: string;
      locations?: Array<{ line: number; column: number }>;
      path?: string[];
    }>;
  }> {
    this.logger.debug('Executing GraphQL mutation', {
      executionId: context.executionId,
      endpoint: this.endpoint
    });

    const requestBody = {
      query: this.resolveQuery(context),
      variables: this.resolveVariables(context),
      operationName: this.extractOperationName()
    };

    const response = await this.makeGraphQLRequest(requestBody, context);
    return response;
  }

  /**
   * Introspect GraphQL schema
   */
  async introspectSchema(context: WorkflowContext): Promise<{
    types: Array<{
      name: string;
      kind: string;
      fields?: Array<{
        name: string;
        type: string;
        description?: string;
      }>;
    }>;
    queries: string[];
    mutations: string[];
    subscriptions: string[];
  }> {
    this.logger.debug('Introspecting GraphQL schema', {
      executionId: context.executionId,
      endpoint: this.endpoint
    });

    const introspectionQuery = `
      query IntrospectionQuery {
        __schema {
          queryType { name }
          mutationType { name }
          subscriptionType { name }
          types {
            ...FullType
          }
        }
      }

      fragment FullType on __Type {
        kind
        name
        description
        fields(includeDeprecated: true) {
          name
          description
          type {
            ...TypeRef
          }
        }
      }

      fragment TypeRef on __Type {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
          }
        }
      }
    `;

    const requestBody = {
      query: introspectionQuery,
      variables: {}
    };

    const response = await this.makeGraphQLRequest(requestBody, context);

    if (response.errors) {
      throw new Error(
        `Schema introspection failed: ${response.errors.map((e: any) => e.message).join(', ')}`
      );
    }

    const schema = response.data.__schema;

    return {
      types: schema.types.map((type: any) => ({
        name: type.name,
        kind: type.kind,
        fields: type.fields?.map((field: any) => ({
          name: field.name,
          type: this.formatType(field.type),
          description: field.description
        }))
      })),
      queries: this.extractOperations(schema.queryType?.name, schema.types),
      mutations: this.extractOperations(schema.mutationType?.name, schema.types),
      subscriptions: this.extractOperations(schema.subscriptionType?.name, schema.types)
    };
  }

  /**
   * Validate GraphQL query syntax
   */
  async validateQuery(query: string): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];

    try {
      // Basic syntax validation
      if (!query.trim()) {
        errors.push('Query cannot be empty');
      }

      if (!query.includes('{') || !query.includes('}')) {
        errors.push('Query must contain valid GraphQL syntax with braces');
      }

      // Check for operation type
      const operationTypes = ['query', 'mutation', 'subscription'];
      const hasOperationType = operationTypes.some((type) => query.toLowerCase().includes(type));

      if (!hasOperationType) {
        errors.push('Query must specify an operation type (query, mutation, or subscription)');
      }

      // More sophisticated validation would require a GraphQL parser
      // For now, we do basic checks
    } catch (error) {
      errors.push(
        `Validation error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`
      );
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    const config = this.config as GraphQLNodeConfig;

    if (!config.endpoint) {
      throw new Error('GraphQL endpoint is required');
    }

    if (!config.query) {
      throw new Error('GraphQL query is required');
    }

    if (!config.operationType) {
      throw new Error('GraphQL operation type is required');
    }

    const validOperations = ['query', 'mutation', 'subscription'];
    if (!validOperations.includes(config.operationType)) {
      throw new Error(`Invalid operation type: ${config.operationType}`);
    }

    try {
      new URL(config.endpoint);
    } catch {
      throw new Error('Invalid GraphQL endpoint URL');
    }
  }

  /**
   * Make HTTP request to GraphQL endpoint
   */
  private async makeGraphQLRequest(requestBody: any, context: WorkflowContext): Promise<any> {
    const config = this.config as GraphQLNodeConfig;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...this.headers
    };

    // Add authentication headers
    if (config.authentication) {
      this.addAuthenticationHeaders(headers, config.authentication);
    }

    const requestOptions = {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody)
    };

    this.logger.debug('Making GraphQL request', {
      executionId: context.executionId,
      endpoint: this.endpoint,
      operationType: this.operationType
    });

    try {
      const response = await fetch(this.endpoint, requestOptions);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      this.logger.error('GraphQL request failed', {
        executionId: context.executionId,
        endpoint: this.endpoint,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Resolve query with context variables
   */
  private resolveQuery(context: WorkflowContext): string {
    let resolvedQuery = this.query;

    // Replace placeholders with context values
    const placeholderRegex = /\{\{([^}]+)\}\}/g;
    resolvedQuery = resolvedQuery.replace(placeholderRegex, (match, path) => {
      const value = this.getContextValue(path.trim(), context);
      return value !== undefined ? String(value) : match;
    });

    return resolvedQuery;
  }

  /**
   * Resolve variables with context values
   */
  private resolveVariables(context: WorkflowContext): Record<string, any> {
    if (!this.variables) return {};

    const resolvedVariables: Record<string, any> = {};

    for (const [key, value] of Object.entries(this.variables)) {
      if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
        const path = value.slice(2, -2).trim();
        resolvedVariables[key] = this.getContextValue(path, context);
      } else {
        resolvedVariables[key] = value;
      }
    }

    return resolvedVariables;
  }

  /**
   * Get value from context using dot notation
   */
  private getContextValue(path: string, context: WorkflowContext): any {
    const parts = path.split('.');
    let value: any = {
      input: context.input,
      variables: context.variables,
      nodeResults: context.nodeResults
    };

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Extract operation name from query
   */
  private extractOperationName(): string | undefined {
    const match = this.query.match(/(?:query|mutation|subscription)\s+(\w+)/);
    return match ? match[1] : undefined;
  }

  /**
   * Add authentication headers
   */
  private addAuthenticationHeaders(headers: Record<string, string>, auth: any): void {
    switch (auth.type) {
      case 'bearer':
        if (auth.token) {
          headers['Authorization'] = `Bearer ${auth.token}`;
        }
        break;
      case 'basic':
        if (auth.username && auth.password) {
          const credentials = Buffer.from(`${auth.username}:${auth.password}`).toString('base64');
          headers['Authorization'] = `Basic ${credentials}`;
        }
        break;
      case 'apikey':
        if (auth.apiKey && auth.headerName) {
          headers[auth.headerName] = auth.apiKey;
        }
        break;
    }
  }

  /**
   * Format GraphQL type for display
   */
  private formatType(type: any): string {
    if (type.ofType) {
      if (type.kind === 'NON_NULL') {
        return `${this.formatType(type.ofType)}!`;
      } else if (type.kind === 'LIST') {
        return `[${this.formatType(type.ofType)}]`;
      }
    }
    return type.name || type.kind;
  }

  /**
   * Extract operations from schema type
   */
  private extractOperations(typeName: string | undefined, types: any[]): string[] {
    if (!typeName) return [];

    const type = types.find((t) => t.name === typeName);
    if (!type || !type.fields) return [];

    return type.fields.map((field: any) => field.name);
  }
}
