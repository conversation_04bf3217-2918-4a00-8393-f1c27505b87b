import { injectable, inject } from 'inversify';
import { BaseNode } from '../../../workflow/nodes/BaseNode';
import {
  IVectorDBNode,
  VectorDBConfig,
  VectorSearchConfig
} from '../../../core/interfaces/IExtendedActionNodes';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import type { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';

/**
 * Configuration for Vector DB node
 */
export interface VectorDBNodeConfig extends VectorDBConfig {
  operation: 'search' | 'insert' | 'update' | 'delete' | 'query';
  searchConfig?: VectorSearchConfig;
  vectors?: Array<{
    id: string;
    values: number[];
    metadata?: Record<string, any>;
  }>;
  vectorIds?: string[];
  timeout?: number;
  retryAttempts?: number;
}

/**
 * Vector database node for similarity search and vector operations
 */
@injectable()
export class VectorDBNode extends BaseNode implements IVectorDBNode {
  public readonly provider: 'pinecone' | 'weaviate' | 'qdrant' | 'chroma';
  public readonly operation: 'search' | 'insert' | 'update' | 'delete' | 'query';
  public readonly config: VectorDBConfig;

  constructor(nodeConfig: VectorDBNodeConfig, @inject(TYPES.Logger) logger: ILogger) {
    super(nodeConfig, logger);
    this.provider = nodeConfig.provider;
    this.operation = nodeConfig.operation;
    this.config = {
      provider: nodeConfig.provider,
      apiKey: nodeConfig.apiKey,
      endpoint: nodeConfig.endpoint,
      indexName: nodeConfig.indexName,
      namespace: nodeConfig.namespace,
      dimension: nodeConfig.dimension,
      metric: nodeConfig.metric
    };
  }

  /**
   * Execute the Vector DB node
   */
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info('Executing Vector DB node', {
      executionId: context.executionId,
      nodeId: (this.config as any).id || 'unknown',
      provider: this.provider,
      operation: this.operation
    });

    try {
      let result: any;

      switch (this.operation) {
        case 'search':
          result = await this.search(this.getSearchConfig(input), context);
          break;
        case 'insert':
          result = await this.insert(this.getVectors(input), context);
          break;
        case 'update':
          result = await this.update(this.getVectors(input), context);
          break;
        case 'delete':
          result = await this.delete(this.getVectorIds(input), context);
          break;
        case 'query':
          result = await this.query(input, context);
          break;
        default:
          throw new Error(`Unsupported Vector DB operation: ${this.operation}`);
      }

      this.logger.info('Vector DB operation completed', {
        executionId: context.executionId,
        operation: this.operation,
        provider: this.provider
      });

      return {
        ...input,
        vectorDBResult: result,
        metadata: {
          operation: this.operation,
          provider: this.provider,
          timestamp: new Date()
        }
      };
    } catch (error) {
      this.logger.error('Vector DB node execution failed', {
        executionId: context.executionId,
        nodeId: (this.config as any).id || 'unknown',
        operation: this.operation,
        provider: this.provider,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      throw error;
    }
  }

  /**
   * Perform vector similarity search
   */
  async search(
    searchConfig: VectorSearchConfig,
    context: WorkflowContext
  ): Promise<{
    matches: Array<{
      id: string;
      score: number;
      values?: number[];
      metadata?: Record<string, any>;
    }>;
    namespace?: string;
  }> {
    this.logger.debug('Performing vector search', {
      executionId: context.executionId,
      provider: this.provider,
      topK: searchConfig.topK,
      hasVector: !!searchConfig.vector,
      hasQuery: !!searchConfig.query
    });

    switch (this.provider) {
      case 'pinecone':
        return this.searchPinecone(searchConfig);
      case 'weaviate':
        return this.searchWeaviate(searchConfig);
      case 'qdrant':
        return this.searchQdrant(searchConfig);
      case 'chroma':
        return this.searchChroma(searchConfig);
      default:
        throw new Error(`Unsupported vector DB provider: ${this.provider}`);
    }
  }

  /**
   * Insert vectors into the database
   */
  async insert(
    vectors: Array<{
      id: string;
      values: number[];
      metadata?: Record<string, any>;
    }>,
    context: WorkflowContext
  ): Promise<{
    upsertedCount: number;
    errors?: string[];
  }> {
    this.logger.debug('Inserting vectors', {
      executionId: context.executionId,
      provider: this.provider,
      vectorCount: vectors.length
    });

    switch (this.provider) {
      case 'pinecone':
        return this.insertPinecone(vectors);
      case 'weaviate':
        return this.insertWeaviate(vectors);
      case 'qdrant':
        return this.insertQdrant(vectors);
      case 'chroma':
        return this.insertChroma(vectors);
      default:
        throw new Error(`Unsupported vector DB provider: ${this.provider}`);
    }
  }

  /**
   * Update existing vectors
   */
  async update(
    updates: Array<{
      id: string;
      values?: number[];
      metadata?: Record<string, any>;
    }>,
    context: WorkflowContext
  ): Promise<{
    updatedCount: number;
    errors?: string[];
  }> {
    this.logger.debug('Updating vectors', {
      executionId: context.executionId,
      provider: this.provider,
      updateCount: updates.length
    });

    switch (this.provider) {
      case 'pinecone':
        return this.updatePinecone(updates);
      case 'weaviate':
        return this.updateWeaviate(updates);
      case 'qdrant':
        return this.updateQdrant(updates);
      case 'chroma':
        return this.updateChroma(updates);
      default:
        throw new Error(`Unsupported vector DB provider: ${this.provider}`);
    }
  }

  /**
   * Delete vectors from the database
   */
  async delete(
    ids: string[],
    context: WorkflowContext
  ): Promise<{
    deletedCount: number;
    errors?: string[];
  }> {
    this.logger.debug('Deleting vectors', {
      executionId: context.executionId,
      provider: this.provider,
      idCount: ids.length
    });

    switch (this.provider) {
      case 'pinecone':
        return this.deletePinecone(ids);
      case 'weaviate':
        return this.deleteWeaviate(ids);
      case 'qdrant':
        return this.deleteQdrant(ids);
      case 'chroma':
        return this.deleteChroma(ids);
      default:
        throw new Error(`Unsupported vector DB provider: ${this.provider}`);
    }
  }

  /**
   * Validate node configuration
   */
  protected validateConfig(): void {
    super.validateConfig();

    const config = this.config as VectorDBNodeConfig;

    if (!config.provider) {
      throw new Error('Vector DB provider is required');
    }

    if (!config.operation) {
      throw new Error('Vector DB operation is required');
    }

    if (!config.apiKey) {
      throw new Error('Vector DB API key is required');
    }

    if (!config.endpoint) {
      throw new Error('Vector DB endpoint is required');
    }

    const validProviders = ['pinecone', 'weaviate', 'qdrant', 'chroma'];
    if (!validProviders.includes(config.provider)) {
      throw new Error(`Invalid provider: ${config.provider}`);
    }

    const validOperations = ['search', 'insert', 'update', 'delete', 'query'];
    if (!validOperations.includes(config.operation)) {
      throw new Error(`Invalid operation: ${config.operation}`);
    }
  }

  /**
   * Extract search configuration from input
   */
  private getSearchConfig(input: any): VectorSearchConfig {
    const nodeConfig = this.config as VectorDBNodeConfig;

    return {
      vector: input.vector || nodeConfig.searchConfig?.vector,
      query: input.query || nodeConfig.searchConfig?.query,
      topK: input.topK || nodeConfig.searchConfig?.topK || 10,
      filter: input.filter || nodeConfig.searchConfig?.filter,
      includeMetadata: input.includeMetadata ?? nodeConfig.searchConfig?.includeMetadata ?? true,
      includeValues: input.includeValues ?? nodeConfig.searchConfig?.includeValues ?? false
    };
  }

  /**
   * Extract vectors from input
   */
  private getVectors(input: any): Array<{
    id: string;
    values: number[];
    metadata?: Record<string, any>;
  }> {
    const nodeConfig = this.config as VectorDBNodeConfig;
    return input.vectors || nodeConfig.vectors || [];
  }

  /**
   * Extract vector IDs from input
   */
  private getVectorIds(input: any): string[] {
    const nodeConfig = this.config as VectorDBNodeConfig;
    return input.vectorIds || input.ids || nodeConfig.vectorIds || [];
  }

  /**
   * Perform query operation
   */
  private async query(input: any, context: WorkflowContext): Promise<any> {
    // Generic query operation - implementation depends on provider
    this.logger.debug('Performing vector query', {
      executionId: context.executionId,
      provider: this.provider
    });

    // For now, delegate to search
    const searchConfig = this.getSearchConfig(input);
    return this.search(searchConfig, context);
  }

  // Provider-specific implementations (simplified for demo)

  private async searchPinecone(searchConfig: VectorSearchConfig): Promise<any> {
    // Pinecone search implementation
    // This would use the actual Pinecone client
    this.logger.debug('Searching Pinecone', { topK: searchConfig.topK });

    // Simulated response
    return {
      matches: [
        {
          id: 'vec1',
          score: 0.95,
          metadata: { category: 'example' }
        }
      ],
      namespace: this.config.namespace
    };
  }

  private async searchWeaviate(searchConfig: VectorSearchConfig): Promise<any> {
    // Weaviate search implementation
    this.logger.debug('Searching Weaviate', { topK: searchConfig.topK });

    return {
      matches: [
        {
          id: 'weaviate-1',
          score: 0.92,
          metadata: { source: 'weaviate' }
        }
      ]
    };
  }

  private async searchQdrant(searchConfig: VectorSearchConfig): Promise<any> {
    // Qdrant search implementation
    this.logger.debug('Searching Qdrant', { topK: searchConfig.topK });

    return {
      matches: [
        {
          id: 'qdrant-1',
          score: 0.88,
          metadata: { engine: 'qdrant' }
        }
      ]
    };
  }

  private async searchChroma(searchConfig: VectorSearchConfig): Promise<any> {
    // Chroma search implementation
    this.logger.debug('Searching Chroma', { topK: searchConfig.topK });

    return {
      matches: [
        {
          id: 'chroma-1',
          score: 0.9,
          metadata: { db: 'chroma' }
        }
      ]
    };
  }

  private async insertPinecone(vectors: any[]): Promise<any> {
    this.logger.debug('Inserting to Pinecone', { count: vectors.length });
    return { upsertedCount: vectors.length };
  }

  private async insertWeaviate(vectors: any[]): Promise<any> {
    this.logger.debug('Inserting to Weaviate', { count: vectors.length });
    return { upsertedCount: vectors.length };
  }

  private async insertQdrant(vectors: any[]): Promise<any> {
    this.logger.debug('Inserting to Qdrant', { count: vectors.length });
    return { upsertedCount: vectors.length };
  }

  private async insertChroma(vectors: any[]): Promise<any> {
    this.logger.debug('Inserting to Chroma', { count: vectors.length });
    return { upsertedCount: vectors.length };
  }

  private async updatePinecone(updates: any[]): Promise<any> {
    this.logger.debug('Updating Pinecone', { count: updates.length });
    return { updatedCount: updates.length };
  }

  private async updateWeaviate(updates: any[]): Promise<any> {
    this.logger.debug('Updating Weaviate', { count: updates.length });
    return { updatedCount: updates.length };
  }

  private async updateQdrant(updates: any[]): Promise<any> {
    this.logger.debug('Updating Qdrant', { count: updates.length });
    return { updatedCount: updates.length };
  }

  private async updateChroma(updates: any[]): Promise<any> {
    this.logger.debug('Updating Chroma', { count: updates.length });
    return { updatedCount: updates.length };
  }

  private async deletePinecone(ids: string[]): Promise<any> {
    this.logger.debug('Deleting from Pinecone', { count: ids.length });
    return { deletedCount: ids.length };
  }

  private async deleteWeaviate(ids: string[]): Promise<any> {
    this.logger.debug('Deleting from Weaviate', { count: ids.length });
    return { deletedCount: ids.length };
  }

  private async deleteQdrant(ids: string[]): Promise<any> {
    this.logger.debug('Deleting from Qdrant', { count: ids.length });
    return { deletedCount: ids.length };
  }

  private async deleteChroma(ids: string[]): Promise<any> {
    this.logger.debug('Deleting from Chroma', { count: ids.length });
    return { deletedCount: ids.length };
  }
}
