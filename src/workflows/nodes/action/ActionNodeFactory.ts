import { Container } from 'inversify';
import { INode } from '../../../core/interfaces/INode';
import { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';

// TODO: Phase 2 - These extended action nodes use old naming convention
// They should be updated to use NodePalette naming convention:
// - 'vectordb' -> 'vectordb-action'
// - 'graphql' -> 'graphql-action'
// - 'soap' -> 'soap-action'
// For now, they are commented out to maintain Phase 6 clean migration approach

/*
import { VectorDBNode, VectorDBNodeConfig } from './VectorDBNode';
import { GraphQLNode, GraphQLNodeConfig } from './GraphQLNode';
import { SOAPNode, SOAPNodeConfig } from './SOAPNode';

/**
 * Factory function for creating Vector DB nodes
 */
/*
export function createVectorDBNodeFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);

    return new VectorDBNode(config as VectorDBNodeConfig, logger);
  };
}

/**
 * Factory function for creating GraphQL nodes
 */
/*
export function createGraphQLNodeFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);

    return new GraphQLNode(config as GraphQLNodeConfig, logger);
  };
}

/**
 * Factory function for creating SOAP nodes
 */
/*
export function createSOAPNodeFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);

    return new SOAPNode(config as SOAPNodeConfig, logger);
  };
}
*/

/**
 * Register all action node factories with the node registry
 */
export function registerActionNodes(container: Container): void {
  // TODO: Phase 2 - Re-enable extended action nodes with NodePalette naming convention
  // const nodeRegistry = container.get(TYPES.NodeRegistry);

  // Register action nodes with NodePalette naming:
  // (nodeRegistry as any).registerNode('vectordb-action', createVectorDBNodeFactory(container));
  // (nodeRegistry as any).registerNode('graphql-action', createGraphQLNodeFactory(container));
  // (nodeRegistry as any).registerNode('soap-action', createSOAPNodeFactory(container));

  console.log('Phase 2 extended action nodes temporarily disabled for clean NodePalette migration');
}
