import { Container } from 'inversify';
import { INode } from '../../../core/interfaces/INode';
import { ILogger } from '../../../core/interfaces/ILogger';
import { IConditionalProcessor } from '../../../core/interfaces/IConditionalProcessor';
import { TYPES } from '../../../types';
// Legacy imports removed - these nodes are now replaced with NodePalette types

/**
 * Legacy factory functions - DEPRECATED
 * These are kept for backward compatibility but should not be used.
 * Use NodePalette types instead: 'if-else-logic', 'switch-logic', 'merge-logic'
 */

/**
 * Register all logic node factories with the node registry
 * NOTE: This function is now empty as legacy logic nodes have been replaced
 * with NodePalette types in registerNodePaletteTypes()
 */
export function registerLogicNodes(container: Container): void {
  // Legacy logic nodes have been replaced with NodePalette types
  // New registrations are handled in registerNodePaletteTypes():
  // - 'if-else-logic' replaces 'if-else'
  // - 'switch-logic' replaces 'switch'
  // - 'merge-logic' replaces 'merge'
  // - 'loop-logic' is new

  console.log('Legacy logic nodes registration skipped - using NodePalette types instead');
}
