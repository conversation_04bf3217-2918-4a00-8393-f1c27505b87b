import { injectable, inject } from 'inversify';
import type { ILogger } from '../../core/interfaces/ILogger';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import { TYPES } from '../../types';

/**
 * Performance metrics for workflow execution
 */
export interface PerformanceMetrics {
  executionId: string;
  workflowId: string;
  totalExecutionTime: number;
  nodeExecutionTimes: Record<string, number>;
  memoryUsage: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  cpuUsage: {
    user: number;
    system: number;
  };
  parallelBranches: number;
  conditionalEvaluations: number;
  dataTransformations: number;
  errorCount: number;
  retryCount: number;
  timestamp: Date;
}

/**
 * Performance optimization recommendations
 */
export interface OptimizationRecommendation {
  type: 'memory' | 'cpu' | 'execution_time' | 'parallel' | 'conditional' | 'data';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  estimatedImprovement: string;
  nodeId?: string;
}

/**
 * Performance monitoring and optimization for workflows
 */
@injectable()
export class PerformanceMonitor {
  private metrics = new Map<string, PerformanceMetrics>();
  private executionStartTimes = new Map<string, number>();
  private nodeStartTimes = new Map<string, number>();

  constructor(@inject(TYPES.Logger) private logger: ILogger) {}

  /**
   * Start monitoring workflow execution
   */
  startMonitoring(context: WorkflowContext): void {
    const startTime = Date.now();
    this.executionStartTimes.set(context.executionId, startTime);

    this.logger.debug('Started performance monitoring', {
      executionId: context.executionId,
      workflowId: context.workflowId
    });

    // Initialize metrics
    const initialMetrics: PerformanceMetrics = {
      executionId: context.executionId,
      workflowId: context.workflowId,
      totalExecutionTime: 0,
      nodeExecutionTimes: {},
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.getCPUUsage(),
      parallelBranches: 0,
      conditionalEvaluations: 0,
      dataTransformations: 0,
      errorCount: 0,
      retryCount: 0,
      timestamp: new Date()
    };

    this.metrics.set(context.executionId, initialMetrics);
  }

  /**
   * Start monitoring node execution
   */
  startNodeMonitoring(executionId: string, nodeId: string): void {
    const startTime = Date.now();
    const nodeKey = `${executionId}_${nodeId}`;
    this.nodeStartTimes.set(nodeKey, startTime);

    this.logger.debug('Started node monitoring', {
      executionId,
      nodeId
    });
  }

  /**
   * End monitoring node execution
   */
  endNodeMonitoring(executionId: string, nodeId: string): void {
    const nodeKey = `${executionId}_${nodeId}`;
    const startTime = this.nodeStartTimes.get(nodeKey);

    if (startTime) {
      const executionTime = Date.now() - startTime;
      const metrics = this.metrics.get(executionId);

      if (metrics) {
        metrics.nodeExecutionTimes[nodeId] = executionTime;
        this.nodeStartTimes.delete(nodeKey);
      }

      this.logger.debug('Ended node monitoring', {
        executionId,
        nodeId,
        executionTime
      });
    }
  }

  /**
   * Record parallel branch execution
   */
  recordParallelExecution(executionId: string, branchCount: number): void {
    const metrics = this.metrics.get(executionId);
    if (metrics) {
      metrics.parallelBranches += branchCount;
    }
  }

  /**
   * Record conditional evaluation
   */
  recordConditionalEvaluation(executionId: string): void {
    const metrics = this.metrics.get(executionId);
    if (metrics) {
      metrics.conditionalEvaluations++;
    }
  }

  /**
   * Record data transformation
   */
  recordDataTransformation(executionId: string): void {
    const metrics = this.metrics.get(executionId);
    if (metrics) {
      metrics.dataTransformations++;
    }
  }

  /**
   * Record error occurrence
   */
  recordError(executionId: string): void {
    const metrics = this.metrics.get(executionId);
    if (metrics) {
      metrics.errorCount++;
    }
  }

  /**
   * Record retry attempt
   */
  recordRetry(executionId: string): void {
    const metrics = this.metrics.get(executionId);
    if (metrics) {
      metrics.retryCount++;
    }
  }

  /**
   * End monitoring workflow execution
   */
  endMonitoring(executionId: string): PerformanceMetrics | null {
    const startTime = this.executionStartTimes.get(executionId);
    const metrics = this.metrics.get(executionId);

    if (!startTime || !metrics) {
      return null;
    }

    const totalExecutionTime = Date.now() - startTime;
    metrics.totalExecutionTime = totalExecutionTime;
    metrics.memoryUsage = this.getMemoryUsage();
    metrics.cpuUsage = this.getCPUUsage();

    this.logger.info('Completed performance monitoring', {
      executionId,
      totalExecutionTime,
      nodeCount: Object.keys(metrics.nodeExecutionTimes).length,
      parallelBranches: metrics.parallelBranches,
      errorCount: metrics.errorCount
    });

    // Cleanup
    this.executionStartTimes.delete(executionId);

    return metrics;
  }

  /**
   * Get performance metrics for an execution
   */
  getMetrics(executionId: string): PerformanceMetrics | null {
    return this.metrics.get(executionId) || null;
  }

  /**
   * Analyze performance and provide optimization recommendations
   */
  analyzePerformance(metrics: PerformanceMetrics): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // Analyze execution time
    if (metrics.totalExecutionTime > 30000) {
      // 30 seconds
      recommendations.push({
        type: 'execution_time',
        severity: 'high',
        description: `Workflow execution took ${metrics.totalExecutionTime}ms, which is longer than recommended`,
        recommendation: 'Consider optimizing slow nodes or implementing parallel execution',
        estimatedImprovement: '30-50% reduction in execution time'
      });
    }

    // Analyze memory usage
    const memoryUsageMB = metrics.memoryUsage.heapUsed / 1024 / 1024;
    if (memoryUsageMB > 100) {
      // 100MB
      recommendations.push({
        type: 'memory',
        severity: 'medium',
        description: `High memory usage detected: ${memoryUsageMB.toFixed(2)}MB`,
        recommendation: 'Review data processing nodes and implement streaming where possible',
        estimatedImprovement: '20-40% reduction in memory usage'
      });
    }

    // Analyze node execution times
    const slowNodes = Object.entries(metrics.nodeExecutionTimes)
      .filter(([, time]) => time > 5000) // 5 seconds
      .sort(([, a], [, b]) => b - a);

    for (const [nodeId, time] of slowNodes.slice(0, 3)) {
      // Top 3 slow nodes
      recommendations.push({
        type: 'execution_time',
        severity: time > 10000 ? 'high' : 'medium',
        description: `Node ${nodeId} took ${time}ms to execute`,
        recommendation: 'Optimize node logic or consider caching results',
        estimatedImprovement: '10-30% reduction in node execution time',
        nodeId
      });
    }

    // Analyze parallel execution opportunities
    if (metrics.parallelBranches === 0 && Object.keys(metrics.nodeExecutionTimes).length > 3) {
      recommendations.push({
        type: 'parallel',
        severity: 'medium',
        description: 'No parallel execution detected in workflow with multiple nodes',
        recommendation: 'Consider implementing parallel execution for independent nodes',
        estimatedImprovement: '20-60% reduction in total execution time'
      });
    }

    // Analyze error rate
    const totalOperations =
      metrics.conditionalEvaluations +
      metrics.dataTransformations +
      Object.keys(metrics.nodeExecutionTimes).length;
    const errorRate = totalOperations > 0 ? (metrics.errorCount / totalOperations) * 100 : 0;

    if (errorRate > 5) {
      // 5% error rate
      recommendations.push({
        type: 'execution_time',
        severity: 'high',
        description: `High error rate detected: ${errorRate.toFixed(1)}%`,
        recommendation: 'Review error handling and add input validation',
        estimatedImprovement: 'Improved reliability and reduced retry overhead'
      });
    }

    // Analyze retry frequency
    if (metrics.retryCount > 0) {
      recommendations.push({
        type: 'execution_time',
        severity: 'medium',
        description: `${metrics.retryCount} retries occurred during execution`,
        recommendation: 'Investigate root causes of failures and improve error handling',
        estimatedImprovement: 'Reduced execution time and improved reliability'
      });
    }

    return recommendations;
  }

  /**
   * Get aggregated performance statistics
   */
  getAggregatedStats(workflowId?: string): {
    totalExecutions: number;
    averageExecutionTime: number;
    averageMemoryUsage: number;
    totalErrors: number;
    totalRetries: number;
    mostCommonRecommendations: Array<{ type: string; count: number }>;
  } {
    const relevantMetrics = Array.from(this.metrics.values()).filter(
      (m) => !workflowId || m.workflowId === workflowId
    );

    if (relevantMetrics.length === 0) {
      return {
        totalExecutions: 0,
        averageExecutionTime: 0,
        averageMemoryUsage: 0,
        totalErrors: 0,
        totalRetries: 0,
        mostCommonRecommendations: []
      };
    }

    const totalExecutionTime = relevantMetrics.reduce((sum, m) => sum + m.totalExecutionTime, 0);
    const totalMemoryUsage = relevantMetrics.reduce((sum, m) => sum + m.memoryUsage.heapUsed, 0);
    const totalErrors = relevantMetrics.reduce((sum, m) => sum + m.errorCount, 0);
    const totalRetries = relevantMetrics.reduce((sum, m) => sum + m.retryCount, 0);

    // Analyze recommendations
    const recommendationCounts = new Map<string, number>();
    for (const metrics of relevantMetrics) {
      const recommendations = this.analyzePerformance(metrics);
      for (const rec of recommendations) {
        recommendationCounts.set(rec.type, (recommendationCounts.get(rec.type) || 0) + 1);
      }
    }

    const mostCommonRecommendations = Array.from(recommendationCounts.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalExecutions: relevantMetrics.length,
      averageExecutionTime: totalExecutionTime / relevantMetrics.length,
      averageMemoryUsage: totalMemoryUsage / relevantMetrics.length,
      totalErrors,
      totalRetries,
      mostCommonRecommendations
    };
  }

  /**
   * Clear old metrics to prevent memory leaks
   */
  cleanup(olderThanHours: number = 24): void {
    const cutoffTime = Date.now() - olderThanHours * 60 * 60 * 1000;

    for (const [executionId, metrics] of this.metrics.entries()) {
      if (metrics.timestamp.getTime() < cutoffTime) {
        this.metrics.delete(executionId);
      }
    }

    this.logger.debug('Performance metrics cleanup completed', {
      remainingMetrics: this.metrics.size
    });
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): PerformanceMetrics['memoryUsage'] {
    const usage = process.memoryUsage();
    return {
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
      external: usage.external,
      rss: usage.rss
    };
  }

  /**
   * Get current CPU usage
   */
  private getCPUUsage(): PerformanceMetrics['cpuUsage'] {
    const usage = process.cpuUsage();
    return {
      user: usage.user,
      system: usage.system
    };
  }
}
