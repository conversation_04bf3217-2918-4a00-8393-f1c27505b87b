import { injectable, inject } from 'inversify';
import { WorkflowDefinition } from '../../core/interfaces/IExecutionCoordinator';
import type { ILogger } from '../../core/interfaces/ILogger';
import { PerformanceMetrics, OptimizationRecommendation } from './PerformanceMonitor';
import { TYPES } from '../../types';

/**
 * Optimization strategy for workflow execution
 */
export interface OptimizationStrategy {
  type: 'parallel' | 'caching' | 'batching' | 'streaming' | 'resource_allocation';
  description: string;
  applicableNodes: string[];
  estimatedImprovement: number; // Percentage
  implementationComplexity: 'low' | 'medium' | 'high';
  resourceImpact: 'positive' | 'neutral' | 'negative';
}

/**
 * Optimized workflow definition
 */
export interface OptimizedWorkflow {
  originalDefinition: WorkflowDefinition;
  optimizedDefinition: WorkflowDefinition;
  appliedStrategies: OptimizationStrategy[];
  estimatedImprovements: {
    executionTime: number; // Percentage reduction
    memoryUsage: number; // Percentage reduction
    cpuUsage: number; // Percentage reduction
  };
  warnings: string[];
}

/**
 * Workflow execution optimizer
 */
@injectable()
export class WorkflowOptimizer {
  constructor(@inject(TYPES.Logger) private logger: ILogger) {}

  /**
   * Optimize workflow definition based on performance metrics
   */
  async optimizeWorkflow(
    definition: WorkflowDefinition,
    performanceHistory: PerformanceMetrics[],
    recommendations: OptimizationRecommendation[]
  ): Promise<OptimizedWorkflow> {
    this.logger.info('Starting workflow optimization', {
      workflowId: definition.id,
      nodeCount: definition.nodes.length,
      edgeCount: definition.edges.length,
      performanceHistoryCount: performanceHistory.length
    });

    const strategies: OptimizationStrategy[] = [];
    const warnings: string[] = [];
    let optimizedDefinition = JSON.parse(JSON.stringify(definition)); // Deep clone

    // Analyze workflow structure for optimization opportunities
    const parallelOpportunities = this.identifyParallelExecutionOpportunities(definition);
    if (parallelOpportunities.length > 0) {
      strategies.push({
        type: 'parallel',
        description: 'Enable parallel execution for independent nodes',
        applicableNodes: parallelOpportunities,
        estimatedImprovement: 30,
        implementationComplexity: 'medium',
        resourceImpact: 'positive'
      });

      optimizedDefinition = this.applyParallelOptimization(
        optimizedDefinition,
        parallelOpportunities
      );
    }

    // Identify caching opportunities
    const cachingOpportunities = this.identifyCachingOpportunities(definition, performanceHistory);
    if (cachingOpportunities.length > 0) {
      strategies.push({
        type: 'caching',
        description: 'Add caching for frequently accessed data',
        applicableNodes: cachingOpportunities,
        estimatedImprovement: 25,
        implementationComplexity: 'low',
        resourceImpact: 'neutral'
      });

      optimizedDefinition = this.applyCachingOptimization(
        optimizedDefinition,
        cachingOpportunities
      );
    }

    // Identify batching opportunities
    const batchingOpportunities = this.identifyBatchingOpportunities(definition);
    if (batchingOpportunities.length > 0) {
      strategies.push({
        type: 'batching',
        description: 'Batch similar operations for efficiency',
        applicableNodes: batchingOpportunities,
        estimatedImprovement: 20,
        implementationComplexity: 'medium',
        resourceImpact: 'positive'
      });

      optimizedDefinition = this.applyBatchingOptimization(
        optimizedDefinition,
        batchingOpportunities
      );
    }

    // Identify streaming opportunities for large data processing
    const streamingOpportunities = this.identifyStreamingOpportunities(
      definition,
      performanceHistory
    );
    if (streamingOpportunities.length > 0) {
      strategies.push({
        type: 'streaming',
        description: 'Use streaming for large data processing',
        applicableNodes: streamingOpportunities,
        estimatedImprovement: 40,
        implementationComplexity: 'high',
        resourceImpact: 'positive'
      });

      optimizedDefinition = this.applyStreamingOptimization(
        optimizedDefinition,
        streamingOpportunities
      );
    }

    // Optimize resource allocation
    const resourceOptimizations = this.optimizeResourceAllocation(definition, performanceHistory);
    if (resourceOptimizations.length > 0) {
      strategies.push({
        type: 'resource_allocation',
        description: 'Optimize resource allocation for nodes',
        applicableNodes: resourceOptimizations,
        estimatedImprovement: 15,
        implementationComplexity: 'low',
        resourceImpact: 'positive'
      });

      optimizedDefinition = this.applyResourceOptimization(
        optimizedDefinition,
        resourceOptimizations
      );
    }

    // Calculate estimated improvements
    const estimatedImprovements = this.calculateEstimatedImprovements(strategies);

    // Generate warnings for potential issues
    warnings.push(...this.generateOptimizationWarnings(strategies, definition));

    this.logger.info('Workflow optimization completed', {
      workflowId: definition.id,
      strategiesApplied: strategies.length,
      estimatedExecutionTimeImprovement: estimatedImprovements.executionTime,
      warningCount: warnings.length
    });

    return {
      originalDefinition: definition,
      optimizedDefinition,
      appliedStrategies: strategies,
      estimatedImprovements,
      warnings
    };
  }

  /**
   * Identify opportunities for parallel execution
   */
  private identifyParallelExecutionOpportunities(definition: WorkflowDefinition): string[] {
    const opportunities: string[] = [];
    const nodeMap = new Map(definition.nodes.map((n) => [n.id, n]));
    const dependencyMap = new Map<string, string[]>();

    // Build dependency map
    for (const edge of definition.edges) {
      const dependencies = dependencyMap.get(edge.target) || [];
      dependencies.push(edge.source);
      dependencyMap.set(edge.target, dependencies);
    }

    // Find nodes that can be executed in parallel
    const processedNodes = new Set<string>();
    const startNodes = definition.nodes.filter((node) => !dependencyMap.has(node.id));

    let currentLevel = startNodes.map((n) => n.id);

    while (currentLevel.length > 0) {
      if (currentLevel.length > 1) {
        // Multiple nodes at the same level can be parallelized
        opportunities.push(...currentLevel);
      }

      currentLevel.forEach((nodeId) => processedNodes.add(nodeId));

      // Find next level
      const nextLevel = definition.nodes
        .filter((node) => !processedNodes.has(node.id))
        .filter((node) => {
          const deps = dependencyMap.get(node.id) || [];
          return deps.every((dep) => processedNodes.has(dep));
        })
        .map((node) => node.id);

      currentLevel = nextLevel;
    }

    return opportunities;
  }

  /**
   * Identify opportunities for caching
   */
  private identifyCachingOpportunities(
    definition: WorkflowDefinition,
    performanceHistory: PerformanceMetrics[]
  ): string[] {
    const opportunities: string[] = [];

    // Look for nodes that are frequently executed and take significant time
    const nodeExecutionStats = new Map<string, { count: number; totalTime: number }>();

    for (const metrics of performanceHistory) {
      for (const [nodeId, executionTime] of Object.entries(metrics.nodeExecutionTimes)) {
        const stats = nodeExecutionStats.get(nodeId) || { count: 0, totalTime: 0 };
        stats.count++;
        stats.totalTime += executionTime;
        nodeExecutionStats.set(nodeId, stats);
      }
    }

    for (const [nodeId, stats] of nodeExecutionStats.entries()) {
      const averageTime = stats.totalTime / stats.count;

      // Cache if node is executed frequently and takes significant time
      if (stats.count > 5 && averageTime > 1000) {
        // More than 5 executions and 1 second average
        const node = definition.nodes.find((n) => n.id === nodeId);
        if (node && this.isCacheable(node)) {
          opportunities.push(nodeId);
        }
      }
    }

    return opportunities;
  }

  /**
   * Identify opportunities for batching operations
   */
  private identifyBatchingOpportunities(definition: WorkflowDefinition): string[] {
    const opportunities: string[] = [];

    // Look for similar nodes that could be batched
    const nodesByType = new Map<string, string[]>();

    for (const node of definition.nodes) {
      const nodes = nodesByType.get(node.type) || [];
      nodes.push(node.id);
      nodesByType.set(node.type, nodes);
    }

    // Identify types with multiple instances that could be batched
    for (const [nodeType, nodeIds] of nodesByType.entries()) {
      if (nodeIds.length > 1 && this.isBatchable(nodeType)) {
        opportunities.push(...nodeIds);
      }
    }

    return opportunities;
  }

  /**
   * Identify opportunities for streaming processing
   */
  private identifyStreamingOpportunities(
    definition: WorkflowDefinition,
    performanceHistory: PerformanceMetrics[]
  ): string[] {
    const opportunities: string[] = [];

    // Look for nodes with high memory usage that could benefit from streaming
    const highMemoryNodes = new Set<string>();

    for (const metrics of performanceHistory) {
      if (metrics.memoryUsage.heapUsed > 50 * 1024 * 1024) {
        // 50MB
        // Find nodes that were executing during high memory usage
        for (const nodeId of Object.keys(metrics.nodeExecutionTimes)) {
          const node = definition.nodes.find((n) => n.id === nodeId);
          if (node && this.isStreamable(node)) {
            highMemoryNodes.add(nodeId);
          }
        }
      }
    }

    opportunities.push(...Array.from(highMemoryNodes));
    return opportunities;
  }

  /**
   * Optimize resource allocation for nodes
   */
  private optimizeResourceAllocation(
    definition: WorkflowDefinition,
    performanceHistory: PerformanceMetrics[]
  ): string[] {
    const opportunities: string[] = [];

    // Analyze resource usage patterns
    const nodeResourceUsage = new Map<
      string,
      { avgMemory: number; avgCpu: number; count: number }
    >();

    for (const metrics of performanceHistory) {
      for (const nodeId of Object.keys(metrics.nodeExecutionTimes)) {
        const usage = nodeResourceUsage.get(nodeId) || { avgMemory: 0, avgCpu: 0, count: 0 };
        usage.avgMemory =
          (usage.avgMemory * usage.count + metrics.memoryUsage.heapUsed) / (usage.count + 1);
        usage.avgCpu = (usage.avgCpu * usage.count + metrics.cpuUsage.user) / (usage.count + 1);
        usage.count++;
        nodeResourceUsage.set(nodeId, usage);
      }
    }

    // Identify nodes that could benefit from resource optimization
    for (const [nodeId, usage] of nodeResourceUsage.entries()) {
      if (usage.count > 3) {
        // Enough data points
        const node = definition.nodes.find((n) => n.id === nodeId);
        if (node && (usage.avgMemory > 10 * 1024 * 1024 || usage.avgCpu > 50000)) {
          // 10MB or high CPU
          opportunities.push(nodeId);
        }
      }
    }

    return opportunities;
  }

  /**
   * Apply parallel execution optimization
   */
  private applyParallelOptimization(
    definition: WorkflowDefinition,
    nodeIds: string[]
  ): WorkflowDefinition {
    // Add parallel execution metadata to nodes
    for (const node of definition.nodes) {
      if (nodeIds.includes(node.id)) {
        node.config = {
          ...node.config,
          parallelExecution: {
            enabled: true,
            maxConcurrency: 3,
            priority: 1
          }
        };
      }
    }

    return definition;
  }

  /**
   * Apply caching optimization
   */
  private applyCachingOptimization(
    definition: WorkflowDefinition,
    nodeIds: string[]
  ): WorkflowDefinition {
    // Add caching configuration to nodes
    for (const node of definition.nodes) {
      if (nodeIds.includes(node.id)) {
        node.config = {
          ...node.config,
          caching: {
            enabled: true,
            ttl: 3600, // 1 hour
            keyStrategy: 'input_hash',
            storage: 'redis'
          }
        };
      }
    }

    return definition;
  }

  /**
   * Apply batching optimization
   */
  private applyBatchingOptimization(
    definition: WorkflowDefinition,
    nodeIds: string[]
  ): WorkflowDefinition {
    // Group similar nodes for batching
    const nodesByType = new Map<string, string[]>();

    for (const nodeId of nodeIds) {
      const node = definition.nodes.find((n) => n.id === nodeId);
      if (node) {
        const nodes = nodesByType.get(node.type) || [];
        nodes.push(nodeId);
        nodesByType.set(node.type, nodes);
      }
    }

    // Add batching configuration
    for (const node of definition.nodes) {
      if (nodeIds.includes(node.id)) {
        node.config = {
          ...node.config,
          batching: {
            enabled: true,
            batchSize: 10,
            maxWaitTime: 1000 // 1 second
          }
        };
      }
    }

    return definition;
  }

  /**
   * Apply streaming optimization
   */
  private applyStreamingOptimization(
    definition: WorkflowDefinition,
    nodeIds: string[]
  ): WorkflowDefinition {
    // Add streaming configuration to nodes
    for (const node of definition.nodes) {
      if (nodeIds.includes(node.id)) {
        node.config = {
          ...node.config,
          streaming: {
            enabled: true,
            chunkSize: 1000,
            backpressure: true
          }
        };
      }
    }

    return definition;
  }

  /**
   * Apply resource optimization
   */
  private applyResourceOptimization(
    definition: WorkflowDefinition,
    nodeIds: string[]
  ): WorkflowDefinition {
    // Add resource allocation configuration
    for (const node of definition.nodes) {
      if (nodeIds.includes(node.id)) {
        node.config = {
          ...node.config,
          resources: {
            memoryLimit: '100MB',
            cpuLimit: '500m',
            timeout: 30000
          }
        };
      }
    }

    return definition;
  }

  /**
   * Calculate estimated improvements from applied strategies
   */
  private calculateEstimatedImprovements(strategies: OptimizationStrategy[]): {
    executionTime: number;
    memoryUsage: number;
    cpuUsage: number;
  } {
    let executionTimeImprovement = 0;
    let memoryUsageImprovement = 0;
    let cpuUsageImprovement = 0;

    for (const strategy of strategies) {
      switch (strategy.type) {
        case 'parallel':
          executionTimeImprovement += strategy.estimatedImprovement;
          break;
        case 'caching':
          executionTimeImprovement += strategy.estimatedImprovement * 0.8;
          break;
        case 'streaming':
          memoryUsageImprovement += strategy.estimatedImprovement;
          break;
        case 'resource_allocation':
          cpuUsageImprovement += strategy.estimatedImprovement;
          memoryUsageImprovement += strategy.estimatedImprovement * 0.5;
          break;
        case 'batching':
          executionTimeImprovement += strategy.estimatedImprovement * 0.6;
          cpuUsageImprovement += strategy.estimatedImprovement * 0.4;
          break;
      }
    }

    // Cap improvements at reasonable maximums
    return {
      executionTime: Math.min(executionTimeImprovement, 70),
      memoryUsage: Math.min(memoryUsageImprovement, 60),
      cpuUsage: Math.min(cpuUsageImprovement, 50)
    };
  }

  /**
   * Generate warnings for potential optimization issues
   */
  private generateOptimizationWarnings(
    strategies: OptimizationStrategy[],
    definition: WorkflowDefinition
  ): string[] {
    const warnings: string[] = [];

    const highComplexityStrategies = strategies.filter(
      (s) => s.implementationComplexity === 'high'
    );
    if (highComplexityStrategies.length > 0) {
      warnings.push(
        'Some optimizations have high implementation complexity and may require significant changes'
      );
    }

    const negativeResourceImpact = strategies.filter((s) => s.resourceImpact === 'negative');
    if (negativeResourceImpact.length > 0) {
      warnings.push(
        'Some optimizations may increase resource usage in exchange for performance gains'
      );
    }

    if (strategies.some((s) => s.type === 'parallel') && definition.nodes.length < 3) {
      warnings.push(
        'Parallel execution may not provide significant benefits for workflows with few nodes'
      );
    }

    return warnings;
  }

  /**
   * Check if a node can be cached
   */
  private isCacheable(node: any): boolean {
    // Nodes that produce deterministic output based on input can be cached
    const cacheableTypes = ['sql', 'http', 'javascript', 'data-mapping', 'aggregation'];
    return cacheableTypes.includes(node.type);
  }

  /**
   * Check if a node type can be batched
   */
  private isBatchable(nodeType: string): boolean {
    const batchableTypes = ['sql', 'http', 'vectordb', 'redis'];
    return batchableTypes.includes(nodeType);
  }

  /**
   * Check if a node can use streaming
   */
  private isStreamable(node: any): boolean {
    const streamableTypes = ['data-mapping', 'aggregation', 'javascript'];
    return streamableTypes.includes(node.type);
  }
}
