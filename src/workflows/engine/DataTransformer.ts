import { injectable, inject } from 'inversify';
import { type IDataTransformer, DataTransformation, FieldMapping, AggregationOperation, DataFilter, JSONPathConfig, SchemaMapping, TransformationResult } from '../../core/interfaces/IDataTransformer';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import { type ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * Data transformer for handling data transformations between workflow nodes
 */
@injectable()
export class DataTransformer implements IDataTransformer {
  private customTransformFunctions = new Map<string, Function>();

  constructor(
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    this.initializeBuiltInTransforms();
  }

  /**
   * Transform data using specified transformation
   */
  async transformData(
    data: any,
    transformation: DataTransformation,
    context?: WorkflowContext
  ): Promise<TransformationResult> {
    const startTime = Date.now();
    let processedRecords = 0;
    let transformedRecords = 0;
    let skippedRecords = 0;
    const errors: string[] = [];
    const warnings: string[] = [];

    this.logger.debug('Starting data transformation', {
      executionId: context?.executionId,
      transformationType: transformation.type,
      dataType: typeof data,
      isArray: Array.isArray(data)
    });

    try {
      let result: any;

      switch (transformation.type) {
        case 'mapping':
          result = await this.mapFields(data, transformation.config.mappings, context);
          processedRecords = Array.isArray(data) ? data.length : 1;
          transformedRecords = processedRecords;
          break;

        case 'aggregation':
          if (!Array.isArray(data)) {
            throw new Error('Aggregation requires array input');
          }
          result = await this.aggregateData(data, transformation.config.operations, transformation.config.groupBy);
          processedRecords = data.length;
          transformedRecords = 1; // Aggregation produces single result
          break;

        case 'filtering':
          if (!Array.isArray(data)) {
            throw new Error('Filtering requires array input');
          }
          result = await this.filterData(data, transformation.config.filters);
          processedRecords = data.length;
          transformedRecords = result.length;
          skippedRecords = processedRecords - transformedRecords;
          break;

        case 'sorting':
          result = this.sortData(data, transformation.config);
          processedRecords = Array.isArray(data) ? data.length : 1;
          transformedRecords = processedRecords;
          break;

        case 'grouping':
          if (!Array.isArray(data)) {
            throw new Error('Grouping requires array input');
          }
          result = this.groupData(data, transformation.config.groupBy);
          processedRecords = data.length;
          transformedRecords = Object.keys(result).length;
          break;

        case 'custom':
          result = await this.applyCustomTransform(data, transformation.config, context);
          processedRecords = Array.isArray(data) ? data.length : 1;
          transformedRecords = processedRecords;
          break;

        default:
          throw new Error(`Unsupported transformation type: ${transformation.type}`);
      }

      const executionTime = Date.now() - startTime;

      this.logger.debug('Data transformation completed', {
        executionId: context?.executionId,
        transformationType: transformation.type,
        processedRecords,
        transformedRecords,
        skippedRecords,
        executionTime
      });

      return {
        data: result,
        metadata: {
          recordsProcessed: processedRecords,
          recordsTransformed: transformedRecords,
          recordsSkipped: skippedRecords,
          errors,
          warnings,
          executionTime
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error('Data transformation failed', {
        executionId: context?.executionId,
        transformationType: transformation.type,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        executionTime
      });

      return {
        data: null,
        metadata: {
          recordsProcessed: processedRecords,
          recordsTransformed: 0,
          recordsSkipped: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
          warnings,
          executionTime
        }
      };
    }
  }

  /**
   * Map fields from source to target schema
   */
  async mapFields(
    data: any,
    mappings: FieldMapping[],
    context?: WorkflowContext
  ): Promise<any> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(item => this.mapSingleRecord(item, mappings, context)));
    } else {
      return this.mapSingleRecord(data, mappings, context);
    }
  }

  /**
   * Aggregate data using specified operations
   */
  async aggregateData(
    data: any[],
    operations: AggregationOperation[],
    groupBy?: string[]
  ): Promise<any> {
    if (groupBy && groupBy.length > 0) {
      // Group data first, then aggregate each group
      const grouped = await this.groupData(data, groupBy);
      const result: any = {};

      for (const [groupKey, groupData] of Object.entries(grouped)) {
        result[groupKey] = this.performAggregations(groupData, operations);
      }

      return result;
    } else {
      // Aggregate all data
      return this.performAggregations(data, operations);
    }
  }

  /**
   * Filter data based on specified criteria
   */
  async filterData(
    data: any[],
    filters: DataFilter[]
  ): Promise<any[]> {
    return data.filter(item => {
      return filters.every(filter => this.evaluateFilter(item, filter));
    });
  }

  /**
   * Extract data using JSONPath expressions
   */
  async extractWithJSONPath(
    data: any,
    config: JSONPathConfig
  ): Promise<any> {
    try {
      // Simple JSONPath implementation - in production, use a proper JSONPath library
      const result = this.evaluateJSONPath(data, config.expression);
      
      if (result === undefined && config.required) {
        throw new Error(`Required JSONPath expression '${config.expression}' returned no results`);
      }

      return result !== undefined ? result : config.defaultValue;
    } catch (error) {
      if (config.required) {
        throw error;
      }
      return config.defaultValue;
    }
  }

  /**
   * Transform data schema from source to target
   */
  async transformSchema(
    data: any,
    schemaMapping: SchemaMapping
  ): Promise<TransformationResult> {
    const startTime = Date.now();

    try {
      // Validate input against source schema if provided
      if (schemaMapping.sourceSchema) {
        const validation = await this.validateData(data, schemaMapping.sourceSchema);
        if (!validation.valid && schemaMapping.strictMode) {
          throw new Error(`Input validation failed: ${validation.errors.join(', ')}`);
        }
      }

      // Apply field mappings
      const transformedData = await this.mapFields(data, schemaMapping.mappings);

      // Validate output against target schema if provided
      if (schemaMapping.targetSchema) {
        const validation = await this.validateData(transformedData, schemaMapping.targetSchema);
        if (!validation.valid && schemaMapping.strictMode) {
          throw new Error(`Output validation failed: ${validation.errors.join(', ')}`);
        }
      }

      return {
        data: transformedData,
        metadata: {
          recordsProcessed: Array.isArray(data) ? data.length : 1,
          recordsTransformed: Array.isArray(transformedData) ? transformedData.length : 1,
          recordsSkipped: 0,
          errors: [],
          warnings: [],
          executionTime: Date.now() - startTime
        }
      };

    } catch (error) {
      return {
        data: null,
        metadata: {
          recordsProcessed: Array.isArray(data) ? data.length : 1,
          recordsTransformed: 0,
          recordsSkipped: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error occurred'],
          warnings: [],
          executionTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Validate data against schema
   */
  async validateData(
    data: any,
    schema: any
  ): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Simple validation - in production, use a proper schema validation library like Joi or Ajv
    if (schema.type) {
      const actualType = Array.isArray(data) ? 'array' : typeof data;
      if (actualType !== schema.type) {
        errors.push(`Expected type ${schema.type}, got ${actualType}`);
      }
    }

    if (schema.required && Array.isArray(schema.required)) {
      for (const field of schema.required) {
        if (data[field] === undefined || data[field] === null) {
          errors.push(`Required field '${field}' is missing`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Register custom transformation function
   */
  async registerTransformFunction(
    name: string,
    func: (data: any, config: any) => any
  ): Promise<boolean> {
    try {
      this.customTransformFunctions.set(name, func);
      this.logger.info('Registered custom transform function', { name });
      return true;
    } catch (error) {
      this.logger.error('Failed to register transform function', {
        name,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      return false;
    }
  }

  /**
   * Get available transformation types
   */
  getAvailableTransformations(): string[] {
    return [
      'mapping',
      'aggregation',
      'filtering',
      'sorting',
      'grouping',
      'custom',
      ...Array.from(this.customTransformFunctions.keys())
    ];
  }

  /**
   * Map a single record using field mappings
   */
  private async mapSingleRecord(
    record: any,
    mappings: FieldMapping[],
    context?: WorkflowContext
  ): Promise<any> {
    const result: any = {};

    for (const mapping of mappings) {
      try {
        let value = this.getNestedValue(record, mapping.source);

        // Apply transformation if specified
        if (mapping.transform && value !== undefined) {
          value = await this.applyFieldTransform(value, mapping.transform, context);
        }

        // Use default value if needed
        if (value === undefined && mapping.defaultValue !== undefined) {
          value = mapping.defaultValue;
        }

        // Validate if required
        if (mapping.validation) {
          const isValid = this.validateFieldValue(value, mapping.validation);
          if (!isValid && mapping.required) {
            throw new Error(`Validation failed for field ${mapping.target}`);
          }
        }

        // Set the value in result
        this.setNestedValue(result, mapping.target, value);

      } catch (error) {
        if (mapping.required) {
          throw error;
        }
        // Skip optional fields that fail
      }
    }

    return result;
  }

  /**
   * Perform aggregation operations on data
   */
  private performAggregations(data: any[], operations: AggregationOperation[]): any {
    const result: any = {};

    for (const operation of operations) {
      const fieldValues = data.map(item => this.getNestedValue(item, operation.field))
        .filter(value => value !== undefined && value !== null);

      let aggregatedValue: any;

      switch (operation.type) {
        case 'sum':
          aggregatedValue = fieldValues.reduce((sum, val) => sum + Number(val), 0);
          break;
        case 'count':
          aggregatedValue = fieldValues.length;
          break;
        case 'average':
          aggregatedValue = fieldValues.length > 0 
            ? fieldValues.reduce((sum, val) => sum + Number(val), 0) / fieldValues.length 
            : 0;
          break;
        case 'min':
          aggregatedValue = fieldValues.length > 0 ? Math.min(...fieldValues.map(Number)) : null;
          break;
        case 'max':
          aggregatedValue = fieldValues.length > 0 ? Math.max(...fieldValues.map(Number)) : null;
          break;
        case 'distinct':
          aggregatedValue = [...new Set(fieldValues)];
          break;
        case 'custom':
          if (operation.customFunction) {
            aggregatedValue = operation.customFunction(fieldValues);
          } else {
            throw new Error('Custom aggregation requires customFunction');
          }
          break;
        default:
          throw new Error(`Unsupported aggregation type: ${operation.type}`);
      }

      const resultKey = operation.alias || `${operation.type}_${operation.field}`;
      result[resultKey] = aggregatedValue;
    }

    return result;
  }

  /**
   * Evaluate a filter against a data item
   */
  private evaluateFilter(item: any, filter: DataFilter): boolean {
    const fieldValue = this.getNestedValue(item, filter.field);
    const compareValue = filter.value;

    switch (filter.operator) {
      case 'equals':
        return fieldValue === compareValue;
      case 'not_equals':
        return fieldValue !== compareValue;
      case 'greater_than':
        return Number(fieldValue) > Number(compareValue);
      case 'less_than':
        return Number(fieldValue) < Number(compareValue);
      case 'contains':
        const fieldStr = String(fieldValue);
        const compareStr = String(compareValue);
        return filter.caseSensitive !== false 
          ? fieldStr.includes(compareStr)
          : fieldStr.toLowerCase().includes(compareStr.toLowerCase());
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue);
      case 'not_in':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
      case 'regex':
        return new RegExp(String(compareValue)).test(String(fieldValue));
      default:
        return false;
    }
  }

  /**
   * Sort data based on configuration
   */
  private sortData(data: any, config: any): any {
    if (!Array.isArray(data)) {
      return data;
    }

    const sortField = config.field || config.sortBy;
    const sortOrder = config.order || config.direction || 'asc';

    return [...data].sort((a, b) => {
      const aVal = this.getNestedValue(a, sortField);
      const bVal = this.getNestedValue(b, sortField);

      if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });
  }

  /**
   * Group data by specified fields
   */
  private groupData(data: any[], groupBy: string[]): Record<string, any[]> {
    const groups: Record<string, any[]> = {};

    for (const item of data) {
      const groupKey = groupBy.map(field => this.getNestedValue(item, field)).join('|');
      
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      
      groups[groupKey].push(item);
    }

    return groups;
  }

  /**
   * Apply custom transformation
   */
  private async applyCustomTransform(data: any, config: any, context?: WorkflowContext): Promise<any> {
    const functionName = config.function || config.name;
    
    if (!functionName) {
      throw new Error('Custom transformation requires function name');
    }

    const transformFunction = this.customTransformFunctions.get(functionName);
    if (!transformFunction) {
      throw new Error(`Custom transform function '${functionName}' not found`);
    }

    return await transformFunction(data, config);
  }

  /**
   * Apply field-level transformation
   */
  private async applyFieldTransform(value: any, transform: string, context?: WorkflowContext): Promise<any> {
    // Simple transformations - can be extended
    switch (transform) {
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'trim':
        return String(value).trim();
      case 'number':
        return Number(value);
      case 'string':
        return String(value);
      case 'boolean':
        return Boolean(value);
      default:
        // Check if it's a custom function
        const customFunction = this.customTransformFunctions.get(transform);
        if (customFunction) {
          return await customFunction(value, {});
        }
        return value;
    }
  }

  /**
   * Validate field value against validation rule
   */
  private validateFieldValue(value: any, validation: any): boolean {
    if (validation.required && (value === undefined || value === null)) {
      return false;
    }

    if (validation.type) {
      const actualType = typeof value;
      if (actualType !== validation.type) {
        return false;
      }
    }

    if (validation.min !== undefined && Number(value) < validation.min) {
      return false;
    }

    if (validation.max !== undefined && Number(value) > validation.max) {
      return false;
    }

    if (validation.pattern && !new RegExp(validation.pattern).test(String(value))) {
      return false;
    }

    if (validation.custom && !validation.custom(value)) {
      return false;
    }

    return true;
  }

  /**
   * Get nested value using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * Set nested value using dot notation
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);

    target[lastKey] = value;
  }

  /**
   * Simple JSONPath evaluation
   */
  private evaluateJSONPath(data: any, expression: string): any {
    // Very basic JSONPath implementation - in production, use a proper library
    if (expression.startsWith('$.')) {
      const path = expression.substring(2);
      return this.getNestedValue(data, path);
    }
    return undefined;
  }

  /**
   * Initialize built-in transformation functions
   */
  private initializeBuiltInTransforms(): void {
    this.customTransformFunctions.set('formatDate', (value: any, config: any) => {
      const date = new Date(value);
      const format = config.format || 'ISO';
      
      switch (format) {
        case 'ISO':
          return date.toISOString();
        case 'date':
          return date.toDateString();
        case 'time':
          return date.toTimeString();
        default:
          return date.toString();
      }
    });

    this.customTransformFunctions.set('slugify', (value: string) => {
      return String(value)
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '');
    });

    this.customTransformFunctions.set('truncate', (value: string, config: any) => {
      const maxLength = config.length || 100;
      const suffix = config.suffix || '...';
      
      if (String(value).length <= maxLength) {
        return value;
      }
      
      return String(value).substring(0, maxLength - suffix.length) + suffix;
    });
  }
}
