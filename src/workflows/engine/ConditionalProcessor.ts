import { injectable, inject } from 'inversify';
import {
  type IConditionalProcessor,
  LogicResult,
  ConditionEvaluationContext,
  RouteSelection
} from '../../core/interfaces/IConditionalProcessor';
import { ConditionalExpression, WorkflowNode } from '../../core/interfaces/IExecutionCoordinator';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import { type ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * Conditional processor for evaluating logic expressions in workflows
 */
@injectable()
export class ConditionalProcessor implements IConditionalProcessor {
  private customFunctions = new Map<string, Function>();

  constructor(@inject(TYPES.Logger) private logger: ILogger) {
    this.initializeBuiltInFunctions();
  }

  /**
   * Evaluate a simple string expression
   * @param expression String expression to evaluate
   * @param context Evaluation context
   * @returns Boolean result
   */
  async evaluate(expression: string, context: any): Promise<boolean> {
    try {
      // Simple evaluation - for now just return true
      // In a real implementation, this would parse and evaluate the expression
      this.logger.debug(`Evaluating expression: ${expression}`);
      return true;
    } catch (error) {
      this.logger.error(`Expression evaluation failed: ${error}`);
      return false;
    }
  }

  /**
   * Evaluate a conditional expression
   */
  async evaluateCondition(
    expression: ConditionalExpression,
    context: WorkflowContext
  ): Promise<LogicResult> {
    this.logger.debug('Evaluating condition', {
      executionId: context.executionId,
      expression: expression.type,
      field: expression.field,
      operator: expression.operator
    });

    const evaluationContext = await this.createEvaluationContext(context);
    const result = await this.evaluateExpression(expression, evaluationContext);

    return {
      result,
      evaluatedConditions: { [expression.field]: result },
      executionPath: [`condition_${expression.field}_${expression.operator}`],
      metadata: {
        evaluationTime: Date.now(),
        expressionType: expression.type
      }
    };
  }

  /**
   * Route workflow execution based on condition
   */
  async routeExecution(
    condition: boolean,
    trueNodes: WorkflowNode[],
    falseNodes: WorkflowNode[]
  ): Promise<RouteSelection> {
    const selectedNodes = condition ? trueNodes : falseNodes;
    const rejectedNodes = condition ? falseNodes : trueNodes;

    this.logger.debug('Routing execution based on condition', {
      condition,
      selectedCount: selectedNodes.length,
      rejectedCount: rejectedNodes.length
    });

    return {
      selectedNodes,
      rejectedNodes,
      reason: condition ? 'Condition evaluated to true' : 'Condition evaluated to false',
      conditionResults: { main: condition }
    };
  }

  /**
   * Evaluate multiple conditions with logical operators
   */
  async evaluateMultipleConditions(
    conditions: ConditionalExpression[],
    logic: 'AND' | 'OR',
    context: WorkflowContext
  ): Promise<LogicResult> {
    this.logger.debug('Evaluating multiple conditions', {
      executionId: context.executionId,
      conditionCount: conditions.length,
      logic
    });

    const evaluationContext = await this.createEvaluationContext(context);
    const results: Record<string, boolean> = {};
    const executionPath: string[] = [];

    let finalResult = logic === 'AND' ? true : false;

    for (const condition of conditions) {
      const conditionResult = await this.evaluateExpression(condition, evaluationContext);
      const conditionKey = `${condition.field}_${condition.operator}`;

      results[conditionKey] = conditionResult;
      executionPath.push(conditionKey);

      if (logic === 'AND') {
        finalResult = finalResult && conditionResult;
        // Short-circuit evaluation for AND
        if (!conditionResult) break;
      } else {
        finalResult = finalResult || conditionResult;
        // Short-circuit evaluation for OR
        if (conditionResult) break;
      }
    }

    return {
      result: finalResult,
      evaluatedConditions: results,
      executionPath,
      metadata: {
        logic,
        shortCircuited: executionPath.length < conditions.length
      }
    };
  }

  /**
   * Create evaluation context from workflow context
   */
  async createEvaluationContext(
    workflowContext: WorkflowContext
  ): Promise<ConditionEvaluationContext> {
    return {
      data: workflowContext.input,
      variables: workflowContext.variables,
      nodeResults: workflowContext.nodeResults,
      functions: Object.fromEntries(this.customFunctions)
    };
  }

  /**
   * Validate conditional expression syntax
   */
  async validateExpression(expression: ConditionalExpression): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate required fields
    if (!expression.field) {
      errors.push('Field is required');
    }

    if (!expression.operator) {
      errors.push('Operator is required');
    }

    if (expression.value === undefined && expression.operator !== 'exists') {
      errors.push('Value is required for this operator');
    }

    // Validate operator
    const validOperators = this.getAvailableOperators();
    if (expression.operator && !validOperators.includes(expression.operator)) {
      errors.push(`Invalid operator: ${expression.operator}`);
    }

    // Validate complex expressions
    if (expression.type === 'complex') {
      if (!expression.conditions || expression.conditions.length === 0) {
        errors.push('Complex expressions must have conditions');
      }

      if (!expression.logic || !['AND', 'OR'].includes(expression.logic)) {
        errors.push('Complex expressions must have valid logic operator (AND/OR)');
      }

      // Recursively validate nested conditions
      if (expression.conditions) {
        for (const condition of expression.conditions) {
          const nestedValidation = await this.validateExpression(condition);
          errors.push(...nestedValidation.errors);
          warnings.push(...nestedValidation.warnings);
        }
      }
    }

    // Performance warnings
    if (expression.operator === 'regex' && typeof expression.value === 'string') {
      try {
        new RegExp(expression.value);
      } catch (e) {
        errors.push('Invalid regex pattern');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Register custom condition function
   */
  async registerConditionFunction(
    name: string,
    func: (value: any, ...args: any[]) => boolean
  ): Promise<boolean> {
    try {
      this.customFunctions.set(name, func);
      this.logger.info('Registered custom condition function', { name });
      return true;
    } catch (error) {
      this.logger.error('Failed to register condition function', {
        name,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      return false;
    }
  }

  /**
   * Get available condition operators
   */
  getAvailableOperators(): string[] {
    return [
      'equals',
      'not_equals',
      'greater_than',
      'less_than',
      'greater_than_or_equal',
      'less_than_or_equal',
      'contains',
      'not_contains',
      'starts_with',
      'ends_with',
      'regex',
      'in',
      'not_in',
      'exists',
      'not_exists',
      'is_null',
      'is_not_null',
      'is_empty',
      'is_not_empty'
    ];
  }

  /**
   * Optimize conditional expression for performance
   */
  async optimizeExpression(expression: ConditionalExpression): Promise<ConditionalExpression> {
    // Create a copy to avoid modifying the original
    const optimized = { ...expression };

    // Optimize complex expressions
    if (expression.type === 'complex' && expression.conditions) {
      // Sort conditions by estimated execution cost (simple heuristic)
      optimized.conditions = [...expression.conditions].sort((a, b) => {
        const costA = this.getOperatorCost(a.operator);
        const costB = this.getOperatorCost(b.operator);
        return costA - costB;
      });

      // Recursively optimize nested conditions
      optimized.conditions = await Promise.all(
        optimized.conditions.map((condition) => this.optimizeExpression(condition))
      );
    }

    return optimized;
  }

  /**
   * Evaluate a single expression
   */
  private async evaluateExpression(
    expression: ConditionalExpression,
    context: ConditionEvaluationContext
  ): Promise<boolean> {
    if (expression.type === 'complex') {
      return this.evaluateComplexExpression(expression, context);
    }

    return this.evaluateSimpleExpression(expression, context);
  }

  /**
   * Evaluate a simple expression
   */
  private async evaluateSimpleExpression(
    expression: ConditionalExpression,
    context: ConditionEvaluationContext
  ): Promise<boolean> {
    const fieldValue = this.getFieldValue(expression.field, context);
    const compareValue = expression.value;

    switch (expression.operator) {
      case 'equals':
        return fieldValue === compareValue;
      case 'not_equals':
        return fieldValue !== compareValue;
      case 'greater_than':
        return Number(fieldValue) > Number(compareValue);
      case 'less_than':
        return Number(fieldValue) < Number(compareValue);
      case 'greater_than_or_equal':
        return Number(fieldValue) >= Number(compareValue);
      case 'less_than_or_equal':
        return Number(fieldValue) <= Number(compareValue);
      case 'contains':
        return String(fieldValue).includes(String(compareValue));
      case 'not_contains':
        return !String(fieldValue).includes(String(compareValue));
      case 'starts_with':
        return String(fieldValue).startsWith(String(compareValue));
      case 'ends_with':
        return String(fieldValue).endsWith(String(compareValue));
      case 'regex':
        return new RegExp(String(compareValue)).test(String(fieldValue));
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue);
      case 'not_in':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue);
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
      case 'not_exists':
        return fieldValue === undefined || fieldValue === null;
      case 'is_null':
        return fieldValue === null;
      case 'is_not_null':
        return fieldValue !== null;
      case 'is_empty':
        return fieldValue === '' || fieldValue === null || fieldValue === undefined;
      case 'is_not_empty':
        return fieldValue !== '' && fieldValue !== null && fieldValue !== undefined;
      default:
        throw new Error(`Unsupported operator: ${expression.operator}`);
    }
  }

  /**
   * Evaluate a complex expression with nested conditions
   */
  private async evaluateComplexExpression(
    expression: ConditionalExpression,
    context: ConditionEvaluationContext
  ): Promise<boolean> {
    if (!expression.conditions || expression.conditions.length === 0) {
      return false;
    }

    const results = await Promise.all(
      expression.conditions.map((condition) => this.evaluateExpression(condition, context))
    );

    if (expression.logic === 'AND') {
      return results.every((result) => result);
    } else if (expression.logic === 'OR') {
      return results.some((result) => result);
    }

    return false;
  }

  /**
   * Get field value from context using dot notation
   */
  private getFieldValue(field: string, context: ConditionEvaluationContext): any {
    const parts = field.split('.');
    let value: any = context;

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * Get estimated execution cost for an operator
   */
  private getOperatorCost(operator: string): number {
    const costs: Record<string, number> = {
      equals: 1,
      not_equals: 1,
      exists: 1,
      not_exists: 1,
      is_null: 1,
      is_not_null: 1,
      greater_than: 2,
      less_than: 2,
      greater_than_or_equal: 2,
      less_than_or_equal: 2,
      contains: 3,
      not_contains: 3,
      starts_with: 3,
      ends_with: 3,
      in: 4,
      not_in: 4,
      regex: 10
    };

    return costs[operator] || 5;
  }

  /**
   * Initialize built-in condition functions
   */
  private initializeBuiltInFunctions(): void {
    this.customFunctions.set('isEmail', (value: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    });

    this.customFunctions.set('isUrl', (value: string) => {
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    });

    this.customFunctions.set('isNumeric', (value: any) => {
      return !isNaN(Number(value)) && isFinite(Number(value));
    });

    this.customFunctions.set('lengthBetween', (value: string, min: number, max: number) => {
      const length = String(value).length;
      return length >= min && length <= max;
    });
  }
}
