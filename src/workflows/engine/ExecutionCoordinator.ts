import { injectable, inject } from 'inversify';
import { IExecutionCoordinator, WorkflowDefinition, WorkflowResult, ExecutionBranch, ParallelExecutionResult, ConditionalExpression } from '../../core/interfaces/IExecutionCoordinator';
import { type IParallelExecutor } from '../../core/interfaces/IParallelExecutor';
import { type IConditionalProcessor } from '../../core/interfaces/IConditionalProcessor';
import { type INodeRegistry } from '../../core/interfaces/INodeRegistry';
import { type ILogger } from '../../core/interfaces/ILogger';
import { WorkflowContext } from '../../core/types/WorkflowContext';
import { TYPES } from '../../types';

/**
 * Execution coordinator for orchestrating complex workflow execution
 */
@injectable()
export class ExecutionCoordinator implements IExecutionCoordinator {
  constructor(
    @inject(TYPES.ParallelExecutor) private parallelExecutor: IParallelExecutor,
    @inject(TYPES.ConditionalProcessor) private conditionalProcessor: IConditionalProcessor,
    @inject(TYPES.NodeRegistry) private nodeRegistry: INodeRegistry,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Execute a complete workflow with advanced coordination
   */
  async executeWorkflow(
    workflow: WorkflowDefinition,
    context: WorkflowContext
  ): Promise<WorkflowResult> {
    const startTime = Date.now();
    
    try {
      this.logger.info('Starting workflow execution', {
        workflowId: workflow.id,
        executionId: context.executionId,
        version: workflow.version
      });

      // Initialize execution context
      context.executionPath = [];
      context.parallelBranches = new Map();
      context.conditionalResults = new Map();
      context.transformationCache = new Map();
      context.syncPoints = new Map();

      // Plan execution based on workflow structure
      const executionPlan = await this.planExecution(workflow.nodes, workflow.edges);
      
      // Execute workflow according to plan
      let result = context.input;
      
      for (const branch of executionPlan) {
        if (branch.nodeIds.length === 1) {
          // Sequential execution for single node
          result = await this.executeNode(branch.nodeIds[0], result, context, workflow);
        } else {
          // Parallel execution for multiple nodes
          const parallelNodes = workflow.nodes.filter(n => branch.nodeIds.includes(n.id));
          const parallelResult = await this.executeParallel(parallelNodes, context);
          
          // Merge parallel results
          result = this.mergeParallelResults(parallelResult, result);
        }
      }

      const executionTime = Date.now() - startTime;
      
      this.logger.info('Workflow execution completed', {
        workflowId: workflow.id,
        executionId: context.executionId,
        executionTime
      });

      return {
        executionId: context.executionId,
        status: 'completed',
        result,
        executionTime,
        nodeExecutions: context.nodeResults
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      this.logger.error('Workflow execution failed', {
        workflowId: workflow.id,
        executionId: context.executionId,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        executionTime
      });

      return {
        executionId: context.executionId,
        status: 'failed',
        result: null,
        error: error as Error,
        executionTime,
        nodeExecutions: context.nodeResults
      };
    }
  }

  /**
   * Execute multiple nodes in parallel
   */
  async executeParallel(
    nodes: any[],
    context: WorkflowContext
  ): Promise<ParallelExecutionResult> {
    this.logger.info('Starting parallel execution', {
      executionId: context.executionId,
      nodeCount: nodes.length
    });

    // Create execution branches for parallel nodes
    const branches: ExecutionBranch[] = nodes.map((node, index) => ({
      id: `branch_${index}`,
      nodeIds: [node.id],
      dependencies: [],
      priority: 1
    }));

    // Execute branches in parallel
    const results = await this.parallelExecutor.executeBranches(branches, context);

    return {
      results,
      totalExecutionTime: Math.max(...results.map(r => r.executionTime)),
      successCount: results.filter(r => r.status === 'completed').length,
      failureCount: results.filter(r => r.status === 'failed').length
    };
  }

  /**
   * Evaluate a conditional expression
   */
  async evaluateCondition(
    condition: ConditionalExpression,
    context: WorkflowContext
  ): Promise<boolean> {
    const result = await this.conditionalProcessor.evaluateCondition(condition, context);
    
    // Cache the result
    const conditionKey = this.generateConditionKey(condition);
    context.conditionalResults?.set(conditionKey, result.result);
    
    return result.result;
  }

  /**
   * Determine execution order based on dependencies
   */
  async planExecution(
    nodes: any[],
    edges: any[]
  ): Promise<ExecutionBranch[]> {
    const executionPlan: ExecutionBranch[] = [];
    const processedNodes = new Set<string>();
    const nodeMap = new Map(nodes.map(n => [n.id, n]));

    // Find nodes with no dependencies (start nodes)
    const startNodes = nodes.filter(node => 
      !edges.some(edge => edge.target === node.id)
    );

    // Process nodes level by level
    let currentLevel = startNodes.map(n => n.id);
    let branchId = 0;

    while (currentLevel.length > 0) {
      // Check if current level nodes can be executed in parallel
      const parallelNodes = this.identifyParallelNodes(currentLevel, edges);
      
      for (const parallelGroup of parallelNodes) {
        executionPlan.push({
          id: `branch_${branchId++}`,
          nodeIds: parallelGroup,
          dependencies: this.getDependencies(parallelGroup, edges),
          priority: 1
        });

        parallelGroup.forEach(nodeId => processedNodes.add(nodeId));
      }

      // Find next level nodes
      currentLevel = this.getNextLevelNodes(processedNodes, edges, nodes);
    }

    return executionPlan;
  }

  /**
   * Handle workflow execution errors and retries
   */
  async handleExecutionError(
    error: Error,
    context: WorkflowContext,
    retryCount: number
  ): Promise<boolean> {
    const maxRetries = context.metadata?.retryCount || 3;
    
    if (retryCount < maxRetries) {
      this.logger.warn('Retrying workflow execution', {
        executionId: context.executionId,
        retryCount,
        maxRetries,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      // Add to error history
      if (!context.errorContext) {
        context.errorContext = {
          retryAttempts: 0,
          errorHistory: []
        };
      }

      context.errorContext.retryAttempts = retryCount;
      context.errorContext.lastError = error;
      context.errorContext.errorHistory.push({
        nodeId: 'workflow',
        error,
        timestamp: new Date(),
        retryAttempt: retryCount
      });

      return true; // Retry
    }

    this.logger.error('Max retries exceeded', {
      executionId: context.executionId,
      retryCount,
      maxRetries,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    });

    return false; // Don't retry
  }

  /**
   * Execute a single node
   */
  private async executeNode(
    nodeId: string,
    input: any,
    context: WorkflowContext,
    workflow: WorkflowDefinition
  ): Promise<any> {
    const node = workflow.nodes.find(n => n.id === nodeId);
    if (!node) {
      throw new Error(`Node ${nodeId} not found in workflow`);
    }

    const nodeImpl = this.nodeRegistry.getNode(node.type, node.config);
    const result = await nodeImpl.execute(input, context);
    
    context.nodeResults[nodeId] = result;
    context.executionPath?.push(nodeId);
    
    return result;
  }

  /**
   * Merge results from parallel execution
   */
  private mergeParallelResults(parallelResult: ParallelExecutionResult, currentResult: any): any {
    // Simple merge strategy - can be enhanced based on requirements
    const mergedData = { ...currentResult };
    
    for (const branchResult of parallelResult.results) {
      if (branchResult.status === 'completed') {
        Object.assign(mergedData, branchResult.nodeResults);
      }
    }
    
    return mergedData;
  }

  /**
   * Generate a unique key for caching condition results
   */
  private generateConditionKey(condition: ConditionalExpression): string {
    return `${condition.field}_${condition.operator}_${JSON.stringify(condition.value)}`;
  }

  /**
   * Identify nodes that can be executed in parallel
   */
  private identifyParallelNodes(nodeIds: string[], edges: any[]): string[][] {
    // For now, treat all nodes at the same level as parallel
    // This can be enhanced with more sophisticated dependency analysis
    return [nodeIds];
  }

  /**
   * Get dependencies for a group of nodes
   */
  private getDependencies(nodeIds: string[], edges: any[]): string[] {
    const dependencies = new Set<string>();
    
    for (const nodeId of nodeIds) {
      const incomingEdges = edges.filter(edge => edge.target === nodeId);
      incomingEdges.forEach(edge => dependencies.add(edge.source));
    }
    
    return Array.from(dependencies);
  }

  /**
   * Get nodes for the next execution level
   */
  private getNextLevelNodes(processedNodes: Set<string>, edges: any[], allNodes: any[]): string[] {
    return allNodes
      .filter(node => !processedNodes.has(node.id))
      .filter(node => {
        const dependencies = edges
          .filter(edge => edge.target === node.id)
          .map(edge => edge.source);
        
        return dependencies.every(dep => processedNodes.has(dep));
      })
      .map(node => node.id);
  }
}
