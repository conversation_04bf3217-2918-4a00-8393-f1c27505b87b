import { injectable, inject } from 'inversify';
import {
  type IParallelExecutor,
  ParallelExecutionConfig,
  ResourceAllocation,
  SynchronizationPoint
} from '../../core/interfaces/IParallelExecutor';
import {
  ExecutionBranch,
  BranchResult,
  MergedR<PERSON>ult
} from '../../core/interfaces/IExecutionCoordinator';
import { type INodeRegistry } from '../../core/interfaces/INodeRegistry';
import { type ILogger } from '../../core/interfaces/ILogger';
import { WorkflowContext, BranchExecutionContext } from '../../core/types/WorkflowContext';
import { TYPES } from '../../types';

/**
 * Parallel executor for concurrent workflow branch execution
 */
@injectable()
export class ParallelExecutor implements IParallelExecutor {
  private activeBranches = new Map<string, BranchExecutionContext>();
  private executionPromises = new Map<string, Promise<BranchResult>>();

  constructor(
    @inject(TYPES.NodeRegistry) private nodeRegistry: INodeRegistry,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Execute multiple branches concurrently
   */
  async executeBranches(
    branches: ExecutionBranch[],
    context: WorkflowContext,
    config?: ParallelExecutionConfig
  ): Promise<BranchResult[]> {
    const defaultConfig: ParallelExecutionConfig = {
      maxConcurrency: 5,
      timeout: 30000,
      failFast: false,
      retryFailedBranches: true,
      maxRetries: 3
    };

    const execConfig = { ...defaultConfig, ...config };

    this.logger.info('Starting parallel branch execution', {
      executionId: context.executionId,
      branchCount: branches.length,
      maxConcurrency: execConfig.maxConcurrency
    });

    // Allocate resources for branches
    const resourceAllocations = await this.allocateResources(branches, {
      branchId: 'total',
      memoryLimit: 1024 * 1024 * 100, // 100MB
      cpuLimit: 100,
      timeoutMs: execConfig.timeout,
      priority: 1
    });

    // Create execution promises with concurrency control
    const semaphore = new Semaphore(execConfig.maxConcurrency);
    const executionPromises = branches.map(async (branch, index) => {
      await semaphore.acquire();

      try {
        const allocation = resourceAllocations[index];
        return await this.executeBranch(branch, context, allocation);
      } finally {
        semaphore.release();
      }
    });

    // Wait for all branches to complete or timeout
    const results = await Promise.allSettled(executionPromises);

    // Process results
    const branchResults: BranchResult[] = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          branchId: branches[index].id,
          nodeResults: {},
          status: 'failed',
          error: result.reason,
          executionTime: 0
        };
      }
    });

    // Handle failed branches with retry if configured
    if (execConfig.retryFailedBranches) {
      const failedBranches = branchResults
        .map((result, index) => ({ result, branch: branches[index], index }))
        .filter(({ result }) => result.status === 'failed');

      for (const { result, branch, index } of failedBranches) {
        if (
          await this.handleBranchFailure(
            branch.id,
            result.error!,
            this.activeBranches.get(branch.id)!,
            0
          )
        ) {
          // Retry the branch
          try {
            const retryResult = await this.executeBranch(
              branch,
              context,
              resourceAllocations[index]
            );
            branchResults[index] = retryResult;
          } catch (error) {
            this.logger.error('Branch retry failed', {
              branchId: branch.id,
              error: error instanceof Error ? error.message : 'Unknown error occurred'
            });
          }
        }
      }
    }

    this.logger.info('Parallel branch execution completed', {
      executionId: context.executionId,
      successCount: branchResults.filter((r) => r.status === 'completed').length,
      failureCount: branchResults.filter((r) => r.status === 'failed').length
    });

    return branchResults;
  }

  /**
   * Synchronize multiple branches at a merge point
   */
  async synchronizeBranches(
    branchResults: BranchResult[],
    syncPoint: SynchronizationPoint
  ): Promise<MergedResult> {
    this.logger.info('Synchronizing branches', {
      syncPointId: syncPoint.id,
      branchCount: branchResults.length
    });

    // Check if all required branches are completed
    const completedBranches = branchResults
      .filter((r) => r.status === 'completed')
      .map((r) => r.branchId);

    const missingBranches = syncPoint.requiredBranches.filter(
      (branchId) => !completedBranches.includes(branchId)
    );

    if (missingBranches.length > 0) {
      throw new Error(
        `Missing required branches for synchronization: ${missingBranches.join(', ')}`
      );
    }

    // Merge data from completed branches
    const combinedData = this.mergeData(branchResults, 'combine');

    return {
      combinedData,
      branchResults,
      mergeStrategy: 'combine'
    };
  }

  /**
   * Allocate resources for parallel execution
   */
  async allocateResources(
    branches: ExecutionBranch[],
    totalResources: ResourceAllocation
  ): Promise<ResourceAllocation[]> {
    const branchCount = branches.length;
    const memoryPerBranch = Math.floor(totalResources.memoryLimit / branchCount);
    const cpuPerBranch = Math.floor(totalResources.cpuLimit / branchCount);

    return branches.map((branch) => ({
      branchId: branch.id,
      memoryLimit: memoryPerBranch,
      cpuLimit: cpuPerBranch,
      timeoutMs: totalResources.timeoutMs,
      priority: branch.priority
    }));
  }

  /**
   * Monitor parallel execution progress
   */
  async getExecutionStatus(executionId: string): Promise<{
    activeBranches: number;
    completedBranches: number;
    failedBranches: number;
    totalBranches: number;
    estimatedCompletion: Date;
  }> {
    const branches = Array.from(this.activeBranches.values()).filter(
      (branch) => branch.parentExecutionId === executionId
    );

    const activeBranches = branches.filter((b) => b.status === 'running').length;
    const completedBranches = branches.filter((b) => b.status === 'completed').length;
    const failedBranches = branches.filter((b) => b.status === 'failed').length;
    const totalBranches = branches.length;

    // Simple estimation based on average completion time
    const completedWithTime = branches.filter((b) => b.endTime && b.status === 'completed');
    const avgExecutionTime =
      completedWithTime.length > 0
        ? completedWithTime.reduce(
            (sum, b) => sum + (b.endTime!.getTime() - b.startTime.getTime()),
            0
          ) / completedWithTime.length
        : 30000; // Default 30 seconds

    const estimatedCompletion = new Date(Date.now() + avgExecutionTime * activeBranches);

    return {
      activeBranches,
      completedBranches,
      failedBranches,
      totalBranches,
      estimatedCompletion
    };
  }

  /**
   * Cancel parallel execution
   */
  async cancelExecution(
    executionId: string,
    reason: string
  ): Promise<{
    cancelled: boolean;
    affectedBranches: string[];
    cleanupRequired: boolean;
  }> {
    const affectedBranches: string[] = [];

    for (const [branchId, branch] of this.activeBranches.entries()) {
      if (branch.parentExecutionId === executionId && branch.status === 'running') {
        branch.status = 'cancelled';
        branch.endTime = new Date();
        affectedBranches.push(branchId);
      }
    }

    this.logger.info('Cancelled parallel execution', {
      executionId,
      reason,
      affectedBranches: affectedBranches.length
    });

    return {
      cancelled: true,
      affectedBranches,
      cleanupRequired: affectedBranches.length > 0
    };
  }

  /**
   * Handle branch execution failure
   */
  async handleBranchFailure(
    branchId: string,
    error: Error,
    context: BranchExecutionContext,
    retryCount: number
  ): Promise<boolean> {
    const maxRetries = 3;

    if (retryCount < maxRetries) {
      this.logger.warn('Retrying failed branch', {
        branchId,
        retryCount,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      return true;
    }

    this.logger.error('Branch failed after max retries', {
      branchId,
      retryCount,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    });

    return false;
  }

  /**
   * Cleanup resources after parallel execution
   */
  async cleanupResources(
    executionId: string,
    branchIds: string[]
  ): Promise<{
    cleaned: boolean;
    errors: Error[];
  }> {
    const errors: Error[] = [];

    for (const branchId of branchIds) {
      try {
        this.activeBranches.delete(branchId);
        this.executionPromises.delete(branchId);
      } catch (error) {
        errors.push(error as Error);
      }
    }

    this.logger.info('Cleaned up parallel execution resources', {
      executionId,
      cleanedBranches: branchIds.length,
      errors: errors.length
    });

    return {
      cleaned: errors.length === 0,
      errors
    };
  }

  /**
   * Execute a single branch
   */
  private async executeBranch(
    branch: ExecutionBranch,
    context: WorkflowContext,
    allocation: ResourceAllocation
  ): Promise<BranchResult> {
    const startTime = Date.now();
    const branchContext: BranchExecutionContext = {
      branchId: branch.id,
      parentExecutionId: context.executionId,
      isolatedMemory: {},
      startTime: new Date(),
      status: 'running'
    };

    this.activeBranches.set(branch.id, branchContext);

    try {
      const nodeResults: Record<string, any> = {};

      // Execute nodes in the branch sequentially
      for (const nodeId of branch.nodeIds) {
        // This would need access to workflow definition to get node details
        // For now, we'll simulate node execution
        const result = await this.executeNodeInBranch(nodeId, context, branchContext);
        nodeResults[nodeId] = result;
      }

      branchContext.status = 'completed';
      branchContext.endTime = new Date();

      return {
        branchId: branch.id,
        nodeResults,
        status: 'completed',
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      branchContext.status = 'failed';
      branchContext.endTime = new Date();
      branchContext.error = error as Error;

      return {
        branchId: branch.id,
        nodeResults: {},
        status: 'failed',
        error: error as Error,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Execute a node within a branch context
   */
  private async executeNodeInBranch(
    nodeId: string,
    context: WorkflowContext,
    branchContext: BranchExecutionContext
  ): Promise<any> {
    // This is a simplified implementation
    // In a real implementation, we would need access to the workflow definition
    // and proper node execution logic

    this.logger.debug('Executing node in branch', {
      nodeId,
      branchId: branchContext.branchId
    });

    // Simulate node execution
    await new Promise((resolve) => setTimeout(resolve, Math.random() * 1000));

    return { nodeId, executed: true, timestamp: new Date() };
  }

  /**
   * Merge data from multiple branches
   */
  private mergeData(branchResults: BranchResult[], strategy: string): any {
    const mergedData: any = {};

    for (const result of branchResults) {
      if (result.status === 'completed') {
        Object.assign(mergedData, result.nodeResults);
      }
    }

    return mergedData;
  }
}

/**
 * Simple semaphore implementation for concurrency control
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>((resolve) => {
      this.waitQueue.push(resolve);
    });
  }

  release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!;
      this.permits--;
      resolve();
    }
  }
}
