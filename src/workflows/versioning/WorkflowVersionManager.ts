import { injectable, inject } from 'inversify';
import {
  IWorkflowVersionManager,
  WorkflowVersion,
  VersionComparison,
  VersionDeployment,
  RollbackConfig
} from '../../core/interfaces/IWorkflowVersionManager';
import { WorkflowDefinition } from '../../core/interfaces/IExecutionCoordinator';
import type { IRepository } from '../../core/interfaces/IRepository';
import type { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

/**
 * Workflow version manager for handling workflow versioning and history
 */
@injectable()
export class WorkflowVersionManager implements IWorkflowVersionManager {
  constructor(
    @inject(TYPES.WorkflowRepository) private workflowRepository: IRepository<any>,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Create a new version of a workflow
   */
  async createVersion(
    workflowId: string,
    definition: WorkflowDefinition,
    createdBy: string,
    changeDescription?: string
  ): Promise<WorkflowVersion> {
    this.logger.info('Creating new workflow version', {
      workflowId,
      createdBy,
      changeDescription
    });

    try {
      // Get current active version to determine next version number
      const currentVersion = await this.getActiveVersion(workflowId);
      const nextVersionNumber = currentVersion ? currentVersion.version + 1 : 1;

      // Validate the workflow definition
      const validation = await this.validateDefinition(definition);
      if (!validation.valid) {
        throw new Error(`Workflow validation failed: ${validation.errors.join(', ')}`);
      }

      // Create version record
      const versionId = this.generateVersionId();
      const now = new Date();

      const workflowVersion: WorkflowVersion = {
        id: versionId,
        workflowId,
        version: nextVersionNumber,
        definition,
        isActive: false, // New versions start as inactive
        createdAt: now,
        createdBy,
        changeType: currentVersion ? 'update' : 'create',
        changeDescription,
        metadata: {
          nodeCount: definition.nodes.length,
          edgeCount: definition.edges.length,
          createdFrom: currentVersion?.version
        }
      };

      // Store in database (this would be actual database operations)
      await this.storeVersion(workflowVersion);

      // Log the version history
      await this.logVersionChange(workflowVersion, 'create');

      this.logger.info('Workflow version created successfully', {
        workflowId,
        version: nextVersionNumber,
        versionId
      });

      return workflowVersion;
    } catch (error) {
      this.logger.error('Failed to create workflow version', {
        workflowId,
        createdBy,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Activate a specific version of a workflow
   */
  async activateVersion(
    workflowId: string,
    version: number,
    deployment?: VersionDeployment
  ): Promise<{
    success: boolean;
    activeVersion: WorkflowVersion;
    previousVersion?: WorkflowVersion;
  }> {
    this.logger.info('Activating workflow version', {
      workflowId,
      version,
      deploymentStrategy: deployment?.deploymentStrategy
    });

    try {
      // Get the version to activate
      const versionToActivate = await this.getVersion(workflowId, version);
      if (!versionToActivate) {
        throw new Error(`Version ${version} not found for workflow ${workflowId}`);
      }

      // Get current active version
      const currentActiveVersion = await this.getActiveVersion(workflowId);

      // Deactivate current version if exists
      if (currentActiveVersion) {
        await this.deactivateVersion(
          workflowId,
          currentActiveVersion.version,
          'Replaced by newer version'
        );
      }

      // Activate the new version
      versionToActivate.isActive = true;
      versionToActivate.changeType = 'activate';
      await this.updateVersion(versionToActivate);

      // Log the activation
      await this.logVersionChange(versionToActivate, 'activate');

      // Handle deployment strategy if specified
      if (deployment) {
        await this.handleDeployment(versionToActivate, deployment);
      }

      this.logger.info('Workflow version activated successfully', {
        workflowId,
        version,
        previousVersion: currentActiveVersion?.version
      });

      return {
        success: true,
        activeVersion: versionToActivate,
        previousVersion: currentActiveVersion || undefined
      };
    } catch (error) {
      this.logger.error('Failed to activate workflow version', {
        workflowId,
        version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      return {
        success: false,
        activeVersion: null as any,
        previousVersion: undefined
      };
    }
  }

  /**
   * Deactivate a specific version of a workflow
   */
  async deactivateVersion(
    workflowId: string,
    version: number,
    reason: string
  ): Promise<{
    success: boolean;
    deactivatedVersion: WorkflowVersion;
  }> {
    this.logger.info('Deactivating workflow version', {
      workflowId,
      version,
      reason
    });

    try {
      const versionToDeactivate = await this.getVersion(workflowId, version);
      if (!versionToDeactivate) {
        throw new Error(`Version ${version} not found for workflow ${workflowId}`);
      }

      if (!versionToDeactivate.isActive) {
        throw new Error(`Version ${version} is already inactive`);
      }

      // Deactivate the version
      versionToDeactivate.isActive = false;
      versionToDeactivate.changeType = 'deactivate';
      versionToDeactivate.changeDescription = reason;
      await this.updateVersion(versionToDeactivate);

      // Log the deactivation
      await this.logVersionChange(versionToDeactivate, 'deactivate');

      this.logger.info('Workflow version deactivated successfully', {
        workflowId,
        version
      });

      return {
        success: true,
        deactivatedVersion: versionToDeactivate
      };
    } catch (error) {
      this.logger.error('Failed to deactivate workflow version', {
        workflowId,
        version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      return {
        success: false,
        deactivatedVersion: null as any
      };
    }
  }

  /**
   * Get version history for a workflow
   */
  async getVersionHistory(
    workflowId: string,
    limit?: number,
    offset?: number
  ): Promise<WorkflowVersion[]> {
    this.logger.debug('Getting version history', {
      workflowId,
      limit,
      offset
    });

    try {
      // This would be actual database query
      const versions = await this.queryVersionHistory(workflowId, limit, offset);

      // Sort by version number descending
      return versions.sort((a, b) => b.version - a.version);
    } catch (error) {
      this.logger.error('Failed to get version history', {
        workflowId,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      return [];
    }
  }

  /**
   * Get the currently active version of a workflow
   */
  async getActiveVersion(workflowId: string): Promise<WorkflowVersion | null> {
    try {
      const versions = await this.queryVersionHistory(workflowId);
      return versions.find((v) => v.isActive) || null;
    } catch (error) {
      this.logger.error('Failed to get active version', {
        workflowId,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      return null;
    }
  }

  /**
   * Get a specific version of a workflow
   */
  async getVersion(workflowId: string, version: number): Promise<WorkflowVersion | null> {
    try {
      const versions = await this.queryVersionHistory(workflowId);
      return versions.find((v) => v.version === version) || null;
    } catch (error) {
      this.logger.error('Failed to get workflow version', {
        workflowId,
        version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      return null;
    }
  }

  /**
   * Rollback to a previous version
   */
  async rollbackToVersion(
    workflowId: string,
    config: RollbackConfig
  ): Promise<{
    success: boolean;
    rolledBackTo: WorkflowVersion;
    previousVersion: WorkflowVersion;
  }> {
    this.logger.info('Rolling back workflow version', {
      workflowId,
      targetVersion: config.targetVersion,
      reason: config.reason
    });

    try {
      const currentVersion = await this.getActiveVersion(workflowId);
      if (!currentVersion) {
        throw new Error('No active version found to rollback from');
      }

      const targetVersion = await this.getVersion(workflowId, config.targetVersion);
      if (!targetVersion) {
        throw new Error(`Target version ${config.targetVersion} not found`);
      }

      // Deactivate current version
      await this.deactivateVersion(
        workflowId,
        currentVersion.version,
        `Rollback: ${config.reason}`
      );

      // Activate target version
      const activationResult = await this.activateVersion(workflowId, config.targetVersion);

      if (!activationResult.success) {
        throw new Error('Failed to activate target version during rollback');
      }

      // Log the rollback
      await this.logVersionChange(targetVersion, 'rollback');

      this.logger.info('Workflow rollback completed successfully', {
        workflowId,
        fromVersion: currentVersion.version,
        toVersion: config.targetVersion
      });

      return {
        success: true,
        rolledBackTo: activationResult.activeVersion,
        previousVersion: currentVersion
      };
    } catch (error) {
      this.logger.error('Failed to rollback workflow version', {
        workflowId,
        targetVersion: config.targetVersion,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      return {
        success: false,
        rolledBackTo: null as any,
        previousVersion: null as any
      };
    }
  }

  /**
   * Compare two versions of a workflow
   */
  async compareVersions(
    workflowId: string,
    fromVersion: number,
    toVersion: number
  ): Promise<VersionComparison> {
    this.logger.debug('Comparing workflow versions', {
      workflowId,
      fromVersion,
      toVersion
    });

    try {
      const fromVersionData = await this.getVersion(workflowId, fromVersion);
      const toVersionData = await this.getVersion(workflowId, toVersion);

      if (!fromVersionData || !toVersionData) {
        throw new Error('One or both versions not found');
      }

      const changes = this.calculateChanges(fromVersionData.definition, toVersionData.definition);

      return {
        fromVersion,
        toVersion,
        changes,
        summary: {
          nodesAdded: changes.filter((c) => c.type === 'node_added').length,
          nodesRemoved: changes.filter((c) => c.type === 'node_removed').length,
          nodesModified: changes.filter((c) => c.type === 'node_modified').length,
          edgesAdded: changes.filter((c) => c.type === 'edge_added').length,
          edgesRemoved: changes.filter((c) => c.type === 'edge_removed').length,
          configChanges: changes.filter((c) => c.type === 'config_changed').length
        }
      };
    } catch (error) {
      this.logger.error('Failed to compare workflow versions', {
        workflowId,
        fromVersion,
        toVersion,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Delete a specific version (if not active)
   */
  async deleteVersion(
    workflowId: string,
    version: number,
    force?: boolean
  ): Promise<{
    success: boolean;
    deletedVersion: WorkflowVersion;
  }> {
    this.logger.info('Deleting workflow version', {
      workflowId,
      version,
      force
    });

    try {
      const versionToDelete = await this.getVersion(workflowId, version);
      if (!versionToDelete) {
        throw new Error(`Version ${version} not found`);
      }

      if (versionToDelete.isActive && !force) {
        throw new Error('Cannot delete active version without force flag');
      }

      // Delete the version
      await this.removeVersion(versionToDelete);

      // Log the deletion
      await this.logVersionChange(versionToDelete, 'delete');

      this.logger.info('Workflow version deleted successfully', {
        workflowId,
        version
      });

      return {
        success: true,
        deletedVersion: versionToDelete
      };
    } catch (error) {
      this.logger.error('Failed to delete workflow version', {
        workflowId,
        version,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });

      return {
        success: false,
        deletedVersion: null as any
      };
    }
  }

  /**
   * Clone a version to create a new workflow
   */
  async cloneVersion(
    sourceWorkflowId: string,
    sourceVersion: number,
    newWorkflowName: string,
    createdBy: string
  ): Promise<WorkflowVersion> {
    this.logger.info('Cloning workflow version', {
      sourceWorkflowId,
      sourceVersion,
      newWorkflowName,
      createdBy
    });

    try {
      const sourceVersionData = await this.getVersion(sourceWorkflowId, sourceVersion);
      if (!sourceVersionData) {
        throw new Error(`Source version ${sourceVersion} not found`);
      }

      // Create new workflow definition
      const newWorkflowId = this.generateWorkflowId();
      const clonedDefinition: WorkflowDefinition = {
        ...sourceVersionData.definition,
        id: newWorkflowId,
        name: newWorkflowName
      };

      // Create the cloned version
      const clonedVersion = await this.createVersion(
        newWorkflowId,
        clonedDefinition,
        createdBy,
        `Cloned from ${sourceWorkflowId} v${sourceVersion}`
      );

      this.logger.info('Workflow version cloned successfully', {
        sourceWorkflowId,
        sourceVersion,
        newWorkflowId,
        newVersion: clonedVersion.version
      });

      return clonedVersion;
    } catch (error) {
      this.logger.error('Failed to clone workflow version', {
        sourceWorkflowId,
        sourceVersion,
        newWorkflowName,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      throw error;
    }
  }

  /**
   * Validate a workflow definition before creating version
   */
  async validateDefinition(definition: WorkflowDefinition): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (!definition.id) {
      errors.push('Workflow ID is required');
    }

    if (!definition.name) {
      errors.push('Workflow name is required');
    }

    if (!definition.nodes || definition.nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }

    // Validate nodes
    if (definition.nodes) {
      const nodeIds = new Set<string>();

      for (const node of definition.nodes) {
        if (!node.id) {
          errors.push('All nodes must have an ID');
        } else if (nodeIds.has(node.id)) {
          errors.push(`Duplicate node ID: ${node.id}`);
        } else {
          nodeIds.add(node.id);
        }

        if (!node.type) {
          errors.push(`Node ${node.id} must have a type`);
        }
      }

      // Validate edges
      if (definition.edges) {
        for (const edge of definition.edges) {
          if (!nodeIds.has(edge.source)) {
            errors.push(`Edge references non-existent source node: ${edge.source}`);
          }
          if (!nodeIds.has(edge.target)) {
            errors.push(`Edge references non-existent target node: ${edge.target}`);
          }
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Private helper methods

  private generateVersionId(): string {
    return `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateWorkflowId(): string {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async storeVersion(version: WorkflowVersion): Promise<void> {
    // This would be actual database storage
    this.logger.debug('Storing workflow version', { versionId: version.id });
  }

  private async updateVersion(version: WorkflowVersion): Promise<void> {
    // This would be actual database update
    this.logger.debug('Updating workflow version', { versionId: version.id });
  }

  private async removeVersion(version: WorkflowVersion): Promise<void> {
    // This would be actual database deletion
    this.logger.debug('Removing workflow version', { versionId: version.id });
  }

  private async queryVersionHistory(
    workflowId: string,
    limit?: number,
    offset?: number
  ): Promise<WorkflowVersion[]> {
    // This would be actual database query
    // For now, return empty array
    return [];
  }

  private async logVersionChange(version: WorkflowVersion, changeType: string): Promise<void> {
    this.logger.info('Workflow version change logged', {
      workflowId: version.workflowId,
      version: version.version,
      changeType,
      createdBy: version.createdBy
    });
  }

  private async handleDeployment(
    version: WorkflowVersion,
    deployment: VersionDeployment
  ): Promise<void> {
    this.logger.info('Handling version deployment', {
      versionId: version.id,
      strategy: deployment.deploymentStrategy
    });

    // Implementation would depend on deployment strategy
    switch (deployment.deploymentStrategy) {
      case 'immediate':
        // Already activated
        break;
      case 'gradual':
        // Implement gradual rollout
        break;
      case 'scheduled':
        // Schedule activation
        break;
    }
  }

  private calculateChanges(fromDef: WorkflowDefinition, toDef: WorkflowDefinition): any[] {
    const changes: any[] = [];

    // Compare nodes
    const fromNodes = new Map(fromDef.nodes.map((n) => [n.id, n]));
    const toNodes = new Map(toDef.nodes.map((n) => [n.id, n]));

    // Find added nodes
    for (const [nodeId, node] of toNodes) {
      if (!fromNodes.has(nodeId)) {
        changes.push({
          type: 'node_added',
          path: `nodes.${nodeId}`,
          newValue: node,
          description: `Added node: ${node.name || nodeId}`
        });
      }
    }

    // Find removed nodes
    for (const [nodeId, node] of fromNodes) {
      if (!toNodes.has(nodeId)) {
        changes.push({
          type: 'node_removed',
          path: `nodes.${nodeId}`,
          oldValue: node,
          description: `Removed node: ${node.name || nodeId}`
        });
      }
    }

    // Find modified nodes
    for (const [nodeId, fromNode] of fromNodes) {
      const toNode = toNodes.get(nodeId);
      if (toNode && JSON.stringify(fromNode) !== JSON.stringify(toNode)) {
        changes.push({
          type: 'node_modified',
          path: `nodes.${nodeId}`,
          oldValue: fromNode,
          newValue: toNode,
          description: `Modified node: ${fromNode.name || nodeId}`
        });
      }
    }

    // Similar logic for edges...

    return changes;
  }
}
