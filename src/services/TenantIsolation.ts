import { injectable, inject } from 'inversify';
import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import * as crypto from 'crypto';
import { TYPES } from '../types';
import { ITenantIsolation } from '../core/interfaces/ITenantIsolation';
import type { ITenantManager } from '../core/interfaces/ITenantManager';
import type { ILogger } from '../core/interfaces/ILogger';
import { TenantSettings } from '../infrastructure/database/entities/Tenant.entity';

/**
 * Tenant isolation implementation
 * Handles data filtering, encryption, and access control for multi-tenant isolation
 */
@injectable()
export class TenantIsolation implements ITenantIsolation {
  private readonly configCache = new Map<string, any>();
  private readonly auditLog: Array<{
    tenantId: string;
    event: string;
    details: any;
    timestamp: Date;
  }> = [];

  constructor(
    @inject(TYPES.TenantManager) private tenantManager: ITenantManager,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Filter TypeORM query builder with tenant isolation
   */
  filterQuery<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    tenantId: string
  ): SelectQueryBuilder<T> {
    try {
      const alias = queryBuilder.alias;

      // Add tenant filter to WHERE clause
      queryBuilder.andWhere(`${alias}.tenant_id = :tenantId OR ${alias}.tenant_id IS NULL`, {
        tenantId
      });

      this.logger.debug('Applied tenant filter to query', { tenantId, alias });
      return queryBuilder;
    } catch (error) {
      this.logger.error('Error applying tenant filter to query', { tenantId, error });
      throw error;
    }
  }

  /**
   * Validate access to resource for tenant
   */
  async validateAccess(resource: string, action: string, tenantId: string): Promise<boolean> {
    try {
      const tenant = await this.tenantManager.getTenant(tenantId);
      if (!tenant || !tenant.enabled) {
        return false;
      }

      const settings = tenant.settings;

      // Check feature flags
      switch (resource) {
        case 'custom_functions':
          return settings.enable_custom_functions;
        case 'external_apis':
          return settings.enable_external_apis;
        default:
          return true; // Allow by default for other resources
      }
    } catch (error) {
      this.logger.error('Error validating access', { resource, action, tenantId, error });
      return false;
    }
  }

  /**
   * Get Redis namespace for tenant
   */
  getRedisNamespace(tenantId: string): string {
    return `tenant:${tenantId}`;
  }

  /**
   * Encrypt sensitive data for tenant
   */
  async encryptSensitiveData(data: any, tenantId: string): Promise<any> {
    try {
      const tenant = await this.tenantManager.getTenant(tenantId);
      if (!tenant) {
        throw new Error(`Tenant ${tenantId} not found`);
      }

      // Use tenant-specific encryption key or default
      const encryptionKey =
        tenant.encryptionKeyId || process.env.DEFAULT_ENCRYPTION_KEY || 'default-key';

      // Convert data to string if it's an object
      const dataString = typeof data === 'string' ? data : JSON.stringify(data);

      // Create cipher
      const algorithm = 'aes-256-gcm';
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(algorithm, key, iv);

      // Encrypt data
      let encrypted = cipher.update(dataString, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      return {
        encrypted,
        iv: iv.toString('hex'),
        algorithm
      };
    } catch (error) {
      this.logger.error('Error encrypting sensitive data', { tenantId, error });
      throw error;
    }
  }

  /**
   * Decrypt sensitive data for tenant
   */
  async decryptSensitiveData(encryptedData: any, tenantId: string): Promise<any> {
    try {
      const tenant = await this.tenantManager.getTenant(tenantId);
      if (!tenant) {
        throw new Error(`Tenant ${tenantId} not found`);
      }

      // Use tenant-specific encryption key or default
      const encryptionKey =
        tenant.encryptionKeyId || process.env.DEFAULT_ENCRYPTION_KEY || 'default-key';

      // Create decipher
      const algorithm = encryptedData.algorithm || 'aes-256-gcm';
      const key = crypto.scryptSync(encryptionKey, 'salt', 32);
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const decipher = crypto.createDecipheriv(algorithm, key, iv);

      // Decrypt data
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Try to parse as JSON, return as string if it fails
      try {
        return JSON.parse(decrypted);
      } catch {
        return decrypted;
      }
    } catch (error) {
      this.logger.error('Error decrypting sensitive data', { tenantId, error });
      throw error;
    }
  }

  /**
   * Apply tenant filter to entity data
   */
  applyTenantFilter(entityName: string, data: any, tenantId: string): any {
    try {
      // Add tenant_id to data if it doesn't exist
      if (data && typeof data === 'object' && !data.tenant_id && !data.tenantId) {
        data.tenant_id = tenantId;
      }

      this.logger.debug('Applied tenant filter to entity data', { entityName, tenantId });
      return data;
    } catch (error) {
      this.logger.error('Error applying tenant filter to entity data', {
        entityName,
        tenantId,
        error
      });
      throw error;
    }
  }

  /**
   * Validate tenant resource limits
   */
  async validateResourceLimits(
    tenantId: string,
    resource: string,
    currentUsage: number
  ): Promise<boolean> {
    try {
      const tenant = await this.tenantManager.getTenant(tenantId);
      if (!tenant) {
        return false;
      }

      const settings = tenant.settings;
      let limit: number;

      switch (resource) {
        case 'concurrent_workflows':
          limit = settings.max_concurrent_workflows;
          break;
        case 'memory_usage':
          limit = settings.max_memory_usage;
          break;
        case 'storage_usage':
          limit = settings.max_storage_usage;
          break;
        default:
          return true; // No limit for unknown resources
      }

      const withinLimit = currentUsage <= limit;

      if (!withinLimit) {
        this.logger.warn('Resource limit exceeded', {
          tenantId,
          resource,
          currentUsage,
          limit
        });
      }

      return withinLimit;
    } catch (error) {
      this.logger.error('Error validating resource limits', {
        tenantId,
        resource,
        currentUsage,
        error
      });
      return false;
    }
  }

  /**
   * Get tenant-specific configuration
   */
  async getTenantConfig(tenantId: string, configKey: string): Promise<any> {
    try {
      const cacheKey = `${tenantId}:${configKey}`;

      // Check cache first
      if (this.configCache.has(cacheKey)) {
        return this.configCache.get(cacheKey);
      }

      const tenant = await this.tenantManager.getTenant(tenantId);
      if (!tenant) {
        return null;
      }

      // Extract config from tenant settings
      const config = this.extractConfigValue(tenant.settings, configKey);

      // Cache the result
      this.configCache.set(cacheKey, config);

      return config;
    } catch (error) {
      this.logger.error('Error getting tenant config', { tenantId, configKey, error });
      return null;
    }
  }

  /**
   * Set tenant-specific configuration
   */
  async setTenantConfig(tenantId: string, configKey: string, configValue: any): Promise<void> {
    try {
      const tenant = await this.tenantManager.getTenant(tenantId);
      if (!tenant) {
        throw new Error(`Tenant ${tenantId} not found`);
      }

      // Update tenant settings
      const updatedSettings = this.setConfigValue(tenant.settings, configKey, configValue);

      await this.tenantManager.updateTenant(tenantId, { settings: updatedSettings });

      // Update cache
      const cacheKey = `${tenantId}:${configKey}`;
      this.configCache.set(cacheKey, configValue);

      this.logger.info('Tenant config updated', { tenantId, configKey });
    } catch (error) {
      this.logger.error('Error setting tenant config', { tenantId, configKey, configValue, error });
      throw error;
    }
  }

  /**
   * Log tenant-specific audit event
   */
  async logAuditEvent(tenantId: string, event: string, details: any): Promise<void> {
    try {
      const auditEntry = {
        tenantId,
        event,
        details,
        timestamp: new Date()
      };

      // Add to in-memory audit log (in production, this should go to a persistent store)
      this.auditLog.push(auditEntry);

      // Keep only last 1000 entries per tenant
      const tenantEntries = this.auditLog.filter((entry) => entry.tenantId === tenantId);
      if (tenantEntries.length > 1000) {
        const entriesToRemove = tenantEntries.slice(0, tenantEntries.length - 1000);
        for (const entryToRemove of entriesToRemove) {
          const index = this.auditLog.indexOf(entryToRemove);
          if (index > -1) {
            this.auditLog.splice(index, 1);
          }
        }
      }

      this.logger.info('Audit event logged', { tenantId, event });
    } catch (error) {
      this.logger.error('Error logging audit event', { tenantId, event, details, error });
    }
  }

  /**
   * Extract configuration value from nested object
   */
  private extractConfigValue(settings: TenantSettings, configKey: string): any {
    const keys = configKey.split('.');
    let value: any = settings;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return null;
      }
    }

    return value;
  }

  /**
   * Set configuration value in nested object
   */
  private setConfigValue(
    settings: TenantSettings,
    configKey: string,
    configValue: any
  ): TenantSettings {
    const keys = configKey.split('.');
    const updatedSettings = JSON.parse(JSON.stringify(settings)); // Deep clone
    let current: any = updatedSettings;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = configValue;
    return updatedSettings;
  }

  /**
   * Get audit log for tenant
   */
  getAuditLog(
    tenantId: string,
    limit: number = 100
  ): Array<{ event: string; details: any; timestamp: Date }> {
    return this.auditLog
      .filter((entry) => entry.tenantId === tenantId)
      .slice(-limit)
      .map((entry) => ({
        event: entry.event,
        details: entry.details,
        timestamp: entry.timestamp
      }));
  }

  /**
   * Clear config cache
   */
  clearConfigCache(): void {
    this.configCache.clear();
    this.logger.info('Tenant config cache cleared');
  }
}
