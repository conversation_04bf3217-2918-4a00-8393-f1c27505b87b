import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { TYPES } from '../types';
import {
  ITenantManager,
  PaginationOptions,
  PaginatedResult,
  ValidationResult
} from '../core/interfaces/ITenantManager';
import { Tenant, TenantSettings } from '../infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../infrastructure/database/entities/TenantUser.entity';
import type { ILogger } from '../core/interfaces/ILogger';
import { ValidationError } from '../core/errors/ValidationError';
import { NotFoundError } from '../core/errors/NotFoundError';

/**
 * Tenant manager implementation
 * Handles all tenant-related operations including CRUD and user management
 */
@injectable()
export class TenantManager implements ITenantManager {
  constructor(
    @inject(TYPES.TenantRepository) private tenantRepository: Repository<Tenant>,
    @inject(TYPES.TenantUserRepository) private tenantUserRepository: Repository<TenantUser>,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Get tenant by ID
   */
  async getTenant(tenantId: string): Promise<Tenant | null> {
    try {
      const tenant = await this.tenantRepository.findOne({
        where: { id: tenantId },
        relations: ['users']
      });
      return tenant || null;
    } catch (error) {
      this.logger.error('Error getting tenant by ID', { tenantId, error });
      throw error;
    }
  }

  /**
   * Get tenant by name
   */
  async getTenantByName(name: string): Promise<Tenant | null> {
    try {
      const tenant = await this.tenantRepository.findOne({
        where: { name },
        relations: ['users']
      });
      return tenant || null;
    } catch (error) {
      this.logger.error('Error getting tenant by name', { name, error });
      throw error;
    }
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenantData: Partial<Tenant>): Promise<Tenant> {
    try {
      // Validate required fields
      if (!tenantData.name || !tenantData.displayName) {
        throw new ValidationError('Tenant name and display name are required');
      }

      // Check if tenant name already exists
      const existingTenant = await this.getTenantByName(tenantData.name);
      if (existingTenant) {
        throw new ValidationError(`Tenant with name '${tenantData.name}' already exists`);
      }

      // Set default settings if not provided
      const settings = tenantData.settings || Tenant.getDefaultSettings();

      // Create tenant
      const tenant = this.tenantRepository.create({
        ...tenantData,
        settings,
        enabled: tenantData.enabled !== undefined ? tenantData.enabled : true
      });

      const savedTenant = await this.tenantRepository.save(tenant);

      this.logger.info('Tenant created successfully', {
        tenantId: savedTenant.id,
        name: savedTenant.name
      });

      return savedTenant;
    } catch (error) {
      this.logger.error('Error creating tenant', { tenantData, error });
      throw error;
    }
  }

  /**
   * Update tenant
   */
  async updateTenant(tenantId: string, updates: Partial<Tenant>): Promise<Tenant> {
    try {
      const tenant = await this.getTenant(tenantId);
      if (!tenant) {
        throw new NotFoundError(`Tenant with ID '${tenantId}' not found`);
      }

      // If name is being updated, check for conflicts
      if (updates.name && updates.name !== tenant.name) {
        const existingTenant = await this.getTenantByName(updates.name);
        if (existingTenant) {
          throw new ValidationError(`Tenant with name '${updates.name}' already exists`);
        }
      }

      // Update tenant
      Object.assign(tenant, updates);
      const updatedTenant = await this.tenantRepository.save(tenant);

      this.logger.info('Tenant updated successfully', {
        tenantId,
        updates: Object.keys(updates)
      });

      return updatedTenant;
    } catch (error) {
      this.logger.error('Error updating tenant', { tenantId, updates, error });
      throw error;
    }
  }

  /**
   * Delete tenant
   */
  async deleteTenant(tenantId: string): Promise<boolean> {
    try {
      const tenant = await this.getTenant(tenantId);
      if (!tenant) {
        throw new NotFoundError(`Tenant with ID '${tenantId}' not found`);
      }

      await this.tenantRepository.remove(tenant);

      this.logger.info('Tenant deleted successfully', { tenantId });
      return true;
    } catch (error) {
      this.logger.error('Error deleting tenant', { tenantId, error });
      throw error;
    }
  }

  /**
   * List tenants with pagination
   */
  async listTenants(pagination?: PaginationOptions): Promise<PaginatedResult<Tenant>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const sortBy = pagination?.sortBy || 'createdAt';
      const sortOrder = pagination?.sortOrder || 'DESC';

      const [tenants, total] = await this.tenantRepository.findAndCount({
        order: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
        relations: ['users']
      });

      return {
        data: tenants,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      this.logger.error('Error listing tenants', { pagination, error });
      throw error;
    }
  }

  /**
   * Validate tenant settings
   */
  async validateTenantSettings(settings: TenantSettings): Promise<ValidationResult> {
    const errors: string[] = [];

    // Validate resource limits
    if (settings.max_concurrent_workflows <= 0) {
      errors.push('max_concurrent_workflows must be greater than 0');
    }
    if (settings.max_script_execution_time <= 0) {
      errors.push('max_script_execution_time must be greater than 0');
    }
    if (settings.max_memory_usage <= 0) {
      errors.push('max_memory_usage must be greater than 0');
    }
    if (settings.max_storage_usage <= 0) {
      errors.push('max_storage_usage must be greater than 0');
    }

    // Validate arrays
    if (!Array.isArray(settings.allowed_node_types)) {
      errors.push('allowed_node_types must be an array');
    }
    if (!Array.isArray(settings.allowed_data_sources)) {
      errors.push('allowed_data_sources must be an array');
    }

    // Validate session timeout
    if (settings.session_timeout <= 0) {
      errors.push('session_timeout must be greater than 0');
    }

    // Validate metrics retention
    if (settings.metrics_retention_days <= 0) {
      errors.push('metrics_retention_days must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get tenant users
   */
  async getTenantUsers(
    tenantId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<TenantUser>> {
    try {
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 10;
      const sortBy = pagination?.sortBy || 'createdAt';
      const sortOrder = pagination?.sortOrder || 'DESC';

      const [users, total] = await this.tenantUserRepository.findAndCount({
        where: { tenantId },
        order: { [sortBy]: sortOrder },
        skip: (page - 1) * limit,
        take: limit,
        relations: ['tenant']
      });

      return {
        data: users,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      this.logger.error('Error getting tenant users', { tenantId, pagination, error });
      throw error;
    }
  }

  /**
   * Create tenant user
   */
  async createTenantUser(tenantId: string, userData: Partial<TenantUser>): Promise<TenantUser> {
    try {
      // Validate tenant exists
      const tenant = await this.getTenant(tenantId);
      if (!tenant) {
        throw new NotFoundError(`Tenant with ID '${tenantId}' not found`);
      }

      // Validate required fields
      if (!userData.username || !userData.email) {
        throw new ValidationError('Username and email are required');
      }

      // Check for existing user with same username or email in tenant
      const existingUser = await this.tenantUserRepository.findOne({
        where: [
          { tenantId, username: userData.username },
          { tenantId, email: userData.email }
        ]
      });

      if (existingUser) {
        throw new ValidationError('User with this username or email already exists in tenant');
      }

      // Hash password if provided
      let passwordHash: string | undefined;
      if (userData.passwordHash) {
        passwordHash = await bcrypt.hash(userData.passwordHash, 10);
      }

      // Create user
      const user = this.tenantUserRepository.create({
        ...userData,
        tenantId,
        passwordHash,
        enabled: userData.enabled !== undefined ? userData.enabled : true,
        roles: userData.roles || [TenantUser.getUserRole()]
      });

      const savedUser = await this.tenantUserRepository.save(user);

      this.logger.info('Tenant user created successfully', {
        tenantId,
        userId: savedUser.id,
        username: savedUser.username
      });

      return savedUser;
    } catch (error) {
      this.logger.error('Error creating tenant user', { tenantId, userData, error });
      throw error;
    }
  }

  /**
   * Update tenant user
   */
  async updateTenantUser(
    tenantId: string,
    userId: string,
    updates: Partial<TenantUser>
  ): Promise<TenantUser> {
    try {
      const user = await this.tenantUserRepository.findOne({
        where: { id: userId, tenantId }
      });

      if (!user) {
        throw new NotFoundError(`User with ID '${userId}' not found in tenant '${tenantId}'`);
      }

      // Hash password if being updated
      if (updates.passwordHash) {
        updates.passwordHash = await bcrypt.hash(updates.passwordHash, 10);
      }

      // Update user
      Object.assign(user, updates);
      const updatedUser = await this.tenantUserRepository.save(user);

      this.logger.info('Tenant user updated successfully', {
        tenantId,
        userId,
        updates: Object.keys(updates)
      });

      return updatedUser;
    } catch (error) {
      this.logger.error('Error updating tenant user', { tenantId, userId, updates, error });
      throw error;
    }
  }

  /**
   * Delete tenant user
   */
  async deleteTenantUser(tenantId: string, userId: string): Promise<boolean> {
    try {
      const user = await this.tenantUserRepository.findOne({
        where: { id: userId, tenantId }
      });

      if (!user) {
        throw new NotFoundError(`User with ID '${userId}' not found in tenant '${tenantId}'`);
      }

      await this.tenantUserRepository.remove(user);

      this.logger.info('Tenant user deleted successfully', { tenantId, userId });
      return true;
    } catch (error) {
      this.logger.error('Error deleting tenant user', { tenantId, userId, error });
      throw error;
    }
  }

  /**
   * Authenticate tenant user
   */
  async authenticateUser(
    tenantId: string,
    username: string,
    password: string
  ): Promise<TenantUser | null> {
    try {
      const user = await this.tenantUserRepository.findOne({
        where: { tenantId, username, enabled: true },
        relations: ['tenant']
      });

      if (!user || !user.passwordHash) {
        return null;
      }

      const isValidPassword = await bcrypt.compare(password, user.passwordHash);
      if (!isValidPassword) {
        return null;
      }

      // Update last login
      user.lastLoginAt = new Date();
      await this.tenantUserRepository.save(user);

      this.logger.info('User authenticated successfully', { tenantId, username });
      return user;
    } catch (error) {
      this.logger.error('Error authenticating user', { tenantId, username, error });
      throw error;
    }
  }
}
