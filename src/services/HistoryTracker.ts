import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import {
  IHistoryTracker,
  WorkflowExecutionRecord,
  UserActionRecord,
  SystemEventRecord,
  HistoryOptions,
  ExecutionHistory,
  UserActionHistory,
  RetentionPolicy,
  CleanupResult
} from '../core/interfaces/IHistoryTracker';
import type { ILogger } from '../core/interfaces/ILogger';
import type { IDatabase } from '../core/interfaces/IDatabase';
import { TYPES } from '../types';
import { WorkflowExecutionHistory } from '../infrastructure/database/entities/WorkflowExecutionHistory.entity';
import { UserActionHistory as UserActionHistoryEntity } from '../infrastructure/database/entities/UserActionHistory.entity';
import { SystemEventHistory } from '../infrastructure/database/entities/SystemEventHistory.entity';

/**
 * History Tracker implementation for comprehensive audit trail
 */
@injectable()
export class HistoryTracker implements IHistoryTracker {
  private workflowExecutionRepo: Repository<WorkflowExecutionHistory>;
  private userActionRepo: Repository<UserActionHistoryEntity>;
  private systemEventRepo: Repository<SystemEventHistory>;

  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    this.initializeRepositories();
  }

  private async initializeRepositories(): Promise<void> {
    const dataSource = await this.database.getDataSource();
    this.workflowExecutionRepo = dataSource.getRepository(WorkflowExecutionHistory);
    this.userActionRepo = dataSource.getRepository(UserActionHistoryEntity);
    this.systemEventRepo = dataSource.getRepository(SystemEventHistory);
  }

  /**
   * Record workflow execution details
   */
  async recordWorkflowExecution(execution: WorkflowExecutionRecord): Promise<void> {
    try {
      this.logger.debug('Recording workflow execution', {
        workflowId: execution.workflowId,
        executionId: execution.executionId,
        status: execution.status
      });

      const historyRecord = this.workflowExecutionRepo.create({
        id: uuidv4(),
        workflowId: execution.workflowId,
        executionId: execution.executionId,
        tenantId: execution.tenantId,
        status: execution.status,
        startTime: execution.startTime,
        endTime: execution.endTime,
        duration: execution.duration,
        inputData: execution.inputData,
        outputData: execution.outputData,
        errorMessage: execution.errorMessage,
        nodeExecutions: execution.nodeExecutions,
        correlationId: execution.correlationId || uuidv4(),
        userId: execution.userId,
        metadata: execution.metadata
      });

      await this.workflowExecutionRepo.save(historyRecord);

      this.logger.info('Workflow execution recorded successfully', {
        workflowId: execution.workflowId,
        executionId: execution.executionId,
        historyId: historyRecord.id
      });
    } catch (error) {
      this.logger.error('Failed to record workflow execution', {
        workflowId: execution.workflowId,
        executionId: execution.executionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Record user action for audit trail
   */
  async recordUserAction(action: UserActionRecord): Promise<void> {
    try {
      this.logger.debug('Recording user action', {
        userId: action.userId,
        action: action.action,
        resourceType: action.resourceType,
        resourceId: action.resourceId
      });

      const actionRecord = this.userActionRepo.create({
        id: uuidv4(),
        userId: action.userId,
        tenantId: action.tenantId,
        action: action.action,
        resourceType: action.resourceType,
        resourceId: action.resourceId,
        timestamp: action.timestamp,
        ipAddress: action.ipAddress,
        userAgent: action.userAgent,
        details: action.details,
        correlationId: action.correlationId || uuidv4()
      });

      await this.userActionRepo.save(actionRecord);

      this.logger.info('User action recorded successfully', {
        userId: action.userId,
        action: action.action,
        actionId: actionRecord.id
      });
    } catch (error) {
      this.logger.error('Failed to record user action', {
        userId: action.userId,
        action: action.action,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Record system event
   */
  async recordSystemEvent(event: SystemEventRecord): Promise<void> {
    try {
      this.logger.debug('Recording system event', {
        eventType: event.eventType,
        eventCategory: event.eventCategory,
        severity: event.severity,
        source: event.source
      });

      const eventRecord = this.systemEventRepo.create({
        id: uuidv4(),
        eventType: event.eventType,
        eventCategory: event.eventCategory,
        severity: event.severity,
        timestamp: event.timestamp,
        source: event.source,
        message: event.message,
        details: event.details,
        correlationId: event.correlationId || uuidv4(),
        tenantId: event.tenantId
      });

      await this.systemEventRepo.save(eventRecord);

      this.logger.info('System event recorded successfully', {
        eventType: event.eventType,
        severity: event.severity,
        eventId: eventRecord.id
      });
    } catch (error) {
      this.logger.error('Failed to record system event', {
        eventType: event.eventType,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get workflow execution history
   */
  async getExecutionHistory(
    workflowId: string,
    options?: HistoryOptions
  ): Promise<ExecutionHistory[]> {
    try {
      this.logger.debug('Getting execution history', { workflowId, options });

      const queryBuilder = this.workflowExecutionRepo
        .createQueryBuilder('weh')
        .where('weh.workflowId = :workflowId', { workflowId });

      // Apply filters
      if (options?.startDate) {
        queryBuilder.andWhere('weh.startTime >= :startDate', { startDate: options.startDate });
      }
      if (options?.endDate) {
        queryBuilder.andWhere('weh.startTime <= :endDate', { endDate: options.endDate });
      }
      if (options?.status?.length) {
        queryBuilder.andWhere('weh.status IN (:...statuses)', { statuses: options.status });
      }
      if (options?.correlationId) {
        queryBuilder.andWhere('weh.correlationId = :correlationId', {
          correlationId: options.correlationId
        });
      }

      // Apply pagination
      if (options?.limit) {
        queryBuilder.limit(options.limit);
      }
      if (options?.offset) {
        queryBuilder.offset(options.offset);
      }

      // Order by start time descending
      queryBuilder.orderBy('weh.startTime', 'DESC');

      const records = await queryBuilder.getMany();

      return records.map((record) => ({
        id: record.id,
        workflowId: record.workflowId,
        executionId: record.executionId,
        tenantId: record.tenantId,
        status: record.status,
        startTime: record.startTime,
        endTime: record.endTime,
        duration: record.duration,
        errorMessage: record.errorMessage,
        correlationId: record.correlationId,
        userId: record.userId,
        nodeCount: record.nodeExecutions?.length || 0,
        successfulNodes:
          record.nodeExecutions?.filter((n: any) => n.status === 'completed').length || 0,
        failedNodes: record.nodeExecutions?.filter((n: any) => n.status === 'failed').length || 0,
        metadata: record.metadata
      }));
    } catch (error) {
      this.logger.error('Failed to get execution history', {
        workflowId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get user action history
   */
  async getUserActionHistory(
    userId: string,
    options?: HistoryOptions
  ): Promise<UserActionHistory[]> {
    try {
      this.logger.debug('Getting user action history', { userId, options });

      const queryBuilder = this.userActionRepo
        .createQueryBuilder('uah')
        .where('uah.userId = :userId', { userId });

      // Apply filters
      if (options?.startDate) {
        queryBuilder.andWhere('uah.timestamp >= :startDate', { startDate: options.startDate });
      }
      if (options?.endDate) {
        queryBuilder.andWhere('uah.timestamp <= :endDate', { endDate: options.endDate });
      }
      if (options?.correlationId) {
        queryBuilder.andWhere('uah.correlationId = :correlationId', {
          correlationId: options.correlationId
        });
      }

      // Apply pagination
      if (options?.limit) {
        queryBuilder.limit(options.limit);
      }
      if (options?.offset) {
        queryBuilder.offset(options.offset);
      }

      // Order by timestamp descending
      queryBuilder.orderBy('uah.timestamp', 'DESC');

      const records = await queryBuilder.getMany();

      return records.map((record) => ({
        id: record.id,
        userId: record.userId,
        tenantId: record.tenantId,
        action: record.action,
        resourceType: record.resourceType,
        resourceId: record.resourceId,
        timestamp: record.timestamp,
        ipAddress: record.ipAddress,
        details: record.details,
        correlationId: record.correlationId
      }));
    } catch (error) {
      this.logger.error('Failed to get user action history', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get system events
   */
  async getSystemEvents(options?: HistoryOptions): Promise<SystemEventRecord[]> {
    try {
      this.logger.debug('Getting system events', { options });

      const queryBuilder = this.systemEventRepo.createQueryBuilder('seh');

      // Apply filters
      if (options?.startDate) {
        queryBuilder.andWhere('seh.timestamp >= :startDate', { startDate: options.startDate });
      }
      if (options?.endDate) {
        queryBuilder.andWhere('seh.timestamp <= :endDate', { endDate: options.endDate });
      }
      if (options?.correlationId) {
        queryBuilder.andWhere('seh.correlationId = :correlationId', {
          correlationId: options.correlationId
        });
      }

      // Apply pagination
      if (options?.limit) {
        queryBuilder.limit(options.limit);
      }
      if (options?.offset) {
        queryBuilder.offset(options.offset);
      }

      // Order by timestamp descending
      queryBuilder.orderBy('seh.timestamp', 'DESC');

      const records = await queryBuilder.getMany();

      return records.map((record) => ({
        eventType: record.eventType,
        eventCategory: record.eventCategory as any,
        severity: record.severity as any,
        timestamp: record.timestamp,
        source: record.source,
        message: record.message,
        details: record.details,
        correlationId: record.correlationId,
        tenantId: record.tenantId
      }));
    } catch (error) {
      this.logger.error('Failed to get system events', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Search across all history types
   */
  async searchHistory(
    query: string,
    options?: HistoryOptions
  ): Promise<{
    executions: ExecutionHistory[];
    userActions: UserActionHistory[];
    systemEvents: SystemEventRecord[];
  }> {
    try {
      this.logger.debug('Searching history', { query, options });

      // Search executions
      const executionQuery = this.workflowExecutionRepo
        .createQueryBuilder('weh')
        .where('weh.errorMessage ILIKE :query OR weh.metadata::text ILIKE :query', {
          query: `%${query}%`
        });

      // Search user actions
      const userActionQuery = this.userActionRepo
        .createQueryBuilder('uah')
        .where('uah.action ILIKE :query OR uah.details::text ILIKE :query', {
          query: `%${query}%`
        });

      // Search system events
      const systemEventQuery = this.systemEventRepo
        .createQueryBuilder('seh')
        .where('seh.message ILIKE :query OR seh.details::text ILIKE :query', {
          query: `%${query}%`
        });

      // Apply common filters
      if (options?.startDate) {
        executionQuery.andWhere('weh.startTime >= :startDate', { startDate: options.startDate });
        userActionQuery.andWhere('uah.timestamp >= :startDate', { startDate: options.startDate });
        systemEventQuery.andWhere('seh.timestamp >= :startDate', { startDate: options.startDate });
      }

      if (options?.endDate) {
        executionQuery.andWhere('weh.startTime <= :endDate', { endDate: options.endDate });
        userActionQuery.andWhere('uah.timestamp <= :endDate', { endDate: options.endDate });
        systemEventQuery.andWhere('seh.timestamp <= :endDate', { endDate: options.endDate });
      }

      // Apply pagination
      const limit = options?.limit || 50;
      executionQuery.limit(limit).orderBy('weh.startTime', 'DESC');
      userActionQuery.limit(limit).orderBy('uah.timestamp', 'DESC');
      systemEventQuery.limit(limit).orderBy('seh.timestamp', 'DESC');

      const [executionRecords, userActionRecords, systemEventRecords] = await Promise.all([
        executionQuery.getMany(),
        userActionQuery.getMany(),
        systemEventQuery.getMany()
      ]);

      return {
        executions: executionRecords.map((record) => ({
          id: record.id,
          workflowId: record.workflowId,
          executionId: record.executionId,
          tenantId: record.tenantId,
          status: record.status,
          startTime: record.startTime,
          endTime: record.endTime,
          duration: record.duration,
          errorMessage: record.errorMessage,
          correlationId: record.correlationId,
          userId: record.userId,
          nodeCount: record.nodeExecutions?.length || 0,
          successfulNodes:
            record.nodeExecutions?.filter((n: any) => n.status === 'completed').length || 0,
          failedNodes: record.nodeExecutions?.filter((n: any) => n.status === 'failed').length || 0,
          metadata: record.metadata
        })),
        userActions: userActionRecords.map((record) => ({
          id: record.id,
          userId: record.userId,
          tenantId: record.tenantId,
          action: record.action,
          resourceType: record.resourceType,
          resourceId: record.resourceId,
          timestamp: record.timestamp,
          ipAddress: record.ipAddress,
          details: record.details,
          correlationId: record.correlationId
        })),
        systemEvents: systemEventRecords.map((record) => ({
          eventType: record.eventType,
          eventCategory: record.eventCategory as any,
          severity: record.severity as any,
          timestamp: record.timestamp,
          source: record.source,
          message: record.message,
          details: record.details,
          correlationId: record.correlationId,
          tenantId: record.tenantId
        }))
      };
    } catch (error) {
      this.logger.error('Failed to search history', {
        query,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Clean up old records based on retention policy
   */
  async cleanup(retentionPolicy: RetentionPolicy): Promise<CleanupResult> {
    const startTime = Date.now();
    let deletedRecords = 0;
    const errors: string[] = [];
    const categories: string[] = [];

    try {
      this.logger.info('Starting history cleanup', { retentionPolicy });

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionPolicy.maxAge);

      // Cleanup workflow execution history
      try {
        const result = await this.workflowExecutionRepo
          .createQueryBuilder()
          .delete()
          .where('startTime < :cutoffDate', { cutoffDate })
          .execute();

        deletedRecords += result.affected || 0;
        categories.push('workflow_executions');
        this.logger.info('Cleaned up workflow execution history', { deleted: result.affected });
      } catch (error) {
        errors.push(
          `Workflow executions: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }

      // Cleanup user action history
      try {
        const result = await this.userActionRepo
          .createQueryBuilder()
          .delete()
          .where('timestamp < :cutoffDate', { cutoffDate })
          .execute();

        deletedRecords += result.affected || 0;
        categories.push('user_actions');
        this.logger.info('Cleaned up user action history', { deleted: result.affected });
      } catch (error) {
        errors.push(`User actions: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Cleanup system event history
      try {
        const result = await this.systemEventRepo
          .createQueryBuilder()
          .delete()
          .where('timestamp < :cutoffDate', { cutoffDate })
          .execute();

        deletedRecords += result.affected || 0;
        categories.push('system_events');
        this.logger.info('Cleaned up system event history', { deleted: result.affected });
      } catch (error) {
        errors.push(`System events: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      const executionTime = Date.now() - startTime;

      this.logger.info('History cleanup completed', {
        deletedRecords,
        categories,
        executionTime,
        errors: errors.length > 0 ? errors : undefined
      });

      return {
        deletedRecords,
        categories,
        executionTime,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      this.logger.error('Failed to cleanup history', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get history statistics
   */
  async getStatistics(timeRange?: { start: Date; end: Date }): Promise<{
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    totalUserActions: number;
    totalSystemEvents: number;
    averageExecutionTime: number;
    topWorkflows: Array<{ workflowId: string; count: number }>;
    topUsers: Array<{ userId: string; count: number }>;
  }> {
    try {
      this.logger.debug('Getting history statistics', { timeRange });

      const executionQuery = this.workflowExecutionRepo.createQueryBuilder('weh');
      const userActionQuery = this.userActionRepo.createQueryBuilder('uah');
      const systemEventQuery = this.systemEventRepo.createQueryBuilder('seh');

      if (timeRange) {
        executionQuery.where('weh.startTime BETWEEN :start AND :end', timeRange);
        userActionQuery.where('uah.timestamp BETWEEN :start AND :end', timeRange);
        systemEventQuery.where('seh.timestamp BETWEEN :start AND :end', timeRange);
      }

      const [
        totalExecutions,
        successfulExecutions,
        failedExecutions,
        totalUserActions,
        totalSystemEvents,
        avgDuration,
        topWorkflows,
        topUsers
      ] = await Promise.all([
        executionQuery.getCount(),
        executionQuery.clone().andWhere('weh.status = :status', { status: 'completed' }).getCount(),
        executionQuery.clone().andWhere('weh.status = :status', { status: 'failed' }).getCount(),
        userActionQuery.getCount(),
        systemEventQuery.getCount(),
        executionQuery.clone().select('AVG(weh.duration)', 'avg').getRawOne(),
        executionQuery
          .clone()
          .select('weh.workflowId', 'workflowId')
          .addSelect('COUNT(*)', 'count')
          .groupBy('weh.workflowId')
          .orderBy('count', 'DESC')
          .limit(10)
          .getRawMany(),
        userActionQuery
          .clone()
          .select('uah.userId', 'userId')
          .addSelect('COUNT(*)', 'count')
          .groupBy('uah.userId')
          .orderBy('count', 'DESC')
          .limit(10)
          .getRawMany()
      ]);

      return {
        totalExecutions,
        successfulExecutions,
        failedExecutions,
        totalUserActions,
        totalSystemEvents,
        averageExecutionTime: Math.round(avgDuration?.avg || 0),
        topWorkflows: topWorkflows.map((w) => ({
          workflowId: w.workflowId,
          count: parseInt(w.count || '0')
        })),
        topUsers: topUsers.map((u) => ({ userId: u.userId, count: parseInt(u.count || '0') }))
      };
    } catch (error) {
      this.logger.error('Failed to get history statistics', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
