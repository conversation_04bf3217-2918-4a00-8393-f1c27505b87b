import { injectable, inject } from 'inversify';
import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HttpServer } from 'http';
import type { ILogger } from '../core/interfaces/ILogger';
import { TYPES } from '../types';

/**
 * WebSocket service for real-time communication
 */
@injectable()
export class WebSocketService {
  private io: SocketIOServer | null = null;
  private connectedClients: Map<string, Socket> = new Map();

  constructor(@inject(TYPES.Logger) private logger: ILogger) {}

  /**
   * Initialize WebSocket server
   * @param httpServer HTTP server instance
   */
  initialize(httpServer: HttpServer): void {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: '*', // Configure appropriately for production
        methods: ['GET', 'POST']
      },
      path: '/socket.io'
    });

    this.setupEventHandlers();
    this.logger.info('WebSocket service initialized');
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket: Socket) => {
      this.logger.info(`Client connected: ${socket.id}`);
      this.connectedClients.set(socket.id, socket);

      // Handle client authentication/subscription
      socket.on('subscribe-workflow', (data: { workflowId: string; executionId?: string }) => {
        this.handleWorkflowSubscription(socket, data);
      });

      socket.on('subscribe-context', (data: { executionId: string }) => {
        this.handleContextSubscription(socket, data);
      });

      socket.on('unsubscribe-workflow', (data: { workflowId: string }) => {
        this.handleWorkflowUnsubscription(socket, data);
      });

      socket.on('unsubscribe-context', (data: { executionId: string }) => {
        this.handleContextUnsubscription(socket, data);
      });

      socket.on('disconnect', () => {
        this.logger.info(`Client disconnected: ${socket.id}`);
        this.connectedClients.delete(socket.id);
      });

      socket.on('error', (error: Error) => {
        this.logger.error(`Socket error for client ${socket.id}:`, error);
      });
    });
  }

  /**
   * Handle workflow subscription
   */
  private handleWorkflowSubscription(
    socket: Socket,
    data: { workflowId: string; executionId?: string }
  ): void {
    const room = `workflow:${data.workflowId}`;
    socket.join(room);

    if (data.executionId) {
      const executionRoom = `execution:${data.executionId}`;
      socket.join(executionRoom);
    }

    this.logger.debug(`Client ${socket.id} subscribed to workflow ${data.workflowId}`);

    socket.emit('subscription-confirmed', {
      type: 'workflow',
      workflowId: data.workflowId,
      executionId: data.executionId
    });
  }

  /**
   * Handle context subscription
   */
  private handleContextSubscription(socket: Socket, data: { executionId: string }): void {
    const room = `context:${data.executionId}`;
    socket.join(room);

    this.logger.debug(`Client ${socket.id} subscribed to context ${data.executionId}`);

    socket.emit('subscription-confirmed', {
      type: 'context',
      executionId: data.executionId
    });
  }

  /**
   * Handle workflow unsubscription
   */
  private handleWorkflowUnsubscription(socket: Socket, data: { workflowId: string }): void {
    const room = `workflow:${data.workflowId}`;
    socket.leave(room);

    this.logger.debug(`Client ${socket.id} unsubscribed from workflow ${data.workflowId}`);
  }

  /**
   * Handle context unsubscription
   */
  private handleContextUnsubscription(socket: Socket, data: { executionId: string }): void {
    const room = `context:${data.executionId}`;
    socket.leave(room);

    this.logger.debug(`Client ${socket.id} unsubscribed from context ${data.executionId}`);
  }

  /**
   * Broadcast workflow execution start
   */
  broadcastWorkflowStart(workflowId: string, executionId: string, data: any): void {
    if (!this.io) return;

    const payload = {
      type: 'workflow-start',
      workflowId,
      executionId,
      timestamp: new Date().toISOString(),
      data
    };

    this.io.to(`workflow:${workflowId}`).emit('workflow-event', payload);
    this.io.to(`execution:${executionId}`).emit('workflow-event', payload);

    this.logger.debug(`Broadcasted workflow start: ${workflowId} (${executionId})`);
  }

  /**
   * Broadcast workflow execution completion
   */
  broadcastWorkflowComplete(workflowId: string, executionId: string, result: any): void {
    if (!this.io) return;

    const payload = {
      type: 'workflow-complete',
      workflowId,
      executionId,
      timestamp: new Date().toISOString(),
      result
    };

    this.io.to(`workflow:${workflowId}`).emit('workflow-event', payload);
    this.io.to(`execution:${executionId}`).emit('workflow-event', payload);

    this.logger.debug(`Broadcasted workflow completion: ${workflowId} (${executionId})`);
  }

  /**
   * Broadcast workflow execution error
   */
  broadcastWorkflowError(workflowId: string, executionId: string, error: any): void {
    if (!this.io) return;

    const payload = {
      type: 'workflow-error',
      workflowId,
      executionId,
      timestamp: new Date().toISOString(),
      error: {
        message: error.message || 'Unknown error',
        stack: error.stack
      }
    };

    this.io.to(`workflow:${workflowId}`).emit('workflow-event', payload);
    this.io.to(`execution:${executionId}`).emit('workflow-event', payload);

    this.logger.debug(`Broadcasted workflow error: ${workflowId} (${executionId})`);
  }

  /**
   * Broadcast node execution start
   */
  broadcastNodeStart(executionId: string, nodeId: string, nodeType: string, input: any): void {
    if (!this.io) return;

    const payload = {
      type: 'node-start',
      executionId,
      nodeId,
      nodeType,
      timestamp: new Date().toISOString(),
      input
    };

    this.io.to(`execution:${executionId}`).emit('node-event', payload);
    this.io.to(`context:${executionId}`).emit('context-event', payload);

    this.logger.debug(`Broadcasted node start: ${nodeId} in execution ${executionId}`);
  }

  /**
   * Broadcast node execution completion
   */
  broadcastNodeComplete(executionId: string, nodeId: string, nodeType: string, output: any): void {
    if (!this.io) return;

    const payload = {
      type: 'node-complete',
      executionId,
      nodeId,
      nodeType,
      timestamp: new Date().toISOString(),
      output
    };

    this.io.to(`execution:${executionId}`).emit('node-event', payload);
    this.io.to(`context:${executionId}`).emit('context-event', payload);

    this.logger.debug(`Broadcasted node completion: ${nodeId} in execution ${executionId}`);
  }

  /**
   * Broadcast context update
   */
  broadcastContextUpdate(
    executionId: string,
    contextData: any,
    changeType: 'variable' | 'nodeResult' | 'full'
  ): void {
    if (!this.io) return;

    const payload = {
      type: 'context-update',
      executionId,
      changeType,
      timestamp: new Date().toISOString(),
      context: contextData
    };

    this.io.to(`context:${executionId}`).emit('context-event', payload);

    this.logger.debug(`Broadcasted context update: ${executionId} (${changeType})`);
  }

  /**
   * Broadcast debug step
   */
  broadcastDebugStep(executionId: string, stepData: any): void {
    if (!this.io) return;

    const payload = {
      type: 'debug-step',
      executionId,
      timestamp: new Date().toISOString(),
      step: stepData
    };

    this.io.to(`execution:${executionId}`).emit('debug-event', payload);

    this.logger.debug(`Broadcasted debug step: ${executionId}`);
  }

  /**
   * Get connected clients count
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * Get room information
   */
  getRoomInfo(room: string): { clientCount: number; clients: string[] } {
    if (!this.io) return { clientCount: 0, clients: [] };

    const roomClients = this.io.sockets.adapter.rooms.get(room);
    if (!roomClients) return { clientCount: 0, clients: [] };

    return {
      clientCount: roomClients.size,
      clients: Array.from(roomClients)
    };
  }

  /**
   * Shutdown WebSocket service
   */
  shutdown(): void {
    if (this.io) {
      this.io.close();
      this.io = null;
    }
    this.connectedClients.clear();
    this.logger.info('WebSocket service shutdown');
  }
}

/**
 * Interface for WebSocket service
 */
export interface IWebSocketService {
  initialize(httpServer: HttpServer): void;
  broadcastWorkflowStart(workflowId: string, executionId: string, data: any): void;
  broadcastWorkflowComplete(workflowId: string, executionId: string, result: any): void;
  broadcastWorkflowError(workflowId: string, executionId: string, error: any): void;
  broadcastNodeStart(executionId: string, nodeId: string, nodeType: string, input: any): void;
  broadcastNodeComplete(executionId: string, nodeId: string, nodeType: string, output: any): void;
  broadcastContextUpdate(
    executionId: string,
    contextData: any,
    changeType: 'variable' | 'nodeResult' | 'full'
  ): void;
  broadcastDebugStep(executionId: string, stepData: any): void;
  getConnectedClientsCount(): number;
  getRoomInfo(room: string): { clientCount: number; clients: string[] };
  shutdown(): void;
}
