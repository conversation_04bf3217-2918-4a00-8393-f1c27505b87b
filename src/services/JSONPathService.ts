import { injectable } from 'inversify';
import { J<PERSON>NPath } from 'jsonpath-plus';

/**
 * Service for JSONPath operations on workflow context data
 */
@injectable()
export class JSONPathService {
  /**
   * Query data using JSONPath expression
   * @param data Data to query
   * @param path JSONPath expression
   * @returns Query result
   */
  query(data: any, path: string): any {
    try {
      // Validate JSONPath expression
      this.validatePath(path);

      // Execute JSONPath query
      const result = JSONPath({ path, json: data, wrap: false });

      return result;
    } catch (error) {
      throw new Error(`JSONPath query failed: ${(error as Error).message}`);
    }
  }

  /**
   * Query data and return multiple results
   * @param data Data to query
   * @param path JSONPath expression
   * @returns Array of results
   */
  queryAll(data: any, path: string): any[] {
    try {
      this.validatePath(path);

      const result = JSONPath({ path, json: data, wrap: true });

      return Array.isArray(result) ? result : [result];
    } catch (error) {
      throw new Error(`JSONPath queryAll failed: ${(error as Error).message}`);
    }
  }

  /**
   * Set value at JSONPath location
   * @param data Data to modify
   * @param path JSONPath expression
   * @param value Value to set
   * @returns Modified data
   */
  set(data: any, path: string, value: any): any {
    try {
      this.validatePath(path);

      // Create a deep copy to avoid mutations
      const result = JSON.parse(JSON.stringify(data));

      // Simple implementation for basic paths like $.property or $.object.property
      if (path.startsWith('$.') && !path.includes('[') && !path.includes('*')) {
        const keys = path.substring(2).split('.');
        let current = result;

        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {};
          }
          current = current[keys[i]];
        }

        current[keys[keys.length - 1]] = value;
      } else {
        // For complex paths, just return the original data
        console.warn(`Complex JSONPath set not supported: ${path}`);
      }

      return result;
    } catch (error) {
      throw new Error(`JSONPath set failed: ${(error as Error).message}`);
    }
  }

  /**
   * Check if path exists in data
   * @param data Data to check
   * @param path JSONPath expression
   * @returns True if path exists
   */
  exists(data: any, path: string): boolean {
    try {
      this.validatePath(path);

      const result = JSONPath({ path, json: data, wrap: false });

      return result !== undefined;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get all paths that match the expression
   * @param data Data to query
   * @param path JSONPath expression
   * @returns Array of matching paths
   */
  getPaths(data: any, path: string): string[] {
    try {
      this.validatePath(path);

      const result = JSONPath({ path, json: data, resultType: 'path' });

      return Array.isArray(result) ? result.map(String) : [String(result)];
    } catch (error) {
      throw new Error(`JSONPath getPaths failed: ${(error as Error).message}`);
    }
  }

  /**
   * Validate JSONPath expression
   * @param path JSONPath expression
   * @throws Error if path is invalid
   */
  private validatePath(path: string): void {
    if (!path || typeof path !== 'string') {
      throw new Error('JSONPath expression must be a non-empty string');
    }

    if (!path.startsWith('$')) {
      throw new Error('JSONPath expression must start with $');
    }

    // Basic validation for common syntax errors
    const invalidPatterns = [
      /\[\s*\]/, // Empty brackets
      /\.\./, // Double dots without proper syntax
      /\[\s*[^0-9"'$@*?:,\]\s]/ // Invalid characters in brackets
    ];

    for (const pattern of invalidPatterns) {
      if (pattern.test(path)) {
        throw new Error(`Invalid JSONPath syntax: ${path}`);
      }
    }
  }

  /**
   * Resolve template variables in JSONPath expressions
   * @param path JSONPath expression with template variables
   * @param context Context data for variable resolution
   * @returns Resolved JSONPath expression
   */
  resolveTemplate(path: string, context: any): string {
    try {
      // Replace template variables like ${variable} with actual values
      return path.replace(/\$\{([^}]+)\}/g, (match, variable) => {
        const value = this.query(context, `$.${variable}`);

        if (value === undefined) {
          throw new Error(`Template variable '${variable}' not found in context`);
        }

        // Convert value to string for path resolution
        return String(value);
      });
    } catch (error) {
      throw new Error(`Template resolution failed: ${(error as Error).message}`);
    }
  }

  /**
   * Create a JSONPath expression builder
   * @returns JSONPath builder
   */
  createBuilder(): JSONPathBuilder {
    return new JSONPathBuilder();
  }

  /**
   * Common JSONPath expressions for workflow context
   */
  static readonly COMMON_PATHS = {
    // Input data
    INPUT: '$.input',
    INPUT_PAYLOAD: '$.input.payload',
    INPUT_HEADERS: '$.input.headers',

    // Variables
    VARIABLES: '$.variables',
    VARIABLE: (name: string) => `$.variables.${name}`,

    // Node results
    NODE_RESULTS: '$.nodeResults',
    NODE_RESULT: (nodeId: string) => `$.nodeResults.${nodeId}`,

    // Metadata
    EXECUTION_ID: '$.executionId',
    WORKFLOW_ID: '$.workflowId',
    STARTED_AT: '$.startedAt',

    // Parallel execution
    PARALLEL_BRANCHES: '$.parallelBranches',
    BRANCH_RESULT: (branchId: string) => `$.parallelBranches.${branchId}`,

    // Error context
    ERROR_CONTEXT: '$.errorContext',
    LAST_ERROR: '$.errorContext.lastError',
    ERROR_HISTORY: '$.errorContext.errorHistory'
  };
}

/**
 * JSONPath expression builder for fluent API
 */
export class JSONPathBuilder {
  private path: string = '$';

  /**
   * Start with root
   */
  root(): JSONPathBuilder {
    this.path = '$';
    return this;
  }

  /**
   * Add property access
   */
  property(name: string): JSONPathBuilder {
    this.path += `.${name}`;
    return this;
  }

  /**
   * Add array index access
   */
  index(index: number): JSONPathBuilder {
    this.path += `[${index}]`;
    return this;
  }

  /**
   * Add array slice
   */
  slice(start?: number, end?: number): JSONPathBuilder {
    if (start !== undefined && end !== undefined) {
      this.path += `[${start}:${end}]`;
    } else if (start !== undefined) {
      this.path += `[${start}:]`;
    } else if (end !== undefined) {
      this.path += `[:${end}]`;
    } else {
      this.path += '[:]';
    }
    return this;
  }

  /**
   * Add wildcard
   */
  wildcard(): JSONPathBuilder {
    this.path += '[*]';
    return this;
  }

  /**
   * Add recursive descent
   */
  recursive(): JSONPathBuilder {
    this.path += '..';
    return this;
  }

  /**
   * Add filter expression
   */
  filter(expression: string): JSONPathBuilder {
    this.path += `[?(${expression})]`;
    return this;
  }

  /**
   * Build the JSONPath expression
   */
  build(): string {
    return this.path;
  }
}

/**
 * Interface for JSONPath service
 */
export interface IJSONPathService {
  query(data: any, path: string): any;
  queryAll(data: any, path: string): any[];
  set(data: any, path: string, value: any): any;
  exists(data: any, path: string): boolean;
  getPaths(data: any, path: string): string[];
  resolveTemplate(path: string, context: any): string;
  createBuilder(): JSONPathBuilder;
}
