/**
 * Helper methods for AlertManager
 */

import { AlertRule } from '../infrastructure/database/entities/AlertRule.entity';
import { NotificationChannel, NotificationResult } from '../core/interfaces/IAlertManager';
import type { ILogger } from '../core/interfaces/ILogger';

export class AlertManagerHelpers {
  constructor(private logger: ILogger) {}

  evaluateCondition(condition: any): boolean {
    // This is a simplified implementation
    // In a real system, this would query actual metrics/events/logs
    
    const actualValue = this.getActualValue(condition);
    const threshold = condition.threshold;

    switch (condition.operator) {
      case '>':
        return actualValue > threshold;
      case '<':
        return actualValue < threshold;
      case '>=':
        return actualValue >= threshold;
      case '<=':
        return actualValue <= threshold;
      case '==':
        return actualValue === threshold;
      case '!=':
        return actualValue !== threshold;
      case 'contains':
        return String(actualValue).includes(String(threshold));
      case 'not_contains':
        return !String(actualValue).includes(String(threshold));
      default:
        return false;
    }
  }

  getActualValue(condition: any): any {
    // This is a placeholder implementation
    // In a real system, this would fetch actual metrics from ObservabilityManager
    // or query logs/events from the database
    
    switch (condition.type) {
      case 'metric':
        // Mock metric value
        return Math.random() * 100;
      case 'event':
        // Mock event count
        return Math.floor(Math.random() * 10);
      case 'log':
        // Mock log level
        return 'error';
      default:
        return 0;
    }
  }

  generateAlertMessage(rule: AlertRule, conditions: any[]): string {
    const triggeredConditions = conditions.filter(c => c.met);
    const conditionMessages = triggeredConditions.map(c => 
      `${c.condition.metric || c.condition.eventType || 'condition'} ${c.condition.operator} ${c.condition.threshold} (actual: ${c.actualValue})`
    );
    
    return `Alert triggered for rule "${rule.name}". Conditions: ${conditionMessages.join(', ')}`;
  }

  determineSeverity(priority: string): string {
    switch (priority) {
      case 'critical':
        return 'critical';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'warning';
    }
  }

  extractActualValues(conditions: any[]): Record<string, any> {
    const values: Record<string, any> = {};
    conditions.forEach((c, index) => {
      const key = c.condition.metric || c.condition.eventType || `condition_${index}`;
      values[key] = c.actualValue;
    });
    return values;
  }

  async sendNotification(channel: NotificationChannel, message: string, title: string): Promise<NotificationResult> {
    try {
      this.logger.debug('Sending notification', { 
        type: channel.type, 
        enabled: channel.enabled 
      });

      if (!channel.enabled) {
        return {
          channelType: channel.type,
          success: false,
          message: 'Channel is disabled'
        };
      }

      switch (channel.type) {
        case 'email':
          return await this.sendEmailNotification(channel, message, title);
        case 'slack':
          return await this.sendSlackNotification(channel, message, title);
        case 'teams':
          return await this.sendTeamsNotification(channel, message, title);
        case 'webhook':
          return await this.sendWebhookNotification(channel, message, title);
        case 'sms':
          return await this.sendSMSNotification(channel, message, title);
        default:
          return {
            channelType: channel.type,
            success: false,
            message: 'Unsupported notification channel type'
          };
      }
    } catch (error) {
      this.logger.error('Failed to send notification', {
        channelType: channel.type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        channelType: channel.type,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async sendEmailNotification(channel: NotificationChannel, message: string, title: string): Promise<NotificationResult> {
    // Mock email implementation
    this.logger.info('Mock email notification sent', {
      emails: channel.emails,
      title,
      message: message.substring(0, 100)
    });

    return {
      channelType: 'email',
      success: true,
      message: `Email sent to ${channel.emails?.length || 0} recipients`,
      deliveredAt: new Date()
    };
  }

  private async sendSlackNotification(channel: NotificationChannel, message: string, title: string): Promise<NotificationResult> {
    // Mock Slack implementation
    this.logger.info('Mock Slack notification sent', {
      webhook: channel.slackWebhook?.substring(0, 50) + '...',
      channel: channel.slackChannel,
      title
    });

    return {
      channelType: 'slack',
      success: true,
      message: 'Slack notification sent successfully',
      deliveredAt: new Date()
    };
  }

  private async sendTeamsNotification(channel: NotificationChannel, message: string, title: string): Promise<NotificationResult> {
    // Mock Teams implementation
    this.logger.info('Mock Teams notification sent', {
      webhook: channel.teamsWebhook?.substring(0, 50) + '...',
      title
    });

    return {
      channelType: 'teams',
      success: true,
      message: 'Teams notification sent successfully',
      deliveredAt: new Date()
    };
  }

  private async sendWebhookNotification(channel: NotificationChannel, message: string, title: string): Promise<NotificationResult> {
    // Mock webhook implementation
    this.logger.info('Mock webhook notification sent', {
      url: channel.webhookUrl?.substring(0, 50) + '...',
      method: channel.webhookMethod || 'POST',
      title
    });

    return {
      channelType: 'webhook',
      success: true,
      message: 'Webhook notification sent successfully',
      deliveredAt: new Date()
    };
  }

  private async sendSMSNotification(channel: NotificationChannel, message: string, title: string): Promise<NotificationResult> {
    // Mock SMS implementation
    this.logger.info('Mock SMS notification sent', {
      phoneNumbers: channel.phoneNumbers,
      provider: channel.smsProvider,
      title
    });

    return {
      channelType: 'sms',
      success: true,
      message: `SMS sent to ${channel.phoneNumbers?.length || 0} recipients`,
      deliveredAt: new Date()
    };
  }
}
