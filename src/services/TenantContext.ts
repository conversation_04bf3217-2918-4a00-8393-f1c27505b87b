import { injectable } from 'inversify';
import { AsyncLocalStorage } from 'async_hooks';
import { ITenantContext } from '../core/interfaces/ITenantContext';
import { Tenant } from '../infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../infrastructure/database/entities/TenantUser.entity';

/**
 * Context data interface
 */
interface ContextData {
  tenant?: Tenant;
  user?: TenantUser;
}

/**
 * Tenant context implementation using AsyncLocalStorage
 * Provides async context propagation for tenant and user isolation
 */
@injectable()
export class TenantContext implements ITenantContext {
  private readonly asyncLocalStorage = new AsyncLocalStorage<ContextData>();

  /**
   * Get current tenant from context
   */
  getCurrentTenant(): Tenant | null {
    const context = this.asyncLocalStorage.getStore();
    return context?.tenant || null;
  }

  /**
   * Set current tenant in context
   */
  setCurrentTenant(tenant: Tenant): void {
    const context = this.asyncLocalStorage.getStore() || {};
    context.tenant = tenant;
    this.asyncLocalStorage.enterWith(context);
  }

  /**
   * Clear current tenant from context
   */
  clearCurrentTenant(): void {
    const context = this.asyncLocalStorage.getStore() || {};
    delete context.tenant;
    this.asyncLocalStorage.enterWith(context);
  }

  /**
   * Execute function with tenant context
   */
  async withTenant<T>(tenant: Tenant, fn: () => Promise<T>): Promise<T> {
    const context = this.asyncLocalStorage.getStore() || {};
    const newContext = { ...context, tenant };
    
    return this.asyncLocalStorage.run(newContext, fn);
  }

  /**
   * Get current tenant ID
   */
  getTenantId(): string | null {
    const tenant = this.getCurrentTenant();
    return tenant?.id || null;
  }

  /**
   * Require tenant context (throws if not set)
   */
  requireTenant(): Tenant {
    const tenant = this.getCurrentTenant();
    if (!tenant) {
      throw new Error('Tenant context is required but not set');
    }
    return tenant;
  }

  /**
   * Get current user from context
   */
  getCurrentUser(): TenantUser | null {
    const context = this.asyncLocalStorage.getStore();
    return context?.user || null;
  }

  /**
   * Set current user in context
   */
  setCurrentUser(user: TenantUser): void {
    const context = this.asyncLocalStorage.getStore() || {};
    context.user = user;
    this.asyncLocalStorage.enterWith(context);
  }

  /**
   * Clear current user from context
   */
  clearCurrentUser(): void {
    const context = this.asyncLocalStorage.getStore() || {};
    delete context.user;
    this.asyncLocalStorage.enterWith(context);
  }

  /**
   * Execute function with user context
   */
  async withUser<T>(user: TenantUser, fn: () => Promise<T>): Promise<T> {
    const context = this.asyncLocalStorage.getStore() || {};
    const newContext = { ...context, user };
    
    return this.asyncLocalStorage.run(newContext, fn);
  }

  /**
   * Get current user ID
   */
  getUserId(): string | null {
    const user = this.getCurrentUser();
    return user?.id || null;
  }

  /**
   * Require user context (throws if not set)
   */
  requireUser(): TenantUser {
    const user = this.getCurrentUser();
    if (!user) {
      throw new Error('User context is required but not set');
    }
    return user;
  }

  /**
   * Check if current user has permission
   */
  hasPermission(resource: string, action: string): boolean {
    const user = this.getCurrentUser();
    if (!user) {
      return false;
    }
    return user.hasPermission(resource, action);
  }

  /**
   * Require permission (throws if not authorized)
   */
  requirePermission(resource: string, action: string): void {
    if (!this.hasPermission(resource, action)) {
      const user = this.getCurrentUser();
      const userId = user?.id || 'anonymous';
      throw new Error(`User ${userId} does not have permission to ${action} on ${resource}`);
    }
  }

  /**
   * Execute function with both tenant and user context
   */
  async withTenantAndUser<T>(tenant: Tenant, user: TenantUser, fn: () => Promise<T>): Promise<T> {
    const context = { tenant, user };
    return this.asyncLocalStorage.run(context, fn);
  }

  /**
   * Get full context data
   */
  getContext(): ContextData {
    return this.asyncLocalStorage.getStore() || {};
  }

  /**
   * Set full context data
   */
  setContext(context: ContextData): void {
    this.asyncLocalStorage.enterWith(context);
  }

  /**
   * Clear all context data
   */
  clearContext(): void {
    this.asyncLocalStorage.enterWith({});
  }

  /**
   * Check if tenant context is set
   */
  hasTenantContext(): boolean {
    return this.getCurrentTenant() !== null;
  }

  /**
   * Check if user context is set
   */
  hasUserContext(): boolean {
    return this.getCurrentUser() !== null;
  }

  /**
   * Validate tenant and user match
   */
  validateContext(): boolean {
    const tenant = this.getCurrentTenant();
    const user = this.getCurrentUser();
    
    if (!tenant || !user) {
      return true; // No validation needed if either is missing
    }
    
    return user.tenantId === tenant.id;
  }

  /**
   * Require valid context (tenant and user must match)
   */
  requireValidContext(): void {
    if (!this.validateContext()) {
      throw new Error('Invalid context: user does not belong to current tenant');
    }
  }
}
