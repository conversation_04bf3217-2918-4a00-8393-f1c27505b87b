import { injectable, inject } from 'inversify';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import {
  IAlertManager,
  AlertRule as IAlertRule,
  Alert as IAlert,
  AlertQuery,
  AlertSummary,
  NotificationChannel,
  NotificationResult,
  AlertEvaluationResult
} from '../core/interfaces/IAlertManager';
import type { ILogger } from '../core/interfaces/ILogger';
import type { IDatabase } from '../core/interfaces/IDatabase';
import { TYPES } from '../types';
import { AlertRule } from '../infrastructure/database/entities/AlertRule.entity';
import { Alert } from '../infrastructure/database/entities/Alert.entity';
import { AlertManagerHelpers } from './AlertManagerHelpers';

/**
 * Alert Manager implementation for comprehensive alerting system
 */
@injectable()
export class AlertManager implements IAlertManager {
  private alertRuleRepo: Repository<AlertRule>;
  private alertRepo: Repository<Alert>;
  private evaluationScheduler?: NodeJS.Timeout;
  private isSchedulerRunning = false;
  private helpers: AlertManagerHelpers;

  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    this.helpers = new AlertManagerHelpers(logger);
    this.initializeRepositories();
  }

  private async initializeRepositories(): Promise<void> {
    const dataSource = await this.database.getDataSource();
    this.alertRuleRepo = dataSource.getRepository(AlertRule);
    this.alertRepo = dataSource.getRepository(Alert);
  }

  /**
   * Create a new alert rule
   */
  async createRule(rule: Omit<IAlertRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      this.logger.debug('Creating alert rule', { name: rule.name, tenantId: rule.tenantId });

      const alertRule = this.alertRuleRepo.create({
        id: uuidv4(),
        name: rule.name,
        description: rule.description,
        enabled: rule.enabled,
        tenantId: rule.tenantId,
        conditions: rule.conditions,
        operator: rule.operator,
        notifications: rule.notifications,
        escalation: rule.escalation,
        evaluationInterval: rule.evaluationInterval,
        cooldownPeriod: rule.cooldownPeriod,
        tags: rule.tags,
        priority: rule.priority,
        createdBy: rule.createdBy
      });

      await this.alertRuleRepo.save(alertRule);

      this.logger.info('Alert rule created successfully', {
        ruleId: alertRule.id,
        name: rule.name
      });

      return alertRule.id;
    } catch (error) {
      this.logger.error('Failed to create alert rule', {
        name: rule.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update an existing alert rule
   */
  async updateRule(ruleId: string, rule: Partial<IAlertRule>): Promise<void> {
    try {
      this.logger.debug('Updating alert rule', { ruleId, updates: Object.keys(rule) });

      const updateData: any = {
        ...rule,
        updatedAt: new Date()
      };
      const result = await this.alertRuleRepo.update(ruleId, updateData);

      if (result.affected === 0) {
        throw new Error(`Alert rule with ID ${ruleId} not found`);
      }

      this.logger.info('Alert rule updated successfully', { ruleId });
    } catch (error) {
      this.logger.error('Failed to update alert rule', {
        ruleId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete an alert rule
   */
  async deleteRule(ruleId: string): Promise<void> {
    try {
      this.logger.debug('Deleting alert rule', { ruleId });

      const result = await this.alertRuleRepo.delete(ruleId);

      if (result.affected === 0) {
        throw new Error(`Alert rule with ID ${ruleId} not found`);
      }

      this.logger.info('Alert rule deleted successfully', { ruleId });
    } catch (error) {
      this.logger.error('Failed to delete alert rule', {
        ruleId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get alert rule by ID
   */
  async getRule(ruleId: string): Promise<IAlertRule | null> {
    try {
      this.logger.debug('Getting alert rule', { ruleId });

      const rule = await this.alertRuleRepo.findOne({ where: { id: ruleId } });

      if (!rule) {
        return null;
      }

      return {
        id: rule.id,
        name: rule.name,
        description: rule.description,
        enabled: rule.enabled,
        tenantId: rule.tenantId,
        conditions: rule.conditions,
        operator: rule.operator as any,
        notifications: rule.notifications,
        escalation: rule.escalation,
        evaluationInterval: rule.evaluationInterval,
        cooldownPeriod: rule.cooldownPeriod,
        tags: rule.tags,
        priority: rule.priority as any,
        createdBy: rule.createdBy,
        createdAt: rule.createdAt,
        updatedAt: rule.updatedAt
      };
    } catch (error) {
      this.logger.error('Failed to get alert rule', {
        ruleId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get all alert rules
   */
  async getRules(tenantId?: string): Promise<IAlertRule[]> {
    try {
      this.logger.debug('Getting alert rules', { tenantId });

      const queryBuilder = this.alertRuleRepo.createQueryBuilder('ar');

      if (tenantId) {
        queryBuilder.where('ar.tenantId = :tenantId', { tenantId });
      }

      queryBuilder.orderBy('ar.createdAt', 'DESC');

      const rules = await queryBuilder.getMany();

      return rules.map((rule) => ({
        id: rule.id,
        name: rule.name,
        description: rule.description,
        enabled: rule.enabled,
        tenantId: rule.tenantId,
        conditions: rule.conditions,
        operator: rule.operator as any,
        notifications: rule.notifications,
        escalation: rule.escalation,
        evaluationInterval: rule.evaluationInterval,
        cooldownPeriod: rule.cooldownPeriod,
        tags: rule.tags,
        priority: rule.priority as any,
        createdBy: rule.createdBy,
        createdAt: rule.createdAt,
        updatedAt: rule.updatedAt
      }));
    } catch (error) {
      this.logger.error('Failed to get alert rules', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Enable/disable an alert rule
   */
  async toggleRule(ruleId: string, enabled: boolean): Promise<void> {
    try {
      this.logger.debug('Toggling alert rule', { ruleId, enabled });

      await this.updateRule(ruleId, { enabled });

      this.logger.info('Alert rule toggled successfully', { ruleId, enabled });
    } catch (error) {
      this.logger.error('Failed to toggle alert rule', {
        ruleId,
        enabled,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Evaluate all active alert rules
   */
  async evaluateRules(): Promise<AlertEvaluationResult[]> {
    try {
      this.logger.debug('Evaluating all alert rules');

      const activeRules = await this.alertRuleRepo.find({
        where: { enabled: true }
      });

      const results: AlertEvaluationResult[] = [];

      for (const rule of activeRules) {
        try {
          const result = await this.evaluateRule(rule.id);
          results.push(result);
        } catch (error) {
          this.logger.error('Failed to evaluate rule', {
            ruleId: rule.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      this.logger.info('Alert rules evaluation completed', {
        totalRules: activeRules.length,
        evaluatedRules: results.length,
        triggeredRules: results.filter((r) => r.triggered).length
      });

      return results;
    } catch (error) {
      this.logger.error('Failed to evaluate alert rules', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Evaluate a specific alert rule
   */
  async evaluateRule(ruleId: string): Promise<AlertEvaluationResult> {
    try {
      this.logger.debug('Evaluating alert rule', { ruleId });

      const rule = await this.alertRuleRepo.findOne({ where: { id: ruleId } });
      if (!rule) {
        throw new Error(`Alert rule with ID ${ruleId} not found`);
      }

      const evaluatedAt = new Date();
      const conditions = rule.conditions.map((condition) => ({
        condition,
        met: this.helpers.evaluateCondition(condition),
        actualValue: this.helpers.getActualValue(condition),
        threshold: condition.threshold
      }));

      // Determine if rule is triggered based on operator
      const triggered =
        rule.operator === 'AND' ? conditions.every((c) => c.met) : conditions.some((c) => c.met);

      // If triggered, create an alert
      if (triggered) {
        await this.createAlert(rule, conditions, evaluatedAt);
      }

      const nextEvaluation = new Date(evaluatedAt.getTime() + rule.evaluationInterval * 1000);

      return {
        ruleId: rule.id,
        triggered,
        conditions,
        evaluatedAt,
        nextEvaluation
      };
    } catch (error) {
      this.logger.error('Failed to evaluate alert rule', {
        ruleId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async createAlert(rule: AlertRule, conditions: any[], triggeredAt: Date): Promise<void> {
    try {
      // Check if there's already an active alert for this rule (cooldown period)
      const existingAlert = await this.alertRepo.findOne({
        where: {
          ruleId: rule.id,
          status: 'active'
        },
        order: { triggeredAt: 'DESC' }
      });

      if (existingAlert) {
        const timeSinceLastAlert = triggeredAt.getTime() - existingAlert.triggeredAt.getTime();
        if (timeSinceLastAlert < rule.cooldownPeriod * 1000) {
          this.logger.debug('Alert suppressed due to cooldown period', {
            ruleId: rule.id,
            timeSinceLastAlert,
            cooldownPeriod: rule.cooldownPeriod
          });
          return;
        }
      }

      const alert = this.alertRepo.create({
        id: uuidv4(),
        ruleId: rule.id,
        ruleName: rule.name,
        tenantId: rule.tenantId,
        title: `Alert: ${rule.name}`,
        message: this.helpers.generateAlertMessage(rule, conditions),
        severity: this.helpers.determineSeverity(rule.priority),
        status: 'active',
        triggeredAt,
        conditions: rule.conditions,
        actualValues: this.helpers.extractActualValues(conditions),
        correlationId: uuidv4(),
        tags: rule.tags
      });

      await this.alertRepo.save(alert);

      // Send notifications
      await this.sendAlert({
        id: alert.id,
        ruleId: alert.ruleId,
        ruleName: alert.ruleName,
        tenantId: alert.tenantId,
        title: alert.title,
        message: alert.message,
        severity: alert.severity as any,
        status: alert.status as any,
        triggeredAt: alert.triggeredAt,
        conditions: alert.conditions,
        actualValues: alert.actualValues,
        correlationId: alert.correlationId,
        tags: alert.tags
      });

      this.logger.info('Alert created and notifications sent', {
        alertId: alert.id,
        ruleId: rule.id,
        ruleName: rule.name
      });
    } catch (error) {
      this.logger.error('Failed to create alert', {
        ruleId: rule.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Send an alert notification
   */
  async sendAlert(alert: IAlert): Promise<NotificationResult[]> {
    try {
      this.logger.debug('Sending alert notifications', { alertId: alert.id });

      const rule = await this.alertRuleRepo.findOne({ where: { id: alert.ruleId } });
      if (!rule) {
        throw new Error(`Alert rule with ID ${alert.ruleId} not found`);
      }

      const results: NotificationResult[] = [];

      for (const channel of rule.notifications) {
        const result = await this.helpers.sendNotification(channel, alert.message, alert.title);
        results.push(result);
      }

      this.logger.info('Alert notifications sent', {
        alertId: alert.id,
        totalChannels: rule.notifications.length,
        successfulChannels: results.filter((r) => r.success).length
      });

      return results;
    } catch (error) {
      this.logger.error('Failed to send alert notifications', {
        alertId: alert.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Acknowledge an alert
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string, note?: string): Promise<void> {
    try {
      this.logger.debug('Acknowledging alert', { alertId, acknowledgedBy });

      const result = await this.alertRepo.update(alertId, {
        status: 'acknowledged',
        acknowledgedAt: new Date(),
        acknowledgedBy,
        acknowledgmentNote: note
      });

      if (result.affected === 0) {
        throw new Error(`Alert with ID ${alertId} not found`);
      }

      this.logger.info('Alert acknowledged successfully', { alertId, acknowledgedBy });
    } catch (error) {
      this.logger.error('Failed to acknowledge alert', {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Resolve an alert
   */
  async resolveAlert(alertId: string, resolvedBy: string, note?: string): Promise<void> {
    try {
      this.logger.debug('Resolving alert', { alertId, resolvedBy });

      const result = await this.alertRepo.update(alertId, {
        status: 'resolved',
        resolvedAt: new Date(),
        resolvedBy,
        resolutionNote: note
      });

      if (result.affected === 0) {
        throw new Error(`Alert with ID ${alertId} not found`);
      }

      this.logger.info('Alert resolved successfully', { alertId, resolvedBy });
    } catch (error) {
      this.logger.error('Failed to resolve alert', {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Suppress an alert
   */
  async suppressAlert(alertId: string, suppressedBy: string, note?: string): Promise<void> {
    try {
      this.logger.debug('Suppressing alert', { alertId, suppressedBy });

      const result = await this.alertRepo.update(alertId, {
        status: 'suppressed',
        resolvedBy: suppressedBy,
        resolutionNote: note
      });

      if (result.affected === 0) {
        throw new Error(`Alert with ID ${alertId} not found`);
      }

      this.logger.info('Alert suppressed successfully', { alertId, suppressedBy });
    } catch (error) {
      this.logger.error('Failed to suppress alert', {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get alerts with filtering
   */
  async getAlerts(query?: AlertQuery): Promise<IAlert[]> {
    try {
      this.logger.debug('Getting alerts', { query });

      const queryBuilder = this.alertRepo.createQueryBuilder('a');

      // Apply filters
      if (query?.ruleIds?.length) {
        queryBuilder.andWhere('a.ruleId IN (:...ruleIds)', { ruleIds: query.ruleIds });
      }
      if (query?.tenantIds?.length) {
        queryBuilder.andWhere('a.tenantId IN (:...tenantIds)', { tenantIds: query.tenantIds });
      }
      if (query?.status?.length) {
        queryBuilder.andWhere('a.status IN (:...statuses)', { statuses: query.status });
      }
      if (query?.severity?.length) {
        queryBuilder.andWhere('a.severity IN (:...severities)', { severities: query.severity });
      }
      if (query?.startDate) {
        queryBuilder.andWhere('a.triggeredAt >= :startDate', { startDate: query.startDate });
      }
      if (query?.endDate) {
        queryBuilder.andWhere('a.triggeredAt <= :endDate', { endDate: query.endDate });
      }

      // Apply pagination
      if (query?.limit) {
        queryBuilder.limit(query.limit);
      }
      if (query?.offset) {
        queryBuilder.offset(query.offset);
      }

      // Apply sorting
      const sortBy = query?.sortBy || 'triggeredAt';
      const sortOrder = query?.sortOrder || 'DESC';
      queryBuilder.orderBy(`a.${sortBy}`, sortOrder);

      const alerts = await queryBuilder.getMany();

      return alerts.map((alert) => ({
        id: alert.id,
        ruleId: alert.ruleId,
        ruleName: alert.ruleName,
        tenantId: alert.tenantId,
        title: alert.title,
        message: alert.message,
        severity: alert.severity as any,
        status: alert.status as any,
        triggeredAt: alert.triggeredAt,
        acknowledgedAt: alert.acknowledgedAt,
        resolvedAt: alert.resolvedAt,
        conditions: alert.conditions,
        actualValues: alert.actualValues,
        correlationId: alert.correlationId,
        escalationLevel: alert.escalationLevel,
        escalatedAt: alert.escalatedAt,
        acknowledgedBy: alert.acknowledgedBy,
        acknowledgmentNote: alert.acknowledgmentNote,
        resolvedBy: alert.resolvedBy,
        resolutionNote: alert.resolutionNote,
        autoResolved: alert.autoResolved,
        tags: alert.tags,
        metadata: alert.metadata
      }));
    } catch (error) {
      this.logger.error('Failed to get alerts', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get alert by ID
   */
  async getAlert(alertId: string): Promise<IAlert | null> {
    try {
      const alert = await this.alertRepo.findOne({ where: { id: alertId } });
      if (!alert) return null;

      return {
        id: alert.id,
        ruleId: alert.ruleId,
        ruleName: alert.ruleName,
        tenantId: alert.tenantId,
        title: alert.title,
        message: alert.message,
        severity: alert.severity as any,
        status: alert.status as any,
        triggeredAt: alert.triggeredAt,
        acknowledgedAt: alert.acknowledgedAt,
        resolvedAt: alert.resolvedAt,
        conditions: alert.conditions,
        actualValues: alert.actualValues,
        correlationId: alert.correlationId,
        escalationLevel: alert.escalationLevel,
        escalatedAt: alert.escalatedAt,
        acknowledgedBy: alert.acknowledgedBy,
        acknowledgmentNote: alert.acknowledgmentNote,
        resolvedBy: alert.resolvedBy,
        resolutionNote: alert.resolutionNote,
        autoResolved: alert.autoResolved,
        tags: alert.tags,
        metadata: alert.metadata
      };
    } catch (error) {
      this.logger.error('Failed to get alert', {
        alertId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get alert summary statistics
   */
  async getAlertSummary(
    tenantId?: string,
    timeRange?: { start: Date; end: Date }
  ): Promise<AlertSummary> {
    try {
      this.logger.debug('Getting alert summary', { tenantId, timeRange });

      const queryBuilder = this.alertRepo.createQueryBuilder('a');

      if (tenantId) {
        queryBuilder.where('a.tenantId = :tenantId', { tenantId });
      }

      if (timeRange) {
        queryBuilder.andWhere('a.triggeredAt BETWEEN :start AND :end', timeRange);
      }

      const [total, active, acknowledged, resolved, suppressed] = await Promise.all([
        queryBuilder.getCount(),
        queryBuilder.clone().andWhere('a.status = :status', { status: 'active' }).getCount(),
        queryBuilder.clone().andWhere('a.status = :status', { status: 'acknowledged' }).getCount(),
        queryBuilder.clone().andWhere('a.status = :status', { status: 'resolved' }).getCount(),
        queryBuilder.clone().andWhere('a.status = :status', { status: 'suppressed' }).getCount()
      ]);

      return {
        total,
        active,
        acknowledged,
        resolved,
        suppressed,
        byPriority: {},
        bySeverity: {},
        byTenant: {}
      };
    } catch (error) {
      this.logger.error('Failed to get alert summary', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Test notification channel
   */
  async testNotificationChannel(
    channel: NotificationChannel,
    testMessage?: string
  ): Promise<NotificationResult> {
    const message = testMessage || 'Test notification from Alert Manager';
    const title = 'Test Alert';

    return this.helpers.sendNotification(channel, message, title);
  }

  /**
   * Get alert history for a specific rule
   */
  async getAlertHistory(ruleId: string, limit?: number): Promise<IAlert[]> {
    try {
      const queryBuilder = this.alertRepo
        .createQueryBuilder('a')
        .where('a.ruleId = :ruleId', { ruleId })
        .orderBy('a.triggeredAt', 'DESC');

      if (limit) {
        queryBuilder.limit(limit);
      }

      const alerts = await queryBuilder.getMany();
      return alerts.map((alert) => ({
        id: alert.id,
        ruleId: alert.ruleId,
        ruleName: alert.ruleName,
        tenantId: alert.tenantId,
        title: alert.title,
        message: alert.message,
        severity: alert.severity as any,
        status: alert.status as any,
        triggeredAt: alert.triggeredAt,
        acknowledgedAt: alert.acknowledgedAt,
        resolvedAt: alert.resolvedAt,
        conditions: alert.conditions,
        actualValues: alert.actualValues,
        correlationId: alert.correlationId,
        escalationLevel: alert.escalationLevel,
        escalatedAt: alert.escalatedAt,
        acknowledgedBy: alert.acknowledgedBy,
        acknowledgmentNote: alert.acknowledgmentNote,
        resolvedBy: alert.resolvedBy,
        resolutionNote: alert.resolutionNote,
        autoResolved: alert.autoResolved,
        tags: alert.tags,
        metadata: alert.metadata
      }));
    } catch (error) {
      this.logger.error('Failed to get alert history', {
        ruleId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Cleanup old resolved alerts
   */
  async cleanupAlerts(retentionDays: number): Promise<{ deletedCount: number }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const result = await this.alertRepo
        .createQueryBuilder()
        .delete()
        .where('status = :status AND resolvedAt < :cutoffDate', {
          status: 'resolved',
          cutoffDate
        })
        .execute();

      return { deletedCount: result.affected || 0 };
    } catch (error) {
      this.logger.error('Failed to cleanup alerts', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Start the alert evaluation scheduler
   */
  async startScheduler(): Promise<void> {
    if (this.isSchedulerRunning) {
      this.logger.warn('Alert scheduler is already running');
      return;
    }

    this.logger.info('Starting alert evaluation scheduler');
    this.isSchedulerRunning = true;

    // Run evaluation every 30 seconds
    this.evaluationScheduler = setInterval(async () => {
      try {
        await this.evaluateRules();
      } catch (error) {
        this.logger.error('Error during scheduled alert evaluation', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }, 30000);
  }

  /**
   * Stop the alert evaluation scheduler
   */
  async stopScheduler(): Promise<void> {
    if (!this.isSchedulerRunning) {
      this.logger.warn('Alert scheduler is not running');
      return;
    }

    this.logger.info('Stopping alert evaluation scheduler');

    if (this.evaluationScheduler) {
      clearInterval(this.evaluationScheduler);
      this.evaluationScheduler = undefined;
    }

    this.isSchedulerRunning = false;
  }
}
