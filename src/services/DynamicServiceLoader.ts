import { injectable, inject, Container } from 'inversify';
import type { IDatabase } from '../core/interfaces/IDatabase';
import type { IScriptEngine } from '../core/interfaces/IScriptEngine';
import { IMCPTool } from '../core/interfaces/IMCPTool';
import type { ILogger } from '../core/interfaces/ILogger';
import type { IWorkflowEngine } from '../core/interfaces/IWorkflowEngine';
import { MCPFunction as MCPFunctionEntity } from '../infrastructure/database/entities/MCPFunction.entity';
import { Workflow } from '../infrastructure/database/entities/Workflow.entity';
import { MCPFunction } from '../core/interfaces/MCPFunction';
import { JavaScriptTool } from '../mcp/tools/JavaScriptTool';
import {
  WorkflowBasedTool,
  createWorkflowBasedToolFactory
} from '../workflow/tools/WorkflowBasedTool';
import { DynamicFunctionConfig } from '../core/types/DynamicFunctionConfig';
import { TYPES } from '../types';
import { ConfigValidationError } from '../core/errors/MCPError';
import { EchoFunction } from '../functions/EchoFunction';

/**
 * Service for dynamically loading MCP functions from the database
 */
@injectable()
export class DynamicServiceLoader {
  private activeFunctions: Map<string, IMCPTool> = new Map();
  private workflowBasedToolFactory: ReturnType<typeof createWorkflowBasedToolFactory>;
  private onFunctionsReloadedCallback?: (functions: Map<string, IMCPTool>) => void;

  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.ScriptEngine) private scriptEngine: IScriptEngine,
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.WorkflowEngine) private workflowEngine: IWorkflowEngine,
    @inject(TYPES.Container) private container: Container
  ) {
    // Create factory for workflow-based tools
    this.workflowBasedToolFactory = createWorkflowBasedToolFactory(container);
  }

  /**
   * Initialize the service and load all enabled functions
   */
  async initialize(): Promise<void> {
    // Add built-in echo function for testing
    const echoFunction = new EchoFunction();
    this.activeFunctions.set(echoFunction.name, echoFunction);

    // Load functions from database
    await this.loadAllFunctions();
  }

  /**
   * Load all enabled functions from the database
   */
  async loadAllFunctions(): Promise<void> {
    try {
      await this.database.initialize();

      const repository = this.database.getRepository<MCPFunctionEntity>(MCPFunctionEntity);
      const functions = await repository.findBy({ enabled: true });

      this.logger.info(`Loading ${functions.length} functions from database`);

      // Store built-in functions (preserve all non-database functions)
      const builtInFunctions = new Map<string, IMCPTool>();
      this.activeFunctions.forEach((tool, name) => {
        // Check if this is a built-in function (not from database)
        // Built-in functions typically don't have database IDs or have specific names
        if (
          name === 'echo' ||
          name === 'getDateTime' ||
          !tool.id ||
          typeof tool.id !== 'string' ||
          !tool.id.includes('-')
        ) {
          builtInFunctions.set(name, tool);
        }
      });

      // Clear existing functions
      this.activeFunctions.clear();

      // Restore built-in functions
      builtInFunctions.forEach((tool, name) => {
        this.activeFunctions.set(name, tool);
      });

      // Load each function from database
      for (const func of functions) {
        try {
          const tool = await this.createToolFromEntity(func);
          this.activeFunctions.set(func.name, tool);
        } catch (error) {
          this.logger.error(`Error loading function ${func.name}`, error);
        }
      }

      this.logger.info(`Successfully loaded ${this.activeFunctions.size} functions`);

      // Notify callback about reloaded functions
      if (this.onFunctionsReloadedCallback) {
        this.onFunctionsReloadedCallback(this.activeFunctions);
      }
    } catch (error) {
      this.logger.error('Error loading functions from database', error);
      throw error;
    }
  }

  /**
   * Get all active functions
   */
  getActiveFunctions(): Map<string, IMCPTool> {
    return this.activeFunctions;
  }

  /**
   * Set callback for when functions are reloaded
   */
  setOnFunctionsReloadedCallback(callback: (functions: Map<string, IMCPTool>) => void): void {
    this.onFunctionsReloadedCallback = callback;
  }

  /**
   * Create an MCP tool from a function entity
   * @param entity Function entity
   */
  async createToolFromEntity(entity: MCPFunctionEntity): Promise<IMCPTool> {
    // Validate entity
    this.validateFunctionEntity(entity);

    // Create tool based on handler type
    if (entity.handler_config.type === 'script') {
      return this.createScriptBasedTool(entity);
    } else if (entity.handler_config.type === 'workflow') {
      return this.createWorkflowBasedTool(entity);
    } else {
      throw new ConfigValidationError(`Unsupported handler type: ${entity.handler_config.type}`);
    }
  }

  /**
   * Create a script-based tool from a function entity
   * @param entity Function entity
   */
  private createScriptBasedTool(entity: MCPFunctionEntity): IMCPTool {
    // Extract script content
    const scriptContent = entity.handler_config.script_content;
    if (!scriptContent) {
      throw new ConfigValidationError('Script content is required for script-based functions');
    }

    // Create and return tool instance
    return new JavaScriptTool(
      {
        id: entity.id,
        name: entity.name,
        description: entity.description || '',
        input_schema: entity.input_schema,
        handler_config: entity.handler_config,
        enabled: entity.enabled,
        created_at: entity.created_at,
        updated_at: entity.updated_at
      },
      this.scriptEngine,
      scriptContent
    );
  }

  /**
   * Create a workflow-based tool from a function entity
   * @param entity Function entity
   */
  private async createWorkflowBasedTool(entity: MCPFunctionEntity): Promise<IMCPTool> {
    // Extract workflow ID
    const workflowId = entity.handler_config.workflow_id;
    if (!workflowId) {
      throw new ConfigValidationError('Workflow ID is required for workflow-based functions');
    }

    // Verify that the workflow exists
    const workflow = await this.workflowEngine.getWorkflow(workflowId);
    if (!workflow) {
      throw new ConfigValidationError(`Workflow with ID ${workflowId} not found`);
    }

    // Create and return tool instance
    return this.workflowBasedToolFactory({
      id: entity.id,
      name: entity.name,
      description: entity.description || '',
      input_schema: entity.input_schema,
      workflow_id: workflowId,
      enabled: entity.enabled
    });
  }

  /**
   * Validate a function entity
   * @param entity Function entity
   */
  private validateFunctionEntity(entity: MCPFunctionEntity): void {
    if (!entity.id) {
      throw new ConfigValidationError('Function ID is required');
    }

    if (!entity.name) {
      throw new ConfigValidationError('Function name is required');
    }

    if (!entity.input_schema) {
      throw new ConfigValidationError('Input schema is required');
    }

    if (!entity.handler_config) {
      throw new ConfigValidationError('Handler configuration is required');
    }

    if (!['script', 'workflow'].includes(entity.handler_config.type)) {
      throw new ConfigValidationError('Handler type must be either "script" or "workflow"');
    }
  }
}
