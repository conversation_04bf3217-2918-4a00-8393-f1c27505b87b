import { injectable, inject } from 'inversify';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import { TYPES } from '../types';
import { ITenantResolver, TenantResolutionStrategy } from '../core/interfaces/ITenantResolver';
import type { ITenantManager } from '../core/interfaces/ITenantManager';
import { Tenant } from '../infrastructure/database/entities/Tenant.entity';
import type { ILogger } from '../core/interfaces/ILogger';

/**
 * Tenant resolver implementation
 * Handles tenant resolution from various sources (headers, subdomains, paths, JWT)
 */
@injectable()
export class TenantResolver implements ITenantResolver {
  private strategy: TenantResolutionStrategy = TenantResolutionStrategy.HEADER;
  private cache = new Map<string, { tenant: Tenant; expires: number }>();
  private readonly defaultCacheTTL = 300; // 5 minutes

  constructor(
    @inject(TYPES.TenantManager) private tenantManager: ITenantManager,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  /**
   * Resolve tenant from HTTP request
   */
  async resolveTenant(request: Request): Promise<Tenant | null> {
    try {
      let tenant: Tenant | null = null;

      switch (this.strategy) {
        case TenantResolutionStrategy.HEADER:
          const tenantHeader = request.headers['x-tenant-id'] as string;
          if (tenantHeader) {
            tenant = await this.resolveFromHeader(tenantHeader);
          }
          break;

        case TenantResolutionStrategy.SUBDOMAIN:
          const hostname = request.hostname;
          if (hostname) {
            tenant = await this.resolveFromSubdomain(hostname);
          }
          break;

        case TenantResolutionStrategy.PATH:
          const path = request.path;
          if (path) {
            tenant = await this.resolveFromPath(path);
          }
          break;

        case TenantResolutionStrategy.JWT:
          const authHeader = request.headers.authorization;
          if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            tenant = await this.resolveFromJWT(token);
          }
          break;

        default:
          this.logger.warn('Unknown tenant resolution strategy', { strategy: this.strategy });
      }

      if (tenant) {
        this.logger.debug('Tenant resolved successfully', {
          tenantId: tenant.id,
          tenantName: tenant.name,
          strategy: this.strategy
        });
      } else {
        this.logger.debug('No tenant resolved', { strategy: this.strategy });
      }

      return tenant;
    } catch (error) {
      this.logger.error('Error resolving tenant', { strategy: this.strategy, error });
      return null;
    }
  }

  /**
   * Resolve tenant from header value
   */
  async resolveFromHeader(headerValue: string): Promise<Tenant | null> {
    try {
      // Check cache first
      const cached = this.getCachedTenant(`header:${headerValue}`);
      if (cached) {
        return cached;
      }

      // Try to resolve by ID first, then by name
      let tenant = await this.tenantManager.getTenant(headerValue);
      if (!tenant) {
        tenant = await this.tenantManager.getTenantByName(headerValue);
      }

      if (tenant && tenant.enabled) {
        this.cacheTenant(`header:${headerValue}`, tenant);
        return tenant;
      }

      return null;
    } catch (error) {
      this.logger.error('Error resolving tenant from header', { headerValue, error });
      return null;
    }
  }

  /**
   * Resolve tenant from subdomain
   */
  async resolveFromSubdomain(hostname: string): Promise<Tenant | null> {
    try {
      // Extract subdomain (first part before first dot)
      const parts = hostname.split('.');
      if (parts.length < 2) {
        return null; // No subdomain
      }

      const subdomain = parts[0];

      // Skip common subdomains
      if (['www', 'api', 'admin'].includes(subdomain)) {
        return null;
      }

      // Check cache first
      const cached = this.getCachedTenant(`subdomain:${subdomain}`);
      if (cached) {
        return cached;
      }

      // Resolve tenant by subdomain (using name field)
      const tenant = await this.tenantManager.getTenantByName(subdomain);

      if (tenant && tenant.enabled) {
        this.cacheTenant(`subdomain:${subdomain}`, tenant);
        return tenant;
      }

      return null;
    } catch (error) {
      this.logger.error('Error resolving tenant from subdomain', { hostname, error });
      return null;
    }
  }

  /**
   * Resolve tenant from URL path
   */
  async resolveFromPath(path: string): Promise<Tenant | null> {
    try {
      // Extract tenant from path like /tenant/{tenantName}/...
      const pathMatch = path.match(/^\/tenant\/([^\/]+)/);
      if (!pathMatch) {
        return null;
      }

      const tenantName = pathMatch[1];

      // Check cache first
      const cached = this.getCachedTenant(`path:${tenantName}`);
      if (cached) {
        return cached;
      }

      // Resolve tenant by name
      const tenant = await this.tenantManager.getTenantByName(tenantName);

      if (tenant && tenant.enabled) {
        this.cacheTenant(`path:${tenantName}`, tenant);
        return tenant;
      }

      return null;
    } catch (error) {
      this.logger.error('Error resolving tenant from path', { path, error });
      return null;
    }
  }

  /**
   * Resolve tenant from JWT token
   */
  async resolveFromJWT(token: string): Promise<Tenant | null> {
    try {
      // Decode JWT without verification (for tenant resolution)
      const decoded = jwt.decode(token) as any;
      if (!decoded || !decoded.tenantId) {
        return null;
      }

      const tenantId = decoded.tenantId;

      // Check cache first
      const cached = this.getCachedTenant(`jwt:${tenantId}`);
      if (cached) {
        return cached;
      }

      // Resolve tenant by ID
      const tenant = await this.tenantManager.getTenant(tenantId);

      if (tenant && tenant.enabled) {
        this.cacheTenant(`jwt:${tenantId}`, tenant);
        return tenant;
      }

      return null;
    } catch (error) {
      this.logger.error('Error resolving tenant from JWT', { error });
      return null;
    }
  }

  /**
   * Set resolution strategy
   */
  setStrategy(strategy: TenantResolutionStrategy): void {
    this.strategy = strategy;
    this.logger.info('Tenant resolution strategy changed', { strategy });
  }

  /**
   * Get current resolution strategy
   */
  getStrategy(): TenantResolutionStrategy {
    return this.strategy;
  }

  /**
   * Clear tenant cache
   */
  clearCache(): void {
    this.cache.clear();
    this.logger.info('Tenant cache cleared');
  }

  /**
   * Get tenant from cache
   */
  getCachedTenant(key: string): Tenant | null {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    // Check if expired
    if (Date.now() > cached.expires) {
      this.cache.delete(key);
      return null;
    }

    return cached.tenant;
  }

  /**
   * Cache tenant
   */
  cacheTenant(key: string, tenant: Tenant, ttl?: number): void {
    const expires = Date.now() + (ttl || this.defaultCacheTTL) * 1000;
    this.cache.set(key, { tenant, expires });
  }

  /**
   * Clean expired cache entries
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expires) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Start cache cleanup interval
   */
  startCacheCleanup(intervalMs: number = 60000): void {
    setInterval(() => {
      this.cleanExpiredCache();
    }, intervalMs);
  }
}
