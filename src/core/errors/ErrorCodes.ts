/**
 * Error codes for application errors
 */
export enum ErrorCode {
  // General errors (1000-1999)
  UNKNOWN_ERROR = 1000,
  INVALID_ARGUMENT = 1001,
  NOT_IMPLEMENTED = 1002,
  OPERATION_TIMEOUT = 1003,
  RESOURCE_NOT_FOUND = 1004,
  RESOURCE_ALREADY_EXISTS = 1005,
  PERMISSION_DENIED = 1006,
  UNAUTHORIZED = 1007,
  FORBIDDEN = 1008,
  NOT_FOUND = 1009,
  CONFLICT = 1010,
  INTERNAL_ERROR = 1011,

  // Database errors (2000-2999)
  DATABASE_ERROR = 2000,
  DATABASE_CONNECTION_ERROR = 2001,
  DATABASE_QUERY_ERROR = 2002,
  DATABASE_TRANSACTION_ERROR = 2003,
  DATABASE_CONSTRAINT_ERROR = 2004,

  // Network errors (3000-3999)
  NETWORK_ERROR = 3000,
  NETWORK_CONNECTION_ERROR = 3001,
  NETWORK_TIMEOUT_ERROR = 3002,
  NETWORK_REQUEST_ERROR = 3003,
  NETWORK_RESPONSE_ERROR = 3004,

  // Validation errors (4000-4999)
  VALIDATION_ERROR = 4000,
  VALIDATION_REQUIRED_FIELD = 4001,
  VALIDATION_INVALID_FORMAT = 4002,
  VALIDATION_INVALID_VALUE = 4003,
  VALIDATION_INVALID_TYPE = 4004,
  REQUIRED_FIELD = 4005,
  INVALID_FORMAT = 4006,
  INVALID_TYPE = 4007,
  INVALID_LENGTH = 4008,
  INVALID_VALUE = 4009,
  DUPLICATE_VALUE = 4010,
  INVALID_REFERENCE = 4011,

  // Workflow errors (5000-5999)
  WORKFLOW_ERROR = 5000,
  WORKFLOW_NOT_FOUND = 5001,
  WORKFLOW_EXECUTION_ERROR = 5002,
  WORKFLOW_NODE_ERROR = 5003,
  WORKFLOW_CONTEXT_ERROR = 5004,
  WORKFLOW_MEMORY_ERROR = 5005,

  // Node errors (6000-6999)
  NODE_ERROR = 6000,
  NODE_NOT_FOUND = 6001,
  NODE_EXECUTION_ERROR = 6002,
  NODE_CONFIGURATION_ERROR = 6003,

  // MCP errors (7000-7999)
  MCP_ERROR = 7000,
  MCP_REQUEST_ERROR = 7001,
  MCP_RESPONSE_ERROR = 7002,
  MCP_FUNCTION_ERROR = 7003,
  MCP_FUNCTION_NOT_FOUND = 7004,

  // Redis errors (8000-8999)
  REDIS_ERROR = 8000,
  REDIS_CONNECTION_ERROR = 8001,
  REDIS_COMMAND_ERROR = 8002,
  REDIS_TIMEOUT_ERROR = 8003,

  // LiteLLM errors (9000-9999)
  LITELLM_ERROR = 9000,
  LITELLM_API_ERROR = 9001,
  LITELLM_TIMEOUT_ERROR = 9002,
  LITELLM_RESPONSE_ERROR = 9003
}
