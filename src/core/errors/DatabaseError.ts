import { ApplicationError } from './ApplicationError';
import { ErrorCode } from './ErrorCodes';

/**
 * Database error details
 */
export interface DatabaseErrorDetails {
  /**
   * SQL query
   */
  query?: string;

  /**
   * Query parameters
   */
  params?: any[];

  /**
   * Database name
   */
  database?: string;

  /**
   * Table name
   */
  table?: string;

  /**
   * Column name
   */
  column?: string;

  /**
   * Constraint name
   */
  constraint?: string;
}

/**
 * Database error
 */
export class DatabaseError extends ApplicationError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: DatabaseErrorDetails,
    cause?: Error
  ) {
    super(message, ErrorCode.DATABASE_ERROR, details, cause);
  }
}

/**
 * Database connection error
 */
export class DatabaseConnectionError extends DatabaseError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: DatabaseErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.DATABASE_CONNECTION_ERROR;
  }
}

/**
 * Database query error
 */
export class DatabaseQueryError extends DatabaseError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: DatabaseErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.DATABASE_QUERY_ERROR;
  }
}

/**
 * Database transaction error
 */
export class DatabaseTransactionError extends DatabaseError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: DatabaseErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.DATABASE_TRANSACTION_ERROR;
  }
}

/**
 * Database constraint error
 */
export class DatabaseConstraintError extends DatabaseError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: DatabaseErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.DATABASE_CONSTRAINT_ERROR;
  }
}
