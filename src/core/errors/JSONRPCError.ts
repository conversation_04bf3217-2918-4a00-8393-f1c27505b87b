import { JSONRPCError as IJSONRPCError } from '../types/mcp/JSONRPCTypes';
import { MCPError, MCPErrorCode } from './MCPError';

/**
 * Convert MCP error to JSON-RPC error
 * @param error MCP error
 * @returns JSON-RPC error object
 */
export function toJSONRPCError(error: Error): IJSONRPCError {
  if (error instanceof MCPError) {
    return {
      code: error.code,
      message: error.message,
      data: error.data,
    };
  }

  // Default to internal error for unknown errors
  return {
    code: MCPErrorCode.INTERNAL_ERROR,
    message: error.message || 'Internal error',
    data: {
      name: error.name,
      stack: error.stack,
    },
  };
}
