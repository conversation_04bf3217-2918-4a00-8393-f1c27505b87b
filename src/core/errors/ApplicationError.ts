import { ErrorCode } from './ErrorCodes';

/**
 * Base class for application errors
 */
export class ApplicationError extends Error {
  /**
   * Error code
   */
  public code: ErrorCode;

  /**
   * Error details
   */
  public details?: Record<string, any>;

  /**
   * Original error
   */
  public readonly cause?: Error;

  /**
   * Constructor
   * @param message Error message
   * @param code Error code
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    details?: Record<string, any>,
    cause?: Error
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
    this.cause = cause;

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Get error details as JSON
   * @returns Error details as JSON
   */
  public toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      details: this.details,
      cause: this.cause
        ? {
            name: this.cause.name,
            message: this.cause.message,
            stack: this.cause.stack
          }
        : undefined,
      stack: this.stack
    };
  }

  /**
   * Get error details as string
   * @returns Error details as string
   */
  public toString(): string {
    return `${this.name} [${this.code}]: ${this.message}`;
  }
}
