import { ApplicationError } from './ApplicationError';
import { ErrorCode } from './ErrorCodes';

/**
 * Network error details
 */
export interface NetworkErrorDetails {
  /**
   * URL
   */
  url?: string;

  /**
   * HTTP method
   */
  method?: string;

  /**
   * HTTP status code
   */
  statusCode?: number;

  /**
   * HTTP status text
   */
  statusText?: string;

  /**
   * Request headers
   */
  requestHeaders?: Record<string, string>;

  /**
   * Request body
   */
  requestBody?: any;

  /**
   * Response headers
   */
  responseHeaders?: Record<string, string>;

  /**
   * Response body
   */
  responseBody?: any;
}

/**
 * Network error
 */
export class NetworkError extends ApplicationError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NetworkErrorDetails,
    cause?: Error
  ) {
    super(message, ErrorCode.NETWORK_ERROR, details, cause);
  }
}

/**
 * Network connection error
 */
export class NetworkConnectionError extends NetworkError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NetworkErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NETWORK_CONNECTION_ERROR;
  }
}

/**
 * Network timeout error
 */
export class NetworkTimeoutError extends NetworkError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NetworkErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NETWORK_TIMEOUT_ERROR;
  }
}

/**
 * Network request error
 */
export class NetworkRequestError extends NetworkError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NetworkErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NETWORK_REQUEST_ERROR;
  }
}

/**
 * Network response error
 */
export class NetworkResponseError extends NetworkError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NetworkErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NETWORK_RESPONSE_ERROR;
  }
}
