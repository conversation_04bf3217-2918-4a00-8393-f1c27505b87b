/**
 * Base MCP error class
 */
export class MCPError extends Error {
  /**
   * Error code
   */
  public readonly code: number;

  /**
   * Error data
   */
  public readonly data?: any;

  /**
   * Create a new MCP error
   * @param message Error message
   * @param code Error code
   * @param data Additional error data
   */
  constructor(message: string, code: number, data?: any) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.data = data;
  }
}

/**
 * Configuration validation error
 */
export class ConfigValidationError extends MCPError {
  constructor(message: string, data?: any) {
    super(message, MCPErrorCode.INVALID_TOOL_CONFIG, data);
  }
}

/**
 * MCP error codes
 */
export enum MCPErrorCode {
  // Generic errors
  INTERNAL_ERROR = -32603,
  INVALID_REQUEST = -32600,
  METHOD_NOT_FOUND = -32601,
  INVALID_PARAMS = -32602,
  PARSE_ERROR = -32700,

  // Custom errors
  TOOL_NOT_FOUND = 1001,
  TOOL_EXECUTION_ERROR = 1002,
  TOOL_DISABLED = 1003,
  INVALID_TOOL_CONFIG = 1004,
  SCRIPT_EXECUTION_ERROR = 1005,
  DATABASE_ERROR = 1006,
  INITIALIZATION_ERROR = 1007
}
