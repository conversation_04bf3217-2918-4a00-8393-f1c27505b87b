import { ApplicationError } from './ApplicationError';
import { ErrorCode } from './ErrorCodes';

/**
 * Workflow error details
 */
export interface WorkflowErrorDetails {
  /**
   * Workflow ID
   */
  workflowId?: string;

  /**
   * Workflow name
   */
  workflowName?: string;

  /**
   * Execution ID
   */
  executionId?: string;

  /**
   * Node ID
   */
  nodeId?: string;

  /**
   * Node name
   */
  nodeName?: string;

  /**
   * Node type
   */
  nodeType?: string;

  /**
   * Input data
   */
  input?: any;

  /**
   * Context data
   */
  context?: any;
}

/**
 * Workflow error
 */
export class WorkflowError extends ApplicationError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: WorkflowErrorDetails,
    cause?: Error
  ) {
    super(message, ErrorCode.WORKFLOW_ERROR, details, cause);
  }
}

/**
 * Workflow not found error
 */
export class WorkflowNotFoundError extends WorkflowError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: WorkflowErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.WORKFLOW_NOT_FOUND;
  }
}

/**
 * Workflow execution error
 */
export class WorkflowExecutionError extends WorkflowError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: WorkflowErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.WORKFLOW_EXECUTION_ERROR;
  }
}

/**
 * Workflow node error
 */
export class WorkflowNodeError extends WorkflowError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: WorkflowErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.WORKFLOW_NODE_ERROR;
  }
}

/**
 * Workflow context error
 */
export class WorkflowContextError extends WorkflowError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: WorkflowErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.WORKFLOW_CONTEXT_ERROR;
  }
}

/**
 * Workflow memory error
 */
export class WorkflowMemoryError extends WorkflowError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: WorkflowErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.WORKFLOW_MEMORY_ERROR;
  }
}
