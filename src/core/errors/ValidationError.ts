import { ApplicationError } from './ApplicationError';
import { ErrorCode } from './ErrorCodes';

/**
 * Validation error field
 */
export interface ValidationErrorField {
  /**
   * Field name
   */
  field: string;

  /**
   * Error message
   */
  message: string;

  /**
   * Error code
   */
  code: ErrorCode;

  /**
   * Field value
   */
  value?: any;

  /**
   * Expected value or pattern
   */
  expected?: any;
}

/**
 * Validation error details
 */
export interface ValidationErrorDetails {
  /**
   * Validation errors
   */
  errors: ValidationErrorField[];

  /**
   * Entity name
   */
  entity?: string;

  /**
   * Entity ID
   */
  entityId?: string;
}

/**
 * Validation error
 */
export class ValidationError extends ApplicationError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: ValidationErrorDetails,
    cause?: Error
  ) {
    super(message, ErrorCode.VALIDATION_ERROR, details, cause);
  }

  /**
   * Add validation error field
   * @param field Field name
   * @param message Error message
   * @param code Error code
   * @param value Field value
   * @param expected Expected value or pattern
   */
  public addError(
    field: string,
    message: string,
    code: ErrorCode = ErrorCode.VALIDATION_ERROR,
    value?: any,
    expected?: any
  ): void {
    if (!this.details) {
      this.details = { errors: [] } as ValidationErrorDetails;
    }
    
    (this.details as ValidationErrorDetails).errors.push({
      field,
      message,
      code,
      value,
      expected
    });
  }

  /**
   * Get validation errors
   * @returns Validation errors
   */
  public getErrors(): ValidationErrorField[] {
    return (this.details as ValidationErrorDetails)?.errors || [];
  }

  /**
   * Check if field has error
   * @param field Field name
   * @returns Whether field has error
   */
  public hasError(field: string): boolean {
    return this.getErrors().some(error => error.field === field);
  }

  /**
   * Get error for field
   * @param field Field name
   * @returns Error for field
   */
  public getError(field: string): ValidationErrorField | undefined {
    return this.getErrors().find(error => error.field === field);
  }
}
