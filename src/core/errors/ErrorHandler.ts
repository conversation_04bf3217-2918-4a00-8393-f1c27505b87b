import { injectable, inject } from 'inversify';
import type { ILogger } from '../interfaces/ILogger';
import { TYPES } from '../../types';
import { ApplicationError, ErrorCode } from './index';
import { DatabaseError, NetworkError, ValidationError, WorkflowError, NodeError } from './index';

/**
 * Error handler options
 */
export interface ErrorHandlerOptions {
  /**
   * Whether to include stack trace in error response
   */
  includeStackTrace?: boolean;

  /**
   * Whether to include error details in error response
   */
  includeErrorDetails?: boolean;

  /**
   * Whether to include cause in error response
   */
  includeErrorCause?: boolean;
}

/**
 * Error response
 */
export interface ErrorResponse {
  /**
   * Error name
   */
  error: string;

  /**
   * Error message
   */
  message: string;

  /**
   * Error code
   */
  code: number;

  /**
   * Error details
   */
  details?: Record<string, any>;

  /**
   * Stack trace
   */
  stack?: string;

  /**
   * Original error
   */
  cause?: {
    error: string;
    message: string;
    stack?: string;
  };
}

/**
 * Error handler for application errors
 */
@injectable()
export class ErrorHandler {
  private options: ErrorHandlerOptions;

  /**
   * Constructor
   * @param logger Logger
   * @param options Error handler options
   */
  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    options: ErrorHandlerOptions = {}
  ) {
    this.options = {
      includeStackTrace: process.env.NODE_ENV !== 'production',
      includeErrorDetails: true,
      includeErrorCause: process.env.NODE_ENV !== 'production',
      ...options
    };
  }

  /**
   * Handle error
   * @param error Error
   * @returns Error response
   */
  public handle(error: Error): ErrorResponse {
    // Log error
    this.logError(error);

    // Convert error to ApplicationError
    const appError = this.convertError(error);

    // Create error response
    return this.createErrorResponse(appError);
  }

  /**
   * Log error
   * @param error Error
   */
  private logError(error: Error): void {
    if (error instanceof ApplicationError) {
      this.logger.error(`${error.toString()}`, error);

      if (error.cause) {
        this.logger.error(`Caused by: ${error.cause.message}`, error.cause);
      }

      if (error.details) {
        this.logger.debug(`Error details: ${JSON.stringify(error.details)}`);
      }
    } else {
      this.logger.error(`Unhandled error: ${error.message}`, error);
    }
  }

  /**
   * Convert error to ApplicationError
   * @param error Error
   * @returns ApplicationError
   */
  private convertError(error: Error): ApplicationError {
    if (error instanceof ApplicationError) {
      return error;
    }

    // Try to determine error type from message or name
    const errorMessage = error.message.toLowerCase();
    const errorName = error.name.toLowerCase();

    if (
      errorName.includes('database') ||
      errorMessage.includes('database') ||
      errorName.includes('sql') ||
      errorMessage.includes('sql') ||
      errorName.includes('query') ||
      errorMessage.includes('query')
    ) {
      return new DatabaseError(error.message, undefined, error);
    }

    if (
      errorName.includes('network') ||
      errorMessage.includes('network') ||
      errorName.includes('http') ||
      errorMessage.includes('http') ||
      errorName.includes('request') ||
      errorMessage.includes('request') ||
      errorName.includes('response') ||
      errorMessage.includes('response')
    ) {
      return new NetworkError(error.message, undefined, error);
    }

    if (
      errorName.includes('validation') ||
      errorMessage.includes('validation') ||
      errorName.includes('invalid') ||
      errorMessage.includes('invalid')
    ) {
      return new ValidationError(error.message, undefined, error);
    }

    if (errorName.includes('workflow') || errorMessage.includes('workflow')) {
      return new WorkflowError(error.message, undefined, error);
    }

    if (errorName.includes('node') || errorMessage.includes('node')) {
      return new NodeError(error.message, undefined, error);
    }

    // Default to generic ApplicationError
    return new ApplicationError(error.message, ErrorCode.UNKNOWN_ERROR, undefined, error);
  }

  /**
   * Create error response
   * @param error ApplicationError
   * @returns Error response
   */
  private createErrorResponse(error: ApplicationError): ErrorResponse {
    const response: ErrorResponse = {
      error: error.name,
      message: error.message,
      code: error.code
    };

    // Include stack trace if enabled
    if (this.options.includeStackTrace) {
      response.stack = error.stack;
    }

    // Include error details if enabled
    if (this.options.includeErrorDetails && error.details) {
      response.details = error.details;
    }

    // Include cause if enabled
    if (this.options.includeErrorCause && error.cause) {
      response.cause = {
        error: error.cause.name,
        message: error.cause.message
      };

      if (this.options.includeStackTrace) {
        response.cause.stack = error.cause.stack;
      }
    }

    return response;
  }
}
