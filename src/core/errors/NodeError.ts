import { ApplicationError } from './ApplicationError';
import { ErrorCode } from './ErrorCodes';

/**
 * Node error details
 */
export interface NodeErrorDetails {
  /**
   * Node ID
   */
  nodeId?: string;

  /**
   * Node name
   */
  nodeName?: string;

  /**
   * Node type
   */
  nodeType?: string;

  /**
   * Node configuration
   */
  config?: Record<string, any>;

  /**
   * Input data
   */
  input?: any;

  /**
   * Context data
   */
  context?: any;
}

/**
 * Node error
 */
export class NodeError extends ApplicationError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NodeErrorDetails,
    cause?: Error
  ) {
    super(message, ErrorCode.NODE_ERROR, details, cause);
  }
}

/**
 * Node not found error
 */
export class NodeNotFoundError extends NodeError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NodeErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NODE_NOT_FOUND;
  }
}

/**
 * Node execution error
 */
export class NodeExecutionError extends NodeError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NodeErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NODE_EXECUTION_ERROR;
  }
}

/**
 * Node configuration error
 */
export class NodeConfigurationError extends NodeError {
  /**
   * Constructor
   * @param message Error message
   * @param details Error details
   * @param cause Original error
   */
  constructor(
    message: string,
    details?: NodeErrorDetails,
    cause?: Error
  ) {
    super(message, { ...details }, cause);
    this.code = ErrorCode.NODE_CONFIGURATION_ERROR;
  }
}
