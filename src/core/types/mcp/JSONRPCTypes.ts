/**
 * JSON-RPC 2.0 request
 */
export interface JSONRPCRequest {
  /**
   * JSON-RPC version
   */
  jsonrpc: '2.0';

  /**
   * Request ID
   */
  id: string | number | null;

  /**
   * Method name
   */
  method: string;

  /**
   * Method parameters
   */
  params?: any;
}

/**
 * JSON-RPC 2.0 success response
 */
export interface JSONRPCSuccessResponse {
  /**
   * JSON-RPC version
   */
  jsonrpc: '2.0';

  /**
   * Request ID
   */
  id: string | number | null;

  /**
   * Response result
   */
  result: any;
}

/**
 * JSON-RPC 2.0 error response
 */
export interface JSONRPCErrorResponse {
  /**
   * JSON-RPC version
   */
  jsonrpc: '2.0';

  /**
   * Request ID
   */
  id: string | number | null;

  /**
   * Error object
   */
  error: JSONRPCError;
}

/**
 * JSON-RPC 2.0 error object
 */
export interface JSONRPCError {
  /**
   * Error code
   */
  code: number;

  /**
   * Error message
   */
  message: string;

  /**
   * Error data
   */
  data?: any;
}

/**
 * JSON-RPC 2.0 response (success or error)
 */
export type JSONRPCResponse = JSONRPCSuccessResponse | JSONRPCErrorResponse;
