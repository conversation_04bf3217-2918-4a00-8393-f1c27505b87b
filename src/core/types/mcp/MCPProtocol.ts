/**
 * MCP Protocol types
 */

/**
 * MCP Server capabilities
 */
export interface MCPServerCapabilities {
  /**
   * Supported protocol version
   */
  protocolVersion: string;

  /**
   * Server name
   */
  serverName: string;

  /**
   * Server version
   */
  serverVersion: string;

  /**
   * Supported transports
   */
  supportedTransports: string[];
}

/**
 * MCP Client capabilities
 */
export interface MCPClientCapabilities {
  /**
   * Supported protocol version
   */
  protocolVersion: string;

  /**
   * Client name
   */
  clientName: string;

  /**
   * Client version
   */
  clientVersion: string;
}

/**
 * MCP Session information
 */
export interface MCPSession {
  /**
   * Session ID
   */
  id: string;

  /**
   * Client capabilities
   */
  clientCapabilities: MCPClientCapabilities;

  /**
   * Session creation timestamp
   */
  createdAt: Date;

  /**
   * Last activity timestamp
   */
  lastActivityAt: Date;
}
