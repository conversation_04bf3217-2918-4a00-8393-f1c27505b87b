/**
 * MCP Tool description
 */
export interface MCPToolDescription {
  /**
   * Tool name
   */
  name: string;

  /**
   * Tool description
   */
  description: string;

  /**
   * Tool input schema
   */
  parameters: any;
}

/**
 * MCP Tool list response
 */
export interface MCPToolListResponse {
  /**
   * List of available tools
   */
  functions: MCPToolDescription[];
}

/**
 * MCP Tool call request
 */
export interface MCPToolCallRequest {
  /**
   * Tool name
   */
  name: string;

  /**
   * Tool parameters
   */
  parameters: Record<string, any>;
}

/**
 * MCP Tool call response
 */
export interface MCPToolCallResponse {
  /**
   * Tool execution result
   */
  result: any;
}
