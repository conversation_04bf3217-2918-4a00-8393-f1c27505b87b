/**
 * Workflow node configuration
 */
export interface WorkflowNode {
  /**
   * Node ID
   */
  id: string;

  /**
   * Node type
   */
  type: string;

  /**
   * Node name
   */
  name: string;

  /**
   * Node configuration
   */
  config: Record<string, any>;
}

/**
 * Workflow edge configuration
 */
export interface WorkflowEdge {
  /**
   * Source node ID
   */
  source: string;

  /**
   * Target node ID
   */
  target: string;
}

/**
 * Workflow configuration
 */
export interface WorkflowConfig {
  /**
   * Workflow ID
   */
  id: string;

  /**
   * Workflow name
   */
  name: string;

  /**
   * Workflow description
   */
  description: string;

  /**
   * Workflow version
   */
  version: number;

  /**
   * Input schema (JSON Schema)
   */
  input_schema: any;

  /**
   * Output schema (JSON Schema)
   */
  output_schema?: any;

  /**
   * Nodes and edges configuration
   */
  nodes_config: {
    /**
     * Workflow nodes
     */
    nodes: WorkflowNode[];

    /**
     * Workflow edges
     */
    edges: WorkflowEdge[];
  };

  /**
   * Whether the workflow is enabled
   */
  enabled: boolean;

  /**
   * Creation timestamp
   */
  created_at: Date;

  /**
   * Last update timestamp
   */
  updated_at: Date;
}
