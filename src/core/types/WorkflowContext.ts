/**
 * Branch execution context for parallel processing
 */
export interface BranchExecutionContext {
  branchId: string;
  parentExecutionId: string;
  isolatedMemory: Record<string, any>;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  error?: Error;
}

/**
 * Workflow version information
 */
export interface WorkflowVersionInfo {
  version: number;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  changeType: string;
}

/**
 * Enhanced workflow execution context for Phase 2
 */
export interface WorkflowContext {
  /**
   * Execution ID
   */
  executionId: string;

  /**
   * Workflow ID
   */
  workflowId: string;

  /**
   * Input data
   */
  input: any;

  /**
   * Output data
   */
  output: any;

  /**
   * Node execution results
   */
  nodeResults: Record<string, any>;

  /**
   * Workflow variables
   */
  variables: Record<string, any>;

  /**
   * Execution start time
   */
  startedAt: Date;

  /**
   * JSONPath query helper methods (Phase 5)
   */
  query?: (path: string) => any;
  queryAll?: (path: string) => any[];
  set?: (path: string, value: any) => void;
  exists?: (path: string) => boolean;

  /**
   * Parallel branch contexts (Phase 2)
   */
  parallelBranches?: Map<string, BranchExecutionContext>;

  /**
   * Conditional evaluation results (Phase 2)
   */
  conditionalResults?: Map<string, boolean>;

  /**
   * Data transformation cache (Phase 2)
   */
  transformationCache?: Map<string, any>;

  /**
   * Workflow version information (Phase 2)
   */
  versionInfo?: WorkflowVersionInfo;

  /**
   * Execution metadata (Phase 2)
   */
  metadata?: {
    executionMode: 'sequential' | 'parallel' | 'hybrid';
    maxConcurrency?: number;
    timeout?: number;
    retryCount?: number;
    tags?: string[];
    priority?: number;
  };

  /**
   * Current execution path (Phase 2)
   */
  executionPath?: string[];

  /**
   * Synchronization points for parallel execution (Phase 2)
   */
  syncPoints?: Map<
    string,
    {
      waitingBranches: string[];
      completedBranches: string[];
      requiredBranches: string[];
      timeoutMs?: number;
    }
  >;

  /**
   * Error handling context (Phase 2)
   */
  errorContext?: {
    retryAttempts: number;
    lastError?: Error;
    errorHistory: Array<{
      nodeId: string;
      error: Error;
      timestamp: Date;
      retryAttempt: number;
    }>;
  };
}
