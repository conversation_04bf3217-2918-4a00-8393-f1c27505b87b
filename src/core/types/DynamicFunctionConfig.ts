/**
 * Configuration for a dynamic MCP function
 */
export interface DynamicFunctionConfig {
  /**
   * Unique identifier
   */
  id: string;

  /**
   * Function name
   */
  name: string;

  /**
   * Function description
   */
  description: string;

  /**
   * JSON Schema for function input
   */
  input_schema: any;

  /**
   * Handler configuration
   */
  handler_config: {
    /**
     * Handler type
     */
    type: 'script' | 'workflow';

    /**
     * JavaScript code for the handler (for script type)
     */
    script_content?: string;

    /**
     * Workflow ID (for workflow type)
     */
    workflow_id?: string;
  };

  /**
   * Whether the function is enabled
   */
  enabled: boolean;

  /**
   * Creation timestamp
   */
  created_at: Date;

  /**
   * Last update timestamp
   */
  updated_at: Date;
}
