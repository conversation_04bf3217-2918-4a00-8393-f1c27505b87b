import { IWorkflowMemory } from './IWorkflowMemory';
import { WorkflowContext } from '../types/WorkflowContext';

/**
 * Interface for Redis-based workflow memory
 */
export interface IRedisWorkflowMemory extends IWorkflowMemory {
  /**
   * Set TTL for workflow context
   * @param executionId Execution ID
   * @param ttlSeconds TTL in seconds
   */
  setContextTTL(executionId: string, ttlSeconds: number): Promise<void>;

  /**
   * Get workflow context by execution ID
   * @param executionId Execution ID
   * @returns Workflow context
   */
  getContext(executionId: string): Promise<WorkflowContext>;

  /**
   * Update workflow context
   * @param executionId Execution ID
   * @param context Workflow context
   */
  updateContext(executionId: string, context: WorkflowContext): Promise<void>;

  /**
   * Delete workflow context
   * @param executionId Execution ID
   */
  deleteContext(executionId: string): Promise<void>;
}
