/**
 * Interface for tracking workflow execution history and audit trail
 */

export interface WorkflowExecutionRecord {
  workflowId: string;
  executionId: string;
  tenantId?: string;
  status: 'started' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  inputData?: any;
  outputData?: any;
  errorMessage?: string;
  nodeExecutions?: NodeExecutionRecord[];
  correlationId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface NodeExecutionRecord {
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: 'started' | 'completed' | 'failed' | 'skipped';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  inputData?: any;
  outputData?: any;
  errorMessage?: string;
  retryCount?: number;
  metadata?: Record<string, any>;
}

export interface UserActionRecord {
  userId: string;
  tenantId?: string;
  action: string;
  resourceType: string;
  resourceId: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  details?: Record<string, any>;
  correlationId?: string;
}

export interface SystemEventRecord {
  eventType: string;
  eventCategory: 'workflow' | 'system' | 'security' | 'performance';
  severity: 'info' | 'warning' | 'error' | 'critical';
  timestamp: Date;
  source: string;
  message: string;
  details?: Record<string, any>;
  correlationId?: string;
  tenantId?: string;
}

export interface HistoryOptions {
  limit?: number;
  offset?: number;
  startDate?: Date;
  endDate?: Date;
  status?: string[];
  correlationId?: string;
  includeDetails?: boolean;
}

export interface ExecutionHistory {
  id: string;
  workflowId: string;
  executionId: string;
  tenantId?: string;
  status: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  errorMessage?: string;
  correlationId?: string;
  userId?: string;
  nodeCount?: number;
  successfulNodes?: number;
  failedNodes?: number;
  metadata?: Record<string, any>;
}

export interface UserActionHistory {
  id: string;
  userId: string;
  tenantId?: string;
  action: string;
  resourceType: string;
  resourceId: string;
  timestamp: Date;
  ipAddress?: string;
  details?: Record<string, any>;
  correlationId?: string;
}

export interface RetentionPolicy {
  maxAge: number; // days
  maxRecords?: number;
  categories?: string[];
  tenantSpecific?: boolean;
}

export interface CleanupResult {
  deletedRecords: number;
  categories: string[];
  executionTime: number;
  errors?: string[];
}

/**
 * History Tracker interface for comprehensive audit trail
 */
export interface IHistoryTracker {
  /**
   * Record workflow execution details
   */
  recordWorkflowExecution(execution: WorkflowExecutionRecord): Promise<void>;

  /**
   * Record user action for audit trail
   */
  recordUserAction(action: UserActionRecord): Promise<void>;

  /**
   * Record system event
   */
  recordSystemEvent(event: SystemEventRecord): Promise<void>;

  /**
   * Get workflow execution history
   */
  getExecutionHistory(workflowId: string, options?: HistoryOptions): Promise<ExecutionHistory[]>;

  /**
   * Get user action history
   */
  getUserActionHistory(userId: string, options?: HistoryOptions): Promise<UserActionHistory[]>;

  /**
   * Get system events
   */
  getSystemEvents(options?: HistoryOptions): Promise<SystemEventRecord[]>;

  /**
   * Search across all history types
   */
  searchHistory(query: string, options?: HistoryOptions): Promise<{
    executions: ExecutionHistory[];
    userActions: UserActionHistory[];
    systemEvents: SystemEventRecord[];
  }>;

  /**
   * Clean up old records based on retention policy
   */
  cleanup(retentionPolicy: RetentionPolicy): Promise<CleanupResult>;

  /**
   * Get history statistics
   */
  getStatistics(timeRange?: { start: Date; end: Date }): Promise<{
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    totalUserActions: number;
    totalSystemEvents: number;
    averageExecutionTime: number;
    topWorkflows: Array<{ workflowId: string; count: number }>;
    topUsers: Array<{ userId: string; count: number }>;
  }>;
}
