/**
 * Interface for Redis client
 */
export interface IRedisClient {
  /**
   * Initialize the Redis client
   */
  initialize(): Promise<void>;

  /**
   * Close the Redis client connection
   */
  close(): Promise<void>;

  /**
   * Get a value from Redis
   * @param key Key
   * @returns Value or null if not found
   */
  get(key: string): Promise<string | null>;

  /**
   * Set a value in Redis
   * @param key Key
   * @param value Value
   * @param ttl TTL in seconds (optional)
   */
  set(key: string, value: string, ttl?: number): Promise<void>;

  /**
   * Set a value with expiration time
   * @param key Key
   * @param ttl TTL in seconds
   * @param value Value
   */
  setex(key: string, ttl: number, value: string): Promise<void>;

  /**
   * Increment a numeric value
   * @param key Key
   * @returns New value after increment
   */
  incr(key: string): Promise<number>;

  /**
   * Decrement a numeric value
   * @param key Key
   * @returns New value after decrement
   */
  decr(key: string): Promise<number>;

  /**
   * Delete a key from Redis
   * @param key Key
   * @returns Number of keys deleted
   */
  del(key: string): Promise<number>;

  /**
   * Check if a key exists in Redis
   * @param key Key
   * @returns Whether the key exists
   */
  exists(key: string): Promise<boolean>;

  /**
   * Set expiration time for a key
   * @param key Key
   * @param ttl TTL in seconds
   * @returns Whether the operation was successful
   */
  expire(key: string, ttl: number): Promise<boolean>;

  /**
   * Get TTL for a key
   * @param key Key
   * @returns TTL in seconds, -1 if no expiration, -2 if key does not exist
   */
  ttl(key: string): Promise<number>;

  /**
   * Execute a Redis command
   * @param command Command
   * @param args Command arguments
   * @returns Command result
   */
  executeCommand(command: string, args: any[]): Promise<any>;

  /**
   * Publish a message to a channel
   * @param channel Channel
   * @param message Message
   * @returns Number of clients that received the message
   */
  publish(channel: string, message: string): Promise<number>;

  /**
   * Subscribe to a channel
   * @param channel Channel
   * @param callback Callback function
   */
  subscribe(channel: string, callback: (message: string) => void): Promise<void>;

  /**
   * Unsubscribe from a channel
   * @param channel Channel
   */
  unsubscribe(channel: string): Promise<void>;

  /**
   * Push element to the left of a list
   * @param key List key
   * @param value Value to push
   * @returns Length of the list after the push operation
   */
  lpush(key: string, value: string): Promise<number>;

  /**
   * Push element to the right of a list
   * @param key List key
   * @param value Value to push
   * @returns Length of the list after the push operation
   */
  rpush(key: string, value: string): Promise<number>;

  /**
   * Pop element from the left of a list
   * @param key List key
   * @returns Popped element or null if list is empty
   */
  lpop(key: string): Promise<string | null>;

  /**
   * Pop element from the right of a list
   * @param key List key
   * @returns Popped element or null if list is empty
   */
  rpop(key: string): Promise<string | null>;

  /**
   * Get the length of a list
   * @param key List key
   * @returns Length of the list
   */
  llen(key: string): Promise<number>;

  /**
   * Get a field value from a hash
   * @param key Hash key
   * @param field Field name
   * @returns Field value or null if not found
   */
  hget(key: string, field: string): Promise<string | null>;

  /**
   * Set a field value in a hash
   * @param key Hash key
   * @param field Field name
   * @param value Field value
   * @returns Number of fields that were added
   */
  hset(key: string, field: string, value: string): Promise<number>;

  /**
   * Delete a field from a hash
   * @param key Hash key
   * @param field Field name
   * @returns Number of fields that were removed
   */
  hdel(key: string, field: string): Promise<number>;

  /**
   * Get all fields and values from a hash
   * @param key Hash key
   * @returns Object with all field-value pairs
   */
  hgetall(key: string): Promise<Record<string, string>>;

  /**
   * Trim a list to the specified range
   * @param key List key
   * @param start Start index
   * @param stop Stop index
   */
  ltrim(key: string, start: number, stop: number): Promise<void>;

  /**
   * Get a range of elements from a list
   * @param key List key
   * @param start Start index
   * @param stop Stop index
   * @returns Array of elements
   */
  lrange(key: string, start: number, stop: number): Promise<string[]>;

  /**
   * Find all keys matching a pattern
   * @param pattern Pattern to match
   * @returns Array of matching keys
   */
  keys(pattern: string): Promise<string[]>;
}
