import { INode } from './INode';

/**
 * Options for LiteLLM completion
 */
export interface LiteLLMCompletionOptions {
  /**
   * Model to use
   */
  model?: string;

  /**
   * Maximum number of tokens to generate
   */
  maxTokens?: number;

  /**
   * Sampling temperature
   */
  temperature?: number;

  /**
   * Top-p sampling
   */
  topP?: number;

  /**
   * Presence penalty
   */
  presencePenalty?: number;

  /**
   * Frequency penalty
   */
  frequencyPenalty?: number;

  /**
   * Stop sequences
   */
  stop?: string[];

  /**
   * System message
   */
  systemMessage?: string;

  /**
   * Whether to stream the response
   */
  stream?: boolean;
}

/**
 * Options for LiteLLM embedding
 */
export interface LiteLLMEmbeddingOptions {
  /**
   * Model to use
   */
  model?: string;

  /**
   * Encoding format
   */
  encodingFormat?: 'float' | 'base64';
}

/**
 * Message for LiteLLM chat completion
 */
export interface LiteLLMMessage {
  /**
   * Message role
   */
  role: 'system' | 'user' | 'assistant' | 'function';

  /**
   * Message content
   */
  content: string;

  /**
   * Function name (for function messages)
   */
  name?: string;
}

/**
 * Interface for LiteLLM node
 */
export interface ILiteLLMNode extends INode {
  /**
   * Generate a completion
   * @param prompt Prompt text
   * @param options Completion options
   * @returns Generated text
   */
  generateCompletion(prompt: string, options?: LiteLLMCompletionOptions): Promise<string>;

  /**
   * Generate a chat completion
   * @param messages Chat messages
   * @param options Completion options
   * @returns Generated message
   */
  generateChatCompletion(messages: LiteLLMMessage[], options?: LiteLLMCompletionOptions): Promise<string>;

  /**
   * Generate an embedding
   * @param text Text to embed
   * @param options Embedding options
   * @returns Embedding vector
   */
  generateEmbedding(text: string, options?: LiteLLMEmbeddingOptions): Promise<number[]>;
}
