/**
 * Interface for Alert Management System
 */

export interface AlertRule {
  id?: string;
  name: string;
  description?: string;
  enabled: boolean;
  tenantId?: string;
  
  // Rule conditions
  conditions: AlertCondition[];
  operator: 'AND' | 'OR'; // How to combine multiple conditions
  
  // Notification settings
  notifications: NotificationChannel[];
  
  // Escalation settings
  escalation?: EscalationPolicy;
  
  // Timing settings
  evaluationInterval: number; // seconds
  cooldownPeriod: number; // seconds to wait before re-alerting
  
  // Metadata
  tags?: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AlertCondition {
  type: 'metric' | 'event' | 'log' | 'custom';
  metric?: string; // For metric-based conditions
  operator: '>' | '<' | '>=' | '<=' | '==' | '!=' | 'contains' | 'not_contains';
  threshold: number | string;
  timeWindow?: number; // seconds
  aggregation?: 'avg' | 'sum' | 'count' | 'min' | 'max';
  
  // For event-based conditions
  eventType?: string;
  eventCategory?: string;
  severity?: string;
  
  // For log-based conditions
  logLevel?: string;
  logMessage?: string;
  
  // For custom conditions
  customQuery?: string;
}

export interface NotificationChannel {
  type: 'email' | 'slack' | 'teams' | 'webhook' | 'sms';
  enabled: boolean;
  
  // Email settings
  emails?: string[];
  emailTemplate?: string;
  
  // Slack settings
  slackWebhook?: string;
  slackChannel?: string;
  slackUsername?: string;
  
  // Teams settings
  teamsWebhook?: string;
  
  // Webhook settings
  webhookUrl?: string;
  webhookHeaders?: Record<string, string>;
  webhookMethod?: 'POST' | 'PUT';
  
  // SMS settings
  phoneNumbers?: string[];
  smsProvider?: string;
  
  // Common settings
  retryCount?: number;
  retryDelay?: number;
}

export interface EscalationPolicy {
  enabled: boolean;
  levels: EscalationLevel[];
}

export interface EscalationLevel {
  level: number;
  delayMinutes: number;
  notifications: NotificationChannel[];
  requireAcknowledgment?: boolean;
}

export interface Alert {
  id?: string;
  ruleId: string;
  ruleName: string;
  tenantId?: string;
  
  // Alert details
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  
  // Timing
  triggeredAt: Date;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  
  // Context
  conditions: AlertCondition[];
  actualValues: Record<string, any>;
  correlationId?: string;
  
  // Escalation
  escalationLevel?: number;
  escalatedAt?: Date;
  
  // Acknowledgment
  acknowledgedBy?: string;
  acknowledgmentNote?: string;
  
  // Resolution
  resolvedBy?: string;
  resolutionNote?: string;
  autoResolved?: boolean;
  
  // Metadata
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface AlertSummary {
  total: number;
  active: number;
  acknowledged: number;
  resolved: number;
  suppressed: number;
  byPriority: Record<string, number>;
  bySeverity: Record<string, number>;
  byTenant: Record<string, number>;
}

export interface AlertQuery {
  ruleIds?: string[];
  tenantIds?: string[];
  status?: string[];
  severity?: string[];
  priority?: string[];
  startDate?: Date;
  endDate?: Date;
  tags?: string[];
  limit?: number;
  offset?: number;
  sortBy?: 'triggeredAt' | 'severity' | 'priority';
  sortOrder?: 'ASC' | 'DESC';
}

export interface NotificationResult {
  channelType: string;
  success: boolean;
  message?: string;
  deliveredAt?: Date;
  retryCount?: number;
  error?: string;
}

export interface AlertEvaluationResult {
  ruleId: string;
  triggered: boolean;
  conditions: Array<{
    condition: AlertCondition;
    met: boolean;
    actualValue: any;
    threshold: any;
  }>;
  evaluatedAt: Date;
  nextEvaluation?: Date;
}

/**
 * Alert Manager interface for comprehensive alerting system
 */
export interface IAlertManager {
  /**
   * Create a new alert rule
   */
  createRule(rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<string>;

  /**
   * Update an existing alert rule
   */
  updateRule(ruleId: string, rule: Partial<AlertRule>): Promise<void>;

  /**
   * Delete an alert rule
   */
  deleteRule(ruleId: string): Promise<void>;

  /**
   * Get alert rule by ID
   */
  getRule(ruleId: string): Promise<AlertRule | null>;

  /**
   * Get all alert rules
   */
  getRules(tenantId?: string): Promise<AlertRule[]>;

  /**
   * Enable/disable an alert rule
   */
  toggleRule(ruleId: string, enabled: boolean): Promise<void>;

  /**
   * Evaluate all active alert rules
   */
  evaluateRules(): Promise<AlertEvaluationResult[]>;

  /**
   * Evaluate a specific alert rule
   */
  evaluateRule(ruleId: string): Promise<AlertEvaluationResult>;

  /**
   * Send an alert notification
   */
  sendAlert(alert: Alert): Promise<NotificationResult[]>;

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string, note?: string): Promise<void>;

  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string, resolvedBy: string, note?: string): Promise<void>;

  /**
   * Suppress an alert
   */
  suppressAlert(alertId: string, suppressedBy: string, note?: string): Promise<void>;

  /**
   * Get alerts with filtering
   */
  getAlerts(query?: AlertQuery): Promise<Alert[]>;

  /**
   * Get alert by ID
   */
  getAlert(alertId: string): Promise<Alert | null>;

  /**
   * Get alert summary statistics
   */
  getAlertSummary(tenantId?: string, timeRange?: { start: Date; end: Date }): Promise<AlertSummary>;

  /**
   * Test notification channel
   */
  testNotificationChannel(channel: NotificationChannel, testMessage?: string): Promise<NotificationResult>;

  /**
   * Get alert history for a specific rule
   */
  getAlertHistory(ruleId: string, limit?: number): Promise<Alert[]>;

  /**
   * Cleanup old resolved alerts
   */
  cleanupAlerts(retentionDays: number): Promise<{ deletedCount: number }>;

  /**
   * Start the alert evaluation scheduler
   */
  startScheduler(): Promise<void>;

  /**
   * Stop the alert evaluation scheduler
   */
  stopScheduler(): Promise<void>;
}
