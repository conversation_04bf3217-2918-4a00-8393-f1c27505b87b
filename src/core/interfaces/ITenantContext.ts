import { Tenant } from '../../infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../../infrastructure/database/entities/TenantUser.entity';

/**
 * Interface for tenant context management
 * Provides async context propagation for tenant isolation
 */
export interface ITenantContext {
  /**
   * Get current tenant from context
   * @returns Current tenant or null if not set
   */
  getCurrentTenant(): Tenant | null;

  /**
   * Set current tenant in context
   * @param tenant Tenant to set
   */
  setCurrentTenant(tenant: Tenant): void;

  /**
   * Clear current tenant from context
   */
  clearCurrentTenant(): void;

  /**
   * Execute function with tenant context
   * @param tenant Tenant to set for execution
   * @param fn Function to execute
   * @returns Function result
   */
  withTenant<T>(tenant: Tenant, fn: () => Promise<T>): Promise<T>;

  /**
   * Get current tenant ID
   * @returns Tenant ID or null if not set
   */
  getTenantId(): string | null;

  /**
   * Require tenant context (throws if not set)
   * @returns Current tenant
   * @throws Error if no tenant is set
   */
  requireTenant(): Tenant;

  /**
   * Get current user from context
   * @returns Current user or null if not set
   */
  getCurrentUser(): TenantUser | null;

  /**
   * Set current user in context
   * @param user User to set
   */
  setCurrentUser(user: TenantUser): void;

  /**
   * Clear current user from context
   */
  clearCurrentUser(): void;

  /**
   * Execute function with user context
   * @param user User to set for execution
   * @param fn Function to execute
   * @returns Function result
   */
  withUser<T>(user: TenantUser, fn: () => Promise<T>): Promise<T>;

  /**
   * Get current user ID
   * @returns User ID or null if not set
   */
  getUserId(): string | null;

  /**
   * Require user context (throws if not set)
   * @returns Current user
   * @throws Error if no user is set
   */
  requireUser(): TenantUser;

  /**
   * Check if current user has permission
   * @param resource Resource name
   * @param action Action name
   * @returns True if user has permission
   */
  hasPermission(resource: string, action: string): boolean;

  /**
   * Require permission (throws if not authorized)
   * @param resource Resource name
   * @param action Action name
   * @throws Error if user doesn't have permission
   */
  requirePermission(resource: string, action: string): void;

  /**
   * Check if tenant context is set
   * @returns True if tenant context is set
   */
  hasTenantContext(): boolean;
}
