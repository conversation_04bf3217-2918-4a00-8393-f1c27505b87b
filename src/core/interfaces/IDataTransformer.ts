import { WorkflowContext } from '../types/WorkflowContext';

/**
 * Data transformation configuration
 */
export interface DataTransformation {
  type: 'mapping' | 'aggregation' | 'filtering' | 'sorting' | 'grouping' | 'custom';
  config: Record<string, any>;
  inputSchema?: any;
  outputSchema?: any;
}

/**
 * Field mapping configuration
 */
export interface FieldMapping {
  source: string;
  target: string;
  transform?: string;
  defaultValue?: any;
  required?: boolean;
  validation?: ValidationRule;
}

/**
 * Validation rule for field mapping
 */
export interface ValidationRule {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date' | 'email' | 'url' | 'regex';
  pattern?: string;
  min?: number;
  max?: number;
  required?: boolean;
  custom?: (value: any) => boolean;
}

/**
 * Aggregation operation configuration
 */
export interface AggregationOperation {
  type: 'sum' | 'count' | 'average' | 'min' | 'max' | 'distinct' | 'group' | 'custom';
  field: string;
  alias?: string;
  filter?: string;
  customFunction?: (values: any[]) => any;
}

/**
 * Data filtering configuration
 */
export interface DataFilter {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in' | 'regex';
  value: any;
  caseSensitive?: boolean;
}

/**
 * JSONPath extraction configuration
 */
export interface JSONPathConfig {
  expression: string;
  defaultValue?: any;
  required?: boolean;
  multiple?: boolean;
}

/**
 * Schema mapping configuration
 */
export interface SchemaMapping {
  sourceSchema: any;
  targetSchema: any;
  mappings: FieldMapping[];
  strictMode?: boolean;
  allowAdditionalFields?: boolean;
}

/**
 * Transformation result
 */
export interface TransformationResult {
  data: any;
  metadata: {
    recordsProcessed: number;
    recordsTransformed: number;
    recordsSkipped: number;
    errors: string[];
    warnings: string[];
    executionTime: number;
  };
}

/**
 * Interface for transforming data between workflow nodes
 */
export interface IDataTransformer {
  /**
   * Transform data using specified transformation
   * @param data Input data to transform
   * @param transformation Transformation configuration
   * @param context Workflow execution context
   * @returns Transformed data
   */
  transformData(
    data: any,
    transformation: DataTransformation,
    context?: WorkflowContext
  ): Promise<TransformationResult>;

  /**
   * Map fields from source to target schema
   * @param data Input data
   * @param mappings Array of field mappings
   * @param context Workflow execution context
   * @returns Mapped data
   */
  mapFields(
    data: any,
    mappings: FieldMapping[],
    context?: WorkflowContext
  ): Promise<any>;

  /**
   * Aggregate data using specified operations
   * @param data Array of data records
   * @param operations Array of aggregation operations
   * @param groupBy Optional grouping fields
   * @returns Aggregated data
   */
  aggregateData(
    data: any[],
    operations: AggregationOperation[],
    groupBy?: string[]
  ): Promise<any>;

  /**
   * Filter data based on specified criteria
   * @param data Input data array
   * @param filters Array of filter conditions
   * @returns Filtered data
   */
  filterData(
    data: any[],
    filters: DataFilter[]
  ): Promise<any[]>;

  /**
   * Extract data using JSONPath expressions
   * @param data Input data object
   * @param config JSONPath configuration
   * @returns Extracted data
   */
  extractWithJSONPath(
    data: any,
    config: JSONPathConfig
  ): Promise<any>;

  /**
   * Transform data schema from source to target
   * @param data Input data
   * @param schemaMapping Schema mapping configuration
   * @returns Schema-transformed data
   */
  transformSchema(
    data: any,
    schemaMapping: SchemaMapping
  ): Promise<TransformationResult>;

  /**
   * Validate data against schema
   * @param data Data to validate
   * @param schema Validation schema
   * @returns Validation result
   */
  validateData(
    data: any,
    schema: any
  ): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }>;

  /**
   * Register custom transformation function
   * @param name Function name
   * @param func Transformation function
   * @returns Registration success
   */
  registerTransformFunction(
    name: string,
    func: (data: any, config: any) => any
  ): Promise<boolean>;

  /**
   * Get available transformation types
   * @returns Array of supported transformation types
   */
  getAvailableTransformations(): string[];
}
