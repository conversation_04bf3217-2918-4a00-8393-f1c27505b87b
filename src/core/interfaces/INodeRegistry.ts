import { INode } from './INode';

/**
 * Factory function for creating nodes
 */
export type NodeFactory = (config: Record<string, any>) => INode;

/**
 * Interface for the node registry
 */
export interface INodeRegistry {
  /**
   * Register a node type with a factory function
   * @param type Node type
   * @param nodeFactory Factory function for creating nodes of this type
   */
  registerNode(type: string, nodeFactory: NodeFactory): void;

  /**
   * Get a node instance for the given type and configuration
   * @param type Node type
   * @param config Node configuration
   * @returns Node instance
   */
  getNode(type: string, config: Record<string, any>): INode;

  /**
   * Get all registered node types
   * @returns Array of node types
   */
  getRegisteredNodeTypes(): string[];
}
