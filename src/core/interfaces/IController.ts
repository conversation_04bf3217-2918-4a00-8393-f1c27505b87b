import { Request, Response } from 'express';

/**
 * Base interface for all controllers
 * Defines common methods that all controllers should implement
 */
export interface IController {
  /**
   * Get all entities
   * @param req Express request
   * @param res Express response
   */
  getAll(req: Request, res: Response): Promise<void>;

  /**
   * Get entity by ID
   * @param req Express request
   * @param res Express response
   */
  getById(req: Request, res: Response): Promise<void>;

  /**
   * Create a new entity
   * @param req Express request
   * @param res Express response
   */
  create(req: Request, res: Response): Promise<void>;

  /**
   * Update an existing entity
   * @param req Express request
   * @param res Express response
   */
  update(req: Request, res: Response): Promise<void>;

  /**
   * Delete an entity
   * @param req Express request
   * @param res Express response
   */
  delete(req: Request, res: Response): Promise<void>;
}
