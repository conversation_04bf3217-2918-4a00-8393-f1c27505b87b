import { INode } from './INode';
import { WorkflowContext } from '../types/WorkflowContext';
import { ConditionalExpression } from './IExecutionCoordinator';
import { LogicResult } from './IConditionalProcessor';

/**
 * Base interface for logic nodes that control workflow flow
 */
export interface ILogicNode extends INode {
  /**
   * Evaluate the logic and determine execution path
   * @param context Workflow execution context
   * @returns Logic evaluation result
   */
  evaluateLogic(context: WorkflowContext): Promise<LogicResult>;

  /**
   * Get the next nodes to execute based on logic result
   * @param logicResult Result from logic evaluation
   * @returns Array of next node IDs to execute
   */
  getNextNodes(logicResult: LogicResult): Promise<string[]>;
}

/**
 * IF/ELSE node for conditional branching
 */
export interface IIfElseNode extends ILogicNode {
  condition: ConditionalExpression;
  trueNodes: string[];
  falseNodes: string[];
  
  /**
   * Evaluate the condition and route to appropriate branch
   * @param context Workflow execution context
   * @returns Logic result with selected branch
   */
  evaluateCondition(context: WorkflowContext): Promise<LogicResult>;
}

/**
 * Switch node for multi-way branching
 */
export interface ISwitchNode extends ILogicNode {
  expression: string;
  cases: Record<string, string[]>;
  defaultCase: string[];
  
  /**
   * Evaluate the expression and select matching case
   * @param context Workflow execution context
   * @returns Logic result with selected case
   */
  evaluateSwitch(context: WorkflowContext): Promise<LogicResult>;
}

/**
 * Merge node for combining parallel branches
 */
export interface IMergeNode extends ILogicNode {
  waitForAll: boolean;
  mergeStrategy: 'combine' | 'override' | 'append' | 'custom';
  timeout?: number;
  customMergeFunction?: string;
  
  /**
   * Merge data from multiple branches
   * @param branchData Data from parallel branches
   * @param context Workflow execution context
   * @returns Merged data result
   */
  mergeData(
    branchData: Record<string, any>,
    context: WorkflowContext
  ): Promise<any>;
  
  /**
   * Check if all required branches have completed
   * @param completedBranches Array of completed branch IDs
   * @param context Workflow execution context
   * @returns Whether merge can proceed
   */
  canMerge(
    completedBranches: string[],
    context: WorkflowContext
  ): Promise<boolean>;
}

/**
 * Loop node for iterative execution
 */
export interface ILoopNode extends ILogicNode {
  loopType: 'for' | 'while' | 'foreach';
  condition?: ConditionalExpression;
  iterations?: number;
  iterableField?: string;
  loopNodes: string[];
  
  /**
   * Initialize loop execution
   * @param context Workflow execution context
   * @returns Loop initialization result
   */
  initializeLoop(context: WorkflowContext): Promise<{
    shouldExecute: boolean;
    iterationCount: number;
    iterableData?: any[];
  }>;
  
  /**
   * Check if loop should continue
   * @param currentIteration Current iteration number
   * @param context Workflow execution context
   * @returns Whether to continue loop
   */
  shouldContinueLoop(
    currentIteration: number,
    context: WorkflowContext
  ): Promise<boolean>;
}

/**
 * Parallel node for concurrent execution
 */
export interface IParallelNode extends ILogicNode {
  parallelBranches: Array<{
    id: string;
    nodes: string[];
    priority?: number;
    timeout?: number;
  }>;
  maxConcurrency?: number;
  failFast?: boolean;
  
  /**
   * Execute branches in parallel
   * @param context Workflow execution context
   * @returns Parallel execution result
   */
  executeParallel(context: WorkflowContext): Promise<{
    results: Record<string, any>;
    failures: Record<string, Error>;
    executionTime: number;
  }>;
}

/**
 * Delay node for timed execution
 */
export interface IDelayNode extends ILogicNode {
  delayType: 'fixed' | 'dynamic' | 'cron';
  delay?: number;
  delayExpression?: string;
  cronExpression?: string;
  
  /**
   * Calculate delay duration
   * @param context Workflow execution context
   * @returns Delay duration in milliseconds
   */
  calculateDelay(context: WorkflowContext): Promise<number>;
  
  /**
   * Execute delay
   * @param delayMs Delay duration in milliseconds
   * @returns Promise that resolves after delay
   */
  executeDelay(delayMs: number): Promise<void>;
}
