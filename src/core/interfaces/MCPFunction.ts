/**
 * Interface for MCP Function
 */
export interface MCPFunction {
  /**
   * Unique identifier for the function
   */
  readonly id: string;

  /**
   * Function name
   */
  readonly name: string;

  /**
   * Function description
   */
  readonly description: string;

  /**
   * JSON Schema for function input
   */
  readonly inputSchema: any;

  /**
   * Execute the function with provided parameters
   * @param params Function parameters
   */
  execute(params: Record<string, any>): Promise<any>;

  /**
   * Check if the function is enabled
   */
  isEnabled(): boolean;

  /**
   * Enable the function
   */
  enable(): void;

  /**
   * Disable the function
   */
  disable(): void;
}
