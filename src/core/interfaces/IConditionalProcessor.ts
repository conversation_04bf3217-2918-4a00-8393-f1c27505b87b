import { WorkflowContext } from '../types/WorkflowContext';
import { ConditionalExpression, WorkflowNode } from './IExecutionCoordinator';

/**
 * Logic evaluation result
 */
export interface LogicResult {
  result: boolean;
  evaluatedConditions: Record<string, boolean>;
  executionPath: string[];
  metadata?: Record<string, any>;
}

/**
 * Condition evaluation context
 */
export interface ConditionEvaluationContext {
  data: any;
  variables: Record<string, any>;
  nodeResults: Record<string, any>;
  functions: Record<string, Function>;
}

/**
 * Route selection result
 */
export interface RouteSelection {
  selectedNodes: WorkflowNode[];
  rejectedNodes: WorkflowNode[];
  reason: string;
  conditionResults: Record<string, boolean>;
}

/**
 * Interface for processing conditional logic in workflows
 */
export interface IConditionalProcessor {
  /**
   * Evaluate a simple string expression
   * @param expression String expression to evaluate
   * @param context Evaluation context
   * @returns Boolean result
   */
  evaluate(expression: string, context: any): Promise<boolean>;

  /**
   * Evaluate a conditional expression
   * @param expression Conditional expression to evaluate
   * @param context Workflow execution context
   * @returns Logic evaluation result
   */
  evaluateCondition(
    expression: ConditionalExpression,
    context: WorkflowContext
  ): Promise<LogicResult>;

  /**
   * Route workflow execution based on condition
   * @param condition Boolean condition result
   * @param trueNodes Nodes to execute if condition is true
   * @param falseNodes Nodes to execute if condition is false
   * @returns Selected workflow nodes
   */
  routeExecution(
    condition: boolean,
    trueNodes: WorkflowNode[],
    falseNodes: WorkflowNode[]
  ): Promise<RouteSelection>;

  /**
   * Evaluate multiple conditions with logical operators
   * @param conditions Array of conditional expressions
   * @param logic Logical operator ('AND' | 'OR')
   * @param context Workflow execution context
   * @returns Combined logic result
   */
  evaluateMultipleConditions(
    conditions: ConditionalExpression[],
    logic: 'AND' | 'OR',
    context: WorkflowContext
  ): Promise<LogicResult>;

  /**
   * Create evaluation context from workflow context
   * @param workflowContext Workflow execution context
   * @returns Condition evaluation context
   */
  createEvaluationContext(workflowContext: WorkflowContext): Promise<ConditionEvaluationContext>;

  /**
   * Validate conditional expression syntax
   * @param expression Conditional expression to validate
   * @returns Validation result with errors if any
   */
  validateExpression(expression: ConditionalExpression): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }>;

  /**
   * Register custom condition function
   * @param name Function name
   * @param func Function implementation
   * @returns Registration success
   */
  registerConditionFunction(
    name: string,
    func: (value: any, ...args: any[]) => boolean
  ): Promise<boolean>;

  /**
   * Get available condition operators
   * @returns Array of supported operators
   */
  getAvailableOperators(): string[];

  /**
   * Optimize conditional expression for performance
   * @param expression Conditional expression to optimize
   * @returns Optimized expression
   */
  optimizeExpression(expression: ConditionalExpression): Promise<ConditionalExpression>;
}
