import { WorkflowContext } from '../types/WorkflowContext';

/**
 * Execution branch for parallel processing
 */
export interface ExecutionBranch {
  id: string;
  nodeIds: string[];
  dependencies: string[];
  priority: number;
}

/**
 * Result from parallel branch execution
 */
export interface BranchResult {
  branchId: string;
  nodeResults: Record<string, any>;
  status: 'completed' | 'failed' | 'cancelled';
  error?: Error;
  executionTime: number;
}

/**
 * Result from merged parallel execution
 */
export interface MergedResult {
  combinedData: any;
  branchResults: BranchResult[];
  mergeStrategy: 'combine' | 'override' | 'append';
}

/**
 * Parallel execution result
 */
export interface ParallelExecutionResult {
  results: BranchResult[];
  totalExecutionTime: number;
  successCount: number;
  failureCount: number;
}

/**
 * Conditional expression for logic evaluation
 */
export interface ConditionalExpression {
  type: 'simple' | 'complex';
  field: string;
  operator:
    | 'equals'
    | 'not_equals'
    | 'greater_than'
    | 'less_than'
    | 'greater_than_or_equal'
    | 'less_than_or_equal'
    | 'contains'
    | 'not_contains'
    | 'starts_with'
    | 'ends_with'
    | 'regex'
    | 'in'
    | 'not_in'
    | 'exists'
    | 'not_exists'
    | 'is_null'
    | 'is_not_null'
    | 'is_empty'
    | 'is_not_empty';
  value: any;
  conditions?: ConditionalExpression[];
  logic?: 'AND' | 'OR';
}

/**
 * Workflow definition for execution
 */
export interface WorkflowDefinition {
  id: string;
  name: string;
  version: number;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  metadata?: Record<string, any>;
}

/**
 * Workflow node definition
 */
export interface WorkflowNode {
  id: string;
  type: string;
  name: string;
  config: Record<string, any>;
  position?: { x: number; y: number };
}

/**
 * Workflow edge definition
 */
export interface WorkflowEdge {
  source: string;
  target: string;
  condition?: ConditionalExpression;
}

/**
 * Workflow execution result
 */
export interface WorkflowResult {
  executionId: string;
  status: 'completed' | 'failed' | 'cancelled';
  result: any;
  error?: Error;
  executionTime: number;
  nodeExecutions: Record<string, any>;
}

/**
 * Interface for coordinating workflow execution with advanced capabilities
 */
export interface IExecutionCoordinator {
  /**
   * Execute a complete workflow with advanced coordination
   * @param workflow Workflow definition
   * @param context Workflow execution context
   * @returns Workflow execution result
   */
  executeWorkflow(workflow: WorkflowDefinition, context: WorkflowContext): Promise<WorkflowResult>;

  /**
   * Execute multiple nodes in parallel
   * @param nodes Array of workflow nodes to execute
   * @param context Workflow execution context
   * @returns Parallel execution result
   */
  executeParallel(
    nodes: WorkflowNode[],
    context: WorkflowContext
  ): Promise<ParallelExecutionResult>;

  /**
   * Evaluate a conditional expression
   * @param condition Conditional expression to evaluate
   * @param context Workflow execution context
   * @returns Boolean result of condition evaluation
   */
  evaluateCondition(condition: ConditionalExpression, context: WorkflowContext): Promise<boolean>;

  /**
   * Determine execution order based on dependencies
   * @param nodes Array of workflow nodes
   * @param edges Array of workflow edges
   * @returns Ordered execution plan
   */
  planExecution(nodes: WorkflowNode[], edges: WorkflowEdge[]): Promise<ExecutionBranch[]>;

  /**
   * Handle workflow execution errors and retries
   * @param error Error that occurred during execution
   * @param context Workflow execution context
   * @param retryCount Current retry count
   * @returns Whether to retry execution
   */
  handleExecutionError(
    error: Error,
    context: WorkflowContext,
    retryCount: number
  ): Promise<boolean>;
}
