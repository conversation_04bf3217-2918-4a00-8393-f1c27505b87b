/**
 * Interface for the workflow engine
 */
export interface IWorkflowEngine {
  /**
   * Initialize the workflow engine
   */
  initialize(): Promise<void>;

  /**
   * Execute a workflow with the given ID and input
   * @param workflowId Workflow ID
   * @param input Input data
   * @returns Workflow execution result
   */
  executeWorkflow(workflowId: string, input: any): Promise<any>;

  /**
   * Execute a workflow with the given configuration and input
   * @param workflowConfig Workflow configuration
   * @param input Input data
   * @returns Workflow execution result
   */
  executeWorkflowConfig(workflowConfig: any, input: any): Promise<any>;

  /**
   * Get a workflow by ID
   * @param workflowId Workflow ID
   * @returns Workflow configuration or null if not found
   */
  getWorkflow(workflowId: string): Promise<any | null>;

  /**
   * Reload a workflow by ID
   * @param workflowId Workflow ID
   * @returns True if the workflow was reloaded, false if not found
   */
  reloadWorkflow(workflowId: string): Promise<boolean>;

  /**
   * Reload all workflows
   * @returns Number of workflows reloaded
   */
  reloadAllWorkflows(): Promise<number>;

  /**
   * Remove a workflow by ID
   * @param workflowId Workflow ID
   * @returns True if the workflow was removed, false if not found
   */
  removeWorkflow(workflowId: string): Promise<boolean>;

  /**
   * Get workflow execution history
   * @param workflowId Workflow ID
   * @param limit Maximum number of executions to return
   * @param offset Offset for pagination
   * @returns Workflow execution history
   */
  getWorkflowExecutionHistory(workflowId: string, limit?: number, offset?: number): Promise<any[]>;
}
