/**
 * Generic repository interface
 */
export interface IRepository<T> {
  /**
   * Find all entities
   */
  findAll(): Promise<T[]>;

  /**
   * Find entity by id
   * @param id Entity id
   */
  findById(id: string): Promise<T | null>;

  /**
   * Find entities by criteria
   * @param criteria Search criteria
   */
  findBy(criteria: Partial<T>): Promise<T[]>;

  /**
   * Find one entity by criteria
   * @param criteria Search criteria
   */
  findOne(criteria: any): Promise<T | null>;

  /**
   * Create a new entity
   * @param entity Entity data
   */
  create(entity: Partial<T>): Promise<T>;

  /**
   * Save an entity
   * @param entity Entity data
   */
  save(entity: Partial<T>): Promise<T>;

  /**
   * Update an existing entity
   * @param id Entity id
   * @param entity Entity data
   */
  update(id: string, entity: Partial<T>): Promise<T>;

  /**
   * Delete an entity
   * @param id Entity id
   */
  delete(id: string): Promise<boolean>;

  /**
   * Execute a function within a transaction
   * @param callback Function to execute
   */
  transaction<R>(callback: (repository: IRepository<T>) => Promise<R>): Promise<R>;

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  query?(sql: string, params?: any[]): Promise<any>;
}
