import { Request } from 'express';
import { Tenant } from '../../infrastructure/database/entities/Tenant.entity';

/**
 * Tenant resolution strategy
 */
export enum TenantResolutionStrategy {
  HEADER = 'header',
  SUBDOMAIN = 'subdomain',
  PATH = 'path',
  JWT = 'jwt'
}

/**
 * Interface for tenant resolution from HTTP requests
 */
export interface ITenantResolver {
  /**
   * Resolve tenant from HTTP request
   * @param request Express request object
   * @returns Tenant or null if not found
   */
  resolveTenant(request: Request): Promise<Tenant | null>;

  /**
   * Resolve tenant from header value
   * @param headerValue Header value containing tenant identifier
   * @returns Tenant or null if not found
   */
  resolveFromHeader(headerValue: string): Promise<Tenant | null>;

  /**
   * Resolve tenant from subdomain
   * @param hostname Request hostname
   * @returns Tenant or null if not found
   */
  resolveFromSubdomain(hostname: string): Promise<Tenant | null>;

  /**
   * Resolve tenant from URL path
   * @param path Request path
   * @returns Tenant or null if not found
   */
  resolveFromPath(path: string): Promise<Tenant | null>;

  /**
   * Resolve tenant from JWT token
   * @param token JWT token
   * @returns Tenant or null if not found
   */
  resolveFromJWT(token: string): Promise<Tenant | null>;

  /**
   * Set resolution strategy
   * @param strategy Resolution strategy to use
   */
  setStrategy(strategy: TenantResolutionStrategy): void;

  /**
   * Get current resolution strategy
   * @returns Current strategy
   */
  getStrategy(): TenantResolutionStrategy;

  /**
   * Clear tenant cache
   */
  clearCache(): void;

  /**
   * Get tenant from cache
   * @param key Cache key
   * @returns Cached tenant or null
   */
  getCachedTenant(key: string): Tenant | null;

  /**
   * Cache tenant
   * @param key Cache key
   * @param tenant Tenant to cache
   * @param ttl Time to live in seconds
   */
  cacheTenant(key: string, tenant: Tenant, ttl?: number): void;
}
