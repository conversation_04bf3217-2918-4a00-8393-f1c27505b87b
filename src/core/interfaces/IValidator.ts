/**
 * Base interface for all validators
 * Defines common methods that all validators should implement
 */
export interface IValidator<T> {
  /**
   * Validate entity for creation
   * @param data Entity data
   * @returns Validated entity data
   * @throws ValidationError if validation fails
   */
  validateForCreate(data: any): Promise<T>;

  /**
   * Validate entity for update
   * @param id Entity ID
   * @param data Entity data
   * @returns Validated entity data
   * @throws ValidationError if validation fails
   */
  validateForUpdate(id: string, data: any): Promise<Partial<T>>;

  /**
   * Validate entity ID
   * @param id Entity ID
   * @throws ValidationError if validation fails
   */
  validateId(id: string): void;
}
