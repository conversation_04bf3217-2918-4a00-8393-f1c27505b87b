import { INode } from './INode';

/**
 * Options for SQL query execution
 */
export interface SQLOptions {
  /**
   * Whether to use a transaction
   */
  useTransaction?: boolean;

  /**
   * Transaction timeout in milliseconds
   */
  transactionTimeout?: number;

  /**
   * Whether to return results as an array of objects
   */
  returnAsObject?: boolean;
}

/**
 * SQL transaction interface
 */
export interface SQLTransaction {
  /**
   * Execute a query within the transaction
   * @param query SQL query
   * @param params Query parameters
   * @returns Query result
   */
  query(query: string, params: any[]): Promise<any>;

  /**
   * Commit the transaction
   */
  commit(): Promise<void>;

  /**
   * Rollback the transaction
   */
  rollback(): Promise<void>;
}

/**
 * Interface for SQL node
 */
export interface ISQLNode extends INode {
  /**
   * Execute a SQL query
   * @param query SQL query
   * @param params Query parameters
   * @param options Query options
   * @returns Query result
   */
  executeQuery(query: string, params: any[], options?: SQLOptions): Promise<any>;

  /**
   * Begin a new transaction
   * @returns Transaction object
   */
  beginTransaction(): Promise<SQLTransaction>;
}
