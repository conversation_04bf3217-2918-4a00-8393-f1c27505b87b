import { WorkflowContext } from '../types/WorkflowContext';

/**
 * Interface for workflow memory
 */
export interface IWorkflowMemory {
  /**
   * Get workflow context by execution ID
   * @param executionId Execution ID
   * @returns Workflow context
   */
  getContext(executionId: string): Promise<WorkflowContext>;

  /**
   * Update workflow context
   * @param executionId Execution ID
   * @param context Workflow context
   */
  updateContext(executionId: string, context: WorkflowContext): Promise<void>;

  /**
   * Delete workflow context
   * @param executionId Execution ID
   */
  deleteContext(executionId: string): Promise<void>;
}
