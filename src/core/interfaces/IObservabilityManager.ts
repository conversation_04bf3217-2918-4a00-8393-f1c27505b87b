/**
 * Interface for observability manager
 * Provides methods for recording and retrieving metrics about workflow and node executions
 */

/**
 * Time range for metrics queries
 */
export interface TimeRange {
  startTime: Date;
  endTime: Date;
}

/**
 * Workflow execution metrics
 */
export interface WorkflowMetrics {
  startTime: Date;
  endTime?: Date;
  durationMs?: number;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';
  error?: string;
}

/**
 * Node execution metrics
 */
export interface NodeMetrics {
  nodeType: string;
  startTime: Date;
  endTime?: Date;
  durationMs?: number;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';
  error?: string;
}

/**
 * Workflow metrics summary
 */
export interface WorkflowMetricsSummary {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageDurationMs: number;
  minDurationMs: number;
  maxDurationMs: number;
  p95DurationMs: number;
  p99DurationMs: number;
  errorRate: number;
  metrics: WorkflowMetrics[];
}

/**
 * Node metrics summary
 */
export interface NodeMetricsSummary {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageDurationMs: number;
  minDurationMs: number;
  maxDurationMs: number;
  p95DurationMs: number;
  p99DurationMs: number;
  errorRate: number;
  metrics: NodeMetrics[];
}

/**
 * Interface for observability manager
 */
export interface IObservabilityManager {
  /**
   * Record workflow execution metrics
   * @param workflowId Workflow ID
   * @param executionId Execution ID
   * @param metrics Workflow metrics
   */
  recordWorkflowExecution(
    workflowId: string,
    executionId: string,
    metrics: WorkflowMetrics
  ): Promise<void>;

  /**
   * Record node execution metrics
   * @param nodeId Node ID
   * @param executionId Execution ID
   * @param metrics Node metrics
   */
  recordNodeExecution(nodeId: string, executionId: string, metrics: NodeMetrics): Promise<void>;

  /**
   * Get workflow metrics
   * @param workflowId Workflow ID (optional)
   * @param timeRange Time range (optional)
   * @returns Workflow metrics summary
   */
  getWorkflowMetrics(workflowId?: string, timeRange?: TimeRange): Promise<WorkflowMetricsSummary[]>;

  /**
   * Get node metrics
   * @param nodeType Node type (optional)
   * @param timeRange Time range (optional)
   * @returns Node metrics summary
   */
  getNodeMetrics(nodeType?: string, timeRange?: TimeRange): Promise<NodeMetricsSummary[]>;

  /**
   * Export Prometheus metrics
   * @returns Prometheus metrics in string format
   */
  exportPrometheusMetrics(): Promise<string>;

  /**
   * Clean up old data
   * Deletes metrics, workflow executions, and node executions older than the retention period
   */
  cleanupOldData(): Promise<void>;
}
