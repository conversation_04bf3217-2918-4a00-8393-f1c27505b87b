import { INode } from './INode';
import { WorkflowContext } from '../types/WorkflowContext';

/**
 * Vector database configuration
 */
export interface VectorDBConfig {
  provider: 'pinecone' | 'weaviate' | 'qdrant' | 'chroma';
  apiKey: string;
  endpoint: string;
  indexName?: string;
  namespace?: string;
  dimension?: number;
  metric?: 'cosine' | 'euclidean' | 'dotproduct';
}

/**
 * Vector search configuration
 */
export interface VectorSearchConfig {
  vector?: number[];
  query?: string;
  topK: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeValues?: boolean;
}

/**
 * Vector DB node for similarity search and vector operations
 */
export interface IVectorDBNode extends INode {
  provider: 'pinecone' | 'weaviate' | 'qdrant' | 'chroma';
  operation: 'search' | 'insert' | 'update' | 'delete' | 'query';
  config: VectorDBConfig;

  /**
   * Perform vector similarity search
   * @param searchConfig Search configuration
   * @param context Workflow execution context
   * @returns Search results
   */
  search(
    searchConfig: VectorSearchConfig,
    context: WorkflowContext
  ): Promise<{
    matches: Array<{
      id: string;
      score: number;
      values?: number[];
      metadata?: Record<string, any>;
    }>;
    namespace?: string;
  }>;

  /**
   * Insert vectors into the database
   * @param vectors Array of vectors to insert
   * @param context Workflow execution context
   * @returns Insert result
   */
  insert(
    vectors: Array<{
      id: string;
      values: number[];
      metadata?: Record<string, any>;
    }>,
    context: WorkflowContext
  ): Promise<{
    upsertedCount: number;
    errors?: string[];
  }>;

  /**
   * Update existing vectors
   * @param updates Array of vector updates
   * @param context Workflow execution context
   * @returns Update result
   */
  update(
    updates: Array<{
      id: string;
      values?: number[];
      metadata?: Record<string, any>;
    }>,
    context: WorkflowContext
  ): Promise<{
    updatedCount: number;
    errors?: string[];
  }>;

  /**
   * Delete vectors from the database
   * @param ids Array of vector IDs to delete
   * @param context Workflow execution context
   * @returns Delete result
   */
  delete(
    ids: string[],
    context: WorkflowContext
  ): Promise<{
    deletedCount: number;
    errors?: string[];
  }>;
}

/**
 * GraphQL node for executing queries and mutations
 */
export interface IGraphQLNode extends INode {
  endpoint: string;
  query: string;
  variables?: Record<string, any>;
  headers?: Record<string, string>;
  operationType: 'query' | 'mutation' | 'subscription';

  /**
   * Execute GraphQL query
   * @param context Workflow execution context
   * @returns Query result
   */
  executeQuery(context: WorkflowContext): Promise<{
    data?: any;
    errors?: Array<{
      message: string;
      locations?: Array<{ line: number; column: number }>;
      path?: string[];
    }>;
    extensions?: Record<string, any>;
  }>;

  /**
   * Execute GraphQL mutation
   * @param context Workflow execution context
   * @returns Mutation result
   */
  executeMutation(context: WorkflowContext): Promise<{
    data?: any;
    errors?: Array<{
      message: string;
      locations?: Array<{ line: number; column: number }>;
      path?: string[];
    }>;
  }>;

  /**
   * Introspect GraphQL schema
   * @param context Workflow execution context
   * @returns Schema information
   */
  introspectSchema(context: WorkflowContext): Promise<{
    types: Array<{
      name: string;
      kind: string;
      fields?: Array<{
        name: string;
        type: string;
        description?: string;
      }>;
    }>;
    queries: string[];
    mutations: string[];
    subscriptions: string[];
  }>;

  /**
   * Validate GraphQL query syntax
   * @param query GraphQL query to validate
   * @returns Validation result
   */
  validateQuery(query: string): Promise<{
    valid: boolean;
    errors: string[];
  }>;
}

/**
 * SOAP authentication configuration
 */
export interface SOAPAuthentication {
  type: 'basic' | 'digest' | 'ntlm' | 'wsse' | 'custom';
  username?: string;
  password?: string;
  token?: string;
  customHeaders?: Record<string, string>;
}

/**
 * SOAP node for calling web services
 */
export interface ISOAPNode extends INode {
  wsdl: string;
  operation: string;
  parameters: Record<string, any>;
  authentication?: SOAPAuthentication;
  timeout?: number;

  /**
   * Call SOAP web service
   * @param context Workflow execution context
   * @returns SOAP response
   */
  callService(context: WorkflowContext): Promise<{
    result: any;
    headers?: Record<string, string>;
    statusCode: number;
    executionTime: number;
  }>;

  /**
   * Parse WSDL and get available operations
   * @param wsdlUrl WSDL URL
   * @returns Available operations
   */
  parseWSDL(wsdlUrl: string): Promise<{
    services: Array<{
      name: string;
      ports: Array<{
        name: string;
        binding: string;
        address: string;
      }>;
    }>;
    operations: Array<{
      name: string;
      input: any;
      output: any;
      documentation?: string;
    }>;
  }>;

  /**
   * Build SOAP envelope for operation
   * @param operation Operation name
   * @param parameters Operation parameters
   * @returns SOAP envelope XML
   */
  buildEnvelope(
    operation: string,
    parameters: Record<string, any>
  ): Promise<string>;

  /**
   * Parse SOAP response
   * @param responseXml SOAP response XML
   * @returns Parsed response data
   */
  parseResponse(responseXml: string): Promise<{
    data: any;
    fault?: {
      code: string;
      message: string;
      detail?: any;
    };
  }>;
}

/**
 * Transform node for data transformation operations
 */
export interface ITransformNode extends INode {
  /**
   * Transform input data
   * @param data Input data to transform
   * @param context Workflow execution context
   * @returns Transformed data
   */
  transformData(data: any, context: WorkflowContext): Promise<any>;
}

/**
 * Data mapping node for schema transformations
 */
export interface IDataMappingNode extends ITransformNode {
  mappings: Array<{
    source: string;
    target: string;
    transform?: string;
    defaultValue?: any;
  }>;
  validation?: {
    required?: string[];
    schema?: any;
  };

  /**
   * Map data from source to target schema
   * @param data Input data
   * @param context Workflow execution context
   * @returns Mapped data
   */
  mapData(data: any, context: WorkflowContext): Promise<any>;

  /**
   * Validate mapped data
   * @param data Data to validate
   * @returns Validation result
   */
  validateMappedData(data: any): Promise<{
    valid: boolean;
    errors: string[];
  }>;
}

/**
 * Aggregation node for data aggregation operations
 */
export interface IAggregationNode extends ITransformNode {
  operations: Array<{
    type: 'sum' | 'count' | 'average' | 'min' | 'max' | 'distinct';
    field: string;
    alias?: string;
  }>;
  groupBy?: string[];

  /**
   * Aggregate data based on operations
   * @param data Input data array
   * @param context Workflow execution context
   * @returns Aggregated data
   */
  aggregateData(data: any[], context: WorkflowContext): Promise<any>;

  /**
   * Group data by specified fields
   * @param data Input data array
   * @param groupFields Fields to group by
   * @returns Grouped data
   */
  groupData(
    data: any[],
    groupFields: string[]
  ): Promise<Record<string, any[]>>;
}
