import { INode } from './INode';

/**
 * Interface for Redis node
 */
export interface IRedisNode extends INode {
  /**
   * Execute a Redis command
   * @param command Command
   * @param args Command arguments
   * @returns Command result
   */
  executeCommand(command: string, args: any[]): Promise<any>;

  /**
   * Get a cached value or fetch it if not in cache
   * @param key Cache key
   * @param fetchFn Function to fetch the value if not in cache
   * @param ttl TTL in seconds (optional)
   * @returns Cached or fetched value
   */
  getCached<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T>;

  /**
   * Publish a message to a channel
   * @param channel Channel
   * @param message Message
   * @returns Number of clients that received the message
   */
  publish(channel: string, message: string): Promise<number>;
}
