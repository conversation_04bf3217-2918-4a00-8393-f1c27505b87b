import { WorkflowDefinition } from './IExecutionCoordinator';

/**
 * Workflow version information
 */
export interface WorkflowVersion {
  id: string;
  workflowId: string;
  version: number;
  definition: WorkflowDefinition;
  isActive: boolean;
  tenantId?: string;
  createdAt: Date;
  createdBy: string;
  changeType: 'create' | 'update' | 'activate' | 'deactivate' | 'rollback';
  changeDescription?: string;
  metadata?: Record<string, any>;
}

/**
 * Version comparison result
 */
export interface VersionComparison {
  fromVersion: number;
  toVersion: number;
  changes: VersionChange[];
  summary: {
    nodesAdded: number;
    nodesRemoved: number;
    nodesModified: number;
    edgesAdded: number;
    edgesRemoved: number;
    configChanges: number;
  };
}

/**
 * Individual version change
 */
export interface VersionChange {
  type: 'node_added' | 'node_removed' | 'node_modified' | 'edge_added' | 'edge_removed' | 'config_changed';
  path: string;
  oldValue?: any;
  newValue?: any;
  description: string;
}

/**
 * Version deployment configuration
 */
export interface VersionDeployment {
  versionId: string;
  deploymentStrategy: 'immediate' | 'gradual' | 'scheduled';
  rolloutPercentage?: number;
  scheduledTime?: Date;
  rollbackOnFailure?: boolean;
  healthChecks?: HealthCheck[];
}

/**
 * Health check configuration for version deployment
 */
export interface HealthCheck {
  type: 'execution_success_rate' | 'response_time' | 'error_rate' | 'custom';
  threshold: number;
  duration: number;
  customCheck?: (metrics: any) => boolean;
}

/**
 * Version rollback configuration
 */
export interface RollbackConfig {
  targetVersion: number;
  reason: string;
  preserveData?: boolean;
  notifyUsers?: boolean;
  rollbackStrategy: 'immediate' | 'gradual';
}

/**
 * Interface for managing workflow versions
 */
export interface IWorkflowVersionManager {
  /**
   * Create a new version of a workflow
   * @param workflowId Workflow ID
   * @param definition Updated workflow definition
   * @param createdBy User who created the version
   * @param changeDescription Description of changes
   * @returns Created workflow version
   */
  createVersion(
    workflowId: string,
    definition: WorkflowDefinition,
    createdBy: string,
    changeDescription?: string
  ): Promise<WorkflowVersion>;

  /**
   * Activate a specific version of a workflow
   * @param workflowId Workflow ID
   * @param version Version number to activate
   * @param deployment Deployment configuration
   * @returns Activation result
   */
  activateVersion(
    workflowId: string,
    version: number,
    deployment?: VersionDeployment
  ): Promise<{
    success: boolean;
    activeVersion: WorkflowVersion;
    previousVersion?: WorkflowVersion;
  }>;

  /**
   * Deactivate a specific version of a workflow
   * @param workflowId Workflow ID
   * @param version Version number to deactivate
   * @param reason Deactivation reason
   * @returns Deactivation result
   */
  deactivateVersion(
    workflowId: string,
    version: number,
    reason: string
  ): Promise<{
    success: boolean;
    deactivatedVersion: WorkflowVersion;
  }>;

  /**
   * Get version history for a workflow
   * @param workflowId Workflow ID
   * @param limit Maximum number of versions to return
   * @param offset Offset for pagination
   * @returns Array of workflow versions
   */
  getVersionHistory(
    workflowId: string,
    limit?: number,
    offset?: number
  ): Promise<WorkflowVersion[]>;

  /**
   * Get the currently active version of a workflow
   * @param workflowId Workflow ID
   * @returns Active workflow version
   */
  getActiveVersion(workflowId: string): Promise<WorkflowVersion | null>;

  /**
   * Get a specific version of a workflow
   * @param workflowId Workflow ID
   * @param version Version number
   * @returns Workflow version
   */
  getVersion(
    workflowId: string,
    version: number
  ): Promise<WorkflowVersion | null>;

  /**
   * Rollback to a previous version
   * @param workflowId Workflow ID
   * @param config Rollback configuration
   * @returns Rollback result
   */
  rollbackToVersion(
    workflowId: string,
    config: RollbackConfig
  ): Promise<{
    success: boolean;
    rolledBackTo: WorkflowVersion;
    previousVersion: WorkflowVersion;
  }>;

  /**
   * Compare two versions of a workflow
   * @param workflowId Workflow ID
   * @param fromVersion Source version number
   * @param toVersion Target version number
   * @returns Version comparison result
   */
  compareVersions(
    workflowId: string,
    fromVersion: number,
    toVersion: number
  ): Promise<VersionComparison>;

  /**
   * Delete a specific version (if not active)
   * @param workflowId Workflow ID
   * @param version Version number to delete
   * @param force Force deletion even if version has dependencies
   * @returns Deletion result
   */
  deleteVersion(
    workflowId: string,
    version: number,
    force?: boolean
  ): Promise<{
    success: boolean;
    deletedVersion: WorkflowVersion;
  }>;

  /**
   * Clone a version to create a new workflow
   * @param sourceWorkflowId Source workflow ID
   * @param sourceVersion Source version number
   * @param newWorkflowName Name for the new workflow
   * @param createdBy User creating the clone
   * @returns Cloned workflow version
   */
  cloneVersion(
    sourceWorkflowId: string,
    sourceVersion: number,
    newWorkflowName: string,
    createdBy: string
  ): Promise<WorkflowVersion>;

  /**
   * Validate a workflow definition before creating version
   * @param definition Workflow definition to validate
   * @returns Validation result
   */
  validateDefinition(definition: WorkflowDefinition): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }>;
}
