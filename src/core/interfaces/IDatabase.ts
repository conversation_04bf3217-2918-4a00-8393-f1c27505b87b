import { EntityTarget, ObjectLiteral } from 'typeorm';
import { IRepository } from './IRepository';

/**
 * Type for notification callback function
 */
export type NotificationCallback = (channel: string, payload: string) => void;

/**
 * Interface for database service
 */
export interface IDatabase {
  /**
   * Initialize the database connection
   */
  initialize(): Promise<void>;

  /**
   * Get a repository for a specific entity
   * @param entity Entity class
   */
  getRepository<T extends ObjectLiteral>(entity: EntityTarget<T>): IRepository<T>;

  /**
   * Close the database connection
   */
  close(): Promise<void>;

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  query(sql: string, params?: any[]): Promise<any>;

  /**
   * Execute operations within a transaction
   * @param operation Function that performs database operations
   * @returns Result of the operation
   */
  transaction<T>(operation: (trx: any) => Promise<T>): Promise<T>;

  /**
   * Listen for notifications on a channel
   * @param channel Channel name
   * @returns Promise that resolves when the listen command is executed
   */
  listen(channel: string): Promise<void>;

  /**
   * Stop listening for notifications on a channel
   * @param channel Channel name
   * @returns Promise that resolves when the unlisten command is executed
   */
  unlisten(channel: string): Promise<void>;

  /**
   * Register a callback for notifications
   * @param callback Callback function
   */
  onNotification(callback: NotificationCallback): void;

  /**
   * Send a notification
   * @param channel Channel name
   * @param payload Notification payload
   * @returns Promise that resolves when the notify command is executed
   */
  notify(channel: string, payload: string): Promise<void>;

  /**
   * Get the TypeORM DataSource
   * @returns TypeORM DataSource
   */
  getConnection(): any;

  /**
   * Get the TypeORM DataSource (alias for getConnection)
   * @returns TypeORM DataSource
   */
  getDataSource(): Promise<any>;
}
