/**
 * Interface for dynamic service loader
 */
export interface IDynamicServiceLoader {
  /**
   * Initialize the service loader
   */
  initialize(): Promise<void>;

  /**
   * Load a function by name
   * @param name Function name
   * @returns Function instance or null if not found
   */
  loadFunction(name: string): Promise<any | null>;

  /**
   * Reload a function by name
   * @param name Function name
   * @returns True if reloaded successfully, false otherwise
   */
  reloadFunction(name: string): Promise<boolean>;

  /**
   * Unload a function by name
   * @param name Function name
   * @returns True if unloaded successfully, false otherwise
   */
  unloadFunction(name: string): Promise<boolean>;

  /**
   * Get all loaded functions
   * @returns Array of function names
   */
  getFunctions(): string[];
}
