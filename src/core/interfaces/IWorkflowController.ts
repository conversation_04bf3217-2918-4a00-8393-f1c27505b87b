import { Request, Response } from 'express';
import { IController } from './IController';

/**
 * Interface for workflow controller
 * Extends the base controller interface with workflow-specific methods
 */
export interface IWorkflowController extends IController {
  /**
   * Test a workflow without saving it
   * @param req Express request
   * @param res Express response
   */
  test(req: Request, res: Response): Promise<void>;

  /**
   * Get workflow execution history
   * @param req Express request
   * @param res Express response
   */
  getExecutionHistory(req: Request, res: Response): Promise<void>;

  /**
   * Get workflow metrics
   * @param req Express request
   * @param res Express response
   */
  getMetrics(req: Request, res: Response): Promise<void>;

  /**
   * Execute a workflow
   * @param req Express request
   * @param res Express response
   */
  execute(req: Request, res: Response): Promise<void>;

  /**
   * Trigger a workflow
   * @param req Express request
   * @param res Express response
   */
  trigger(req: Request, res: Response): Promise<void>;
}
