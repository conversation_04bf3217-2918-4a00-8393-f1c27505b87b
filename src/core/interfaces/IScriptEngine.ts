/**
 * Interface for JavaScript execution engine
 */
export interface IScriptEngine {
  /**
   * Execute JavaScript code in a sandbox
   * @param code JavaScript code to execute
   * @param context Context variables available to the code
   * @param options Additional execution options
   */
  execute<T = any>(
    code: string,
    context?: Record<string, any>,
    options?: ScriptExecutionOptions
  ): Promise<T>;
}

/**
 * Options for script execution
 */
export interface ScriptExecutionOptions {
  /**
   * Timeout in milliseconds
   */
  timeout?: number;

  /**
   * Memory limit in MB
   */
  memoryLimit?: number;

  /**
   * Allow external modules to be required
   */
  allowRequire?: boolean;

  /**
   * Allowed external modules
   */
  allowedModules?: string[];
}
