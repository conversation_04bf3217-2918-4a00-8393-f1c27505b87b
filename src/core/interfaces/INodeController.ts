import { Request, Response } from 'express';
import { IController } from './IController';

/**
 * Interface for node controller
 * Extends the base controller interface with node-specific methods
 */
export interface INodeController extends IController {
  /**
   * Test a node configuration without saving it
   * @param req Express request
   * @param res Express response
   */
  test(req: Request, res: Response): Promise<void>;

  /**
   * Get available node types
   * @param req Express request
   * @param res Express response
   */
  getNodeTypes(req: Request, res: Response): Promise<void>;
}
