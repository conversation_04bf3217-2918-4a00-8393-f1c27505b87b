/**
 * Interface for MCP Tool
 */
export interface IMCPTool {
  /**
   * Unique identifier for the tool
   */
  readonly id: string;

  /**
   * Tool name
   */
  readonly name: string;

  /**
   * Tool description
   */
  readonly description: string;

  /**
   * JSON Schema for tool input
   */
  readonly inputSchema: any;

  /**
   * Execute the tool with provided parameters
   * @param params Tool parameters
   */
  execute(params: Record<string, any>): Promise<any>;

  /**
   * Check if the tool is enabled
   */
  isEnabled(): boolean;
}
