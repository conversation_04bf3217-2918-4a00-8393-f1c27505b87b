import { WorkflowContext, BranchExecutionContext } from '../types/WorkflowContext';
import { ExecutionBranch, BranchResult, MergedResult } from './IExecutionCoordinator';

/**
 * Parallel execution configuration
 */
export interface ParallelExecutionConfig {
  maxConcurrency: number;
  timeout: number;
  failFast: boolean;
  retryFailedBranches: boolean;
  maxRetries: number;
}

/**
 * Resource allocation for parallel execution
 */
export interface ResourceAllocation {
  branchId: string;
  memoryLimit: number;
  cpuLimit: number;
  timeoutMs: number;
  priority: number;
}

/**
 * Synchronization point for parallel branches
 */
export interface SynchronizationPoint {
  id: string;
  waitingBranches: string[];
  completedBranches: string[];
  requiredBranches: string[];
  timeoutMs: number;
}

/**
 * Interface for executing workflow branches in parallel
 */
export interface IParallelExecutor {
  /**
   * Execute multiple branches concurrently
   * @param branches Array of execution branches
   * @param context Workflow execution context
   * @param config Parallel execution configuration
   * @returns Array of branch results
   */
  executeBranches(
    branches: ExecutionBranch[],
    context: WorkflowContext,
    config?: ParallelExecutionConfig
  ): Promise<BranchResult[]>;

  /**
   * Synchronize multiple branches at a merge point
   * @param branchResults Results from parallel branches
   * @param syncPoint Synchronization configuration
   * @returns Merged result
   */
  synchronizeBranches(
    branchResults: BranchResult[],
    syncPoint: SynchronizationPoint
  ): Promise<MergedResult>;

  /**
   * Allocate resources for parallel execution
   * @param branches Array of execution branches
   * @param totalResources Available system resources
   * @returns Resource allocation plan
   */
  allocateResources(
    branches: ExecutionBranch[],
    totalResources: ResourceAllocation
  ): Promise<ResourceAllocation[]>;

  /**
   * Monitor parallel execution progress
   * @param executionId Workflow execution ID
   * @returns Current execution status
   */
  getExecutionStatus(executionId: string): Promise<{
    activeBranches: number;
    completedBranches: number;
    failedBranches: number;
    totalBranches: number;
    estimatedCompletion: Date;
  }>;

  /**
   * Cancel parallel execution
   * @param executionId Workflow execution ID
   * @param reason Cancellation reason
   * @returns Cancellation result
   */
  cancelExecution(
    executionId: string,
    reason: string
  ): Promise<{
    cancelled: boolean;
    affectedBranches: string[];
    cleanupRequired: boolean;
  }>;

  /**
   * Handle branch execution failure
   * @param branchId Failed branch ID
   * @param error Error that occurred
   * @param context Branch execution context
   * @param retryCount Current retry count
   * @returns Whether to retry the branch
   */
  handleBranchFailure(
    branchId: string,
    error: Error,
    context: BranchExecutionContext,
    retryCount: number
  ): Promise<boolean>;

  /**
   * Cleanup resources after parallel execution
   * @param executionId Workflow execution ID
   * @param branchIds Array of branch IDs to cleanup
   * @returns Cleanup result
   */
  cleanupResources(
    executionId: string,
    branchIds: string[]
  ): Promise<{
    cleaned: boolean;
    errors: Error[];
  }>;
}
