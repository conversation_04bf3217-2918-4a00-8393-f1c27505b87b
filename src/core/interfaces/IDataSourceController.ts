import { Request, Response } from 'express';
import { IController } from './IController';

/**
 * Interface for data source controller
 * Extends the base controller interface with data source-specific methods
 */
export interface IDataSourceController extends IController {
  /**
   * Test a data source connection without saving it
   * @param req Express request
   * @param res Express response
   */
  testConnection(req: Request, res: Response): Promise<void>;

  /**
   * Get available data source types
   * @param req Express request
   * @param res Express response
   */
  getDataSourceTypes(req: Request, res: Response): Promise<void>;
}
