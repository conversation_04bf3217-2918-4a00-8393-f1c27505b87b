import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';

/**
 * Interface for tenant isolation and data filtering
 */
export interface ITenantIsolation {
  /**
   * Filter TypeORM query builder with tenant isolation
   * @param queryBuilder TypeORM query builder
   * @param tenantId Tenant ID to filter by
   * @returns Modified query builder
   */
  filterQuery<T extends ObjectLiteral>(
    queryBuilder: SelectQueryBuilder<T>,
    tenantId: string
  ): SelectQueryBuilder<T>;

  /**
   * Validate access to resource for tenant
   * @param resource Resource name
   * @param action Action being performed
   * @param tenantId Tenant ID
   * @returns True if access is allowed
   */
  validateAccess(resource: string, action: string, tenantId: string): Promise<boolean>;

  /**
   * Get Redis namespace for tenant
   * @param tenantId Tenant ID
   * @returns Redis namespace string
   */
  getRedisNamespace(tenantId: string): string;

  /**
   * Encrypt sensitive data for tenant
   * @param data Data to encrypt
   * @param tenantId Tenant ID
   * @returns Encrypted data
   */
  encryptSensitiveData(data: any, tenantId: string): Promise<any>;

  /**
   * Decrypt sensitive data for tenant
   * @param encryptedData Encrypted data
   * @param tenantId Tenant ID
   * @returns Decrypted data
   */
  decryptSensitiveData(encryptedData: any, tenantId: string): Promise<any>;

  /**
   * Apply tenant filter to entity data
   * @param entityName Entity name
   * @param data Entity data
   * @param tenantId Tenant ID
   * @returns Filtered data
   */
  applyTenantFilter(entityName: string, data: any, tenantId: string): any;

  /**
   * Validate tenant resource limits
   * @param tenantId Tenant ID
   * @param resource Resource type
   * @param currentUsage Current usage
   * @returns True if within limits
   */
  validateResourceLimits(
    tenantId: string,
    resource: string,
    currentUsage: number
  ): Promise<boolean>;

  /**
   * Get tenant-specific configuration
   * @param tenantId Tenant ID
   * @param configKey Configuration key
   * @returns Configuration value
   */
  getTenantConfig(tenantId: string, configKey: string): Promise<any>;

  /**
   * Set tenant-specific configuration
   * @param tenantId Tenant ID
   * @param configKey Configuration key
   * @param configValue Configuration value
   */
  setTenantConfig(tenantId: string, configKey: string, configValue: any): Promise<void>;

  /**
   * Log tenant-specific audit event
   * @param tenantId Tenant ID
   * @param event Event type
   * @param details Event details
   */
  logAuditEvent(tenantId: string, event: string, details: any): Promise<void>;
}
