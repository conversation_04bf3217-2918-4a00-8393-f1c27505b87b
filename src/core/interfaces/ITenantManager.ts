import { Tenant, TenantSettings } from '../../infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../../infrastructure/database/entities/TenantUser.entity';

/**
 * Pagination options interface
 */
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Interface for tenant management operations
 */
export interface ITenantManager {
  /**
   * Get tenant by ID
   * @param tenantId Tenant ID
   * @returns Tenant or null if not found
   */
  getTenant(tenantId: string): Promise<Tenant | null>;

  /**
   * Get tenant by name
   * @param name Tenant name
   * @returns Tenant or null if not found
   */
  getTenantByName(name: string): Promise<Tenant | null>;

  /**
   * Create a new tenant
   * @param tenant Partial tenant data
   * @returns Created tenant
   */
  createTenant(tenant: Partial<Tenant>): Promise<Tenant>;

  /**
   * Update tenant
   * @param tenantId Tenant ID
   * @param updates Partial tenant updates
   * @returns Updated tenant
   */
  updateTenant(tenantId: string, updates: Partial<Tenant>): Promise<Tenant>;

  /**
   * Delete tenant
   * @param tenantId Tenant ID
   * @returns True if deleted successfully
   */
  deleteTenant(tenantId: string): Promise<boolean>;

  /**
   * List tenants with pagination
   * @param pagination Pagination options
   * @returns Paginated tenant list
   */
  listTenants(pagination?: PaginationOptions): Promise<PaginatedResult<Tenant>>;

  /**
   * Validate tenant settings
   * @param settings Tenant settings to validate
   * @returns Validation result
   */
  validateTenantSettings(settings: TenantSettings): Promise<ValidationResult>;

  /**
   * Get tenant users
   * @param tenantId Tenant ID
   * @param pagination Pagination options
   * @returns Paginated user list
   */
  getTenantUsers(tenantId: string, pagination?: PaginationOptions): Promise<PaginatedResult<TenantUser>>;

  /**
   * Create tenant user
   * @param tenantId Tenant ID
   * @param user Partial user data
   * @returns Created user
   */
  createTenantUser(tenantId: string, user: Partial<TenantUser>): Promise<TenantUser>;

  /**
   * Update tenant user
   * @param tenantId Tenant ID
   * @param userId User ID
   * @param updates Partial user updates
   * @returns Updated user
   */
  updateTenantUser(tenantId: string, userId: string, updates: Partial<TenantUser>): Promise<TenantUser>;

  /**
   * Delete tenant user
   * @param tenantId Tenant ID
   * @param userId User ID
   * @returns True if deleted successfully
   */
  deleteTenantUser(tenantId: string, userId: string): Promise<boolean>;

  /**
   * Authenticate tenant user
   * @param tenantId Tenant ID
   * @param username Username
   * @param password Password
   * @returns User if authenticated, null otherwise
   */
  authenticateUser(tenantId: string, username: string, password: string): Promise<TenantUser | null>;
}
