import 'reflect-metadata';
import { Container } from 'inversify';
import { IDatabase } from '../../src/core/interfaces/IDatabase';
import { ILogger } from '../../src/core/interfaces/ILogger';
import { TYPES } from '../../src/types';

/**
 * Test database setup utilities
 */
export class TestDatabaseSetup {
  private database: IDatabase;
  private logger: ILogger;

  constructor(container: Container) {
    this.database = container.get<IDatabase>(TYPES.Database);
    this.logger = container.get<ILogger>(TYPES.Logger);
  }

  /**
   * Initialize test database
   */
  async initialize(): Promise<void> {
    try {
      await this.database.initialize();
      this.logger.info('Test database initialized');
    } catch (error) {
      this.logger.error('Failed to initialize test database', error);
      throw error;
    }
  }

  /**
   * Clean up test database
   */
  async cleanup(): Promise<void> {
    try {
      // Clean up test data
      await this.cleanupTestData();
      
      // Close database connection
      await this.database.close();
      this.logger.info('Test database cleaned up');
    } catch (error) {
      this.logger.error('Failed to cleanup test database', error);
      throw error;
    }
  }

  /**
   * Clean up test data from tables
   */
  private async cleanupTestData(): Promise<void> {
    const tables = [
      'mcp_workflow_node_executions',
      'mcp_workflow_executions',
      'mcp_workflows',
      'mcp_node_configs',
      'mcp_data_sources',
      'mcp_workflow_metrics',
      'mcp_node_metrics'
    ];

    for (const table of tables) {
      try {
        await this.database.query(`DELETE FROM ${table} WHERE name LIKE 'Test%' OR name LIKE '%test%'`);
      } catch (error) {
        // Ignore errors for tables that might not exist
        this.logger.debug(`Failed to clean table ${table}`, error);
      }
    }
  }

  /**
   * Reset auto-increment sequences
   */
  async resetSequences(): Promise<void> {
    const sequences = [
      'mcp_workflows_id_seq',
      'mcp_workflow_executions_id_seq',
      'mcp_workflow_node_executions_id_seq'
    ];

    for (const sequence of sequences) {
      try {
        await this.database.query(`ALTER SEQUENCE ${sequence} RESTART WITH 1`);
      } catch (error) {
        // Ignore errors for sequences that might not exist
        this.logger.debug(`Failed to reset sequence ${sequence}`, error);
      }
    }
  }

  /**
   * Create test data
   */
  async createTestData(): Promise<void> {
    // Create test workflows
    await this.createTestWorkflows();
    
    // Create test node configurations
    await this.createTestNodeConfigs();
    
    // Create test data sources
    await this.createTestDataSources();
  }

  /**
   * Create test workflows
   */
  private async createTestWorkflows(): Promise<void> {
    const testWorkflows = [
      {
        name: 'Test Simple Workflow',
        description: 'A simple test workflow',
        input_schema: JSON.stringify({
          type: 'object',
          properties: { message: { type: 'string' } }
        }),
        nodes_config: JSON.stringify({
          nodes: [
            {
              id: 'node1',
              type: 'javascript',
              name: 'Test Node',
              config: { script: 'return { result: input.message };' }
            }
          ],
          edges: []
        }),
        enabled: true,
        version: 1
      }
    ];

    for (const workflow of testWorkflows) {
      try {
        await this.database.query(`
          INSERT INTO mcp_workflows (name, description, input_schema, nodes_config, enabled, version, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        `, [
          workflow.name,
          workflow.description,
          workflow.input_schema,
          workflow.nodes_config,
          workflow.enabled,
          workflow.version
        ]);
      } catch (error) {
        this.logger.debug('Failed to create test workflow', error);
      }
    }
  }

  /**
   * Create test node configurations
   */
  private async createTestNodeConfigs(): Promise<void> {
    const testNodeConfigs = [
      {
        name: 'Test JavaScript Node',
        description: 'A test JavaScript node configuration',
        node_type: 'javascript',
        config: JSON.stringify({
          script: 'return { processed: true, input: input };'
        }),
        enabled: true
      },
      {
        name: 'Test HTTP Node',
        description: 'A test HTTP node configuration',
        node_type: 'http',
        config: JSON.stringify({
          url: 'https://httpbin.org/get',
          method: 'GET'
        }),
        enabled: true
      }
    ];

    for (const nodeConfig of testNodeConfigs) {
      try {
        await this.database.query(`
          INSERT INTO mcp_node_configs (name, description, node_type, config, enabled, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        `, [
          nodeConfig.name,
          nodeConfig.description,
          nodeConfig.node_type,
          nodeConfig.config,
          nodeConfig.enabled
        ]);
      } catch (error) {
        this.logger.debug('Failed to create test node config', error);
      }
    }
  }

  /**
   * Create test data sources
   */
  private async createTestDataSources(): Promise<void> {
    const testDataSources = [
      {
        name: 'test-postgres',
        description: 'Test PostgreSQL data source',
        type: 'postgresql',
        connection_config_encrypted: 'encrypted_test_config',
        enabled: true
      },
      {
        name: 'test-redis',
        description: 'Test Redis data source',
        type: 'redis',
        connection_config_encrypted: 'encrypted_redis_config',
        enabled: true
      }
    ];

    for (const dataSource of testDataSources) {
      try {
        await this.database.query(`
          INSERT INTO mcp_data_sources (name, description, type, connection_config_encrypted, enabled, created_at, updated_at)
          VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
        `, [
          dataSource.name,
          dataSource.description,
          dataSource.type,
          dataSource.connection_config_encrypted,
          dataSource.enabled
        ]);
      } catch (error) {
        this.logger.debug('Failed to create test data source', error);
      }
    }
  }
}

/**
 * Mock logger for testing
 */
export class TestLogger implements ILogger {
  debug(message: string, ...args: any[]): void {
    if (process.env.TEST_VERBOSE) {
      console.log(`[DEBUG] ${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    if (process.env.TEST_VERBOSE) {
      console.log(`[INFO] ${message}`, ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (process.env.TEST_VERBOSE) {
      console.warn(`[WARN] ${message}`, ...args);
    }
  }

  error(message: string, ...args: any[]): void {
    if (process.env.TEST_VERBOSE) {
      console.error(`[ERROR] ${message}`, ...args);
    }
  }

  trace(message: string, ...args: any[]): void {
    if (process.env.TEST_VERBOSE) {
      console.log(`[TRACE] ${message}`, ...args);
    }
  }
}

/**
 * Test utilities
 */
export class TestUtils {
  /**
   * Generate a random UUID for testing
   */
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Wait for a specified amount of time
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Create a mock Express request
   */
  static createMockRequest(options: {
    params?: any;
    body?: any;
    query?: any;
    headers?: any;
  } = {}): any {
    return {
      params: options.params || {},
      body: options.body || {},
      query: options.query || {},
      headers: options.headers || {},
      ...options
    };
  }

  /**
   * Create a mock Express response
   */
  static createMockResponse(): any {
    const res: any = {
      statusCode: 200,
      headers: {},
      body: null
    };

    res.status = jest.fn().mockImplementation((code: number) => {
      res.statusCode = code;
      return res;
    });

    res.json = jest.fn().mockImplementation((data: any) => {
      res.body = data;
      return res;
    });

    res.send = jest.fn().mockImplementation((data: any) => {
      res.body = data;
      return res;
    });

    res.header = jest.fn().mockImplementation((name: string, value: string) => {
      res.headers[name] = value;
      return res;
    });

    return res;
  }

  /**
   * Validate UUID format
   */
  static isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * Deep clone an object
   */
  static deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }
}
