/**
 * Test data for unit and integration tests
 */

export const sampleWorkflows = {
  simple: {
    name: 'Simple Test Workflow',
    description: 'A simple workflow for testing',
    input_schema: {
      type: 'object',
      properties: {
        message: { type: 'string' }
      },
      required: ['message']
    },
    output_schema: {
      type: 'object',
      properties: {
        result: { type: 'string' }
      }
    },
    nodes_config: {
      nodes: [
        {
          id: 'transform',
          type: 'javascript',
          name: 'Transform Message',
          config: {
            script: 'return { result: `Hello, ${input.message}!` };'
          }
        }
      ],
      edges: []
    },
    retry_config: {
      maxRetries: 3,
      retryDelay: 1000,
      retryBackoffMultiplier: 2
    },
    observability_config: {
      logLevel: 'info',
      metricsEnabled: true,
      logInputOutput: true
    }
  },

  complex: {
    name: 'Complex Test Workflow',
    description: 'A complex workflow with multiple nodes',
    input_schema: {
      type: 'object',
      properties: {
        userId: { type: 'string' },
        action: { type: 'string' }
      },
      required: ['userId', 'action']
    },
    nodes_config: {
      nodes: [
        {
          id: 'validate',
          type: 'javascript',
          name: 'Validate Input',
          config: {
            script: `
              if (!input.userId || !input.action) {
                throw new Error('Missing required fields');
              }
              return { userId: input.userId, action: input.action, validated: true };
            `
          }
        },
        {
          id: 'fetch_user',
          type: 'sql',
          name: 'Fetch User Data',
          config: {
            dataSource: 'postgres-main',
            query: 'SELECT * FROM users WHERE id = $1',
            parameters: ['${userId}']
          }
        },
        {
          id: 'process',
          type: 'javascript',
          name: 'Process Action',
          config: {
            script: `
              const user = input.fetch_user[0];
              const action = input.action;
              return {
                userId: user.id,
                userName: user.name,
                action: action,
                timestamp: new Date().toISOString(),
                processed: true
              };
            `
          }
        }
      ],
      edges: [
        { source: 'validate', target: 'fetch_user' },
        { source: 'fetch_user', target: 'process' }
      ]
    }
  },

  withRetries: {
    name: 'Workflow with Retries',
    description: 'A workflow that demonstrates retry mechanisms',
    input_schema: {
      type: 'object',
      properties: {
        url: { type: 'string' }
      }
    },
    nodes_config: {
      nodes: [
        {
          id: 'http_call',
          type: 'http',
          name: 'HTTP Request',
          config: {
            url: '${url}',
            method: 'GET',
            timeout: 5000
          }
        },
        {
          id: 'process_response',
          type: 'javascript',
          name: 'Process Response',
          config: {
            script: `
              if (input.http_call.status !== 200) {
                throw new Error('HTTP request failed');
              }
              return { data: input.http_call.data, processed: true };
            `
          }
        }
      ],
      edges: [
        { source: 'http_call', target: 'process_response' }
      ]
    },
    retry_config: {
      maxRetries: 5,
      retryDelay: 2000,
      retryBackoffMultiplier: 1.5,
      retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'HTTP_ERROR']
    }
  }
};

export const sampleNodeConfigs = {
  javascript: {
    name: 'String Processor',
    description: 'Processes string inputs',
    node_type: 'javascript',
    config: {
      script: `
        const input_text = input.text || '';
        return {
          original: input_text,
          uppercase: input_text.toUpperCase(),
          lowercase: input_text.toLowerCase(),
          length: input_text.length,
          reversed: input_text.split('').reverse().join('')
        };
      `
    }
  },

  http: {
    name: 'Weather API',
    description: 'Fetches weather data from external API',
    node_type: 'http',
    config: {
      url: 'https://api.openweathermap.org/data/2.5/weather',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      queryParams: {
        q: '${city}',
        appid: '${api_key}'
      }
    }
  },

  sql: {
    name: 'User Lookup',
    description: 'Looks up user information from database',
    node_type: 'sql',
    config: {
      dataSource: 'postgres-main',
      query: `
        SELECT u.id, u.name, u.email, u.created_at,
               COUNT(o.id) as order_count
        FROM users u
        LEFT JOIN orders o ON u.id = o.user_id
        WHERE u.id = $1
        GROUP BY u.id, u.name, u.email, u.created_at
      `,
      parameters: ['${userId}']
    }
  },

  redis: {
    name: 'Cache Manager',
    description: 'Manages cache operations',
    node_type: 'redis',
    config: {
      command: 'GET',
      args: ['user:${userId}:profile']
    }
  },

  litellm: {
    name: 'Text Summarizer',
    description: 'Summarizes text using LLM',
    node_type: 'litellm',
    config: {
      model: 'gpt-3.5-turbo',
      dataSource: 'openai-api',
      prompt: 'Summarize the following text in 2-3 sentences: ${text}',
      maxTokens: 150,
      temperature: 0.3
    }
  }
};

export const sampleDataSources = {
  postgresql: {
    name: 'postgres-main',
    description: 'Main PostgreSQL database',
    type: 'postgresql',
    connection_config: {
      host: 'localhost',
      port: 5432,
      database: 'myapp_production',
      username: 'app_user',
      password: 'secure_password_123'
    }
  },

  mysql: {
    name: 'mysql-analytics',
    description: 'MySQL analytics database',
    type: 'mysql',
    connection_config: {
      host: 'analytics.example.com',
      port: 3306,
      database: 'analytics',
      username: 'analytics_user',
      password: 'analytics_pass_456'
    }
  },

  redis: {
    name: 'redis-cache',
    description: 'Redis cache cluster',
    type: 'redis',
    connection_config: {
      host: 'redis.example.com',
      port: 6379,
      password: 'redis_password_789',
      db: 0
    }
  },

  restApi: {
    name: 'external-api',
    description: 'External REST API',
    type: 'rest',
    connection_config: {
      baseUrl: 'https://api.example.com/v1',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MyApp/1.0'
      },
      auth: {
        type: 'bearer',
        token: 'bearer_token_abc123'
      }
    }
  },

  openaiApi: {
    name: 'openai-api',
    description: 'OpenAI API connection',
    type: 'litellm',
    connection_config: {
      apiUrl: 'https://api.openai.com/v1',
      apiKey: 'sk-test-key-xyz789'
    }
  }
};

export const sampleInputs = {
  simple: {
    message: 'World'
  },

  complex: {
    userId: 'user-123',
    action: 'login'
  },

  httpTest: {
    url: 'https://httpbin.org/get'
  },

  stringProcessor: {
    text: 'Hello, World! This is a test string.'
  },

  weatherApi: {
    city: 'London',
    api_key: 'test_api_key'
  },

  userLookup: {
    userId: 'user-456'
  },

  cacheTest: {
    userId: 'user-789'
  },

  textSummarizer: {
    text: `
      Artificial Intelligence (AI) is a branch of computer science that aims to create 
      intelligent machines that work and react like humans. Some of the activities 
      computers with artificial intelligence are designed for include speech recognition, 
      learning, planning, and problem solving. AI research has been highly successful 
      in developing effective techniques for solving a wide range of problems, from 
      game playing to medical diagnosis.
    `
  }
};

export const invalidData = {
  workflow: {
    missingName: {
      description: 'Missing name field'
    },
    invalidSchema: {
      name: 'Invalid Schema Workflow',
      input_schema: 'not an object',
      nodes_config: { nodes: [], edges: [] }
    },
    emptyNodes: {
      name: 'Empty Nodes Workflow',
      input_schema: { type: 'object' },
      nodes_config: { nodes: [], edges: [] }
    },
    duplicateNodeIds: {
      name: 'Duplicate Node IDs',
      input_schema: { type: 'object' },
      nodes_config: {
        nodes: [
          { id: 'node1', type: 'javascript', name: 'Node 1', config: {} },
          { id: 'node1', type: 'javascript', name: 'Node 2', config: {} }
        ],
        edges: []
      }
    }
  },

  nodeConfig: {
    missingType: {
      name: 'Missing Type Node',
      config: {}
    },
    unsupportedType: {
      name: 'Unsupported Type Node',
      node_type: 'unsupported',
      config: {}
    },
    invalidJavaScript: {
      name: 'Invalid JavaScript Node',
      node_type: 'javascript',
      config: {} // Missing script
    }
  },

  dataSource: {
    missingConfig: {
      name: 'Missing Config',
      type: 'postgresql'
    },
    invalidPostgres: {
      name: 'Invalid Postgres',
      type: 'postgresql',
      connection_config: {
        host: 'localhost'
        // Missing required fields
      }
    },
    invalidPort: {
      name: 'Invalid Port',
      type: 'postgresql',
      connection_config: {
        host: 'localhost',
        port: 70000, // Invalid port
        database: 'test',
        username: 'user',
        password: 'pass'
      }
    }
  }
};
