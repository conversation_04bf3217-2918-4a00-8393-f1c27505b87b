#!/usr/bin/env ts-node

import 'reflect-metadata';
import { configureContainer } from '../src/inversify.config';
import { TestDatabaseSetup } from './setup/testSetup';
import { TYPES } from '../src/types';
import { ILogger } from '../src/core/interfaces/ILogger';

/**
 * Test runner script
 * Sets up the test environment and runs all tests
 */
async function runTests() {
  const container = configureContainer();
  const logger = container.get<ILogger>(TYPES.Logger);
  const testSetup = new TestDatabaseSetup(container);

  try {
    logger.info('Setting up test environment...');
    
    // Initialize test database
    await testSetup.initialize();
    
    // Create test data
    await testSetup.createTestData();
    
    logger.info('Test environment setup complete');
    logger.info('Running tests...');
    
    // Tests will be run by Jest
    // This script just sets up the environment
    
  } catch (error) {
    logger.error('Failed to setup test environment', error);
    process.exit(1);
  } finally {
    // Cleanup will be handled by individual test files
  }
}

// Run if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

export { runTests };
