import 'reflect-metadata';
import request from 'supertest';
import express from 'express';
import { WorkflowController } from '../../../src/api/controllers/WorkflowController';
import { NodeController } from '../../../src/api/controllers/NodeController';
import { DataSourceController } from '../../../src/api/controllers/DataSourceController';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { IValidator } from '../../../src/core/interfaces/IValidator';
import { IRepository } from '../../../src/core/interfaces/IRepository';
import { IWorkflowEngine } from '../../../src/core/interfaces/IWorkflowEngine';
import { IObservabilityManager } from '../../../src/core/interfaces/IObservabilityManager';
import { INodeRegistry } from '../../../src/core/interfaces/INodeRegistry';

describe('Admin API Integration Tests (Mock)', () => {
  let app: express.Application;
  let mockLogger: jest.Mocked<ILogger>;
  let mockRepository: jest.Mocked<IRepository<any>>;
  let mockValidator: jest.Mocked<IValidator<any>>;
  let mockWorkflowEngine: jest.Mocked<IWorkflowEngine>;
  let mockObservabilityManager: jest.Mocked<IObservabilityManager>;
  let mockNodeRegistry: jest.Mocked<INodeRegistry>;

  beforeAll(async () => {
    // Create mocks
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    };

    mockRepository = {
      findAll: jest.fn(),
      findById: jest.fn(),
      findBy: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      transaction: jest.fn(),
      query: jest.fn()
    };

    mockValidator = {
      validateForCreate: jest.fn(),
      validateForUpdate: jest.fn(),
      validateId: jest.fn()
    };

    mockWorkflowEngine = {
      initialize: jest.fn(),
      executeWorkflow: jest.fn(),
      executeWorkflowConfig: jest.fn(),
      getWorkflow: jest.fn(),
      reloadWorkflow: jest.fn(),
      reloadAllWorkflows: jest.fn(),
      removeWorkflow: jest.fn(),
      getWorkflowExecutionHistory: jest.fn()
    };

    mockObservabilityManager = {
      recordWorkflowExecution: jest.fn(),
      recordNodeExecution: jest.fn(),
      getWorkflowMetrics: jest.fn(),
      getNodeMetrics: jest.fn(),
      exportPrometheusMetrics: jest.fn(),
      cleanupOldData: jest.fn()
    };

    mockNodeRegistry = {
      getRegisteredNodeTypes: jest
        .fn()
        .mockReturnValue([
          'javascript-action',
          'http-action',
          'sql-action',
          'redis-action',
          'litellm-action'
        ]),
      getNode: jest.fn(),
      registerNode: jest.fn()
    };

    // Create Express app
    app = express();
    app.use(express.json());

    // Create controllers
    const workflowController = new WorkflowController(
      mockRepository,
      mockValidator,
      mockLogger,
      mockWorkflowEngine,
      mockObservabilityManager
    );

    const nodeController = new NodeController(
      mockRepository,
      mockValidator,
      mockLogger,
      mockNodeRegistry
    );

    const dataSourceController = new DataSourceController(
      mockRepository,
      mockValidator,
      mockLogger
    );

    // Mount basic routes
    app.get('/admin/workflows', (req, res) => workflowController.getAll(req, res));
    app.post('/admin/workflows', (req, res) => workflowController.create(req, res));
    app.get('/admin/workflows/:id', (req, res) => workflowController.getById(req, res));
    app.post('/admin/workflows/test', (req, res) => workflowController.test(req, res));

    app.get('/admin/nodes/types', (req, res) => nodeController.getNodeTypes(req, res));
    app.get('/admin/datasources/types', (req, res) =>
      dataSourceController.getDataSourceTypes(req, res)
    );
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Workflow API', () => {
    it('should create a new workflow', async () => {
      const workflowData = {
        name: 'Test Workflow',
        description: 'A test workflow',
        input_schema: { type: 'object' },
        nodes_config: { nodes: [], edges: [] }
      };

      const validatedWorkflow = { ...workflowData, enabled: true, version: 1 };
      const createdWorkflow = { id: '123', ...validatedWorkflow };

      mockValidator.validateForCreate.mockResolvedValue(validatedWorkflow);
      mockRepository.create.mockResolvedValue(createdWorkflow);

      const response = await request(app).post('/admin/workflows').send(workflowData).expect(201);

      expect(response.body.id).toBe('123');
      expect(response.body.name).toBe(workflowData.name);
      expect(mockValidator.validateForCreate).toHaveBeenCalledWith(workflowData);
      expect(mockRepository.create).toHaveBeenCalledWith(validatedWorkflow);
    });

    it('should get all workflows', async () => {
      const workflows = [
        { id: '1', name: 'Workflow 1' },
        { id: '2', name: 'Workflow 2' }
      ];

      mockRepository.findAll.mockResolvedValue(workflows);

      const response = await request(app).get('/admin/workflows').expect(200);

      expect(response.body).toEqual(workflows);
      expect(mockRepository.findAll).toHaveBeenCalled();
    });

    it('should get workflow by ID', async () => {
      const workflow = { id: '123', name: 'Test Workflow' };

      mockValidator.validateId.mockImplementation(() => {});
      mockRepository.findById.mockResolvedValue(workflow);

      const response = await request(app).get('/admin/workflows/123').expect(200);

      expect(response.body).toEqual(workflow);
      expect(mockValidator.validateId).toHaveBeenCalledWith('123');
      expect(mockRepository.findById).toHaveBeenCalledWith('123');
    });

    it('should test a workflow configuration', async () => {
      const testData = {
        workflow: {
          name: 'Test Workflow',
          input_schema: { type: 'object' },
          nodes_config: { nodes: [], edges: [] }
        },
        input: { test: 'data' }
      };

      const validatedWorkflow = testData.workflow;
      const executionResult = { result: 'success' };

      mockValidator.validateForCreate.mockResolvedValue(validatedWorkflow);
      mockWorkflowEngine.executeWorkflowConfig.mockResolvedValue(executionResult);

      const response = await request(app).post('/admin/workflows/test').send(testData).expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.result).toEqual(executionResult);
      expect(mockValidator.validateForCreate).toHaveBeenCalledWith(testData.workflow);
      expect(mockWorkflowEngine.executeWorkflowConfig).toHaveBeenCalledWith(
        validatedWorkflow,
        testData.input
      );
    });

    it('should return 404 for non-existent workflow', async () => {
      mockValidator.validateId.mockImplementation(() => {});
      mockRepository.findById.mockResolvedValue(null);

      await request(app).get('/admin/workflows/nonexistent').expect(404);

      expect(mockRepository.findById).toHaveBeenCalledWith('nonexistent');
    });
  });

  describe('Node Types API', () => {
    it('should get available node types', async () => {
      const response = await request(app).get('/admin/nodes/types').expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toContain('javascript-action');
      expect(response.body).toContain('http-action');
      expect(response.body).toContain('sql-action');
      expect(response.body).toContain('redis-action');
      expect(response.body).toContain('litellm-action');
      expect(mockNodeRegistry.getRegisteredNodeTypes).toHaveBeenCalled();
    });
  });

  describe('Data Source Types API', () => {
    it('should get available data source types', async () => {
      const response = await request(app).get('/admin/datasources/types').expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body).toContain('postgresql');
      expect(response.body).toContain('mysql');
      expect(response.body).toContain('redis');
      expect(response.body).toContain('rest');
      expect(response.body).toContain('litellm');
    });
  });

  describe('Error Handling', () => {
    it('should handle validation errors', async () => {
      const invalidData = { name: '' }; // Invalid workflow data

      mockValidator.validateForCreate.mockRejectedValue(new Error('Validation failed'));

      await request(app).post('/admin/workflows').send(invalidData).expect(500); // Will be 500 because we're throwing a generic Error, not ValidationError

      expect(mockValidator.validateForCreate).toHaveBeenCalledWith(invalidData);
    });

    it('should handle missing workflow in test endpoint', async () => {
      const testData = { input: { test: 'data' } }; // Missing workflow

      await request(app).post('/admin/workflows/test').send(testData).expect(400);
    });

    it('should handle missing input in test endpoint', async () => {
      const testData = { workflow: { name: 'Test' } }; // Missing input

      await request(app).post('/admin/workflows/test').send(testData).expect(400);
    });
  });
});
