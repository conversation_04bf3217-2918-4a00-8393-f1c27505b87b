import fetch from 'node-fetch';

/**
 * A simple client for testing the MCP server
 */
export class SimpleMcpClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3000/mcp') {
    this.baseUrl = baseUrl;
  }

  /**
   * Call the getDateTime tool
   * @param format The date format (ISO, UTC, local)
   * @returns The formatted date string
   */
  async getDateTime(format: 'ISO' | 'UTC' | 'local' = 'ISO'): Promise<string> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'callTool',
        params: {
          name: 'getDateTime',
          arguments: { format }
        },
        id: 1
      })
    });

    const data = await response.json();

    if (data.error) {
      throw new Error(`Error calling getDateTime: ${data.error.message}`);
    }

    return data.result.toolResult;
  }
}

export default SimpleMcpClient;
