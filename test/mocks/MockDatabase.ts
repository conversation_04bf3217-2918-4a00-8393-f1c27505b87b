/**
 * Mock Database implementation for unit testing
 */

import { DataSource, Repository, <PERSON>ry<PERSON><PERSON>ner, EntityManager } from 'typeorm';
import { IDatabase } from '../../src/core/interfaces/IDatabase';

export class MockDatabase implements IDatabase {
  private mockDataSource: Partial<DataSource>;
  private mockRepositories: Map<string, any> = new Map();
  private mockData: Map<string, any[]> = new Map();

  constructor() {
    this.mockDataSource = {
      getRepository: jest.fn((entity: any) => {
        const entityName = entity.name || entity;
        if (!this.mockRepositories.has(entityName)) {
          this.mockRepositories.set(entityName, this.createMockRepository(entityName));
        }
        return this.mockRepositories.get(entityName);
      }),
      createQueryRunner: jest.fn(() => this.createMockQueryRunner()),
      manager: this.createMockEntityManager(),
      isInitialized: true,
      initialize: jest.fn().mockResolvedValue(undefined),
      destroy: jest.fn().mockResolvedValue(undefined)
    };
  }

  async getDataSource(): Promise<DataSource> {
    return this.mockDataSource as DataSource;
  }

  async initialize(): Promise<void> {
    // Mock initialization
  }

  async close(): Promise<void> {
    // Mock close
  }

  async runMigrations(): Promise<void> {
    // Mock migrations
  }

  private createMockRepository(entityName: string): Repository<any> {
    const mockRepo = {
      create: jest.fn((data: any) => ({ id: this.generateId(), ...data })),
      save: jest.fn(async (entity: any) => {
        const entities = Array.isArray(entity) ? entity : [entity];
        const savedEntities = entities.map((e) => {
          if (!e.id) e.id = this.generateId();
          this.addToMockData(entityName, e);
          return e;
        });
        return Array.isArray(entity) ? savedEntities : savedEntities[0];
      }),
      find: jest.fn(async (options?: any) => {
        const data = this.getMockData(entityName);
        if (!options) return data;

        let result = data;

        // Apply where conditions
        if (options.where) {
          result = result.filter((item) => this.matchesWhere(item, options.where));
        }

        // Apply order
        if (options.order) {
          result = this.applyOrder(result, options.order);
        }

        // Apply take/skip
        if (options.skip) {
          result = result.slice(options.skip);
        }
        if (options.take) {
          result = result.slice(0, options.take);
        }

        return result;
      }),
      findOne: jest.fn(async (options: any) => {
        const data = this.getMockData(entityName);
        if (options.where) {
          return data.find((item) => this.matchesWhere(item, options.where)) || null;
        }
        return data[0] || null;
      }),
      findOneBy: jest.fn(async (where: any) => {
        const data = this.getMockData(entityName);
        return data.find((item) => this.matchesWhere(item, where)) || null;
      }),
      update: jest.fn(async (criteria: any, partialEntity: any) => {
        const data = this.getMockData(entityName);
        let affected = 0;

        data.forEach((item) => {
          if (this.matchesCriteria(item, criteria)) {
            Object.assign(item, partialEntity);
            affected++;
          }
        });

        return { affected };
      }),
      delete: jest.fn(async (criteria: any) => {
        const data = this.getMockData(entityName);
        const initialLength = data.length;

        const filteredData = data.filter((item) => !this.matchesCriteria(item, criteria));
        this.setMockData(entityName, filteredData);

        return { affected: initialLength - filteredData.length };
      }),
      count: jest.fn(async (options?: any) => {
        const data = this.getMockData(entityName);
        if (!options || !options.where) return data.length;

        return data.filter((item) => this.matchesWhere(item, options.where)).length;
      }),
      createQueryBuilder: jest.fn((alias?: string) =>
        this.createMockQueryBuilder(entityName, alias)
      ),
      remove: jest.fn(async (entity: any) => {
        const entities = Array.isArray(entity) ? entity : [entity];
        entities.forEach((e) => this.removeFromMockData(entityName, e.id));
        return entity;
      })
    };

    return mockRepo as any;
  }

  private createMockQueryBuilder(entityName: string, alias?: string) {
    const data = this.getMockData(entityName);
    let currentData = [...data];
    let whereConditions: any[] = [];
    let orderConditions: any[] = [];
    let limitValue: number | undefined;
    let offsetValue: number | undefined;
    let selectFields: string[] = [];
    let groupByFields: string[] = [];

    const applyFilters = () => {
      let result = [...data];

      // Apply where conditions
      if (whereConditions.length > 0) {
        result = result.filter((item) => {
          return whereConditions.every((condition) => {
            // Simple mock condition evaluation
            if (condition.condition.includes('=')) {
              const [field, value] = condition.condition.split('=').map((s) => s.trim());
              const fieldName = field.replace(/^\w+\./, ''); // Remove alias prefix
              const paramKey = Object.keys(condition.parameters || {})[0];
              const paramValue = condition.parameters?.[paramKey];
              return item[fieldName] === paramValue;
            }
            if (condition.condition.includes('IN')) {
              const field = condition.condition.split(' ')[0].replace(/^\w+\./, '');
              const paramKey = Object.keys(condition.parameters || {})[0];
              const paramValues = condition.parameters?.[paramKey];
              return Array.isArray(paramValues) && paramValues.includes(item[field]);
            }
            if (condition.condition.includes('>=')) {
              const [field, value] = condition.condition.split('>=').map((s) => s.trim());
              const fieldName = field.replace(/^\w+\./, '');
              const paramKey = Object.keys(condition.parameters || {})[0];
              const paramValue = condition.parameters?.[paramKey];
              return new Date(item[fieldName]) >= new Date(paramValue);
            }
            if (condition.condition.includes('<=')) {
              const [field, value] = condition.condition.split('<=').map((s) => s.trim());
              const fieldName = field.replace(/^\w+\./, '');
              const paramKey = Object.keys(condition.parameters || {})[0];
              const paramValue = condition.parameters?.[paramKey];
              return new Date(item[fieldName]) <= new Date(paramValue);
            }
            if (condition.condition.includes('<')) {
              const [field, value] = condition.condition.split('<').map((s) => s.trim());
              const fieldName = field.replace(/^\w+\./, '');
              const paramKey = Object.keys(condition.parameters || {})[0];
              const paramValue = condition.parameters?.[paramKey];
              return new Date(item[fieldName]) < new Date(paramValue);
            }
            if (condition.condition.includes('ILIKE')) {
              const parts = condition.condition.split('ILIKE').map((s) => s.trim());
              const fieldPart = parts[0];
              const paramKey = Object.keys(condition.parameters || {})[0];
              const paramValue = condition.parameters?.[paramKey];
              const searchValue = paramValue?.replace(/%/g, '');

              // Handle complex field expressions like "field::text"
              let fieldValue = '';
              if (fieldPart.includes('::text')) {
                const fieldName = fieldPart.split('::')[0].replace(/^\w+\./, '');
                fieldValue = JSON.stringify(item[fieldName] || '');
              } else {
                const fieldName = fieldPart.replace(/^\w+\./, '');
                fieldValue = String(item[fieldName] || '');
              }

              // For history search, also search in details, action, and other relevant fields
              const searchInFields = [
                fieldValue,
                JSON.stringify(item.details || ''),
                String(item.action || ''),
                String(item.message || ''),
                String(item.errorMessage || '')
              ];

              return searchInFields.some((field) =>
                field.toLowerCase().includes(String(searchValue || '').toLowerCase())
              );
            }
            return true;
          });
        });
      }

      // Apply ordering
      if (orderConditions.length > 0) {
        result.sort((a, b) => {
          for (const order of orderConditions) {
            const fieldName = order.field.replace(/^\w+\./, '');
            const aVal = a[fieldName];
            const bVal = b[fieldName];

            if (aVal < bVal) return order.order === 'ASC' ? -1 : 1;
            if (aVal > bVal) return order.order === 'ASC' ? 1 : -1;
          }
          return 0;
        });
      }

      // Apply offset
      if (offsetValue) {
        result = result.slice(offsetValue);
      }

      // Apply limit
      if (limitValue) {
        result = result.slice(0, limitValue);
      }

      return result;
    };

    const queryBuilder = {
      where: jest.fn((condition: string, parameters?: any) => {
        whereConditions = [{ condition, parameters }]; // Reset conditions for where
        return queryBuilder;
      }),
      andWhere: jest.fn((condition: string, parameters?: any) => {
        whereConditions.push({ condition, parameters, operator: 'AND' });
        return queryBuilder;
      }),
      orWhere: jest.fn((condition: string, parameters?: any) => {
        whereConditions.push({ condition, parameters, operator: 'OR' });
        return queryBuilder;
      }),
      orderBy: jest.fn((field: string, order: 'ASC' | 'DESC' = 'ASC') => {
        orderConditions = [{ field, order }]; // Reset for orderBy
        return queryBuilder;
      }),
      limit: jest.fn((limit: number) => {
        limitValue = limit;
        return queryBuilder;
      }),
      offset: jest.fn((offset: number) => {
        offsetValue = offset;
        return queryBuilder;
      }),
      take: jest.fn((take: number) => {
        limitValue = take;
        return queryBuilder;
      }),
      skip: jest.fn((skip: number) => {
        offsetValue = skip;
        return queryBuilder;
      }),
      select: jest.fn((fields?: string | string[]) => {
        if (fields) {
          selectFields = Array.isArray(fields) ? fields : [fields];
        }
        return queryBuilder;
      }),
      addSelect: jest.fn((field: string, alias?: string) => {
        selectFields.push(alias || field);
        return queryBuilder;
      }),
      groupBy: jest.fn((field: string) => {
        groupByFields = [field];
        return queryBuilder;
      }),
      having: jest.fn(() => queryBuilder),
      clone: jest.fn(() => {
        // Create a new query builder with the same state
        const cloned = this.createMockQueryBuilder(entityName, alias);
        return cloned;
      }),
      getMany: jest.fn(async () => {
        return applyFilters();
      }),
      getOne: jest.fn(async () => {
        const results = applyFilters();
        return results[0] || null;
      }),
      getCount: jest.fn(async () => {
        const results = applyFilters();
        return results.length;
      }),
      getRawMany: jest.fn(async () => {
        const results = applyFilters();

        // Handle GROUP BY aggregation
        if (groupByFields.length > 0) {
          const grouped = new Map();
          results.forEach((item) => {
            const groupKey = groupByFields
              .map((field) => {
                const fieldName = field.replace(/^\w+\./, '');
                return item[fieldName];
              })
              .join('|');

            if (!grouped.has(groupKey)) {
              const groupItem = { ...item };
              groupItem.count = 1;
              grouped.set(groupKey, groupItem);
            } else {
              const existing = grouped.get(groupKey);
              existing.count = (existing.count || 0) + 1;
            }
          });

          return Array.from(grouped.values())
            .map((item) => {
              const result: any = {};
              groupByFields.forEach((field) => {
                const fieldName = field.replace(/^\w+\./, '');
                result[fieldName] = item[fieldName];
              });
              result.count = String(item.count || 0); // Convert to string as SQL would
              return result;
            })
            .sort((a, b) => {
              // Apply ORDER BY if specified
              if (orderConditions.length > 0) {
                for (const order of orderConditions) {
                  const fieldName =
                    order.field === 'count' ? 'count' : order.field.replace(/^\w+\./, '');
                  const aVal = fieldName === 'count' ? parseInt(a.count) : a[fieldName];
                  const bVal = fieldName === 'count' ? parseInt(b.count) : b[fieldName];

                  if (aVal < bVal) return order.order === 'ASC' ? -1 : 1;
                  if (aVal > bVal) return order.order === 'ASC' ? 1 : -1;
                }
              }
              return 0;
            });
        }

        return results.map((item) => ({ ...item }));
      }),
      getRawOne: jest.fn(async () => {
        const results = applyFilters();

        // Handle aggregation functions like AVG
        if (selectFields.some((f) => f.includes('AVG'))) {
          const avgField = selectFields.find((f) => f.includes('AVG'));
          if (avgField && results.length > 0) {
            const fieldMatch = avgField.match(/AVG\((\w+\.)?(\w+)\)/);
            if (fieldMatch) {
              const fieldName = fieldMatch[2];
              const values = results.map((r) => r[fieldName]).filter((v) => v != null);
              const avg = values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
              return { avg };
            }
          }
        }

        return results[0] ? { ...results[0] } : null;
      }),
      execute: jest.fn(async () => {
        return { affected: 1 };
      }),
      delete: jest.fn(() => ({
        where: jest.fn((condition: string, parameters?: any) => ({
          execute: jest.fn(async () => {
            // Mock delete operation
            const originalLength = data.length;
            // Simple mock - remove items that match condition
            const filtered = data.filter((item) => {
              if (condition.includes('<')) {
                const [field, value] = condition.split('<').map((s) => s.trim());
                const fieldName = field.replace(/^\w+\./, '');
                const paramKey = Object.keys(parameters || {})[0];
                const paramValue = parameters?.[paramKey];
                return !(new Date(item[fieldName]) < new Date(paramValue));
              }
              return true;
            });
            // Note: this context is lost here, but for mock purposes we'll keep it simple
            return { affected: originalLength - filtered.length };
          })
        }))
      }))
    };

    return queryBuilder;
  }

  private createMockQueryRunner(): QueryRunner {
    return {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      query: jest.fn(),
      manager: this.createMockEntityManager()
    } as any;
  }

  private createMockEntityManager(): EntityManager {
    return {
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      query: jest.fn(),
      transaction: jest.fn()
    } as any;
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private getMockData(entityName: string): any[] {
    return this.mockData.get(entityName) || [];
  }

  private setMockData(entityName: string, data: any[]): void {
    this.mockData.set(entityName, data);
  }

  private addToMockData(entityName: string, entity: any): void {
    const data = this.getMockData(entityName);
    const existingIndex = data.findIndex((item) => item.id === entity.id);
    if (existingIndex >= 0) {
      data[existingIndex] = entity;
    } else {
      data.push(entity);
    }
    this.setMockData(entityName, data);
  }

  private removeFromMockData(entityName: string, id: string): void {
    const data = this.getMockData(entityName);
    const filteredData = data.filter((item) => item.id !== id);
    this.setMockData(entityName, filteredData);
  }

  private matchesWhere(item: any, where: any): boolean {
    if (typeof where === 'string') return true; // Simple string conditions

    for (const [key, value] of Object.entries(where)) {
      if (item[key] !== value) return false;
    }
    return true;
  }

  private matchesCriteria(item: any, criteria: any): boolean {
    if (typeof criteria === 'string' || typeof criteria === 'number') {
      return item.id === criteria;
    }
    return this.matchesWhere(item, criteria);
  }

  private applyOrder(data: any[], order: any): any[] {
    const entries = Object.entries(order);
    if (entries.length === 0) return data;

    return data.sort((a, b) => {
      for (const [field, direction] of entries) {
        const aVal = a[field];
        const bVal = b[field];

        if (aVal < bVal) return direction === 'ASC' ? -1 : 1;
        if (aVal > bVal) return direction === 'ASC' ? 1 : -1;
      }
      return 0;
    });
  }

  // Helper methods for testing
  public clearAllData(): void {
    this.mockData.clear();
  }

  public setTestData(entityName: string, data: any[]): void {
    this.setMockData(entityName, data);
  }

  public getTestData(entityName: string): any[] {
    return this.getMockData(entityName);
  }

  public getRepository(entityName: string): any {
    return {
      find: jest.fn(async (options?: any) => {
        const data = this.getMockData(entityName);
        if (!options) return data;

        let result = data;

        // Apply where conditions
        if (options.where) {
          result = result.filter((item) => this.matchesWhere(item, options.where));
        }

        // Apply order
        if (options.order) {
          result = this.applyOrder(result, options.order);
        }

        return result;
      }),

      findOne: jest.fn(async (options?: any) => {
        const data = this.getMockData(entityName);
        if (!options) return data[0] || null;

        if (options.where) {
          const found = data.find((item) => this.matchesWhere(item, options.where));
          return found || null;
        }

        return data[0] || null;
      }),

      save: jest.fn(async (entity: any) => {
        if (Array.isArray(entity)) {
          entity.forEach((e) => this.addToMockData(entityName, e));
          return entity;
        } else {
          if (!entity.id) {
            entity.id = this.generateId();
          }
          this.addToMockData(entityName, entity);
          return entity;
        }
      }),

      create: jest.fn((data: any) => {
        if (!data.id) {
          data.id = this.generateId();
        }
        return data;
      }),

      update: jest.fn(async (criteria: any, updateData: any) => {
        const data = this.getMockData(entityName);
        let affected = 0;

        data.forEach((item) => {
          if (this.matchesCriteria(item, criteria)) {
            Object.assign(item, updateData);
            affected++;
          }
        });

        return { affected };
      }),

      delete: jest.fn(async (criteria: any) => {
        const data = this.getMockData(entityName);
        const originalLength = data.length;

        const filtered = data.filter((item) => !this.matchesCriteria(item, criteria));
        this.setMockData(entityName, filtered);

        return { affected: originalLength - filtered.length };
      }),

      createQueryBuilder: jest.fn((alias?: string) => {
        return this.createMockQueryBuilder(entityName, alias);
      }),

      findAndCount: jest.fn(async (options?: any) => {
        const data = this.getMockData(entityName);
        let result = data;

        // Apply where conditions
        if (options?.where) {
          result = result.filter((item) => this.matchesWhere(item, options.where));
        }

        // Apply order
        if (options?.order) {
          result = this.applyOrder(result, options.order);
        }

        const total = result.length;

        // Apply pagination
        if (options?.skip) {
          result = result.slice(options.skip);
        }
        if (options?.take) {
          result = result.slice(0, options.take);
        }

        return [result, total];
      }),

      remove: jest.fn(async (entity: any) => {
        if (Array.isArray(entity)) {
          entity.forEach((e) => {
            if (e.id) {
              this.removeFromMockData(entityName, e.id);
            }
          });
          return entity;
        } else {
          if (entity.id) {
            this.removeFromMockData(entityName, entity.id);
          }
          return entity;
        }
      })
    };
  }
}
