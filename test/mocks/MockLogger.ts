/**
 * Mock Logger implementation for unit testing
 */

import { ILogger } from '../../src/core/interfaces/ILogger';

export interface LogEntry {
  level: string;
  message: string;
  meta?: any;
  timestamp: Date;
}

export class MockLogger implements ILogger {
  private logs: LogEntry[] = [];
  private isEnabled = true;

  debug(message: string, meta?: any): void {
    this.log('debug', message, meta);
  }

  info(message: string, meta?: any): void {
    this.log('info', message, meta);
  }

  warn(message: string, meta?: any): void {
    this.log('warn', message, meta);
  }

  error(message: string, meta?: any): void {
    this.log('error', message, meta);
  }

  private log(level: string, message: string, meta?: any): void {
    if (!this.isEnabled) return;

    this.logs.push({
      level,
      message,
      meta,
      timestamp: new Date()
    });
  }

  // Helper methods for testing
  public getLogs(): LogEntry[] {
    return [...this.logs];
  }

  public getLogsByLevel(level: string): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  public getLastLog(): LogEntry | undefined {
    return this.logs[this.logs.length - 1];
  }

  public clearLogs(): void {
    this.logs = [];
  }

  public hasLogWithMessage(message: string): boolean {
    return this.logs.some(log => log.message.includes(message));
  }

  public hasLogWithMeta(key: string, value: any): boolean {
    return this.logs.some(log => 
      log.meta && 
      typeof log.meta === 'object' && 
      log.meta[key] === value
    );
  }

  public getLogCount(): number {
    return this.logs.length;
  }

  public getLogCountByLevel(level: string): number {
    return this.logs.filter(log => log.level === level).length;
  }

  public enable(): void {
    this.isEnabled = true;
  }

  public disable(): void {
    this.isEnabled = false;
  }

  public isLoggingEnabled(): boolean {
    return this.isEnabled;
  }

  // Jest mock functions for spying
  public debugSpy = jest.fn(this.debug.bind(this));
  public infoSpy = jest.fn(this.info.bind(this));
  public warnSpy = jest.fn(this.warn.bind(this));
  public errorSpy = jest.fn(this.error.bind(this));
}
