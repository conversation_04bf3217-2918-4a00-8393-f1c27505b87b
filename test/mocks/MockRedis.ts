/**
 * Mock Redis implementation for unit testing
 */

import { IRedisClient } from '../../src/core/interfaces/IRedisClient';

export class MockRedis implements IRedisClient {
  private storage: Map<string, string> = new Map();
  private hashStorage: Map<string, Map<string, string>> = new Map();
  private listStorage: Map<string, string[]> = new Map();
  private setStorage: Map<string, Set<string>> = new Map();
  private expirations: Map<string, number> = new Map();
  private subscribers: Map<string, Array<(message: string) => void>> = new Map();

  async connect(): Promise<void> {
    // Mock connection
  }

  async disconnect(): Promise<void> {
    // Mock disconnection
  }

  async get(key: string): Promise<string | null> {
    this.checkExpiration(key);
    return this.storage.get(key) || null;
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    this.storage.set(key, value);
    if (ttl) {
      this.expirations.set(key, Date.now() + ttl * 1000);
    }
  }

  async del(key: string): Promise<number> {
    const existed = this.storage.has(key);
    this.storage.delete(key);
    this.expirations.delete(key);
    this.hashStorage.delete(key);
    this.listStorage.delete(key);
    this.setStorage.delete(key);
    return existed ? 1 : 0;
  }

  async exists(key: string): Promise<number> {
    this.checkExpiration(key);
    return this.storage.has(key) ? 1 : 0;
  }

  async expire(key: string, seconds: number): Promise<number> {
    if (this.storage.has(key)) {
      this.expirations.set(key, Date.now() + seconds * 1000);
      return 1;
    }
    return 0;
  }

  async ttl(key: string): Promise<number> {
    const expiration = this.expirations.get(key);
    if (!expiration) return -1;
    
    const remaining = Math.ceil((expiration - Date.now()) / 1000);
    return remaining > 0 ? remaining : -2;
  }

  async incr(key: string): Promise<number> {
    const current = parseInt(this.storage.get(key) || '0');
    const newValue = current + 1;
    this.storage.set(key, newValue.toString());
    return newValue;
  }

  async decr(key: string): Promise<number> {
    const current = parseInt(this.storage.get(key) || '0');
    const newValue = current - 1;
    this.storage.set(key, newValue.toString());
    return newValue;
  }

  async mget(keys: string[]): Promise<(string | null)[]> {
    return keys.map(key => {
      this.checkExpiration(key);
      return this.storage.get(key) || null;
    });
  }

  async mset(keyValues: Record<string, string>): Promise<void> {
    for (const [key, value] of Object.entries(keyValues)) {
      this.storage.set(key, value);
    }
  }

  // Hash operations
  async hget(key: string, field: string): Promise<string | null> {
    this.checkExpiration(key);
    const hash = this.hashStorage.get(key);
    return hash?.get(field) || null;
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    if (!this.hashStorage.has(key)) {
      this.hashStorage.set(key, new Map());
    }
    const hash = this.hashStorage.get(key)!;
    const isNew = !hash.has(field);
    hash.set(field, value);
    return isNew ? 1 : 0;
  }

  async hdel(key: string, field: string): Promise<number> {
    const hash = this.hashStorage.get(key);
    if (hash && hash.has(field)) {
      hash.delete(field);
      return 1;
    }
    return 0;
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    this.checkExpiration(key);
    const hash = this.hashStorage.get(key);
    if (!hash) return {};
    
    const result: Record<string, string> = {};
    for (const [field, value] of hash.entries()) {
      result[field] = value;
    }
    return result;
  }

  async hkeys(key: string): Promise<string[]> {
    this.checkExpiration(key);
    const hash = this.hashStorage.get(key);
    return hash ? Array.from(hash.keys()) : [];
  }

  async hvals(key: string): Promise<string[]> {
    this.checkExpiration(key);
    const hash = this.hashStorage.get(key);
    return hash ? Array.from(hash.values()) : [];
  }

  // List operations
  async lpush(key: string, value: string): Promise<number> {
    if (!this.listStorage.has(key)) {
      this.listStorage.set(key, []);
    }
    const list = this.listStorage.get(key)!;
    list.unshift(value);
    return list.length;
  }

  async rpush(key: string, value: string): Promise<number> {
    if (!this.listStorage.has(key)) {
      this.listStorage.set(key, []);
    }
    const list = this.listStorage.get(key)!;
    list.push(value);
    return list.length;
  }

  async lpop(key: string): Promise<string | null> {
    const list = this.listStorage.get(key);
    return list?.shift() || null;
  }

  async rpop(key: string): Promise<string | null> {
    const list = this.listStorage.get(key);
    return list?.pop() || null;
  }

  async llen(key: string): Promise<number> {
    const list = this.listStorage.get(key);
    return list?.length || 0;
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    const list = this.listStorage.get(key);
    if (!list) return [];
    
    const length = list.length;
    const startIndex = start < 0 ? Math.max(0, length + start) : start;
    const stopIndex = stop < 0 ? length + stop : stop;
    
    return list.slice(startIndex, stopIndex + 1);
  }

  // Set operations
  async sadd(key: string, member: string): Promise<number> {
    if (!this.setStorage.has(key)) {
      this.setStorage.set(key, new Set());
    }
    const set = this.setStorage.get(key)!;
    const sizeBefore = set.size;
    set.add(member);
    return set.size - sizeBefore;
  }

  async srem(key: string, member: string): Promise<number> {
    const set = this.setStorage.get(key);
    if (set && set.has(member)) {
      set.delete(member);
      return 1;
    }
    return 0;
  }

  async smembers(key: string): Promise<string[]> {
    const set = this.setStorage.get(key);
    return set ? Array.from(set) : [];
  }

  async sismember(key: string, member: string): Promise<number> {
    const set = this.setStorage.get(key);
    return set?.has(member) ? 1 : 0;
  }

  async scard(key: string): Promise<number> {
    const set = this.setStorage.get(key);
    return set?.size || 0;
  }

  // Pub/Sub operations
  async publish(channel: string, message: string): Promise<number> {
    const subscribers = this.subscribers.get(channel) || [];
    subscribers.forEach(callback => {
      try {
        callback(message);
      } catch (error) {
        // Ignore callback errors in mock
      }
    });
    return subscribers.length;
  }

  async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, []);
    }
    this.subscribers.get(channel)!.push(callback);
  }

  async unsubscribe(channel: string): Promise<void> {
    this.subscribers.delete(channel);
  }

  // JSON operations (if supported)
  async jsonGet(key: string, path?: string): Promise<any> {
    const value = await this.get(key);
    if (!value) return null;
    
    try {
      const parsed = JSON.parse(value);
      return path ? this.getJsonPath(parsed, path) : parsed;
    } catch {
      return null;
    }
  }

  async jsonSet(key: string, path: string, value: any): Promise<void> {
    const existing = await this.jsonGet(key);
    const updated = this.setJsonPath(existing || {}, path, value);
    await this.set(key, JSON.stringify(updated));
  }

  private getJsonPath(obj: any, path: string): any {
    if (path === '$' || path === '.') return obj;
    
    const parts = path.replace(/^\$\./, '').split('.');
    let current = obj;
    
    for (const part of parts) {
      if (current === null || current === undefined) return null;
      current = current[part];
    }
    
    return current;
  }

  private setJsonPath(obj: any, path: string, value: any): any {
    if (path === '$' || path === '.') return value;
    
    const parts = path.replace(/^\$\./, '').split('.');
    let current = obj;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!(part in current) || typeof current[part] !== 'object') {
        current[part] = {};
      }
      current = current[part];
    }
    
    current[parts[parts.length - 1]] = value;
    return obj;
  }

  private checkExpiration(key: string): void {
    const expiration = this.expirations.get(key);
    if (expiration && Date.now() > expiration) {
      this.storage.delete(key);
      this.expirations.delete(key);
      this.hashStorage.delete(key);
      this.listStorage.delete(key);
      this.setStorage.delete(key);
    }
  }

  // Helper methods for testing
  public clearAll(): void {
    this.storage.clear();
    this.hashStorage.clear();
    this.listStorage.clear();
    this.setStorage.clear();
    this.expirations.clear();
    this.subscribers.clear();
  }

  public getStorageSize(): number {
    return this.storage.size;
  }

  public getAllKeys(): string[] {
    return Array.from(this.storage.keys());
  }
}
