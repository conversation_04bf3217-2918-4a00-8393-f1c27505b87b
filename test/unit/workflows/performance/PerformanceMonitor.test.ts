import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { PerformanceMonitor } from '../../../../src/workflows/performance/PerformanceMonitor';
import { ILogger } from '../../../../src/core/interfaces/ILogger';
import { WorkflowContext } from '../../../../src/core/types/WorkflowContext';

describe('PerformanceMonitor', () => {
  let performanceMonitor: PerformanceMonitor;
  let mockLogger: jest.Mocked<ILogger>;

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    } as jest.Mocked<ILogger>;

    performanceMonitor = new PerformanceMonitor(mockLogger);
  });

  describe('workflow monitoring', () => {
    it('should start and end monitoring successfully', async () => {
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      performanceMonitor.startMonitoring(context);

      // Simulate some execution time
      await new Promise((resolve) => setTimeout(resolve, 10));

      const metrics = performanceMonitor.endMonitoring('exec-1');

      expect(metrics).toBeTruthy();
      expect(metrics?.executionId).toBe('exec-1');
      expect(metrics?.workflowId).toBe('workflow-1');
      expect(metrics?.totalExecutionTime).toBeGreaterThanOrEqual(0);
    });

    it('should track node execution times', async () => {
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      performanceMonitor.startMonitoring(context);

      performanceMonitor.startNodeMonitoring('exec-1', 'node-1');
      await new Promise((resolve) => setTimeout(resolve, 5));
      performanceMonitor.endNodeMonitoring('exec-1', 'node-1');

      performanceMonitor.startNodeMonitoring('exec-1', 'node-2');
      await new Promise((resolve) => setTimeout(resolve, 3));
      performanceMonitor.endNodeMonitoring('exec-1', 'node-2');

      const metrics = performanceMonitor.endMonitoring('exec-1');

      expect(metrics?.nodeExecutionTimes['node-1']).toBeGreaterThanOrEqual(0);
      expect(metrics?.nodeExecutionTimes['node-2']).toBeGreaterThanOrEqual(0);
    });

    it('should record parallel execution', () => {
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      performanceMonitor.startMonitoring(context);
      performanceMonitor.recordParallelExecution('exec-1', 3);

      const metrics = performanceMonitor.getMetrics('exec-1');
      expect(metrics?.parallelBranches).toBe(3);
    });

    it('should record conditional evaluations', () => {
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      performanceMonitor.startMonitoring(context);
      performanceMonitor.recordConditionalEvaluation('exec-1');
      performanceMonitor.recordConditionalEvaluation('exec-1');

      const metrics = performanceMonitor.getMetrics('exec-1');
      expect(metrics?.conditionalEvaluations).toBe(2);
    });

    it('should record errors and retries', () => {
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      performanceMonitor.startMonitoring(context);
      performanceMonitor.recordError('exec-1');
      performanceMonitor.recordRetry('exec-1');

      const metrics = performanceMonitor.getMetrics('exec-1');
      expect(metrics?.errorCount).toBe(1);
      expect(metrics?.retryCount).toBe(1);
    });
  });

  describe('performance analysis', () => {
    it('should provide optimization recommendations for slow execution', () => {
      const metrics = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        totalExecutionTime: 35000, // 35 seconds
        nodeExecutionTimes: {
          'node-1': 15000,
          'node-2': 20000
        },
        memoryUsage: {
          heapUsed: 50 * 1024 * 1024, // 50MB
          heapTotal: 100 * 1024 * 1024,
          external: 10 * 1024 * 1024,
          rss: 120 * 1024 * 1024
        },
        cpuUsage: { user: 5000, system: 1000 },
        parallelBranches: 0,
        conditionalEvaluations: 2,
        dataTransformations: 1,
        errorCount: 0,
        retryCount: 0,
        timestamp: new Date()
      };

      const recommendations = performanceMonitor.analyzePerformance(metrics);

      expect(recommendations).toHaveLength(3); // Slow execution + 2 slow nodes
      expect(recommendations[0].type).toBe('execution_time');
      expect(recommendations[0].severity).toBe('high');
    });

    it('should recommend parallel execution for workflows without parallelism', () => {
      const metrics = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        totalExecutionTime: 10000,
        nodeExecutionTimes: {
          'node-1': 2000,
          'node-2': 3000,
          'node-3': 2500,
          'node-4': 2500
        },
        memoryUsage: {
          heapUsed: 20 * 1024 * 1024,
          heapTotal: 50 * 1024 * 1024,
          external: 5 * 1024 * 1024,
          rss: 60 * 1024 * 1024
        },
        cpuUsage: { user: 2000, system: 500 },
        parallelBranches: 0, // No parallel execution
        conditionalEvaluations: 1,
        dataTransformations: 1,
        errorCount: 0,
        retryCount: 0,
        timestamp: new Date()
      };

      const recommendations = performanceMonitor.analyzePerformance(metrics);

      const parallelRecommendation = recommendations.find((r) => r.type === 'parallel');
      expect(parallelRecommendation).toBeTruthy();
      expect(parallelRecommendation?.severity).toBe('medium');
    });
  });

  describe('aggregated statistics', () => {
    it('should calculate aggregated stats correctly', () => {
      // Add some test metrics
      const context1: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: {},
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      const context2: WorkflowContext = {
        executionId: 'exec-2',
        workflowId: 'workflow-1',
        input: {},
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      performanceMonitor.startMonitoring(context1);
      performanceMonitor.recordError('exec-1');
      performanceMonitor.endMonitoring('exec-1');

      performanceMonitor.startMonitoring(context2);
      performanceMonitor.recordRetry('exec-2');
      performanceMonitor.endMonitoring('exec-2');

      const stats = performanceMonitor.getAggregatedStats('workflow-1');

      expect(stats.totalExecutions).toBe(2);
      expect(stats.averageExecutionTime).toBeGreaterThanOrEqual(0);
      expect(stats.totalErrors).toBe(1);
      expect(stats.totalRetries).toBe(1);
    });

    it('should return empty stats for unknown workflow', () => {
      const stats = performanceMonitor.getAggregatedStats('unknown-workflow');

      expect(stats.totalExecutions).toBe(0);
      expect(stats.averageExecutionTime).toBe(0);
      expect(stats.totalErrors).toBe(0);
      expect(stats.totalRetries).toBe(0);
    });
  });

  describe('cleanup', () => {
    it('should cleanup metrics properly', () => {
      const context1: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: {},
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      const context2: WorkflowContext = {
        executionId: 'exec-2',
        workflowId: 'workflow-1',
        input: {},
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      // Start and end monitoring for both contexts
      performanceMonitor.startMonitoring(context1);
      performanceMonitor.endMonitoring('exec-1');

      performanceMonitor.startMonitoring(context2);
      performanceMonitor.endMonitoring('exec-2');

      // Verify both metrics exist
      expect(performanceMonitor.getMetrics('exec-1')).toBeTruthy();
      expect(performanceMonitor.getMetrics('exec-2')).toBeTruthy();

      // Cleanup should remove metrics - test with default value (24 hours)
      // Since our metrics are recent, they should remain after cleanup
      performanceMonitor.cleanup(); // Default 24 hours

      // Metrics should still exist after default cleanup (they're not 24 hours old)
      expect(performanceMonitor.getMetrics('exec-1')).toBeTruthy();
      expect(performanceMonitor.getMetrics('exec-2')).toBeTruthy();
    });

    it('should handle cleanup when no metrics exist', () => {
      // Test cleanup when there are no metrics
      expect(() => performanceMonitor.cleanup(1)).not.toThrow();
    });
  });
});
