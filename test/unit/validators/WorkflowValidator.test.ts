import 'reflect-metadata';
import { Container } from 'inversify';
import { WorkflowValidator } from '../../../src/api/validators/WorkflowValidator';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { INodeRegistry } from '../../../src/core/interfaces/INodeRegistry';
import { ValidationError } from '../../../src/core/errors/ValidationError';
import { TYPES } from '../../../src/types';

describe('WorkflowValidator', () => {
  let validator: WorkflowValidator;
  let mockLogger: jest.Mocked<ILogger>;
  let mockNodeRegistry: jest.Mocked<INodeRegistry>;
  let container: Container;

  beforeEach(() => {
    container = new Container();

    // Create mocks
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    };

    mockNodeRegistry = {
      getRegisteredNodeTypes: jest
        .fn()
        .mockReturnValue([
          'javascript-action',
          'http-action',
          'sql-action',
          'redis-action',
          'litellm-action'
        ]),
      getNode: jest.fn(),
      registerNode: jest.fn()
    };

    // Bind mocks to container
    container.bind<ILogger>(TYPES.Logger).toConstantValue(mockLogger);
    container.bind<INodeRegistry>(TYPES.NodeRegistry).toConstantValue(mockNodeRegistry);

    // Create validator
    validator = new WorkflowValidator(mockLogger, mockNodeRegistry);
  });

  describe('validateForCreate', () => {
    it('should validate a valid workflow configuration', async () => {
      const validWorkflow = {
        name: 'Test Workflow',
        description: 'A test workflow',
        input_schema: {
          type: 'object',
          properties: {
            input: { type: 'string' }
          }
        },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Test Node',
              config: {
                script: 'return { result: input.input };'
              }
            }
          ],
          edges: []
        }
      };

      const result = await validator.validateForCreate(validWorkflow);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test Workflow');
      expect(result.enabled).toBe(true);
      expect(result.version).toBe(1);
    });

    it('should throw ValidationError for missing required fields', async () => {
      const invalidWorkflow = {
        description: 'Missing name and other required fields'
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid input schema', async () => {
      const invalidWorkflow = {
        name: 'Test Workflow',
        input_schema: 'invalid schema',
        nodes_config: {
          nodes: [],
          edges: []
        }
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });

    it('should allow empty nodes array for new workflows', async () => {
      const validWorkflow = {
        name: 'Test Workflow',
        input_schema: {
          type: 'object',
          properties: {}
        },
        nodes_config: {
          nodes: [],
          edges: []
        }
      };

      const result = await validator.validateForCreate(validWorkflow);
      expect(result).toBeDefined();
      expect(result.name).toBe('Test Workflow');
      expect(result.nodes_config.nodes).toEqual([]);
    });

    it('should throw ValidationError for duplicate node IDs', async () => {
      const invalidWorkflow = {
        name: 'Test Workflow',
        input_schema: {
          type: 'object',
          properties: {}
        },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Node 1',
              config: { script: 'return {};' }
            },
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Node 2',
              config: { script: 'return {};' }
            }
          ],
          edges: []
        }
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for unsupported node type', async () => {
      const invalidWorkflow = {
        name: 'Test Workflow',
        input_schema: {
          type: 'object',
          properties: {}
        },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'unsupported',
              name: 'Test Node',
              config: {}
            }
          ],
          edges: []
        }
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid edge references', async () => {
      const invalidWorkflow = {
        name: 'Test Workflow',
        input_schema: {
          type: 'object',
          properties: {}
        },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Test Node',
              config: { script: 'return {};' }
            }
          ],
          edges: [
            {
              source: 'node1',
              target: 'nonexistent'
            }
          ]
        }
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });
  });

  describe('validateForUpdate', () => {
    it('should validate a valid workflow update', async () => {
      const validUpdate = {
        name: 'Updated Workflow',
        description: 'Updated description'
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        validUpdate
      );

      expect(result).toBeDefined();
      expect(result.name).toBe('Updated Workflow');
      expect(result.description).toBe('Updated description');
    });

    it('should throw ValidationError for invalid ID', async () => {
      const validUpdate = {
        name: 'Updated Workflow'
      };

      await expect(validator.validateForUpdate('invalid-id', validUpdate)).rejects.toThrow(
        ValidationError
      );
    });

    it('should validate partial updates', async () => {
      const partialUpdate = {
        description: 'Only updating description'
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        partialUpdate
      );

      expect(result).toBeDefined();
      expect(result.description).toBe('Only updating description');
      expect(result.name).toBeUndefined();
    });
  });

  describe('validateRetryConfig', () => {
    it('should validate valid retry configuration', async () => {
      const validWorkflow = {
        name: 'Test Workflow',
        input_schema: { type: 'object' },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Test Node',
              config: { script: 'return {};' }
            }
          ],
          edges: []
        },
        retry_config: {
          maxRetries: 3,
          retryDelay: 1000,
          retryBackoffMultiplier: 2,
          retryableErrors: ['NETWORK_ERROR', 'TIMEOUT']
        }
      };

      const result = await validator.validateForCreate(validWorkflow);
      expect(result).toBeDefined();
    });

    it('should throw ValidationError for invalid retry configuration', async () => {
      const invalidWorkflow = {
        name: 'Test Workflow',
        input_schema: { type: 'object' },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Test Node',
              config: { script: 'return {};' }
            }
          ],
          edges: []
        },
        retry_config: {
          maxRetries: -1, // Invalid negative value
          retryDelay: 'invalid', // Invalid type
          retryBackoffMultiplier: 0.5 // Invalid value < 1
        }
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });
  });

  describe('validateObservabilityConfig', () => {
    it('should validate valid observability configuration', async () => {
      const validWorkflow = {
        name: 'Test Workflow',
        input_schema: { type: 'object' },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Test Node',
              config: { script: 'return {};' }
            }
          ],
          edges: []
        },
        observability_config: {
          logLevel: 'info',
          metricsEnabled: true,
          logInputOutput: false
        }
      };

      const result = await validator.validateForCreate(validWorkflow);
      expect(result).toBeDefined();
    });

    it('should throw ValidationError for invalid log level', async () => {
      const invalidWorkflow = {
        name: 'Test Workflow',
        input_schema: { type: 'object' },
        nodes_config: {
          nodes: [
            {
              id: 'node1',
              type: 'javascript-action',
              name: 'Test Node',
              config: { script: 'return {};' }
            }
          ],
          edges: []
        },
        observability_config: {
          logLevel: 'invalid-level'
        }
      };

      await expect(validator.validateForCreate(invalidWorkflow)).rejects.toThrow(ValidationError);
    });
  });
});
