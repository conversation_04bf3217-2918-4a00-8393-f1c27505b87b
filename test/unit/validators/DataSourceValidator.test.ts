import 'reflect-metadata';
import { Container } from 'inversify';
import { DataSourceValidator } from '../../../src/api/validators/DataSourceValidator';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { ValidationError } from '../../../src/core/errors/ValidationError';
import { TYPES } from '../../../src/types';

describe('DataSourceValidator', () => {
  let validator: DataSourceValidator;
  let mockLogger: jest.Mocked<ILogger>;
  let container: Container;

  beforeEach(() => {
    container = new Container();

    // Create mocks
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    };

    // Bind mocks to container
    container.bind<ILogger>(TYPES.Logger).toConstantValue(mockLogger);

    // Create validator
    validator = new DataSourceValidator(mockLogger);
  });

  describe('validateForCreate', () => {
    it('should validate a valid PostgreSQL data source', async () => {
      const validDataSource = {
        name: 'postgres-main',
        description: 'Main PostgreSQL database',
        type: 'postgresql',
        connection_config: {
          host: 'localhost',
          port: 5432,
          database: 'myapp',
          username: 'user',
          password: 'password'
        }
      };

      const result = await validator.validateForCreate(validDataSource);

      expect(result).toBeDefined();
      expect(result.name).toBe('postgres-main');
      expect(result.type).toBe('postgresql');
      expect(result.enabled).toBe(true);
    });

    it('should validate a valid MySQL data source', async () => {
      const validDataSource = {
        name: 'mysql-main',
        type: 'mysql',
        connection_config: {
          host: 'localhost',
          port: 3306,
          database: 'myapp',
          username: 'user',
          password: 'password'
        }
      };

      const result = await validator.validateForCreate(validDataSource);

      expect(result).toBeDefined();
      expect(result.name).toBe('mysql-main');
      expect(result.type).toBe('mysql');
    });

    it('should validate a valid Redis data source', async () => {
      const validDataSource = {
        name: 'redis-cache',
        type: 'redis',
        connection_config: {
          host: 'localhost',
          port: 6379,
          password: 'redis-password'
        }
      };

      const result = await validator.validateForCreate(validDataSource);

      expect(result).toBeDefined();
      expect(result.name).toBe('redis-cache');
      expect(result.type).toBe('redis');
    });

    it('should validate a valid REST API data source', async () => {
      const validDataSource = {
        name: 'external-api',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          headers: {
            'Content-Type': 'application/json'
          },
          auth: {
            type: 'bearer',
            token: 'bearer-token'
          }
        }
      };

      const result = await validator.validateForCreate(validDataSource);

      expect(result).toBeDefined();
      expect(result.name).toBe('external-api');
      expect(result.type).toBe('rest');
    });

    it('should validate a valid LiteLLM data source', async () => {
      const validDataSource = {
        name: 'openai-api',
        type: 'litellm',
        connection_config: {
          apiUrl: 'https://api.openai.com/v1',
          apiKey: 'sk-test-key'
        }
      };

      const result = await validator.validateForCreate(validDataSource);

      expect(result).toBeDefined();
      expect(result.name).toBe('openai-api');
      expect(result.type).toBe('litellm');
    });

    it('should throw ValidationError for missing required fields', async () => {
      const invalidDataSource = {
        description: 'Missing name and other required fields'
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid PostgreSQL config', async () => {
      const invalidDataSource = {
        name: 'postgres-invalid',
        type: 'postgresql',
        connection_config: {
          // Missing required fields
          host: 'localhost'
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid Redis config', async () => {
      const invalidDataSource = {
        name: 'redis-invalid',
        type: 'redis',
        connection_config: {
          // Missing host
          port: 6379
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid REST API config', async () => {
      const invalidDataSource = {
        name: 'api-invalid',
        type: 'rest',
        connection_config: {
          // Missing baseUrl
          headers: {}
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid LiteLLM config', async () => {
      const invalidDataSource = {
        name: 'llm-invalid',
        type: 'litellm',
        connection_config: {
          // Missing apiUrl and apiKey
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });
  });

  describe('validateForUpdate', () => {
    it('should validate a valid data source update', async () => {
      const validUpdate = {
        name: 'updated-datasource',
        description: 'Updated description'
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        validUpdate
      );

      expect(result).toBeDefined();
      expect(result.name).toBe('updated-datasource');
      expect(result.description).toBe('Updated description');
    });

    it('should throw ValidationError for invalid ID', async () => {
      const validUpdate = {
        name: 'updated-datasource'
      };

      await expect(validator.validateForUpdate('invalid-id', validUpdate)).rejects.toThrow(
        ValidationError
      );
    });

    it('should validate partial updates', async () => {
      const partialUpdate = {
        description: 'Only updating description'
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        partialUpdate
      );

      expect(result).toBeDefined();
      expect(result.description).toBe('Only updating description');
      expect(result.name).toBeUndefined();
    });
  });

  describe('REST API authentication validation', () => {
    it('should validate basic authentication', async () => {
      const validDataSource = {
        name: 'api-basic',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          auth: {
            type: 'basic',
            username: 'user',
            password: 'pass'
          }
        }
      };

      const result = await validator.validateForCreate(validDataSource);
      expect(result).toBeDefined();
    });

    it('should validate API key authentication', async () => {
      const validDataSource = {
        name: 'api-key',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          auth: {
            type: 'apiKey',
            key: 'X-API-Key',
            value: 'api-key-value',
            in: 'header'
          }
        }
      };

      const result = await validator.validateForCreate(validDataSource);
      expect(result).toBeDefined();
    });

    it('should throw ValidationError for invalid auth type', async () => {
      const invalidDataSource = {
        name: 'api-invalid-auth',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          auth: {
            type: 'invalid-type'
          }
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for missing basic auth credentials', async () => {
      const invalidDataSource = {
        name: 'api-missing-basic',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          auth: {
            type: 'basic'
            // Missing username and password
          }
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for missing API key details', async () => {
      const invalidDataSource = {
        name: 'api-missing-key',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          auth: {
            type: 'apiKey'
            // Missing key, value, and in
          }
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid API key location', async () => {
      const invalidDataSource = {
        name: 'api-invalid-location',
        type: 'rest',
        connection_config: {
          baseUrl: 'https://api.example.com',
          auth: {
            type: 'apiKey',
            key: 'X-API-Key',
            value: 'api-key-value',
            in: 'invalid-location'
          }
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });
  });

  describe('port validation', () => {
    it('should validate valid port numbers', async () => {
      const validDataSource = {
        name: 'postgres-custom-port',
        type: 'postgresql',
        connection_config: {
          host: 'localhost',
          port: 5433,
          database: 'myapp',
          username: 'user',
          password: 'password'
        }
      };

      const result = await validator.validateForCreate(validDataSource);
      expect(result).toBeDefined();
    });

    it('should throw ValidationError for invalid port numbers', async () => {
      const invalidDataSource = {
        name: 'postgres-invalid-port',
        type: 'postgresql',
        connection_config: {
          host: 'localhost',
          port: 70000, // Invalid port > 65535
          database: 'myapp',
          username: 'user',
          password: 'password'
        }
      };

      await expect(validator.validateForCreate(invalidDataSource)).rejects.toThrow(ValidationError);
    });
  });
});
