import 'reflect-metadata';
import { Container } from 'inversify';
import { NodeValidator } from '../../../src/api/validators/NodeValidator';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { INodeRegistry } from '../../../src/core/interfaces/INodeRegistry';
import { ValidationError } from '../../../src/core/errors/ValidationError';
import { TYPES } from '../../../src/types';

describe('NodeValidator', () => {
  let validator: NodeValidator;
  let mockLogger: jest.Mocked<ILogger>;
  let mockNodeRegistry: jest.Mocked<INodeRegistry>;
  let container: Container;

  beforeEach(() => {
    container = new Container();

    // Create mocks
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    };

    mockNodeRegistry = {
      getRegisteredNodeTypes: jest
        .fn()
        .mockReturnValue([
          'javascript-action',
          'http-action',
          'sql-action',
          'redis-action',
          'litellm-action'
        ]),
      getNode: jest.fn(),
      registerNode: jest.fn()
    };

    // Bind mocks to container
    container.bind<ILogger>(TYPES.Logger).toConstantValue(mockLogger);
    container.bind<INodeRegistry>(TYPES.NodeRegistry).toConstantValue(mockNodeRegistry);

    // Create validator
    validator = new NodeValidator(mockLogger, mockNodeRegistry);
  });

  describe('validateForCreate', () => {
    it('should validate a valid JavaScript node configuration', async () => {
      const validNode = {
        name: 'Test JavaScript Node',
        description: 'A test JavaScript node',
        node_type: 'javascript-action',
        config: {
          script: 'return { result: input.value * 2 };'
        }
      };

      const result = await validator.validateForCreate(validNode);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test JavaScript Node');
      expect(result.node_type).toBe('javascript-action');
      expect(result.enabled).toBe(true);
    });

    it('should validate a valid HTTP node configuration', async () => {
      const validNode = {
        name: 'Test HTTP Node',
        node_type: 'http-action',
        config: {
          url: 'https://api.example.com/data',
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      };

      const result = await validator.validateForCreate(validNode);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test HTTP Node');
      expect(result.node_type).toBe('http-action');
    });

    it('should validate a valid SQL node configuration', async () => {
      const validNode = {
        name: 'Test SQL Node',
        node_type: 'sql-action',
        config: {
          dataSource: 'postgres-main',
          query: 'SELECT * FROM users WHERE id = $1'
        }
      };

      const result = await validator.validateForCreate(validNode);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test SQL Node');
      expect(result.node_type).toBe('sql-action');
    });

    it('should validate a valid Redis node configuration', async () => {
      const validNode = {
        name: 'Test Redis Node',
        node_type: 'redis-action',
        config: {
          command: 'GET',
          args: ['user:123']
        }
      };

      const result = await validator.validateForCreate(validNode);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test Redis Node');
      expect(result.node_type).toBe('redis-action');
    });

    it('should validate a valid LiteLLM node configuration', async () => {
      const validNode = {
        name: 'Test LiteLLM Node',
        node_type: 'litellm-action',
        config: {
          model: 'gpt-3.5-turbo',
          apiKey: 'sk-test-key'
        }
      };

      const result = await validator.validateForCreate(validNode);

      expect(result).toBeDefined();
      expect(result.name).toBe('Test LiteLLM Node');
      expect(result.node_type).toBe('litellm-action');
    });

    it('should throw ValidationError for missing required fields', async () => {
      const invalidNode = {
        description: 'Missing name and other required fields'
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for unsupported node type', async () => {
      const invalidNode = {
        name: 'Test Node',
        node_type: 'unsupported',
        config: {}
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid JavaScript node config', async () => {
      const invalidNode = {
        name: 'Test JavaScript Node',
        node_type: 'javascript-action',
        config: {
          // Missing script
        }
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid HTTP node config', async () => {
      const invalidNode = {
        name: 'Test HTTP Node',
        node_type: 'http-action',
        config: {
          // Missing url
          method: 'GET'
        }
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid SQL node config', async () => {
      const invalidNode = {
        name: 'Test SQL Node',
        node_type: 'sql-action',
        config: {
          // Missing both dataSource and connectionString, and query
        }
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid Redis node config', async () => {
      const invalidNode = {
        name: 'Test Redis Node',
        node_type: 'redis-action',
        config: {
          // Missing both command and cacheKey
        }
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid LiteLLM node config', async () => {
      const invalidNode = {
        name: 'Test LiteLLM Node',
        node_type: 'litellm-action',
        config: {
          // Missing model and apiKey/dataSource
        }
      };

      await expect(validator.validateForCreate(invalidNode)).rejects.toThrow(ValidationError);
    });
  });

  describe('validateForUpdate', () => {
    it('should validate a valid node update', async () => {
      const validUpdate = {
        name: 'Updated Node',
        description: 'Updated description'
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        validUpdate
      );

      expect(result).toBeDefined();
      expect(result.name).toBe('Updated Node');
      expect(result.description).toBe('Updated description');
    });

    it('should throw ValidationError for invalid ID', async () => {
      const validUpdate = {
        name: 'Updated Node'
      };

      await expect(validator.validateForUpdate('invalid-id', validUpdate)).rejects.toThrow(
        ValidationError
      );
    });

    it('should validate partial updates', async () => {
      const partialUpdate = {
        description: 'Only updating description'
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        partialUpdate
      );

      expect(result).toBeDefined();
      expect(result.description).toBe('Only updating description');
      expect(result.name).toBeUndefined();
    });

    it('should validate node type and config update together', async () => {
      const validUpdate = {
        node_type: 'javascript-action',
        config: {
          script: 'return { updated: true };'
        }
      };

      const result = await validator.validateForUpdate(
        '550e8400-e29b-41d4-a716-446655440000',
        validUpdate
      );

      expect(result).toBeDefined();
      expect(result.node_type).toBe('javascript-action');
      expect(result.config).toEqual({ script: 'return { updated: true };' });
    });
  });

  describe('node type specific validations', () => {
    it('should validate JavaScript node with valid script', async () => {
      const validNode = {
        name: 'JS Node',
        node_type: 'javascript-action',
        config: {
          script: 'const result = input.value * 2; return { result };'
        }
      };

      const result = await validator.validateForCreate(validNode);
      expect(result).toBeDefined();
    });

    it('should validate HTTP node with all optional fields', async () => {
      const validNode = {
        name: 'HTTP Node',
        node_type: 'http-action',
        config: {
          url: 'https://api.example.com/data',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer token'
          }
        }
      };

      const result = await validator.validateForCreate(validNode);
      expect(result).toBeDefined();
    });

    it('should validate SQL node with connectionString instead of dataSource', async () => {
      const validNode = {
        name: 'SQL Node',
        node_type: 'sql-action',
        config: {
          connectionString: 'postgresql://user:pass@localhost:5432/db',
          query: 'SELECT COUNT(*) FROM users'
        }
      };

      const result = await validator.validateForCreate(validNode);
      expect(result).toBeDefined();
    });

    it('should validate Redis node with cacheKey instead of command', async () => {
      const validNode = {
        name: 'Redis Node',
        node_type: 'redis-action',
        config: {
          cacheKey: 'user:cache:123'
        }
      };

      const result = await validator.validateForCreate(validNode);
      expect(result).toBeDefined();
    });

    it('should validate LiteLLM node with dataSource instead of apiKey', async () => {
      const validNode = {
        name: 'LiteLLM Node',
        node_type: 'litellm-action',
        config: {
          model: 'claude-3-sonnet',
          dataSource: 'anthropic-api'
        }
      };

      const result = await validator.validateForCreate(validNode);
      expect(result).toBeDefined();
    });
  });
});
