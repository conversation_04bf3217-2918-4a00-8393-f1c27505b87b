import 'reflect-metadata';
import { Container } from 'inversify';
import { WorkflowConfigWatcher } from '../../../src/workflow/config/WorkflowConfigWatcher';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { IDatabase } from '../../../src/core/interfaces/IDatabase';
import { IWorkflowEngine } from '../../../src/core/interfaces/IWorkflowEngine';
import { IDynamicServiceLoader } from '../../../src/core/interfaces/IDynamicServiceLoader';
import { TYPES } from '../../../src/types';

describe('WorkflowConfigWatcher', () => {
  let watcher: WorkflowConfigWatcher;
  let mockLogger: jest.Mocked<ILogger>;
  let mockDatabase: jest.Mocked<IDatabase>;
  let mockWorkflowEngine: jest.Mocked<IWorkflowEngine>;
  let mockServiceLoader: jest.Mocked<IDynamicServiceLoader>;
  let container: Container;

  beforeEach(() => {
    container = new Container();

    // Create mocks
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    };

    mockDatabase = {
      initialize: jest.fn(),
      getRepository: jest.fn(),
      close: jest.fn(),
      query: jest.fn(),
      listen: jest.fn(),
      unlisten: jest.fn(),
      onNotification: jest.fn(),
      notify: jest.fn(),
      getConnection: jest.fn()
    };

    mockWorkflowEngine = {
      initialize: jest.fn(),
      executeWorkflow: jest.fn(),
      executeWorkflowConfig: jest.fn(),
      getWorkflow: jest.fn(),
      reloadWorkflow: jest.fn(),
      reloadAllWorkflows: jest.fn(),
      removeWorkflow: jest.fn(),
      getWorkflowExecutionHistory: jest.fn()
    };

    mockServiceLoader = {
      loadFunction: jest.fn(),
      reloadFunction: jest.fn(),
      unloadFunction: jest.fn(),
      getFunctions: jest.fn(),
      initialize: jest.fn()
    };

    // Create watcher
    watcher = new WorkflowConfigWatcher(
      mockLogger,
      mockDatabase,
      mockWorkflowEngine,
      mockServiceLoader
    );
  });

  describe('startWatching', () => {
    it('should start watching for configuration changes', async () => {
      await watcher.startWatching();

      expect(mockDatabase.onNotification).toHaveBeenCalled();
      expect(mockDatabase.listen).toHaveBeenCalledWith('mcp_config_updates');
      expect(mockLogger.info).toHaveBeenCalledWith('Started watching for configuration changes');
    });

    it('should not start watching if already watching', async () => {
      await watcher.startWatching();
      await watcher.startWatching(); // Second call

      expect(mockLogger.warn).toHaveBeenCalledWith('Config watcher is already running');
      expect(mockDatabase.listen).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when starting', async () => {
      const error = new Error('Database error');
      mockDatabase.listen.mockRejectedValue(error);

      await expect(watcher.startWatching()).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to start config watcher', error);
    });
  });

  describe('stopWatching', () => {
    it('should stop watching for configuration changes', async () => {
      await watcher.startWatching();
      await watcher.stopWatching();

      expect(mockDatabase.unlisten).toHaveBeenCalledWith('mcp_config_updates');
      expect(mockLogger.info).toHaveBeenCalledWith('Stopped watching for configuration changes');
    });

    it('should not stop watching if not watching', async () => {
      await watcher.stopWatching();

      expect(mockLogger.warn).toHaveBeenCalledWith('Config watcher is not running');
      expect(mockDatabase.unlisten).not.toHaveBeenCalled();
    });

    it('should handle errors when stopping', async () => {
      const error = new Error('Database error');
      await watcher.startWatching();
      mockDatabase.unlisten.mockRejectedValue(error);

      await expect(watcher.stopWatching()).rejects.toThrow(error);
      expect(mockLogger.error).toHaveBeenCalledWith('Failed to stop config watcher', error);
    });
  });

  describe('handleNotification', () => {
    let notificationCallback: (channel: string, payload: string) => void;

    beforeEach(async () => {
      await watcher.startWatching();
      // Get the callback that was registered
      notificationCallback = mockDatabase.onNotification.mock.calls[0][0];
    });

    it('should handle workflow change notifications', async () => {
      const notification = {
        entity_type: 'mcp_workflows',
        id: 'workflow-123',
        name: 'Test Workflow',
        action: 'UPDATE'
      };

      mockWorkflowEngine.reloadWorkflow.mockResolvedValue(true);

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Received configuration change notification',
        notification
      );
      expect(mockWorkflowEngine.reloadWorkflow).toHaveBeenCalledWith('workflow-123');
    });

    it('should handle workflow deletion notifications', async () => {
      const notification = {
        entity_type: 'mcp_workflows',
        id: 'workflow-123',
        name: 'Test Workflow',
        action: 'DELETE'
      };

      mockWorkflowEngine.removeWorkflow.mockResolvedValue(true);

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockWorkflowEngine.removeWorkflow).toHaveBeenCalledWith('workflow-123');
    });

    it('should handle node config change notifications', async () => {
      const notification = {
        entity_type: 'mcp_node_configs',
        id: 'node-123',
        name: 'Test Node Config',
        action: 'UPDATE'
      };

      mockWorkflowEngine.reloadAllWorkflows.mockResolvedValue(5);

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Node config changed: Test Node Config (node-123)'
      );
      expect(mockWorkflowEngine.reloadAllWorkflows).toHaveBeenCalled();
    });

    it('should handle data source change notifications', async () => {
      const notification = {
        entity_type: 'mcp_data_sources',
        id: 'datasource-123',
        name: 'Test Data Source',
        action: 'UPDATE'
      };

      mockWorkflowEngine.reloadAllWorkflows.mockResolvedValue(3);

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockLogger.info).toHaveBeenCalledWith(
        'Data source changed: Test Data Source (datasource-123)'
      );
      expect(mockWorkflowEngine.reloadAllWorkflows).toHaveBeenCalled();
    });

    it('should ignore notifications from other channels', () => {
      const notification = {
        entity_type: 'mcp_workflows',
        id: 'workflow-123',
        name: 'Test Workflow',
        action: 'UPDATE'
      };

      notificationCallback('other_channel', JSON.stringify(notification));

      expect(mockWorkflowEngine.reloadWorkflow).not.toHaveBeenCalled();
    });

    it('should handle unknown entity types', () => {
      const notification = {
        entity_type: 'unknown_entity',
        id: 'entity-123',
        name: 'Unknown Entity',
        action: 'UPDATE'
      };

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      expect(mockLogger.warn).toHaveBeenCalledWith('Unknown entity type: unknown_entity');
    });

    it('should handle invalid JSON payloads', () => {
      notificationCallback('mcp_config_updates', 'invalid json');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling notification',
        expect.any(Error)
      );
    });

    it('should handle workflow engine errors', async () => {
      const notification = {
        entity_type: 'mcp_workflows',
        id: 'workflow-123',
        name: 'Test Workflow',
        action: 'UPDATE'
      };

      const error = new Error('Workflow engine error');
      mockWorkflowEngine.reloadWorkflow.mockRejectedValue(error);

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error handling workflow change for workflow-123',
        error
      );
    });
  });

  describe('workflow insertion notifications', () => {
    let notificationCallback: (channel: string, payload: string) => void;

    beforeEach(async () => {
      await watcher.startWatching();
      notificationCallback = mockDatabase.onNotification.mock.calls[0][0];
    });

    it('should handle workflow insertion notifications', async () => {
      const notification = {
        entity_type: 'mcp_workflows',
        id: 'workflow-123',
        name: 'New Workflow',
        action: 'INSERT'
      };

      mockWorkflowEngine.reloadWorkflow.mockResolvedValue(true);

      notificationCallback('mcp_config_updates', JSON.stringify(notification));

      // Wait for async operations
      await new Promise((resolve) => setTimeout(resolve, 0));

      expect(mockWorkflowEngine.reloadWorkflow).toHaveBeenCalledWith('workflow-123');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Reloaded workflow: New Workflow (workflow-123)'
      );
    });
  });
});
