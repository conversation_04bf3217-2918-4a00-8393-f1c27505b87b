import 'reflect-metadata';
import { TenantManager } from '../../../src/services/TenantManager';
import { Tenant } from '../../../src/infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../../../src/infrastructure/database/entities/TenantUser.entity';
import { ILogger } from '../../../src/core/interfaces/ILogger';

describe('TenantManager', () => {
  let tenantManager: TenantManager;
  let mockTenantRepository: any;
  let mockTenantUserRepository: any;
  let mockLogger: jest.Mocked<ILogger>;

  beforeEach(() => {
    mockTenantRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      findAndCount: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      remove: jest.fn(),
      count: jest.fn()
    };

    mockTenantUserRepository = {
      findOne: jest.fn(),
      find: jest.fn(),
      findAndCount: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      remove: jest.fn(),
      count: jest.fn()
    };

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    };

    tenantManager = new TenantManager(
      mockTenantRepository,
      mockTenantUserRepository,
      mockLogger
    );
  });

  describe('getTenant', () => {
    it('should return tenant when found', async () => {
      const mockTenant = {
        id: 'tenant-1',
        name: 'test-tenant',
        displayName: 'Test Tenant',
        enabled: true
      };

      mockTenantRepository.findOne.mockResolvedValue(mockTenant);

      const result = await tenantManager.getTenant('tenant-1');

      expect(result).toEqual(mockTenant);
      expect(mockTenantRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'tenant-1' },
        relations: ['users']
      });
    });

    it('should return null when tenant not found', async () => {
      mockTenantRepository.findOne.mockResolvedValue(null);

      const result = await tenantManager.getTenant('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('createTenant', () => {
    it('should create tenant with default settings', async () => {
      const tenantData = {
        name: 'new-tenant',
        displayName: 'New Tenant'
      };

      const mockCreatedTenant = {
        id: 'tenant-1',
        ...tenantData,
        settings: Tenant.getDefaultSettings(),
        enabled: true
      };

      mockTenantRepository.findOne.mockResolvedValue(null); // No existing tenant
      mockTenantRepository.create.mockReturnValue(mockCreatedTenant);
      mockTenantRepository.save.mockResolvedValue(mockCreatedTenant);

      const result = await tenantManager.createTenant(tenantData);

      expect(result).toEqual(mockCreatedTenant);
      expect(mockTenantRepository.create).toHaveBeenCalledWith({
        ...tenantData,
        settings: Tenant.getDefaultSettings(),
        enabled: true
      });
      expect(mockTenantRepository.save).toHaveBeenCalledWith(mockCreatedTenant);
    });

    it('should throw error when tenant name already exists', async () => {
      const tenantData = {
        name: 'existing-tenant',
        displayName: 'Existing Tenant'
      };

      const existingTenant = { id: 'tenant-1', name: 'existing-tenant' };
      mockTenantRepository.findOne.mockResolvedValue(existingTenant);

      await expect(tenantManager.createTenant(tenantData)).rejects.toThrow(
        "Tenant with name 'existing-tenant' already exists"
      );
    });

    it('should throw error when required fields are missing', async () => {
      const tenantData = { name: 'test' }; // Missing displayName

      await expect(tenantManager.createTenant(tenantData)).rejects.toThrow(
        'Tenant name and display name are required'
      );
    });
  });

  describe('validateTenantSettings', () => {
    it('should validate correct settings', async () => {
      const settings = Tenant.getDefaultSettings();

      const result = await tenantManager.validateTenantSettings(settings);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid settings', async () => {
      const settings = {
        ...Tenant.getDefaultSettings(),
        max_concurrent_workflows: -1, // Invalid
        session_timeout: 0 // Invalid
      };

      const result = await tenantManager.validateTenantSettings(settings);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('max_concurrent_workflows must be greater than 0');
      expect(result.errors).toContain('session_timeout must be greater than 0');
    });
  });

  describe('createTenantUser', () => {
    it('should create user with default role', async () => {
      const tenantId = 'tenant-1';
      const userData = {
        username: 'testuser',
        email: '<EMAIL>'
      };

      const mockTenant = { id: tenantId, name: 'test-tenant' };
      const mockCreatedUser = {
        id: 'user-1',
        tenantId,
        ...userData,
        roles: [TenantUser.getUserRole()],
        enabled: true
      };

      mockTenantRepository.findOne.mockResolvedValue(mockTenant);
      mockTenantUserRepository.findOne.mockResolvedValue(null); // No existing user
      mockTenantUserRepository.create.mockReturnValue(mockCreatedUser);
      mockTenantUserRepository.save.mockResolvedValue(mockCreatedUser);

      const result = await tenantManager.createTenantUser(tenantId, userData);

      expect(result).toEqual(mockCreatedUser);
      expect(mockTenantUserRepository.create).toHaveBeenCalledWith({
        ...userData,
        tenantId,
        passwordHash: undefined,
        enabled: true,
        roles: [TenantUser.getUserRole()]
      });
    });

    it('should throw error when tenant not found', async () => {
      const tenantId = 'non-existent';
      const userData = {
        username: 'testuser',
        email: '<EMAIL>'
      };

      mockTenantRepository.findOne.mockResolvedValue(null);

      await expect(tenantManager.createTenantUser(tenantId, userData)).rejects.toThrow(
        "Tenant with ID 'non-existent' not found"
      );
    });
  });

  describe('listTenants', () => {
    it('should return paginated tenant list', async () => {
      const mockTenants = [
        { id: 'tenant-1', name: 'tenant1' },
        { id: 'tenant-2', name: 'tenant2' }
      ];

      mockTenantRepository.findAndCount.mockResolvedValue([mockTenants, 2]);

      const result = await tenantManager.listTenants({
        page: 1,
        limit: 10
      });

      expect(result).toEqual({
        data: mockTenants,
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1
      });
    });
  });
});
