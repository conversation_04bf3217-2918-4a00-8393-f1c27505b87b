/**
 * Unit tests for AlertManager service
 */

import { AlertManager } from '../../../src/services/AlertManager';
import { MockDatabase } from '../../mocks/MockDatabase';
import { MockLogger } from '../../mocks/MockLogger';
import {
  AlertRule,
  Alert,
  NotificationChannel,
  AlertCondition
} from '../../../src/core/interfaces/IAlertManager';

describe('AlertManager', () => {
  let alertManager: AlertManager;
  let mockDatabase: MockDatabase;
  let mockLogger: MockLogger;

  beforeEach(() => {
    mockDatabase = new MockDatabase();
    mockLogger = new MockLogger();
    alertManager = new AlertManager(mockDatabase, mockLogger);
  });

  afterEach(() => {
    mockDatabase.clearAllData();
    mockLogger.clearLogs();
  });

  describe('createRule', () => {
    it('should create alert rule successfully', async () => {
      const rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'> = {
        name: 'High CPU Usage',
        description: 'Alert when CPU usage exceeds 80%',
        enabled: true,
        tenantId: 'tenant-1',
        conditions: [
          {
            type: 'metric',
            metric: 'cpu_usage',
            operator: '>',
            threshold: 80,
            timeWindow: 300,
            aggregation: 'avg'
          }
        ],
        operator: 'AND',
        notifications: [
          {
            type: 'email',
            enabled: true,
            emails: ['<EMAIL>']
          }
        ],
        evaluationInterval: 60,
        cooldownPeriod: 300,
        priority: 'high',
        tags: ['cpu', 'performance'],
        createdBy: 'user-1'
      };

      const ruleId = await alertManager.createRule(rule);

      expect(ruleId).toBeDefined();
      expect(typeof ruleId).toBe('string');

      const savedData = mockDatabase.getTestData('AlertRule');
      expect(savedData).toHaveLength(1);
      expect(savedData[0]).toMatchObject({
        name: 'High CPU Usage',
        enabled: true,
        tenantId: 'tenant-1',
        priority: 'high'
      });

      expect(mockLogger.hasLogWithMessage('Alert rule created successfully')).toBe(true);
    });

    it('should handle errors when creating alert rule', async () => {
      const rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'> = {
        name: 'Test Rule',
        enabled: true,
        conditions: [],
        operator: 'AND',
        notifications: [],
        evaluationInterval: 60,
        cooldownPeriod: 300,
        priority: 'medium'
      };

      // Mock repository to throw error
      const dataSource = await mockDatabase.getDataSource();
      const repo = dataSource.getRepository('AlertRule');
      repo.save = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(alertManager.createRule(rule)).rejects.toThrow('Database error');
      expect(mockLogger.hasLogWithMessage('Failed to create alert rule')).toBe(true);
    });
  });

  describe('updateRule', () => {
    it('should update alert rule successfully', async () => {
      // Setup existing rule
      mockDatabase.setTestData('AlertRule', [
        {
          id: 'rule-1',
          name: 'Original Rule',
          enabled: true,
          priority: 'medium'
        }
      ]);

      await alertManager.updateRule('rule-1', {
        name: 'Updated Rule',
        enabled: false,
        priority: 'high'
      });

      expect(mockLogger.hasLogWithMessage('Alert rule updated successfully')).toBe(true);
    });

    it('should throw error when rule not found', async () => {
      await expect(alertManager.updateRule('non-existent', { name: 'Test' })).rejects.toThrow(
        'Alert rule with ID non-existent not found'
      );
    });
  });

  describe('deleteRule', () => {
    it('should delete alert rule successfully', async () => {
      // Setup existing rule
      mockDatabase.setTestData('AlertRule', [{ id: 'rule-1', name: 'Test Rule' }]);

      await alertManager.deleteRule('rule-1');

      expect(mockLogger.hasLogWithMessage('Alert rule deleted successfully')).toBe(true);
    });

    it('should throw error when rule not found', async () => {
      await expect(alertManager.deleteRule('non-existent')).rejects.toThrow(
        'Alert rule with ID non-existent not found'
      );
    });
  });

  describe('getRule', () => {
    it('should get alert rule by ID', async () => {
      const testRule = {
        id: 'rule-1',
        name: 'Test Rule',
        description: 'Test description',
        enabled: true,
        tenantId: 'tenant-1',
        conditions: [
          {
            type: 'metric',
            metric: 'cpu_usage',
            operator: '>',
            threshold: 80
          }
        ],
        operator: 'AND',
        notifications: [
          {
            type: 'email',
            enabled: true,
            emails: ['<EMAIL>']
          }
        ],
        evaluationInterval: 60,
        cooldownPeriod: 300,
        priority: 'high',
        tags: ['test'],
        createdBy: 'user-1',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockDatabase.setTestData('AlertRule', [testRule]);

      const rule = await alertManager.getRule('rule-1');

      expect(rule).toMatchObject({
        id: 'rule-1',
        name: 'Test Rule',
        enabled: true,
        priority: 'high'
      });
    });

    it('should return null when rule not found', async () => {
      const rule = await alertManager.getRule('non-existent');
      expect(rule).toBeNull();
    });
  });

  describe('getRules', () => {
    beforeEach(() => {
      mockDatabase.setTestData('AlertRule', [
        {
          id: 'rule-1',
          name: 'Rule 1',
          tenantId: 'tenant-1',
          enabled: true,
          conditions: [],
          operator: 'AND',
          notifications: [],
          evaluationInterval: 60,
          cooldownPeriod: 300,
          priority: 'high',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-01')
        },
        {
          id: 'rule-2',
          name: 'Rule 2',
          tenantId: 'tenant-2',
          enabled: false,
          conditions: [],
          operator: 'OR',
          notifications: [],
          evaluationInterval: 120,
          cooldownPeriod: 600,
          priority: 'medium',
          createdAt: new Date('2024-01-02'),
          updatedAt: new Date('2024-01-02')
        }
      ]);
    });

    it('should get all alert rules', async () => {
      const rules = await alertManager.getRules();

      expect(rules).toHaveLength(2);
      // Rules are ordered by createdAt DESC, so Rule 2 (2024-01-02) comes first
      expect(rules[0].name).toBe('Rule 2');
      expect(rules[1].name).toBe('Rule 1');
    });

    it('should filter rules by tenant', async () => {
      const rules = await alertManager.getRules('tenant-1');

      expect(rules).toHaveLength(1);
      expect(rules[0].tenantId).toBe('tenant-1');
    });
  });

  describe('toggleRule', () => {
    it('should enable/disable alert rule', async () => {
      mockDatabase.setTestData('AlertRule', [{ id: 'rule-1', name: 'Test Rule', enabled: true }]);

      await alertManager.toggleRule('rule-1', false);

      expect(mockLogger.hasLogWithMessage('Alert rule toggled successfully')).toBe(true);
    });
  });

  describe('evaluateRules', () => {
    beforeEach(() => {
      mockDatabase.setTestData('AlertRule', [
        {
          id: 'rule-1',
          name: 'CPU Rule',
          enabled: true,
          conditions: [
            {
              type: 'metric',
              metric: 'cpu_usage',
              operator: '>',
              threshold: 50
            }
          ],
          operator: 'AND',
          notifications: [],
          evaluationInterval: 60,
          cooldownPeriod: 300
        },
        {
          id: 'rule-2',
          name: 'Memory Rule',
          enabled: false,
          conditions: [],
          operator: 'AND',
          notifications: [],
          evaluationInterval: 60,
          cooldownPeriod: 300
        }
      ]);
    });

    it('should evaluate all active alert rules', async () => {
      const results = await alertManager.evaluateRules();

      expect(results).toHaveLength(1); // Only enabled rules
      expect(results[0].ruleId).toBe('rule-1');
      expect(results[0]).toHaveProperty('triggered');
      expect(results[0]).toHaveProperty('conditions');
      expect(results[0]).toHaveProperty('evaluatedAt');

      expect(mockLogger.hasLogWithMessage('Alert rules evaluation completed')).toBe(true);
    });
  });

  describe('evaluateRule', () => {
    it('should evaluate specific alert rule', async () => {
      mockDatabase.setTestData('AlertRule', [
        {
          id: 'rule-1',
          name: 'Test Rule',
          enabled: true,
          conditions: [
            {
              type: 'metric',
              metric: 'cpu_usage',
              operator: '>',
              threshold: 80
            }
          ],
          operator: 'AND',
          notifications: [],
          evaluationInterval: 60,
          cooldownPeriod: 300
        }
      ]);

      const result = await alertManager.evaluateRule('rule-1');

      expect(result.ruleId).toBe('rule-1');
      expect(result.conditions).toHaveLength(1);
      expect(result.conditions[0]).toHaveProperty('met');
      expect(result.conditions[0]).toHaveProperty('actualValue');
      expect(result.evaluatedAt).toBeInstanceOf(Date);
      expect(result.nextEvaluation).toBeInstanceOf(Date);
    });

    it('should throw error when rule not found', async () => {
      await expect(alertManager.evaluateRule('non-existent')).rejects.toThrow(
        'Alert rule with ID non-existent not found'
      );
    });
  });

  describe('sendAlert', () => {
    it('should send alert notifications', async () => {
      const alert: Alert = {
        id: 'alert-1',
        ruleId: 'rule-1',
        ruleName: 'Test Rule',
        title: 'Test Alert',
        message: 'Test alert message',
        severity: 'warning',
        status: 'active',
        triggeredAt: new Date(),
        conditions: [],
        actualValues: {}
      };

      mockDatabase.setTestData('AlertRule', [
        {
          id: 'rule-1',
          name: 'Test Rule',
          notifications: [
            {
              type: 'email',
              enabled: true,
              emails: ['<EMAIL>']
            }
          ]
        }
      ]);

      const results = await alertManager.sendAlert(alert);

      expect(results).toHaveLength(1);
      expect(results[0].channelType).toBe('email');
      expect(results[0].success).toBe(true);

      expect(mockLogger.hasLogWithMessage('Alert notifications sent')).toBe(true);
    });
  });

  describe('acknowledgeAlert', () => {
    it('should acknowledge alert successfully', async () => {
      mockDatabase.setTestData('Alert', [{ id: 'alert-1', status: 'active' }]);

      await alertManager.acknowledgeAlert('alert-1', 'user-1', 'Investigating issue');

      expect(mockLogger.hasLogWithMessage('Alert acknowledged successfully')).toBe(true);
    });

    it('should throw error when alert not found', async () => {
      await expect(alertManager.acknowledgeAlert('non-existent', 'user-1')).rejects.toThrow(
        'Alert with ID non-existent not found'
      );
    });
  });

  describe('resolveAlert', () => {
    it('should resolve alert successfully', async () => {
      mockDatabase.setTestData('Alert', [{ id: 'alert-1', status: 'active' }]);

      await alertManager.resolveAlert('alert-1', 'user-1', 'Issue resolved');

      expect(mockLogger.hasLogWithMessage('Alert resolved successfully')).toBe(true);
    });
  });

  describe('suppressAlert', () => {
    it('should suppress alert successfully', async () => {
      mockDatabase.setTestData('Alert', [{ id: 'alert-1', status: 'active' }]);

      await alertManager.suppressAlert('alert-1', 'user-1', 'False positive');

      expect(mockLogger.hasLogWithMessage('Alert suppressed successfully')).toBe(true);
    });
  });
});
