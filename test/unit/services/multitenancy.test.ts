import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from '../../../src/types';
import { ITenantManager } from '../../../src/core/interfaces/ITenantManager';
import { IDatabase } from '../../../src/core/interfaces/IDatabase';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { Tenant } from '../../../src/infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../../../src/infrastructure/database/entities/TenantUser.entity';
import { MockDatabase } from '../../mocks/MockDatabase';
import { MockLogger } from '../../mocks/MockLogger';
import { TenantManager } from '../../../src/services/TenantManager';

describe('Multi-tenancy Unit Tests', () => {
  let container: Container;
  let tenantManager: ITenantManager;
  let mockDatabase: MockDatabase;
  let mockLogger: MockLogger;

  beforeAll(async () => {
    // Create container with mocks
    container = new Container();
    mockDatabase = new MockDatabase();
    mockLogger = new MockLogger();

    // Bind dependencies
    container.bind<IDatabase>(TYPES.Database).toConstantValue(mockDatabase);
    container.bind<ILogger>(TYPES.Logger).toConstantValue(mockLogger);

    // Mock repositories
    const mockTenantRepo = mockDatabase.getRepository('Tenant');
    const mockTenantUserRepo = mockDatabase.getRepository('TenantUser');

    container.bind(TYPES.TenantRepository).toConstantValue(mockTenantRepo);
    container.bind(TYPES.TenantUserRepository).toConstantValue(mockTenantUserRepo);

    // Bind service
    container.bind<ITenantManager>(TYPES.TenantManager).to(TenantManager);

    tenantManager = container.get<ITenantManager>(TYPES.TenantManager);
  });

  beforeEach(async () => {
    // Clear mock data before each test
    mockDatabase.clearAllData();
    mockLogger.clearLogs();
  });

  describe('Tenant Management', () => {
    it('should create a new tenant with default settings', async () => {
      const tenantData = {
        name: 'test-tenant',
        displayName: 'Test Tenant',
        createdBy: 'test-user'
      };

      const tenant = await tenantManager.createTenant(tenantData);

      expect(tenant).toBeDefined();
      expect(tenant.id).toBeDefined();
      expect(tenant.name).toBe(tenantData.name);
      expect(tenant.displayName).toBe(tenantData.displayName);
      expect(tenant.enabled).toBe(true);
      expect(tenant.settings).toEqual(Tenant.getDefaultSettings());
    });

    it('should retrieve tenant by ID', async () => {
      const tenantData = {
        name: 'test-tenant-2',
        displayName: 'Test Tenant 2'
      };

      const createdTenant = await tenantManager.createTenant(tenantData);
      const retrievedTenant = await tenantManager.getTenant(createdTenant.id);

      expect(retrievedTenant).toBeDefined();
      expect(retrievedTenant!.id).toBe(createdTenant.id);
      expect(retrievedTenant!.name).toBe(tenantData.name);
    });

    it('should retrieve tenant by name', async () => {
      const tenantData = {
        name: 'test-tenant-3',
        displayName: 'Test Tenant 3'
      };

      const createdTenant = await tenantManager.createTenant(tenantData);
      const retrievedTenant = await tenantManager.getTenantByName(tenantData.name);

      expect(retrievedTenant).toBeDefined();
      expect(retrievedTenant!.id).toBe(createdTenant.id);
      expect(retrievedTenant!.name).toBe(tenantData.name);
    });

    it('should list tenants with pagination', async () => {
      // Create multiple tenants
      const tenants = [];
      for (let i = 1; i <= 5; i++) {
        const tenant = await tenantManager.createTenant({
          name: `test-tenant-${i}`,
          displayName: `Test Tenant ${i}`
        });
        tenants.push(tenant);
      }

      const result = await tenantManager.listTenants({
        page: 1,
        limit: 3,
        sortBy: 'name',
        sortOrder: 'ASC'
      });

      expect(result.data).toHaveLength(3);
      expect(result.total).toBe(5);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(3);
      expect(result.totalPages).toBe(2);
    });

    it('should update tenant settings', async () => {
      const tenant = await tenantManager.createTenant({
        name: 'test-tenant-update',
        displayName: 'Test Tenant Update'
      });

      const newSettings = {
        ...Tenant.getDefaultSettings(),
        max_concurrent_workflows: 20,
        enable_custom_functions: false
      };

      const updatedTenant = await tenantManager.updateTenant(tenant.id, {
        settings: newSettings
      });

      expect(updatedTenant.settings.max_concurrent_workflows).toBe(20);
      expect(updatedTenant.settings.enable_custom_functions).toBe(false);
    });

    it('should delete tenant', async () => {
      const tenant = await tenantManager.createTenant({
        name: 'test-tenant-delete',
        displayName: 'Test Tenant Delete'
      });

      const success = await tenantManager.deleteTenant(tenant.id);
      expect(success).toBe(true);

      const deletedTenant = await tenantManager.getTenant(tenant.id);
      expect(deletedTenant).toBeNull();
    });
  });

  describe('Tenant User Management', () => {
    let tenant: Tenant;

    beforeEach(async () => {
      tenant = await tenantManager.createTenant({
        name: 'test-tenant-users',
        displayName: 'Test Tenant Users'
      });
    });

    it('should create tenant user with default role', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: 'hashedpassword'
      };

      const user = await tenantManager.createTenantUser(tenant.id, userData);

      expect(user).toBeDefined();
      expect(user.id).toBeDefined();
      expect(user.tenantId).toBe(tenant.id);
      expect(user.username).toBe(userData.username);
      expect(user.email).toBe(userData.email);
      expect(user.enabled).toBe(true);
      expect(user.roles).toEqual([TenantUser.getUserRole()]);
    });

    it('should authenticate tenant user', async () => {
      const userData = {
        username: 'authuser',
        email: '<EMAIL>',
        passwordHash: 'testpassword'
      };

      await tenantManager.createTenantUser(tenant.id, userData);

      const authenticatedUser = await tenantManager.authenticateUser(
        tenant.id,
        userData.username,
        'testpassword'
      );

      expect(authenticatedUser).toBeDefined();
      expect(authenticatedUser!.username).toBe(userData.username);
      expect(authenticatedUser!.lastLoginAt).toBeDefined();
    });

    it('should fail authentication with wrong password', async () => {
      const userData = {
        username: 'authuser2',
        email: '<EMAIL>',
        passwordHash: 'testpassword'
      };

      await tenantManager.createTenantUser(tenant.id, userData);

      const authenticatedUser = await tenantManager.authenticateUser(
        tenant.id,
        userData.username,
        'wrongpassword'
      );

      expect(authenticatedUser).toBeNull();
    });

    it('should list tenant users with pagination', async () => {
      // Create multiple users
      for (let i = 1; i <= 3; i++) {
        await tenantManager.createTenantUser(tenant.id, {
          username: `user${i}`,
          email: `user${i}@example.com`
        });
      }

      const result = await tenantManager.getTenantUsers(tenant.id, {
        page: 1,
        limit: 2
      });

      expect(result.data).toHaveLength(2);
      expect(result.total).toBe(3);
      expect(result.totalPages).toBe(2);
    });

    it('should update tenant user', async () => {
      const user = await tenantManager.createTenantUser(tenant.id, {
        username: 'updateuser',
        email: '<EMAIL>'
      });

      const updatedUser = await tenantManager.updateTenantUser(tenant.id, user.id, {
        enabled: false,
        roles: [TenantUser.getAdminRole()]
      });

      expect(updatedUser.enabled).toBe(false);
      expect(updatedUser.roles).toEqual([TenantUser.getAdminRole()]);
    });

    it('should delete tenant user', async () => {
      const user = await tenantManager.createTenantUser(tenant.id, {
        username: 'deleteuser',
        email: '<EMAIL>'
      });

      const success = await tenantManager.deleteTenantUser(tenant.id, user.id);
      expect(success).toBe(true);

      const users = await tenantManager.getTenantUsers(tenant.id);
      expect(users.data.find((u) => u.id === user.id)).toBeUndefined();
    });
  });

  describe('Tenant Settings Validation', () => {
    it('should validate correct tenant settings', async () => {
      const settings = Tenant.getDefaultSettings();
      const validation = await tenantManager.validateTenantSettings(settings);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should reject invalid tenant settings', async () => {
      const invalidSettings = {
        ...Tenant.getDefaultSettings(),
        max_concurrent_workflows: -1,
        session_timeout: 0,
        allowed_node_types: 'not-an-array' as any
      };

      const validation = await tenantManager.validateTenantSettings(invalidSettings);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors).toContain('max_concurrent_workflows must be greater than 0');
      expect(validation.errors).toContain('session_timeout must be greater than 0');
      expect(validation.errors).toContain('allowed_node_types must be an array');
    });
  });
});
