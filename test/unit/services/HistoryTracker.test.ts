/**
 * Unit tests for HistoryTracker service
 */

import { HistoryTracker } from '../../../src/services/HistoryTracker';
import { MockDatabase } from '../../mocks/MockDatabase';
import { MockLogger } from '../../mocks/MockLogger';
import {
  WorkflowExecutionRecord,
  UserActionRecord,
  SystemEventRecord,
  RetentionPolicy
} from '../../../src/core/interfaces/IHistoryTracker';

describe('HistoryTracker', () => {
  let historyTracker: HistoryTracker;
  let mockDatabase: MockDatabase;
  let mockLogger: MockLogger;

  beforeEach(() => {
    mockDatabase = new MockDatabase();
    mockLogger = new MockLogger();
    historyTracker = new HistoryTracker(mockDatabase, mockLogger);
  });

  afterEach(() => {
    mockDatabase.clearAllData();
    mockLogger.clearLogs();
  });

  describe('recordWorkflowExecution', () => {
    it('should record workflow execution successfully', async () => {
      const execution: WorkflowExecutionRecord = {
        workflowId: 'workflow-1',
        executionId: 'exec-1',
        tenantId: 'tenant-1',
        status: 'completed',
        startTime: new Date('2024-01-01T10:00:00Z'),
        endTime: new Date('2024-01-01T10:05:00Z'),
        duration: 300000,
        inputData: { input: 'test' },
        outputData: { output: 'result' },
        userId: 'user-1'
      };

      await historyTracker.recordWorkflowExecution(execution);

      const savedData = mockDatabase.getTestData('WorkflowExecutionHistory');
      expect(savedData).toHaveLength(1);
      expect(savedData[0]).toMatchObject({
        workflowId: 'workflow-1',
        executionId: 'exec-1',
        status: 'completed',
        duration: 300000
      });

      expect(mockLogger.hasLogWithMessage('Workflow execution recorded successfully')).toBe(true);
    });

    it('should handle errors when recording workflow execution', async () => {
      const execution: WorkflowExecutionRecord = {
        workflowId: 'workflow-1',
        executionId: 'exec-1',
        status: 'failed',
        startTime: new Date(),
        errorMessage: 'Test error'
      };

      // Mock repository to throw error
      const dataSource = await mockDatabase.getDataSource();
      const repo = dataSource.getRepository('WorkflowExecutionHistory');
      repo.save = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(historyTracker.recordWorkflowExecution(execution)).rejects.toThrow(
        'Database error'
      );
      expect(mockLogger.hasLogWithMessage('Failed to record workflow execution')).toBe(true);
    });

    it('should generate correlation ID if not provided', async () => {
      const execution: WorkflowExecutionRecord = {
        workflowId: 'workflow-1',
        executionId: 'exec-1',
        status: 'started',
        startTime: new Date()
      };

      await historyTracker.recordWorkflowExecution(execution);

      const savedData = mockDatabase.getTestData('WorkflowExecutionHistory');
      expect(savedData[0].correlationId).toBeDefined();
      expect(savedData[0].correlationId).toMatch(/^[a-z0-9-]+$/);
    });
  });

  describe('recordUserAction', () => {
    it('should record user action successfully', async () => {
      const action: UserActionRecord = {
        userId: 'user-1',
        tenantId: 'tenant-1',
        action: 'create_workflow',
        resourceType: 'workflow',
        resourceId: 'workflow-1',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        ipAddress: '***********',
        details: { name: 'Test Workflow' }
      };

      await historyTracker.recordUserAction(action);

      const savedData = mockDatabase.getTestData('UserActionHistory');
      expect(savedData).toHaveLength(1);
      expect(savedData[0]).toMatchObject({
        userId: 'user-1',
        action: 'create_workflow',
        resourceType: 'workflow',
        resourceId: 'workflow-1',
        ipAddress: '***********'
      });

      expect(mockLogger.hasLogWithMessage('User action recorded successfully')).toBe(true);
    });

    it('should handle errors when recording user action', async () => {
      const action: UserActionRecord = {
        userId: 'user-1',
        action: 'test_action',
        resourceType: 'test',
        resourceId: 'test-1',
        timestamp: new Date()
      };

      // Mock repository to throw error
      const dataSource = await mockDatabase.getDataSource();
      const repo = dataSource.getRepository('UserActionHistory');
      repo.save = jest.fn().mockRejectedValue(new Error('Database error'));

      await expect(historyTracker.recordUserAction(action)).rejects.toThrow('Database error');
      expect(mockLogger.hasLogWithMessage('Failed to record user action')).toBe(true);
    });
  });

  describe('recordSystemEvent', () => {
    it('should record system event successfully', async () => {
      const event: SystemEventRecord = {
        eventType: 'workflow_started',
        eventCategory: 'workflow',
        severity: 'info',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        source: 'workflow-engine',
        message: 'Workflow execution started',
        details: { workflowId: 'workflow-1' },
        tenantId: 'tenant-1'
      };

      await historyTracker.recordSystemEvent(event);

      const savedData = mockDatabase.getTestData('SystemEventHistory');
      expect(savedData).toHaveLength(1);
      expect(savedData[0]).toMatchObject({
        eventType: 'workflow_started',
        eventCategory: 'workflow',
        severity: 'info',
        source: 'workflow-engine',
        message: 'Workflow execution started'
      });

      expect(mockLogger.hasLogWithMessage('System event recorded successfully')).toBe(true);
    });
  });

  describe('getExecutionHistory', () => {
    beforeEach(async () => {
      // Setup test data - order by startTime DESC (newer first)
      const executions = [
        {
          id: '2',
          workflowId: 'workflow-1',
          executionId: 'exec-2',
          status: 'failed',
          startTime: new Date('2024-01-01T11:00:00Z'),
          errorMessage: 'Test error',
          nodeExecutions: [{ status: 'completed' }, { status: 'failed' }]
        },
        {
          id: '1',
          workflowId: 'workflow-1',
          executionId: 'exec-1',
          status: 'completed',
          startTime: new Date('2024-01-01T10:00:00Z'),
          endTime: new Date('2024-01-01T10:05:00Z'),
          duration: 300000,
          nodeExecutions: [{ status: 'completed' }, { status: 'completed' }]
        }
      ];

      mockDatabase.setTestData('WorkflowExecutionHistory', executions);
    });

    it('should get execution history for workflow', async () => {
      const history = await historyTracker.getExecutionHistory('workflow-1');

      expect(history).toHaveLength(2);
      expect(history[0]).toMatchObject({
        workflowId: 'workflow-1',
        status: 'failed',
        nodeCount: 2,
        successfulNodes: 1,
        failedNodes: 1
      });
      expect(history[1]).toMatchObject({
        workflowId: 'workflow-1',
        status: 'completed',
        nodeCount: 2,
        successfulNodes: 2,
        failedNodes: 0
      });
    });

    it('should filter by status', async () => {
      const history = await historyTracker.getExecutionHistory('workflow-1', {
        status: ['completed']
      });

      expect(history).toHaveLength(1);
      expect(history[0].status).toBe('completed');
    });

    it('should apply pagination', async () => {
      const history = await historyTracker.getExecutionHistory('workflow-1', {
        limit: 1,
        offset: 1
      });

      expect(history).toHaveLength(1);
    });
  });

  describe('cleanup', () => {
    beforeEach(() => {
      const oldDate = new Date('2024-01-01T10:00:00Z');
      const recentDate = new Date();

      // Setup old test data
      mockDatabase.setTestData('WorkflowExecutionHistory', [
        { id: '1', startTime: oldDate },
        { id: '2', startTime: recentDate }
      ]);
      mockDatabase.setTestData('UserActionHistory', [
        { id: '1', timestamp: oldDate },
        { id: '2', timestamp: recentDate }
      ]);
      mockDatabase.setTestData('SystemEventHistory', [
        { id: '1', timestamp: oldDate },
        { id: '2', timestamp: recentDate }
      ]);
    });

    it('should cleanup old records based on retention policy', async () => {
      const retentionPolicy: RetentionPolicy = {
        maxAge: 30 // 30 days
      };

      const result = await historyTracker.cleanup(retentionPolicy);

      expect(result.deletedRecords).toBeGreaterThan(0);
      expect(result.categories).toContain('workflow_executions');
      expect(result.categories).toContain('user_actions');
      expect(result.categories).toContain('system_events');
      expect(result.executionTime).toBeGreaterThanOrEqual(0);

      expect(mockLogger.hasLogWithMessage('History cleanup completed')).toBe(true);
    });
  });

  describe('getStatistics', () => {
    beforeEach(() => {
      // Setup test data for statistics
      mockDatabase.setTestData('WorkflowExecutionHistory', [
        { id: '1', workflowId: 'wf-1', status: 'completed', duration: 1000 },
        { id: '2', workflowId: 'wf-1', status: 'completed', duration: 2000 },
        { id: '3', workflowId: 'wf-2', status: 'failed', duration: 500 }
      ]);
      mockDatabase.setTestData('UserActionHistory', [
        { id: '1', userId: 'user-1' },
        { id: '2', userId: 'user-1' },
        { id: '3', userId: 'user-2' }
      ]);
      mockDatabase.setTestData('SystemEventHistory', [{ id: '1' }, { id: '2' }]);
    });

    it('should return comprehensive statistics', async () => {
      const stats = await historyTracker.getStatistics();

      expect(stats).toMatchObject({
        totalExecutions: 3,
        successfulExecutions: 2,
        failedExecutions: 1,
        totalUserActions: 3,
        totalSystemEvents: 2
      });

      expect(stats.topWorkflows).toEqual([
        { workflowId: 'wf-1', count: 2 },
        { workflowId: 'wf-2', count: 1 }
      ]);

      expect(stats.topUsers).toEqual([
        { userId: 'user-1', count: 2 },
        { userId: 'user-2', count: 1 }
      ]);
    });
  });

  describe('searchHistory', () => {
    beforeEach(() => {
      mockDatabase.setTestData('WorkflowExecutionHistory', [
        {
          id: '1',
          workflowId: 'wf-1',
          executionId: 'exec-1',
          status: 'failed',
          startTime: new Date(),
          errorMessage: 'connection timeout',
          metadata: { error: 'network' },
          nodeExecutions: []
        }
      ]);
      mockDatabase.setTestData('UserActionHistory', [
        {
          id: '1',
          userId: 'user-1',
          action: 'create_workflow',
          resourceType: 'workflow',
          resourceId: 'wf-1',
          timestamp: new Date(),
          details: { name: 'test workflow' }
        }
      ]);
      mockDatabase.setTestData('SystemEventHistory', [
        {
          id: '1',
          eventType: 'startup',
          eventCategory: 'system',
          severity: 'info',
          timestamp: new Date(),
          source: 'server',
          message: 'system startup',
          details: { version: '1.0.0' }
        }
      ]);
    });

    it('should search across all history types', async () => {
      const results = await historyTracker.searchHistory('test');

      expect(results.executions).toHaveLength(0); // No match in executions
      expect(results.userActions).toHaveLength(1); // Match in user actions
      expect(results.systemEvents).toHaveLength(0); // No match in system events
    });
  });
});
