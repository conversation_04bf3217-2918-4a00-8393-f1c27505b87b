import 'reflect-metadata';
import { Request, Response } from 'express';
import { Container } from 'inversify';
import { WorkflowController } from '../../../src/api/controllers/WorkflowController';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { IValidator } from '../../../src/core/interfaces/IValidator';
import { IRepository } from '../../../src/core/interfaces/IRepository';
import { IWorkflowEngine } from '../../../src/core/interfaces/IWorkflowEngine';
import { IObservabilityManager } from '../../../src/core/interfaces/IObservabilityManager';
import { Workflow } from '../../../src/infrastructure/database/entities/Workflow.entity';
import { ValidationError } from '../../../src/core/errors/ValidationError';
import { TYPES } from '../../../src/types';

describe('WorkflowController', () => {
  let controller: WorkflowController;
  let mockRepository: jest.Mocked<IRepository<Workflow>>;
  let mockValidator: jest.Mocked<IValidator<Workflow>>;
  let mockLogger: jest.Mocked<ILogger>;
  let mockWorkflowEngine: jest.Mocked<IWorkflowEngine>;
  let mockObservabilityManager: jest.Mocked<IObservabilityManager>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let container: Container;

  beforeEach(() => {
    container = new Container();

    // Create mocks
    mockRepository = {
      findAll: jest.fn(),
      findById: jest.fn(),
      findBy: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      transaction: jest.fn(),
      query: jest.fn()
    };

    mockValidator = {
      validateForCreate: jest.fn(),
      validateForUpdate: jest.fn(),
      validateId: jest.fn()
    };

    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      fatal: jest.fn(),
      trace: jest.fn(),
      child: jest.fn().mockReturnThis()
    };

    mockWorkflowEngine = {
      initialize: jest.fn(),
      executeWorkflow: jest.fn(),
      executeWorkflowConfig: jest.fn(),
      getWorkflow: jest.fn(),
      reloadWorkflow: jest.fn(),
      reloadAllWorkflows: jest.fn(),
      removeWorkflow: jest.fn(),
      getWorkflowExecutionHistory: jest.fn()
    };

    mockObservabilityManager = {
      recordWorkflowExecution: jest.fn(),
      recordNodeExecution: jest.fn(),
      getWorkflowMetrics: jest.fn(),
      getNodeMetrics: jest.fn(),
      exportPrometheusMetrics: jest.fn(),
      cleanupOldData: jest.fn()
    };

    mockRequest = {
      params: {},
      body: {},
      query: {}
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    // Create controller
    controller = new WorkflowController(
      mockRepository,
      mockValidator,
      mockLogger,
      mockWorkflowEngine,
      mockObservabilityManager
    );
  });

  describe('getAll', () => {
    it('should return all workflows', async () => {
      const workflows = [
        { id: '1', name: 'Workflow 1' },
        { id: '2', name: 'Workflow 2' }
      ] as Workflow[];

      mockRepository.findAll.mockResolvedValue(workflows);

      await controller.getAll(mockRequest as Request, mockResponse as Response);

      expect(mockRepository.findAll).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(workflows);
    });

    it('should handle errors', async () => {
      const error = new Error('Database error');
      mockRepository.findAll.mockRejectedValue(error);

      await controller.getAll(mockRequest as Request, mockResponse as Response);

      expect(mockLogger.error).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });
  });

  describe('getById', () => {
    it('should return workflow by ID', async () => {
      const workflow = { id: '1', name: 'Test Workflow' } as Workflow;
      mockRequest.params = { id: '1' };

      mockRepository.findById.mockResolvedValue(workflow);

      await controller.getById(mockRequest as Request, mockResponse as Response);

      expect(mockValidator.validateId).toHaveBeenCalledWith('1');
      expect(mockRepository.findById).toHaveBeenCalledWith('1');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(workflow);
    });

    it('should return 404 when workflow not found', async () => {
      mockRequest.params = { id: '1' };
      mockRepository.findById.mockResolvedValue(null);

      await controller.getById(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: {
          code: 1009, // ErrorCode.NOT_FOUND
          message: 'Entity not found'
        }
      });
    });

    it('should handle validation errors', async () => {
      mockRequest.params = { id: 'invalid-id' };
      mockValidator.validateId.mockImplementation(() => {
        throw new ValidationError('Invalid ID');
      });

      await controller.getById(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });
  });

  describe('create', () => {
    it('should create a new workflow', async () => {
      const workflowData = { name: 'New Workflow' };
      const validatedData = { name: 'New Workflow', enabled: true } as Workflow;
      const createdWorkflow = { ...validatedData, id: '1' } as Workflow;

      mockRequest.body = workflowData;
      mockValidator.validateForCreate.mockResolvedValue(validatedData);
      mockRepository.create.mockResolvedValue(createdWorkflow);

      await controller.create(mockRequest as Request, mockResponse as Response);

      expect(mockValidator.validateForCreate).toHaveBeenCalledWith(workflowData);
      expect(mockRepository.create).toHaveBeenCalledWith(validatedData);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(createdWorkflow);
    });

    it('should handle validation errors', async () => {
      const workflowData = { name: '' }; // Invalid data
      mockRequest.body = workflowData;

      mockValidator.validateForCreate.mockRejectedValue(new ValidationError('Name is required'));

      await controller.create(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });
  });

  describe('test', () => {
    it('should test a workflow configuration', async () => {
      const workflowConfig = { name: 'Test Workflow', nodes_config: { nodes: [], edges: [] } };
      const input = { test: 'data' };
      const result = { output: 'test result' };

      mockRequest.body = { workflow: workflowConfig, input };
      mockValidator.validateForCreate.mockResolvedValue(workflowConfig as unknown as Workflow);
      mockWorkflowEngine.executeWorkflowConfig.mockResolvedValue(result);

      await controller.test(mockRequest as Request, mockResponse as Response);

      expect(mockValidator.validateForCreate).toHaveBeenCalledWith(workflowConfig);
      expect(mockWorkflowEngine.executeWorkflowConfig).toHaveBeenCalledWith(workflowConfig, input);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        result,
        success: true
      });
    });

    it('should handle missing workflow configuration', async () => {
      mockRequest.body = { input: { test: 'data' } }; // Missing workflow

      await controller.test(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });

    it('should handle missing input data', async () => {
      mockRequest.body = { workflow: { name: 'Test' } }; // Missing input

      await controller.test(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });
  });

  describe('getExecutionHistory', () => {
    it('should return workflow execution history', async () => {
      const workflowId = '1';
      const workflow = { id: workflowId, name: 'Test Workflow' } as Workflow;
      const history = [
        { id: '1', workflowId, status: 'completed' },
        { id: '2', workflowId, status: 'failed' }
      ];

      mockRequest.params = { id: workflowId };
      mockRepository.findById.mockResolvedValue(workflow);
      mockWorkflowEngine.getWorkflowExecutionHistory.mockResolvedValue(history);

      await controller.getExecutionHistory(mockRequest as Request, mockResponse as Response);

      expect(mockValidator.validateId).toHaveBeenCalledWith(workflowId);
      expect(mockRepository.findById).toHaveBeenCalledWith(workflowId);
      expect(mockWorkflowEngine.getWorkflowExecutionHistory).toHaveBeenCalledWith(workflowId);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(history);
    });

    it('should return 404 when workflow not found', async () => {
      const workflowId = '1';
      mockRequest.params = { id: workflowId };
      mockRepository.findById.mockResolvedValue(null);

      await controller.getExecutionHistory(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: {
          code: 1009, // ErrorCode.NOT_FOUND
          message: 'Workflow not found'
        }
      });
    });
  });

  describe('getMetrics', () => {
    it('should return workflow metrics', async () => {
      const workflowId = '1';
      const workflow = { id: workflowId, name: 'Test Workflow' } as Workflow;
      const metrics = [
        {
          totalExecutions: 10,
          successfulExecutions: 8,
          failedExecutions: 2,
          averageDurationMs: 1500,
          minDurationMs: 500,
          maxDurationMs: 3000,
          p95DurationMs: 2500,
          p99DurationMs: 2800,
          errorRate: 0.2,
          metrics: []
        }
      ];

      mockRequest.params = { id: workflowId };
      mockRequest.query = {};
      mockRepository.findById.mockResolvedValue(workflow);
      mockObservabilityManager.getWorkflowMetrics.mockResolvedValue(metrics);

      await controller.getMetrics(mockRequest as Request, mockResponse as Response);

      expect(mockValidator.validateId).toHaveBeenCalledWith(workflowId);
      expect(mockRepository.findById).toHaveBeenCalledWith(workflowId);
      expect(mockObservabilityManager.getWorkflowMetrics).toHaveBeenCalledWith(
        workflowId,
        undefined
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(metrics);
    });

    it('should handle time range parameters', async () => {
      const workflowId = '1';
      const workflow = { id: workflowId, name: 'Test Workflow' } as Workflow;
      const metrics = [
        {
          totalExecutions: 5,
          successfulExecutions: 4,
          failedExecutions: 1,
          averageDurationMs: 1000,
          minDurationMs: 500,
          maxDurationMs: 1500,
          p95DurationMs: 1400,
          p99DurationMs: 1450,
          errorRate: 0.2,
          metrics: []
        }
      ];

      mockRequest.params = { id: workflowId };
      mockRequest.query = {
        startTime: '2024-01-01T00:00:00Z',
        endTime: '2024-01-31T23:59:59Z'
      };
      mockRepository.findById.mockResolvedValue(workflow);
      mockObservabilityManager.getWorkflowMetrics.mockResolvedValue(metrics);

      await controller.getMetrics(mockRequest as Request, mockResponse as Response);

      expect(mockObservabilityManager.getWorkflowMetrics).toHaveBeenCalledWith(workflowId, {
        startTime: new Date('2024-01-01T00:00:00Z'),
        endTime: new Date('2024-01-31T23:59:59Z')
      });
    });

    it('should return 404 when workflow not found', async () => {
      const workflowId = '1';
      mockRequest.params = { id: workflowId };
      mockRepository.findById.mockResolvedValue(null);

      await controller.getMetrics(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: {
          code: 1009, // ErrorCode.NOT_FOUND
          message: 'Workflow not found'
        }
      });
    });
  });
});
