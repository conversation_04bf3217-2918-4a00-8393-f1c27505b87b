.idea

.env.development
.env.test
.env.lab

# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace
# Local History for Visual Studio Code
.history/

# Common credential files
**/credentials.json
**/client_secrets.json
**/client_secret.json
*creds*
*.dat
*password*
*.httr-oauth*

# Private Node Modules
node_modules/
creds.js

# Private Files
#*.json
*.csv
*.csv.gz
*.tsv
*.tsv.gz
*.xlsx

# Mac/OSX
.DS_Store



# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
cover/



# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.env.local

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yalc
yalc.lock

# testing
/coverage-test
/coverage-type

# production
/build
/dist
/storybook-static

# misc
.DS_Store
*.pem
.idea
.vscode
audit-ci.jsonc

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# typescript
*.tsbuildinfo

devtunnel.json