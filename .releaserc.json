{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/changelog", ["@semantic-release/exec", {"prepareCmd": "echo ${nextRelease.version} > version && node scripts/update-helm-values.js ${nextRelease.version}"}], ["@semantic-release/git", {"assets": ["CHANGELOG.md", "version", "package.json", "charts/ccczautomationmcpserver/values-dev.yaml", "charts/ccczautomationmcpserver/values-prod.yaml", "charts/ccczautomationmcpserver/Chart.yaml"], "message": "chore(release): ${nextRelease.version}\n\n${nextRelease.notes}"}], "@semantic-release/gitlab"]}