# Public REST API Endpoints Guide

This guide explains how to create and use public REST API endpoints for workflows without authentication.

## Overview

By default, all API endpoints in the system require authentication. However, you can create public endpoints for workflows that have REST API triggers with `authentication: false`.

## Implementation

We've implemented a public routes handler that:

1. Bypasses the authentication middleware
2. Respects the workflow's REST API trigger configuration
3. Routes requests to the appropriate workflow based on path and method

### File Structure

```
src/api/routes/publicRoutes.ts    # Public routes handler
src/api/adminServer.ts            # Updated to mount public routes
scripts/test-public-rest-api.ts   # Test script
```

## How It Works

### 1. Public Route Prefix

All public endpoints are available under the `/api/public` prefix:

- Protected endpoint: `/api/workflows/:id/trigger`
- Public endpoint: `/api/public/your-custom-path`

### 2. Workflow Configuration

Your workflow must have a REST API trigger node configured like this:

```javascript
{
  id: 'rest-trigger',
  type: 'rest-api-trigger',
  name: 'REST API Trigger',
  config: {
    method: 'POST',              // HTTP method
    path: '/process-message',    // Your custom path
    authentication: false,       // IMPORTANT: Set to false for public access
    validation: true
  }
}
```

### 3. Request Flow

1. Client sends request to `/api/public/process-message`
2. Public routes handler finds matching workflow
3. Checks if authentication is required (based on trigger config)
4. Executes workflow with request data
5. Returns workflow output as HTTP response

## Usage Example

### Step 1: Create a Workflow

Create a workflow with a REST API trigger:

```javascript
const workflow = {
  name: 'public-message-processor',
  description: 'Processes messages without authentication',
  enabled: true,
  nodes_config: {
    nodes: [
      {
        id: 'trigger',
        type: 'rest-api-trigger',
        name: 'Public REST API',
        config: {
          method: 'POST',
          path: '/process-message',
          authentication: false, // Public access
          validation: true
        }
      },
      {
        id: 'process',
        type: 'javascript-action',
        name: 'Process Message',
        config: {
          code: `
            // Access request data
            const message = input.request?.body?.message;
            
            if (!message) {
              return {
                statusCode: 400,
                body: { error: 'Message is required' }
              };
            }
            
            // Process and return response
            return {
              statusCode: 200,
              body: {
                original: message,
                processed: message.toUpperCase(),
                timestamp: new Date().toISOString()
              }
            };
          `
        }
      }
    ],
    edges: [{ source: 'trigger', target: 'process' }]
  }
};
```

### Step 2: Call the Public Endpoint

```bash
# No authentication required!
curl -X POST http://localhost:7002/api/public/process-message \
  -H "Content-Type: application/json" \
  -d '{"message": "hello world"}'
```

### Step 3: Response

```json
{
  "original": "hello world",
  "processed": "HELLO WORLD",
  "timestamp": "2024-01-20T10:30:00.000Z"
}
```

## Request Data Structure

The workflow receives the following input structure:

```javascript
{
  trigger: 'rest-api',
  request: {
    method: 'POST',
    path: '/process-message',
    headers: { /* all request headers */ },
    query: { /* query parameters */ },
    body: { /* request body */ },
    params: { /* URL parameters */ }
  },
  timestamp: '2024-01-20T10:30:00.000Z',
  workflowId: 'workflow-id',
  executionId: 'unique-execution-id'
}
```

Access request data in your workflow nodes:

- Body: `input.request.body`
- Query params: `input.request.query`
- Headers: `input.request.headers`
- Method: `input.request.method`
- Path: `input.request.path`

## Response Format

Your workflow can return different response formats:

### Simple Response

```javascript
return { message: 'Success' };
// Returns: 200 OK with { message: 'Success' }
```

### Custom Status Code

```javascript
return {
  statusCode: 201,
  body: { id: '123', created: true }
};
// Returns: 201 Created with { id: '123', created: true }
```

### With Headers

```javascript
return {
  statusCode: 200,
  headers: {
    'X-Custom-Header': 'value'
  },
  body: { data: 'response' }
};
// Returns: 200 OK with custom header and body
```

## Testing

Use the provided test script:

```bash
# Install dependencies
npm install axios

# Run test
npm run test:public-api
# or
node scripts/test-public-rest-api.ts
```

## Security Considerations

1. **Public Access**: Endpoints with `authentication: false` are publicly accessible
2. **Input Validation**: Always validate input data in your workflow
3. **Rate Limiting**: Consider implementing rate limiting for public endpoints
4. **Error Handling**: Don't expose sensitive information in error messages
5. **Monitoring**: Log all public endpoint requests for security monitoring

## Troubleshooting

### 404 Not Found

- Check workflow is enabled
- Verify path and method match exactly
- Ensure workflow has REST API trigger node

### 401 Unauthorized

- Verify `authentication: false` in trigger config
- Check public routes are properly mounted
- Ensure using `/api/public` prefix

### 500 Internal Server Error

- Check workflow logic for errors
- Verify all required fields are present
- Check server logs for details

## Development vs Production

In development mode (`NODE_ENV=development`), authentication is bypassed for all endpoints. In production, only endpoints with `authentication: false` are public.

## Summary

Public REST API endpoints allow you to:

- Create webhooks without authentication
- Build public APIs using workflows
- Integrate with external systems easily
- Maintain workflow-based request handling

Remember to carefully consider security implications when creating public endpoints!
