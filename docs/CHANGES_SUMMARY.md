# Souhrn změn - Rozšíření o Frontend a Deployment

Tento dokument shrnuje všechny změny provedené pro rozšíření projektu o frontend aplikaci a kompletní deployment pipeline.

## Přehled změn

### 1. GitLab CI/CD Pipeline (.gitlab-ci.yml)

**Změny:**

- Aktualizace Node.js verze z 20 na 22
- Změna z npm na yarn pro všechny operace
- Přidání frontend build a test stages
- Rozdělení Docker build na backend a frontend
- Rozšíření test stages o frontend typecheck a lint

**Nové stages:**

- `backend typecheck`: TypeScript kontrola pro backend
- `frontend typecheck`: TypeScript kontrola pro frontend
- `backend lint`: ESLint kontrola pro backend
- `frontend lint`: ESLint kontrola pro frontend
- `build frontend docker image and push`: Build a push frontend Docker image

### 2. Frontend Docker Setup

**Nové soubory:**

- `frontend/Dockerfile`: Multi-stage build s nginx
- `frontend/nginx.conf`: Nginx konfigurace pro SPA routing

**Dockerfile features:**

- Multi-stage build (build + production)
- Nginx pro servírování statických souborů
- Health check endpoint
- Optimalizace pro production

### 3. Helm Chart rozšíření

**Nové templates:**

- `frontend-deployment.yaml`: Frontend deployment
- `frontend-service.yaml`: Frontend service
- `frontend-configmap.yaml`: Frontend environment variables

**Upravené templates:**

- `ingress.yaml`: Routing pro frontend a backend
- `deployment.yaml`: Dynamické image tagy

**Ingress routing:**

- `/api/*` → Backend service
- `/socket.io/*` → Backend service
- `/health` → Backend service
- `/*` → Frontend service

### 4. Values soubory

**Přidané konfigurace:**

- Frontend image repository a tag
- Frontend service konfigurace
- Frontend resources (CPU, memory)
- Frontend environment variables
- Kompletní backend environment variables z .env.development
- Database connection settings
- Redis connection settings
- JWT authentication settings
- Workflow configuration
- Data management settings
- LiteLLM integration settings

**Struktura:**

```yaml
frontend:
  NODE_ENV: production
  apiUrl: /api
  wsUrl: /socket.io
  image:
    repository: 'registry/image-frontend'
    tag: '1.0.0'
  service:
    type: ClusterIP
    port: 80
  resources:
    limits:
      cpu: 500m
      memory: 512Mi

environment:
  # Server Configuration
  HOST: '0.0.0.0'
  API_PORT: '7001'
  LOG_LEVEL: 'debug' # dev: debug, prod: info

  # Database Configuration
  DB_HOST: 'ccczautomationmcpserver-dev-postgresql'
  DB_MIGRATIONS_RUN: 'true' # dev: true, prod: false

  # Redis Configuration
  REDIS_HOST: 'ccczautomationmcpserver-dev-redis'
  USE_REDIS_WORKFLOW_MEMORY: 'false' # dev: false, prod: true

  # Data Management
  DATA_RETENTION_DAYS: '5' # dev: 5, prod: 30
  DATA_CLEANUP_INTERVAL_MINUTES: '5' # dev: 5, prod: 60

  # Authentication
  JWT_SECRET: 'your-secret-key-change-in-production'
```

### 5. Semantic Release

**Nové soubory:**

- `.releaserc.json`: Semantic release konfigurace
- `scripts/update-helm-values.js`: Automatická aktualizace Helm values
- `version`: Soubor s aktuální verzí

**Automatizace:**

- Conventional commits pro verzování
- Automatická aktualizace package.json verze
- Automatická aktualizace Helm values s novou verzí
- Automatická aktualizace Chart.yaml (version + appVersion)
- Git commit s aktualizovanými soubory
- Vytvoření Git tagu

**Proces:**

1. Semantic release analyzuje commits
2. Generuje novou verzi
3. Aktualizuje: version, package.json, Helm values, Chart.yaml
4. Vytvoří CHANGELOG.md
5. Commitne změny s [skip ci]
6. Vytvoří Git tag
7. Docker build používá verzi ze souboru "version"

### 6. Package.json úpravy

**Backend (package.json):**

- Změna npm na yarn v scriptech
- Přidání semantic-release dependency
- Přidání frontend scriptů

**Frontend (frontend/package.json):**

- Změna npx na yarn v build scriptech

### 7. Dokumentace

**Nové soubory:**

- `DEPLOYMENT.md`: Detailní deployment guide
- `CHANGES_SUMMARY.md`: Tento soubor

**Aktualizované soubory:**

- `README.md`: Aktualizace s informacemi o CI/CD a deployment

## Deployment Process

### Automatický workflow:

1. **Commit s conventional commit message** do main branch
2. **Semantic release** analyzuje změny a vytvoří novou verzi
3. **Automatická aktualizace** Helm values s novou verzí
4. **Docker build** pro backend a frontend s novou verzí
5. **Manual trigger** pro deployment v GitLab UI

### Docker Images:

- **Backend**: `${CI_REGISTRY_IMAGE}:${VERSION}`
  - Příklad: `network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:1.0.1`
- **Frontend**: `${CI_REGISTRY_IMAGE}:frontend-${VERSION}`
  - Příklad: `network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:frontend-1.0.1`

### Conventional Commits:

- `feat:` → minor version bump
- `fix:` → patch version bump
- `feat!:` nebo `BREAKING CHANGE:` → major version bump

## Architektura

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │
│   (React/Vite)  │    │   (Node.js)     │
│   Port: 80      │    │   Port: 3000    │
│   (nginx)       │    │                 │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                     │
         ┌─────────────────┐
         │   Ingress       │
         │   (nginx)       │
         └─────────────────┘
                     │
         ┌─────────────────┐    ┌─────────────────┐
         │   PostgreSQL    │    │   Redis         │
         │   Port: 5432    │    │   Port: 6379    │
         └─────────────────┘    └─────────────────┘
```

## Testování

### Lokální development:

```bash
# Backend
yarn dev

# Frontend
yarn frontend:dev
```

### Production build:

```bash
# Backend
docker build -t backend .

# Frontend
docker build -t frontend ./frontend
```

### Helm deployment:

```bash
helm upgrade --install myapp ./charts/ccczautomationmcpserver \
  --values ./charts/ccczautomationmcpserver/values-dev.yaml
```

## Monitoring

- Backend health: `/health`
- Frontend health: `/health`
- Prometheus metrics: Backend exportuje metriky
- Logs: Strukturované logy s Pino

## Další kroky

1. Testování celého deployment procesu
2. Nastavení monitoring a alerting
3. Optimalizace Docker images
4. Dokumentace pro vývojáře
5. Backup a recovery procedury
