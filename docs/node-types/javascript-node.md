# JavaScript Node

The JavaScript node allows you to execute custom JavaScript code within your workflow, providing maximum flexibility for data transformation and business logic.

## Overview

The JavaScript node runs user-provided JavaScript code in a secure VM2 sandbox environment. It's perfect for:

- Data transformation and manipulation
- Complex business logic
- Mathematical calculations
- String processing
- Conditional logic
- Data validation

## Configuration

### Basic Configuration

```json
{
  "type": "javascript",
  "config": {
    "script": "return { result: input.value * 2 };"
  }
}
```

### Advanced Configuration

```json
{
  "type": "javascript",
  "config": {
    "script": `
      // Complex data transformation
      const processData = (data) => {
        if (!data || !Array.isArray(data.items)) {
          throw new Error('Invalid input: items must be an array');
        }
        
        return data.items
          .filter(item => item.active)
          .map(item => ({
            id: item.id,
            name: item.name.toUpperCase(),
            value: item.value * 1.1,
            processed: true,
            timestamp: new Date().toISOString()
          }))
          .sort((a, b) => b.value - a.value);
      };
      
      return {
        processedItems: processData(input),
        totalCount: input.items ? input.items.length : 0,
        activeCount: input.items ? input.items.filter(i => i.active).length : 0
      };
    `
  }
}
```

## Script Environment

### Available Variables

- `input`: The input data passed to the node
- `console`: Limited console object for debugging (console.log, console.error)

### Available Functions

The script has access to standard JavaScript functions and objects:

- `Math`: Mathematical functions
- `Date`: Date and time operations
- `JSON`: JSON parsing and stringification
- `String`, `Array`, `Object`: Standard JavaScript types
- `parseInt`, `parseFloat`: Number parsing
- `encodeURIComponent`, `decodeURIComponent`: URL encoding

### Restricted Access

For security reasons, the following are NOT available:

- `require()`: Cannot import external modules
- File system access
- Network access
- Process manipulation
- Global variables modification

## Examples

### Data Transformation

```json
{
  "id": "transform_users",
  "type": "javascript",
  "name": "Transform User Data",
  "config": {
    "script": `
      const users = input.users || [];
      
      return {
        transformedUsers: users.map(user => ({
          id: user.id,
          fullName: \`\${user.firstName} \${user.lastName}\`,
          email: user.email.toLowerCase(),
          age: new Date().getFullYear() - new Date(user.birthDate).getFullYear(),
          isAdult: new Date().getFullYear() - new Date(user.birthDate).getFullYear() >= 18
        })),
        totalUsers: users.length,
        adultUsers: users.filter(u => 
          new Date().getFullYear() - new Date(u.birthDate).getFullYear() >= 18
        ).length
      };
    `
  }
}
```

### Conditional Logic

```json
{
  "id": "route_logic",
  "type": "javascript",
  "name": "Routing Logic",
  "config": {
    "script": `
      const { userType, amount, country } = input;
      
      let route = 'default';
      let requiresApproval = false;
      let processingFee = 0;
      
      if (userType === 'premium') {
        route = 'premium_processing';
        processingFee = amount * 0.01; // 1% fee
      } else if (userType === 'standard') {
        route = 'standard_processing';
        processingFee = amount * 0.02; // 2% fee
        
        if (amount > 10000) {
          requiresApproval = true;
        }
      } else {
        route = 'basic_processing';
        processingFee = amount * 0.03; // 3% fee
        
        if (amount > 5000) {
          requiresApproval = true;
        }
      }
      
      // Country-specific rules
      if (country === 'US' && amount > 10000) {
        requiresApproval = true;
      }
      
      return {
        route,
        requiresApproval,
        processingFee,
        finalAmount: amount - processingFee
      };
    `
  }
}
```

### Data Validation

```json
{
  "id": "validate_order",
  "type": "javascript",
  "name": "Validate Order",
  "config": {
    "script": `
      const order = input.order;
      const errors = [];
      
      // Required fields validation
      if (!order.customerId) {
        errors.push('Customer ID is required');
      }
      
      if (!order.items || !Array.isArray(order.items) || order.items.length === 0) {
        errors.push('Order must contain at least one item');
      }
      
      // Item validation
      if (order.items) {
        order.items.forEach((item, index) => {
          if (!item.productId) {
            errors.push(\`Item \${index + 1}: Product ID is required\`);
          }
          
          if (!item.quantity || item.quantity <= 0) {
            errors.push(\`Item \${index + 1}: Quantity must be greater than 0\`);
          }
          
          if (!item.price || item.price <= 0) {
            errors.push(\`Item \${index + 1}: Price must be greater than 0\`);
          }
        });
      }
      
      // Business rules validation
      const totalAmount = order.items ? 
        order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) : 0;
      
      if (totalAmount > 50000) {
        errors.push('Order amount exceeds maximum limit of $50,000');
      }
      
      return {
        isValid: errors.length === 0,
        errors,
        totalAmount,
        validatedOrder: errors.length === 0 ? {
          ...order,
          totalAmount,
          validatedAt: new Date().toISOString()
        } : null
      };
    `
  }
}
```

### Mathematical Calculations

```json
{
  "id": "calculate_metrics",
  "type": "javascript",
  "name": "Calculate Business Metrics",
  "config": {
    "script": `
      const { sales, costs, previousPeriod } = input;
      
      const totalRevenue = sales.reduce((sum, sale) => sum + sale.amount, 0);
      const totalCosts = costs.reduce((sum, cost) => sum + cost.amount, 0);
      const profit = totalRevenue - totalCosts;
      const profitMargin = totalRevenue > 0 ? (profit / totalRevenue) * 100 : 0;
      
      // Growth calculations
      const previousRevenue = previousPeriod ? previousPeriod.revenue : 0;
      const revenueGrowth = previousRevenue > 0 ? 
        ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
      
      // Statistical calculations
      const salesAmounts = sales.map(s => s.amount);
      const averageSale = salesAmounts.length > 0 ? 
        salesAmounts.reduce((sum, amount) => sum + amount, 0) / salesAmounts.length : 0;
      
      const sortedSales = [...salesAmounts].sort((a, b) => a - b);
      const medianSale = sortedSales.length > 0 ? 
        sortedSales[Math.floor(sortedSales.length / 2)] : 0;
      
      return {
        financial: {
          totalRevenue,
          totalCosts,
          profit,
          profitMargin: Math.round(profitMargin * 100) / 100
        },
        growth: {
          revenueGrowth: Math.round(revenueGrowth * 100) / 100,
          previousRevenue
        },
        statistics: {
          totalSales: sales.length,
          averageSale: Math.round(averageSale * 100) / 100,
          medianSale,
          maxSale: Math.max(...salesAmounts),
          minSale: Math.min(...salesAmounts)
        }
      };
    `
  }
}
```

## Error Handling

### Throwing Errors

```javascript
if (!input.requiredField) {
  throw new Error('Required field is missing');
}
```

### Try-Catch Blocks

```javascript
try {
  const result = JSON.parse(input.jsonString);
  return { parsed: result };
} catch (error) {
  return { 
    error: 'Invalid JSON format',
    originalInput: input.jsonString 
  };
}
```

## Best Practices

### 1. Input Validation

Always validate input data before processing:

```javascript
if (!input || typeof input !== 'object') {
  throw new Error('Invalid input: expected object');
}

if (!Array.isArray(input.items)) {
  throw new Error('Invalid input: items must be an array');
}
```

### 2. Error Handling

Use try-catch blocks for operations that might fail:

```javascript
try {
  const result = complexOperation(input.data);
  return { success: true, result };
} catch (error) {
  return { 
    success: false, 
    error: error.message,
    input: input.data 
  };
}
```

### 3. Performance Considerations

- Avoid infinite loops
- Limit array processing for large datasets
- Use efficient algorithms for data processing

### 4. Debugging

Use console.log for debugging (available in development mode):

```javascript
console.log('Processing input:', input);
const result = processData(input);
console.log('Result:', result);
return result;
```

### 5. Return Consistent Data Structures

Always return consistent data structures:

```javascript
// Good
return {
  success: true,
  data: processedData,
  metadata: {
    processedAt: new Date().toISOString(),
    itemCount: processedData.length
  }
};

// Avoid returning different structures based on conditions
```

## Security Considerations

1. **Sandbox Environment**: Scripts run in a secure VM2 sandbox
2. **No External Access**: Cannot access files, network, or system resources
3. **Memory Limits**: Scripts have memory usage limits
4. **Execution Timeout**: Scripts have execution time limits
5. **No Persistent State**: Scripts cannot maintain state between executions

## Troubleshooting

### Common Issues

1. **Syntax Errors**: Check JavaScript syntax
2. **Reference Errors**: Ensure all variables are defined
3. **Type Errors**: Validate input data types
4. **Timeout Errors**: Optimize script performance

### Debugging Tips

1. Use console.log for debugging
2. Test scripts with sample data
3. Validate input structure
4. Handle edge cases
5. Use the Admin API test endpoint to validate scripts
