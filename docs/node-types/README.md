# Node Types Documentation

This document describes all available node types in the Dynamic MCP Server and their configuration options.

## Overview

The Dynamic MCP Server supports multiple node types that can be used to build complex workflows. Each node type has specific configuration requirements and capabilities.

## Available Node Types

1. [JavaScript Node](#javascript-node)
2. [HTTP Node](#http-node)
3. [SQL Node](#sql-node)
4. [Redis Node](#redis-node)
5. [LiteLLM Node](#litellm-node)

## JavaScript Node

The JavaScript node allows you to execute custom JavaScript code within your workflow.

### Configuration

```json
{
  "type": "javascript",
  "config": {
    "script": "return { result: input.value * 2 };"
  }
}
```

### Parameters

- `script` (required): JavaScript code to execute
  - The script has access to an `input` variable containing the node's input data
  - The script must return a value that will be passed to the next node
  - The script runs in a secure VM2 sandbox

### Example

```json
{
  "id": "transform",
  "type": "javascript",
  "name": "Transform Data",
  "config": {
    "script": "const result = input.numbers.map(n => n * 2); return { doubled: result };"
  }
}
```

### Input/Output

- **Input**: Any JSON object
- **Output**: The value returned by the script

### Security

- Scripts run in a secure VM2 sandbox
- No access to file system or network
- Limited execution time to prevent infinite loops

## HTTP Node

The HTTP node makes HTTP requests to external APIs.

### Configuration

```json
{
  "type": "http",
  "config": {
    "url": "https://api.example.com/data",
    "method": "GET",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer ${token}"
    },
    "timeout": 5000
  }
}
```

### Parameters

- `url` (required): The URL to make the request to
  - Supports variable interpolation using `${variable}` syntax
- `method` (optional): HTTP method (GET, POST, PUT, DELETE, etc.). Default: GET
- `headers` (optional): HTTP headers to include in the request
- `body` (optional): Request body for POST/PUT requests
- `timeout` (optional): Request timeout in milliseconds. Default: 5000

### Example

```json
{
  "id": "fetch_user",
  "type": "http",
  "name": "Fetch User Data",
  "config": {
    "url": "https://api.example.com/users/${userId}",
    "method": "GET",
    "headers": {
      "Authorization": "Bearer ${apiToken}",
      "Content-Type": "application/json"
    },
    "timeout": 10000
  }
}
```

### Input/Output

- **Input**: JSON object that can contain variables for URL/header interpolation
- **Output**: HTTP response object with `status`, `headers`, and `data` properties

### Error Handling

- Network errors are automatically retried based on workflow retry configuration
- HTTP error status codes (4xx, 5xx) can be configured as retryable errors

## SQL Node

The SQL node executes SQL queries against configured databases.

### Configuration

```json
{
  "type": "sql",
  "config": {
    "dataSource": "postgres-main",
    "query": "SELECT * FROM users WHERE id = $1",
    "parameters": ["${userId}"]
  }
}
```

### Parameters

- `dataSource` (required if no connectionString): Name of the configured data source
- `connectionString` (required if no dataSource): Direct database connection string
- `query` (required): SQL query to execute
  - Supports parameterized queries using $1, $2, etc. placeholders
- `parameters` (optional): Array of parameter values
  - Supports variable interpolation using `${variable}` syntax

### Example

```json
{
  "id": "get_orders",
  "type": "sql",
  "name": "Get User Orders",
  "config": {
    "dataSource": "postgres-main",
    "query": "SELECT o.*, p.name as product_name FROM orders o JOIN products p ON o.product_id = p.id WHERE o.user_id = $1 AND o.created_at >= $2",
    "parameters": ["${userId}", "${startDate}"]
  }
}
```

### Input/Output

- **Input**: JSON object containing variables for parameter interpolation
- **Output**: Array of query result rows

### Supported Databases

- PostgreSQL
- MySQL
- SQLite (via connection string)

### Security

- Always use parameterized queries to prevent SQL injection
- Database credentials are encrypted in data source configurations

## Redis Node

The Redis node interacts with Redis for caching and data storage.

### Configuration

```json
{
  "type": "redis",
  "config": {
    "command": "GET",
    "args": ["user:${userId}:profile"]
  }
}
```

### Parameters

- `command` (required): Redis command to execute
- `args` (optional): Array of command arguments
  - Supports variable interpolation using `${variable}` syntax
- `cacheKey` (alternative): For simple cache operations, specify just the key

### Example

```json
{
  "id": "cache_result",
  "type": "redis",
  "name": "Cache User Profile",
  "config": {
    "command": "SETEX",
    "args": ["user:${userId}:profile", "3600", "${profileData}"]
  }
}
```

### Input/Output

- **Input**: JSON object containing variables for argument interpolation
- **Output**: Redis command result

### Common Use Cases

- Caching API responses
- Session storage
- Rate limiting
- Pub/Sub messaging

## LiteLLM Node

The LiteLLM node integrates with various LLM providers through the LiteLLM library.

### Configuration

```json
{
  "type": "litellm",
  "config": {
    "model": "gpt-3.5-turbo",
    "dataSource": "openai-api",
    "prompt": "Summarize the following text: ${text}",
    "maxTokens": 150,
    "temperature": 0.7
  }
}
```

### Parameters

- `model` (required): LLM model to use (e.g., gpt-3.5-turbo, claude-3-sonnet)
- `dataSource` (required if no apiKey): Name of the configured LLM data source
- `apiKey` (required if no dataSource): Direct API key
- `prompt` (required): Prompt template
  - Supports variable interpolation using `${variable}` syntax
- `maxTokens` (optional): Maximum tokens to generate
- `temperature` (optional): Sampling temperature (0.0 to 1.0)
- `systemPrompt` (optional): System prompt for the conversation

### Example

```json
{
  "id": "analyze_sentiment",
  "type": "litellm",
  "name": "Analyze Text Sentiment",
  "config": {
    "model": "gpt-3.5-turbo",
    "dataSource": "openai-api",
    "systemPrompt": "You are a sentiment analysis expert. Respond only with 'positive', 'negative', or 'neutral'.",
    "prompt": "Analyze the sentiment of this text: ${text}",
    "maxTokens": 10,
    "temperature": 0.1
  }
}
```

### Input/Output

- **Input**: JSON object containing variables for prompt interpolation
- **Output**: LLM response object with `content`, `usage`, and `model` properties

### Supported Providers

- OpenAI (GPT models)
- Anthropic (Claude models)
- Google (Gemini models)
- And many others supported by LiteLLM

## Variable Interpolation

All node types support variable interpolation in their configuration using the `${variable}` syntax. Variables can come from:

1. Workflow input
2. Previous node outputs
3. Environment variables
4. Workflow context

### Example

```json
{
  "input": {
    "userId": "123",
    "apiToken": "abc123"
  },
  "nodes": [
    {
      "id": "fetch_user",
      "type": "http",
      "config": {
        "url": "https://api.example.com/users/${userId}",
        "headers": {
          "Authorization": "Bearer ${apiToken}"
        }
      }
    }
  ]
}
```

## Error Handling

All node types support error handling and retry mechanisms:

- **Automatic Retries**: Configurable retry logic for transient failures
- **Circuit Breaker**: Prevents cascading failures
- **Error Propagation**: Errors can be caught and handled by subsequent nodes
- **Logging**: All errors are logged with correlation IDs for debugging

## Performance Considerations

- **Caching**: Use Redis nodes for caching expensive operations
- **Parallel Execution**: Nodes without dependencies can run in parallel
- **Resource Limits**: Each node type has configurable resource limits
- **Monitoring**: All nodes emit metrics for performance monitoring

## Best Practices

1. **Use Parameterized Queries**: Always use parameterized queries in SQL nodes
2. **Cache Expensive Operations**: Use Redis nodes to cache API responses and computations
3. **Handle Errors Gracefully**: Configure appropriate retry policies
4. **Monitor Performance**: Use observability features to monitor node performance
5. **Secure Credentials**: Use data sources instead of hardcoding API keys
6. **Test Configurations**: Use the Admin API test endpoints to validate node configurations
