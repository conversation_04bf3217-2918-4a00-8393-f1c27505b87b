# Semantic Release Process

Tento dokument popisuje kompletní semantic release proces pro automatické verzování a deployment.

## P<PERSON>ehled procesu

Semantic release automaticky:
1. **Analyzuje commit messages** (conventional commits)
2. **Generuje novou verzi** podle typu změn
3. **Aktualizuje všechny soubory** s novou verzí
4. **Vytvoří CHANGELOG.md**
5. **Commitne změny** zpět do repository
6. **Vytvoří Git tag**
7. **Spustí Docker build** s novou verzí

## Konfigurace (.releaserc.json)

```json
{
  "branches": ["main"],
  "plugins": [
    "@semantic-release/commit-analyzer",
    "@semantic-release/changelog",
    [
      "@semantic-release/exec",
      {
        "prepareCmd": "echo ${nextRelease.version} > version && node scripts/update-helm-values.js ${nextRelease.version}"
      }
    ],
    [
      "@semantic-release/git",
      {
        "assets": [
          "CHANGELOG.md",
          "version",
          "package.json",
          "charts/ccczautomationmcpserver/values-dev.yaml",
          "charts/ccczautomationmcpserver/values-prod.yaml",
          "charts/ccczautomationmcpserver/Chart.yaml"
        ],
        "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"
      }
    ],
    "@semantic-release/gitlab"
  ]
}
```

## Update Script (scripts/update-helm-values.js)

Script automaticky aktualizuje:

### 1. Version soubor
```bash
echo "1.0.3" > version
```

### 2. Package.json
```json
{
  "version": "1.0.3"
}
```

### 3. Helm Chart values
```yaml
# values-dev.yaml & values-prod.yaml
image:
  tag: '1.0.3'

frontend:
  image:
    tag: '1.0.3'
```

### 4. Chart.yaml
```yaml
version: 1.0.3
appVersion: 1.0.3
```

## GitLab CI/CD Pipeline

### 1. Create Release Job
```yaml
create release:
  stage: deploy
  script:
    - yarn install --frozen-lockfile
    - yarn semantic-release
    - echo "Version bumped to $(<version)."
```

### 2. Docker Build Jobs
```yaml
# Backend
build backend docker image and push:
  before_script:
    - export CURRENT_VERSION_ID="$(<version)"
  script:
    - docker build -t ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID} .
    - docker push ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}

# Frontend  
build frontend docker image and push:
  before_script:
    - export CURRENT_VERSION_ID="$(<version)"
  script:
    - docker build -t ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID} ./frontend
    - docker push ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID}
```

## Conventional Commits

### Typy commitů a jejich vliv na verzi:

| Commit Type | Příklad | Version Bump |
|-------------|---------|--------------|
| `feat:` | `feat: add user authentication` | Minor (1.0.0 → 1.1.0) |
| `fix:` | `fix: resolve database connection` | Patch (1.0.0 → 1.0.1) |
| `feat!:` | `feat!: change API response format` | Major (1.0.0 → 2.0.0) |
| `BREAKING CHANGE:` | `feat: new API\n\nBREAKING CHANGE: API format changed` | Major (1.0.0 → 2.0.0) |
| `chore:`, `docs:`, `style:` | `chore: update dependencies` | Žádný bump |

### Příklady správných commit messages:

```bash
feat: add workflow execution history
fix: resolve frontend routing issue
feat!: redesign admin API endpoints
chore: update Docker base image
docs: add deployment guide
```

## Workflow

### 1. Development
```bash
# Vývoj s conventional commits
git commit -m "feat: add new feature"
git commit -m "fix: resolve bug"
git push origin feature-branch
```

### 2. Merge do main
```bash
# Pull request merge do main branch
git checkout main
git merge feature-branch
git push origin main
```

### 3. Automatický release
```bash
# GitLab CI/CD automaticky spustí:
1. create release job
   - Semantic release analyzuje commits
   - Generuje novou verzi (např. 1.0.3)
   - Aktualizuje všechny soubory
   - Commitne změny s [skip ci]
   - Vytvoří Git tag

2. Docker build jobs (manual trigger)
   - Čte verzi ze souboru "version"
   - Buildí backend: registry:1.0.3
   - Buildí frontend: registry:frontend-1.0.3
```

## Výsledek

Po úspěšném release:

### Git Repository
- ✅ Nový commit: `chore(release): 1.0.3 [skip ci]`
- ✅ Nový tag: `v1.0.3`
- ✅ Aktualizovaný CHANGELOG.md

### Soubory
- ✅ `version`: `1.0.3`
- ✅ `package.json`: `"version": "1.0.3"`
- ✅ `charts/*/values-*.yaml`: `tag: '1.0.3'`
- ✅ `charts/*/Chart.yaml`: `version: 1.0.3, appVersion: 1.0.3`

### Docker Registry
- ✅ `registry:1.0.3` (backend)
- ✅ `registry:frontend-1.0.3` (frontend)

## Troubleshooting

### Problém: Semantic release nefunguje
```bash
# Zkontrolujte:
1. Conventional commit format
2. Semantic release dependencies v package.json
3. GitLab permissions pro push
4. .releaserc.json konfigurace
```

### Problém: Verze se neaktualizuje
```bash
# Zkontrolujte:
1. scripts/update-helm-values.js existuje
2. Všechny soubory jsou v git assets
3. Regex patterns v update scriptu
```

### Problém: Docker build používá starou verzi
```bash
# Zkontrolujte:
1. version soubor existuje
2. CURRENT_VERSION_ID="$(<version)" v before_script
3. Docker build depends na create release job
```

## Monitoring

Sledujte v GitLab:
- **CI/CD Pipelines**: Úspěšnost release procesu
- **Repository Tags**: Nové verze
- **Container Registry**: Nové Docker images
- **Commits**: Automatické release commity
