# Standard MCP SDK Implementation

## Overview

The Dynamic MCP Server has been refactored to use the **standard MCP SDK approach** with proper `setRequestHandler` methods. This provides automatic support for `tools/call` and `tools/list` requests through the official MCP protocol implementation.

## Implementation Details

### What Changed

The `MCPServerImpl.ts` has been completely refactored to use the standard MCP SDK patterns:

1. **Replaced CustomMcpServer with standard Server class** from `@modelcontextprotocol/sdk/server/index.js`
2. **Added proper request handlers** using `setRequestHandler(ListToolsRequestSchema, ...)` and `setRequestHandler(CallToolRequestSchema, ...)`
3. **Removed manual HTTP interception** - the SDK now handles protocol requests automatically
4. **Eliminated custom transport handling** - using standard SDK transport patterns

This provides:

1. **Automatic protocol handling** - SDK manages all MCP protocol details
2. **Standard compliance** - follows official MCP specification exactly
3. **Simplified architecture** - no custom transport or routing logic needed
4. **Better maintainability** - uses well-tested SDK patterns
5. **Future-proof** - automatically gets SDK updates and improvements

### Request Format

MCP clients can now send `tools/call` requests in the following format:

```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "toolName",
    "arguments": {
      "param1": "value1",
      "param2": "value2"
    }
  },
  "id": 1
}
```

### Response Format

Successful tool execution returns:

```json
{
  "jsonrpc": "2.0",
  "result": {
    // Tool execution result (varies by tool)
  },
  "id": 1
}
```

Error responses follow MCP protocol standards:

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32601,
    "message": "Tool not found: nonexistentTool"
  },
  "id": 1
}
```

## Error Codes

The implementation uses standard JSON-RPC error codes:

- `-32602`: Invalid params (missing tool name)
- `-32601`: Method not found (tool not found)
- `-32603`: Internal error (tool execution error)

## Usage Examples

### Using the MCP SDK

```typescript
import { MCPClient } from './examples/test-call-tool';

const client = new MCPClient('http://localhost:3000/mcp');

// Call a tool
const result = await client.callTool('echo', {
  message: 'Hello, World!'
});

console.log('Tool result:', result);
```

### Direct HTTP Request

```typescript
const response = await fetch('http://localhost:3000/mcp', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    jsonrpc: '2.0',
    method: 'tools/call',
    params: {
      name: 'getDateTime',
      arguments: { format: 'ISO' }
    },
    id: 1
  })
});

const data = await response.json();
```

## Testing

A test script is provided at `examples/test-call-tool.ts` that demonstrates:

1. Listing available tools
2. Calling tools with the new `callTool` method
3. Error handling for invalid tool calls

To run the test:

```bash
npx ts-node examples/test-call-tool.ts
```

## Backward Compatibility

This enhancement is fully backward compatible:

- Existing `tools/list` functionality remains unchanged
- Session-based MCP protocol requests still work through the MessageRouter
- No breaking changes to existing tool implementations

## Benefits

1. **Simplified Integration**: MCP clients can use standard SDK methods without complex session management
2. **Better Performance**: Direct tool execution without transport overhead
3. **Improved Debugging**: Enhanced logging for tool calls
4. **Standard Compliance**: Follows MCP protocol specifications exactly
5. **Error Handling**: Proper MCP error codes and messages

## Architecture

The new implementation uses the standard MCP SDK architecture:

```
MCP Client Request
       ↓
StreamableHTTPServerTransport
       ↓
Standard MCP Server (SDK)
       ↓
setRequestHandler(ListToolsRequestSchema) → Returns tools list
setRequestHandler(CallToolRequestSchema)  → Executes tools
```

This approach:
- **Eliminates custom routing** - SDK handles all protocol details
- **Uses standard patterns** - follows official MCP SDK examples
- **Simplifies maintenance** - no custom transport or session management
- **Ensures compatibility** - works with all standard MCP clients
