# CallTool Support Documentation

## Overview

The Dynamic MCP Server now supports the standard `callTool` method, allowing MCP clients to execute tools using the MCP protocol's `tools/call` method. This enhancement provides better compatibility with MCP SDK clients and follows the standard MCP protocol specification.

## Implementation Details

### What Changed

The `DynamicMcpServer.ts` has been enhanced to handle `tools/call` requests directly, similar to how it already handles `tools/list` requests. This provides:

1. **Direct tool execution** without requiring complex session management
2. **Standard MCP protocol compliance** for tool calls
3. **Better error handling** with proper MCP error codes
4. **Improved logging** for debugging tool execution

### Request Format

MCP clients can now send `tools/call` requests in the following format:

```json
{
  "jsonrpc": "2.0",
  "method": "tools/call",
  "params": {
    "name": "toolName",
    "arguments": {
      "param1": "value1",
      "param2": "value2"
    }
  },
  "id": 1
}
```

### Response Format

Successful tool execution returns:

```json
{
  "jsonrpc": "2.0",
  "result": {
    // Tool execution result (varies by tool)
  },
  "id": 1
}
```

Error responses follow MCP protocol standards:

```json
{
  "jsonrpc": "2.0",
  "error": {
    "code": -32601,
    "message": "Tool not found: nonexistentTool"
  },
  "id": 1
}
```

## Error Codes

The implementation uses standard JSON-RPC error codes:

- `-32602`: Invalid params (missing tool name)
- `-32601`: Method not found (tool not found)
- `-32603`: Internal error (tool execution error)

## Usage Examples

### Using the MCP SDK

```typescript
import { MCPClient } from './examples/test-call-tool';

const client = new MCPClient('http://localhost:3000/mcp');

// Call a tool
const result = await client.callTool('echo', {
  message: 'Hello, World!'
});

console.log('Tool result:', result);
```

### Direct HTTP Request

```typescript
const response = await fetch('http://localhost:3000/mcp', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    jsonrpc: '2.0',
    method: 'tools/call',
    params: {
      name: 'getDateTime',
      arguments: { format: 'ISO' }
    },
    id: 1
  })
});

const data = await response.json();
```

## Testing

A test script is provided at `examples/test-call-tool.ts` that demonstrates:

1. Listing available tools
2. Calling tools with the new `callTool` method
3. Error handling for invalid tool calls

To run the test:

```bash
npx ts-node examples/test-call-tool.ts
```

## Backward Compatibility

This enhancement is fully backward compatible:

- Existing `tools/list` functionality remains unchanged
- Session-based MCP protocol requests still work through the MessageRouter
- No breaking changes to existing tool implementations

## Benefits

1. **Simplified Integration**: MCP clients can use standard SDK methods without complex session management
2. **Better Performance**: Direct tool execution without transport overhead
3. **Improved Debugging**: Enhanced logging for tool calls
4. **Standard Compliance**: Follows MCP protocol specifications exactly
5. **Error Handling**: Proper MCP error codes and messages

## Architecture

The implementation adds a new branch in the `/mcp` POST handler:

```
POST /mcp
├── tools/list → Direct tools listing
├── tools/call → Direct tool execution (NEW)
└── other → MessageRouter handling
```

This approach maintains the existing architecture while adding the new functionality in a clean, maintainable way.
