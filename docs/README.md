# Dynamic MCP Server - Complete Documentation

A powerful, enterprise-grade Model Context Protocol (MCP) server implementation with workflow automation, hot-reloading capabilities, and comprehensive admin API.

## 🚀 Features

### Core Capabilities
- **Dynamic Workflow Engine**: Create complex, multi-step workflows using visual node-based configuration
- **Hot Configuration Reload**: Update workflows, nodes, and data sources without server restart
- **Multiple Node Types**: JavaScript, HTTP, SQL, Redis, and LiteLLM nodes for diverse use cases
- **Admin API**: Comprehensive REST API for managing all server components
- **Real-time Observability**: Built-in metrics, logging, and monitoring with Prometheus integration

### Enterprise Features
- **Redis Workflow Memory**: Persistent workflow state with distributed execution support
- **Advanced Error Handling**: Retry mechanisms, circuit breakers, and comprehensive error recovery
- **Security**: Encrypted data source credentials and secure JavaScript execution sandbox
- **Scalability**: Horizontal scaling support with Redis-based state management
- **Testing**: Comprehensive unit and integration test coverage

### Transport Support
- **HTTP Transport**: RESTful API for web-based integrations
- **Stdio Transport**: Standard MCP protocol support for direct integrations

## 📋 Prerequisites

- **Node.js**: Version 22 LTS or higher
- **PostgreSQL**: Version 12+ (for workflow and configuration storage)
- **Redis**: Version 6+ (for workflow memory and caching)
- **Yarn**: Package manager

## 🛠️ Installation

### 1. Clone and Install

```bash
git clone <repository-url>
cd dynamic-mcp-server
yarn install
```

### 2. Database Setup

#### PostgreSQL
```bash
# Using Docker
docker run --name postgres-mcp \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=mcp_server \
  -p 5432:5432 \
  -d postgres:15

# Or install locally and create database
createdb mcp_server
```

#### Redis
```bash
# Using Docker
docker run --name redis-mcp \
  -p 6379:6379 \
  -d redis:7-alpine

# Or install locally
redis-server
```

### 3. Environment Configuration

Create a `.env` file:

```env
# Server Configuration
PORT=3000
API_PORT=3001
LOG_LEVEL=info
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_DATABASE=mcp_server
DB_MIGRATIONS_RUN=true

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
ENCRYPTION_KEY=your-32-character-encryption-key-here
ENCRYPTION_IV=your-16-char-iv

# MCP Configuration
MCP_SERVER_NAME=dynamic-mcp-server
MCP_SERVER_VERSION=1.0.0
```

### 4. Database Migration

```bash
# Run database migrations
yarn run build
yarn run migrate
```

## 🚀 Quick Start

### Start the Server

```bash
# Development mode with hot reload
yarn run dev

# Production mode
yarn run build
yarn start
```

The server will start with:
- **MCP Server**: Available via stdio transport
- **Admin API**: http://localhost:3001/admin
- **Health Check**: http://localhost:3001/health

### Test with MCP Inspector

```bash
# In another terminal
npx @modelcontextprotocol/inspector node ./dist/server/index.js
```

### Create Your First Workflow

Using the Admin API:

```bash
curl -X POST http://localhost:3001/admin/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Hello World Workflow",
    "description": "A simple greeting workflow",
    "input_schema": {
      "type": "object",
      "properties": {
        "name": { "type": "string" }
      },
      "required": ["name"]
    },
    "nodes_config": {
      "nodes": [
        {
          "id": "greet",
          "type": "javascript",
          "name": "Generate Greeting",
          "config": {
            "script": "return { greeting: `Hello, ${input.name}!` };"
          }
        }
      ],
      "edges": []
    }
  }'
```

### Test the Workflow

```bash
curl -X POST http://localhost:3001/admin/workflows/test \
  -H "Content-Type: application/json" \
  -d '{
    "workflow": {
      "name": "Test Workflow",
      "input_schema": {
        "type": "object",
        "properties": { "name": { "type": "string" } }
      },
      "nodes_config": {
        "nodes": [
          {
            "id": "greet",
            "type": "javascript",
            "name": "Greet",
            "config": {
              "script": "return { greeting: `Hello, ${input.name}!` };"
            }
          }
        ],
        "edges": []
      }
    },
    "input": { "name": "World" }
  }'
```

## 📚 Documentation

### Core Documentation
- **[Node Types](node-types/README.md)**: Complete guide to all available node types
- **[Workflow Engine](workflow-engine/README.md)**: Workflow design and execution
- **[Admin API](api/README.md)**: REST API reference

### Node Type Guides
- **[JavaScript Node](node-types/javascript-node.md)**: Custom JavaScript execution
- **HTTP Node**: REST API integrations
- **SQL Node**: Database operations
- **Redis Node**: Caching and data storage
- **LiteLLM Node**: AI/LLM integrations

### Advanced Topics
- **Hot-Reload Mechanism**: Dynamic configuration updates
- **Error Handling**: Retry policies and circuit breakers
- **Observability**: Metrics, logging, and monitoring
- **Security**: Data encryption and access control

## 🏗️ Architecture

### System Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin API     │    │  MCP Server     │    │  Workflow       │
│   (Express.js)  │    │  (stdio/http)   │    │  Engine         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
         │   PostgreSQL    │    │     Redis       │    │  Node Registry  │
         │  (Config/State) │    │  (Memory/Cache) │    │  (Processors)   │
         └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components

- **Workflow Engine**: Executes multi-step workflows with parallel processing
- **Node Registry**: Manages different node types (JavaScript, HTTP, SQL, Redis, LiteLLM)
- **Admin API**: REST API for configuration management
- **Hot-Reload System**: PostgreSQL LISTEN/NOTIFY for real-time updates
- **Observability Manager**: Metrics collection and monitoring
- **Redis Memory**: Distributed workflow state management

## 🧪 Testing

### Run Tests

```bash
# Unit tests
yarn test

# Integration tests
yarn test:integration

# Test coverage
yarn test:coverage

# Watch mode
yarn test:watch
```

### Test Structure

```
test/
├── unit/                 # Unit tests
│   ├── validators/       # Validator tests
│   ├── controllers/      # Controller tests
│   └── config/          # Configuration tests
├── integration/         # Integration tests
│   └── api/            # API endpoint tests
└── setup/              # Test utilities and data
```

## 📊 Monitoring and Observability

### Metrics

The server exposes Prometheus metrics at `/metrics`:

```bash
curl http://localhost:3001/metrics
```

Key metrics include:
- Workflow execution counts and durations
- Node performance metrics
- Error rates and types
- System resource usage

### Logging

Structured JSON logging with correlation IDs:

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "info",
  "message": "Workflow execution completed",
  "executionId": "exec_123",
  "workflowId": "workflow_456",
  "duration": 2500,
  "status": "success"
}
```

### Health Checks

```bash
# Basic health check
curl http://localhost:3001/health

# Detailed health check
curl http://localhost:3001/health/detailed
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PORT` | MCP server port | `3000` | No |
| `API_PORT` | Admin API port | `3001` | No |
| `DB_HOST` | PostgreSQL host | `localhost` | Yes |
| `DB_PORT` | PostgreSQL port | `5432` | No |
| `DB_USERNAME` | Database username | `postgres` | Yes |
| `DB_PASSWORD` | Database password | - | Yes |
| `DB_DATABASE` | Database name | `mcp_server` | Yes |
| `REDIS_HOST` | Redis host | `localhost` | Yes |
| `REDIS_PORT` | Redis port | `6379` | No |
| `ENCRYPTION_KEY` | Data encryption key | - | Yes |
| `LOG_LEVEL` | Logging level | `info` | No |

## 🤝 Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Install dependencies: `yarn install`
4. Set up development environment (PostgreSQL, Redis)
5. Run tests: `yarn test`
6. Make your changes
7. Add tests for new functionality
8. Ensure all tests pass
9. Submit a pull request

### Code Standards

- **TypeScript**: Strict type checking enabled
- **ESLint**: Code linting with Prettier formatting
- **Testing**: Minimum 80% test coverage required
- **Documentation**: Update docs for new features
- **Commits**: Conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
