# Environment Variables Configuration

Tento dokument popisuje všechny environment variables používané v aplikaci a jejich konfiguraci v Helm charts.

## Přehled

Environment variables jsou konfigurovány na třech úrovních:

1. **Lokální development** - `.env.development` soubor
2. **Kubernetes development** - `charts/ccczautomationmcpserver/values-dev.yaml`
3. **Kubernetes production** - `charts/ccczautomationmcpserver/values-prod.yaml`

## Backend Environment Variables

### Server Configuration

| Variable     | Description     | Dev Default | Prod Default |
| ------------ | --------------- | ----------- | ------------ |
| `NODE_ENV`   | Environment     | development | production   |
| `PORT`       | MCP server port | 7000        | 3000         |
| `HOST`       | Server host     | 0.0.0.0     | 0.0.0.0      |
| `API_PORT`   | Admin API port  | 7001        | 7001         |
| `ADMIN_PORT` | Admin UI port   | 7002        | 7002         |
| `LOG_LEVEL`  | Logging level   | debug       | info         |

### Database Configuration

| Variable            | Description       | Dev Default | Prod Default                       |
| ------------------- | ----------------- | ----------- | ---------------------------------- |
| `DB_HOST`           | PostgreSQL host   | localhost   | ccczautomationmcpserver-postgresql |
| `DB_PORT`           | PostgreSQL port   | 5432        | 5432                               |
| `DB_USERNAME`       | Database username | postgres    | postgres                           |
| `DB_PASSWORD`       | Database password | password    | password                           |
| `DB_DATABASE`       | Database name     | mcp_server  | mcp_server                         |
| `DB_MIGRATIONS_RUN` | Run migrations    | true        | false                              |

### Redis Configuration

| Variable         | Description    | Dev Default | Prod Default                  |
| ---------------- | -------------- | ----------- | ----------------------------- |
| `REDIS_HOST`     | Redis host     | localhost   | ccczautomationmcpserver-redis |
| `REDIS_PORT`     | Redis port     | 6379        | 6379                          |
| `REDIS_PASSWORD` | Redis password | (empty)     | redis123                      |

### Workflow Configuration

| Variable                    | Description                    | Dev Default | Prod Default |
| --------------------------- | ------------------------------ | ----------- | ------------ |
| `USE_REDIS_WORKFLOW_MEMORY` | Use Redis for workflow memory  | false       | true         |
| `WORKFLOW_CONTEXT_TTL`      | Workflow context TTL (seconds) | 3600        | 3600         |

### Data Management

| Variable                        | Description           | Dev Default | Prod Default |
| ------------------------------- | --------------------- | ----------- | ------------ |
| `DATA_RETENTION_DAYS`           | Data retention period | 5           | 30           |
| `DATA_CLEANUP_INTERVAL_MINUTES` | Cleanup interval      | 5           | 60           |
| `DATA_CLEANUP_BATCH_SIZE`       | Cleanup batch size    | 1000        | 1000         |

### Authentication

| Variable         | Description         | Dev Default                          | Prod Default                      |
| ---------------- | ------------------- | ------------------------------------ | --------------------------------- |
| `JWT_SECRET`     | JWT secret key      | your-secret-key-change-in-production | production-secret-key-change-this |
| `JWT_EXPIRES_IN` | JWT expiration time | 24h                                  | 24h                               |

### LiteLLM Integration

| Variable           | Description      | Dev Default           | Prod Default          |
| ------------------ | ---------------- | --------------------- | --------------------- |
| `LITELLM_API_KEY`  | LiteLLM API key  | (empty)               | (empty)               |
| `LITELLM_BASE_URL` | LiteLLM base URL | http://localhost:8000 | http://localhost:8000 |

## Frontend Environment Variables

| Variable            | Description                        | Dev Default           | Prod Default |
| ------------------- | ---------------------------------- | --------------------- | ------------ |
| `NODE_ENV`          | Environment                        | development           | production   |
| `VITE_API_URL`      | Backend API URL for axios client   | /api                  | /api         |
| `VITE_API_BASE_URL` | Backend API base URL for admin API | http://localhost:7002 | /api         |

## Kubernetes Service Names

### Development Environment

- **Backend**: `ccczautomationmcpserver-dev-mcp`
- **Frontend**: `ccczautomationmcpserver-dev-frontend`
- **PostgreSQL**: `ccczautomationmcpserver-dev-postgresql`
- **Redis**: `ccczautomationmcpserver-dev-redis`

### Production Environment

- **Backend**: `ccczautomationmcpserver-mcp`
- **Frontend**: `ccczautomationmcpserver-frontend`
- **PostgreSQL**: `ccczautomationmcpserver-postgresql`
- **Redis**: `ccczautomationmcpserver-redis`

## Konfigurace v Helm Charts

### Backend ConfigMap

Environment variables jsou konfigurovány v `templates/ConfigMap.env.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-env
data:
  {{- range $key, $val := .Values.environment }}
  {{ $key }}: {{ $val | quote }}
  {{- end }}
```

### Frontend ConfigMap

Frontend environment variables jsou v `templates/frontend-configmap.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Release.Name }}-frontend-env
data:
  REACT_APP_API_URL: "{{ .Values.frontend.apiUrl }}"
  REACT_APP_WS_URL: "{{ .Values.frontend.wsUrl }}"
  {{- range $key, $value := .Values.frontend.environment }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
```

## Bezpečnostní poznámky

### Development

- Používá slabé hesla pro rychlý setup
- Debug logging je zapnutý
- Migrace se spouštějí automaticky

### Production

- **ZMĚŇTE** všechny default hesla a secret keys
- Info logging pro lepší performance
- Migrace se spouštějí manuálně
- Redis workflow memory pro lepší škálovatelnost
- Delší data retention pro audit

### Doporučení

1. Použijte Kubernetes Secrets pro citlivé údaje
2. Rotujte JWT secret keys pravidelně
3. Nastavte silná hesla pro databázi a Redis
4. Monitorujte přístup k admin API
5. Použijte TLS pro všechny komunikace
