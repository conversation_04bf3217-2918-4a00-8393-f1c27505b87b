# REST API Trigger Data Access Guide

## Problem Analysis

When using the REST API trigger node with POST requests, the input data `{"message":"ahojky"}` was not reaching the JavaScript action nodes because of incorrect data access patterns.

## Root Cause

The [`RestAPITriggerNode`](../src/workflow/nodes/trigger/RestAPITriggerNode.ts) wraps incoming HTTP request data in a structured format rather than passing it directly. This is the actual data structure passed to subsequent nodes:

```javascript
{
  trigger: 'rest-api',
  request: {
    method: 'POST',
    path: '/api/process-message',
    headers: { ... },
    query: { ... },
    body: { message: 'ahojky' },  // ← POST data is nested here
    params: { ... }
  },
  timestamp: '2025-01-03T15:18:00.000Z',
  workflowId: 'message-processor-workflow',
  executionId: '...'
}
```

## Solution

### 1. Correct Data Access Pattern

Instead of directly accessing `input.message`, use the proper nested path:

```javascript
// ❌ INCORRECT - Direct access
const { message } = input;

// ✅ CORRECT - Nested access
const message = input.request?.body?.message;
```

### 2. Updated JavaScript Action Script

The corrected script with proper error handling:

```javascript
// Extract the message from REST API trigger input
// The POST data is nested in input.request.body
const message = input.request?.body?.message;

// Validate input
if (!message || typeof message !== 'string') {
  throw new Error(
    'Invalid input: message must be a non-empty string. Received input structure: ' +
      JSON.stringify(input, null, 2)
  );
}

// Store as rawMessage variable in workflow context
workflowContext.variables.rawMessage = message;

console.log('Stored raw message:', message);
console.log('Full input structure:', JSON.stringify(input, null, 2));

// Return the message for next node
return {
  rawMessage: message
};
```

## Data Flow Analysis

### 1. HTTP Request

```http
POST /api/process-message
Content-Type: application/json

{"message":"ahojky"}
```

### 2. REST API Trigger Output

```javascript
{
  trigger: 'rest-api',
  request: {
    body: {"message":"ahojky"}  // POST data here
  }
  // ... other metadata
}
```

### 3. JavaScript Action Input

The JavaScript action receives the full trigger output, so access POST data via:

- `input.request.body.message` for the message field
- `input.request.body` for the entire POST payload
- `input.request.headers` for HTTP headers
- `input.request.query` for query parameters

## Best Practices

### 1. Always Use Optional Chaining

```javascript
const message = input.request?.body?.message;
```

### 2. Validate Input Structure

```javascript
if (!input.request?.body) {
  throw new Error('No request body found in input');
}
```

### 3. Log Input Structure for Debugging

```javascript
console.log('Full input structure:', JSON.stringify(input, null, 2));
```

### 4. Handle Different HTTP Methods

```javascript
// For GET requests, data might be in query parameters
const data = input.request?.method === 'GET' ? input.request.query : input.request.body;
```

## Testing

Use the test script [`scripts/test-rest-api-trigger.ts`](../scripts/test-rest-api-trigger.ts) to verify data extraction logic:

```bash
npx ts-node scripts/test-rest-api-trigger.ts
```

## Common Pitfalls

1. **Direct Access**: Trying to access `input.message` directly
2. **Missing Validation**: Not checking if `input.request.body` exists
3. **No Error Handling**: Not providing helpful error messages when data is missing
4. **Hardcoded Paths**: Not using optional chaining for nested properties

## Implementation Details

The REST API trigger node is implemented in [`src/workflow/nodes/trigger/RestAPITriggerNode.ts`](../src/workflow/nodes/trigger/RestAPITriggerNode.ts) and follows this pattern:

1. Receives HTTP request data through the workflow engine
2. Validates the request against the configured schema
3. Wraps the request data in a structured format
4. Passes the structured data to the next node in the workflow

This design provides consistency and enables nodes to access not just the request body, but also headers, query parameters, and other HTTP metadata when needed.
