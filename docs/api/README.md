# Admin API Documentation

The Dynamic MCP Server provides a comprehensive REST API for managing workflows, node configurations, and data sources.

## Base URL

```
http://localhost:3001/admin
```

## Authentication

Currently, the API does not require authentication. In production environments, you should implement appropriate authentication and authorization mechanisms.

## Content Type

All API requests and responses use JSON format:

```
Content-Type: application/json
```

## Error Responses

All API endpoints return consistent error responses:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "specific_field",
      "errors": [
        {
          "field": "field_name",
          "message": "Field-specific error message",
          "code": "VALIDATION_ERROR",
          "value": "invalid_value"
        }
      ]
    }
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `NOT_FOUND`: Resource not found
- `CONFLICT`: Resource already exists
- `INTERNAL_ERROR`: Server error
- `INVALID_FORMAT`: Invalid data format
- `REQUIRED_FIELD`: Required field missing

## API Endpoints

### Workflows

#### List All Workflows

```http
GET /admin/workflows
```

**Response:**

```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "User Registration Workflow",
    "description": "Handles new user registration process",
    "enabled": true,
    "version": 1,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
]
```

#### Get Workflow by ID

```http
GET /admin/workflows/{id}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "User Registration Workflow",
  "description": "Handles new user registration process",
  "input_schema": {
    "type": "object",
    "properties": {
      "email": { "type": "string", "format": "email" },
      "name": { "type": "string" }
    },
    "required": ["email", "name"]
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "userId": { "type": "string" },
      "success": { "type": "boolean" }
    }
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "validate",
        "type": "javascript",
        "name": "Validate Input",
        "config": {
          "script": "return { valid: !!input.email && !!input.name };"
        }
      }
    ],
    "edges": []
  },
  "retry_config": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "observability_config": {
    "logLevel": "info",
    "metricsEnabled": true
  },
  "enabled": true,
  "version": 1,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

#### Create Workflow

```http
POST /admin/workflows
```

**Request Body:**

```json
{
  "name": "New Workflow",
  "description": "A new workflow for testing",
  "input_schema": {
    "type": "object",
    "properties": {
      "message": { "type": "string" }
    },
    "required": ["message"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "process",
        "type": "javascript",
        "name": "Process Message",
        "config": {
          "script": "return { result: input.message.toUpperCase() };"
        }
      }
    ],
    "edges": []
  }
}
```

**Response:** `201 Created`

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "name": "New Workflow",
  "description": "A new workflow for testing",
  "enabled": true,
  "version": 1,
  "created_at": "2024-01-01T12:05:00Z",
  "updated_at": "2024-01-01T12:05:00Z"
}
```

#### Update Workflow

```http
PUT /admin/workflows/{id}
```

**Request Body:**

```json
{
  "description": "Updated workflow description",
  "enabled": false
}
```

**Response:** `200 OK`

#### Delete Workflow

```http
DELETE /admin/workflows/{id}
```

**Response:** `204 No Content`

#### Test Workflow

```http
POST /admin/workflows/test
```

**Request Body:**

```json
{
  "workflow": {
    "name": "Test Workflow",
    "input_schema": {
      "type": "object",
      "properties": {
        "message": { "type": "string" }
      }
    },
    "nodes_config": {
      "nodes": [
        {
          "id": "transform",
          "type": "javascript",
          "name": "Transform",
          "config": {
            "script": "return { result: input.message.toUpperCase() };"
          }
        }
      ],
      "edges": []
    }
  },
  "input": {
    "message": "hello world"
  }
}
```

**Response:** `200 OK`

```json
{
  "success": true,
  "result": {
    "result": "HELLO WORLD"
  }
}
```

#### Get Workflow Execution History

```http
GET /admin/workflows/{id}/executions
```

**Query Parameters:**

- `limit` (optional): Number of executions to return (default: 50)
- `offset` (optional): Offset for pagination (default: 0)

**Response:**

```json
[
  {
    "id": "exec_123",
    "workflowId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "input": { "message": "test" },
    "output": { "result": "TEST" },
    "startTime": "2024-01-01T12:00:00Z",
    "endTime": "2024-01-01T12:00:02Z",
    "duration": 2000
  }
]
```

#### Get Workflow Metrics

```http
GET /admin/workflows/{id}/metrics
```

**Query Parameters:**

- `startTime` (optional): Start time for metrics (ISO 8601)
- `endTime` (optional): End time for metrics (ISO 8601)

**Response:**

```json
{
  "totalExecutions": 150,
  "successfulExecutions": 145,
  "failedExecutions": 5,
  "averageExecutionTime": 2500,
  "successRate": 96.67,
  "errorRate": 3.33,
  "lastExecution": "2024-01-01T12:00:00Z"
}
```

### Node Configurations

#### List All Node Configurations

```http
GET /admin/nodes
```

**Response:**

```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440002",
    "name": "Email Validator",
    "description": "Validates email addresses",
    "node_type": "javascript",
    "enabled": true,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
]
```

#### Get Node Configuration by ID

```http
GET /admin/nodes/{id}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440002",
  "name": "Email Validator",
  "description": "Validates email addresses",
  "node_type": "javascript",
  "config": {
    "script": "const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/; return { valid: emailRegex.test(input.email) };"
  },
  "enabled": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

#### Create Node Configuration

```http
POST /admin/nodes
```

**Request Body:**

```json
{
  "name": "String Processor",
  "description": "Processes string inputs",
  "node_type": "javascript",
  "config": {
    "script": "return { uppercase: input.text.toUpperCase(), lowercase: input.text.toLowerCase() };"
  }
}
```

**Response:** `201 Created`

#### Update Node Configuration

```http
PUT /admin/nodes/{id}
```

#### Delete Node Configuration

```http
DELETE /admin/nodes/{id}
```

#### Test Node Configuration

```http
POST /admin/nodes/test
```

**Request Body:**

```json
{
  "node": {
    "name": "Test Node",
    "node_type": "javascript",
    "config": {
      "script": "return { result: input.value * 2 };"
    }
  },
  "input": {
    "value": 5
  }
}
```

**Response:**

```json
{
  "success": true,
  "result": {
    "result": 10
  }
}
```

#### Get Available Node Types

```http
GET /admin/nodes/types
```

**Response:**

```json
[
  "javascript",
  "http",
  "sql",
  "redis",
  "litellm"
]
```

### Data Sources

#### List All Data Sources

```http
GET /admin/datasources
```

**Response:**

```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440003",
    "name": "postgres-main",
    "description": "Main PostgreSQL database",
    "type": "postgresql",
    "enabled": true,
    "created_at": "2024-01-01T12:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  }
]
```

#### Get Data Source by ID

```http
GET /admin/datasources/{id}
```

**Response:**

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440003",
  "name": "postgres-main",
  "description": "Main PostgreSQL database",
  "type": "postgresql",
  "connection_config": {
    "host": "localhost",
    "port": 5432,
    "database": "myapp",
    "username": "user"
  },
  "enabled": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

#### Create Data Source

```http
POST /admin/datasources
```

**Request Body:**

```json
{
  "name": "redis-cache",
  "description": "Redis cache server",
  "type": "redis",
  "connection_config": {
    "host": "localhost",
    "port": 6379,
    "password": "redis_password"
  }
}
```

**Response:** `201 Created`

#### Update Data Source

```http
PUT /admin/datasources/{id}
```

#### Delete Data Source

```http
DELETE /admin/datasources/{id}
```

#### Test Data Source Connection

```http
POST /admin/datasources/test
```

**Request Body:**

```json
{
  "dataSource": {
    "name": "test-postgres",
    "type": "postgresql",
    "connection_config": {
      "host": "localhost",
      "port": 5432,
      "database": "testdb",
      "username": "testuser",
      "password": "testpass"
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Connection test successful"
}
```

#### Get Available Data Source Types

```http
GET /admin/datasources/types
```

**Response:**

```json
[
  "postgresql",
  "mysql",
  "redis",
  "rest",
  "litellm"
]
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Rate Limit**: 100 requests per minute per IP
- **Headers**: Rate limit information is included in response headers

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Pagination

List endpoints support pagination:

**Query Parameters:**

- `limit`: Number of items per page (default: 50, max: 100)
- `offset`: Number of items to skip (default: 0)

**Response Headers:**

```http
X-Total-Count: 150
X-Page-Count: 3
Link: <http://localhost:3001/admin/workflows?limit=50&offset=50>; rel="next"
```

## Filtering and Sorting

List endpoints support filtering and sorting:

**Query Parameters:**

- `filter[field]`: Filter by field value
- `sort`: Sort field (prefix with `-` for descending)

**Examples:**

```http
GET /admin/workflows?filter[enabled]=true&sort=-created_at
GET /admin/nodes?filter[node_type]=javascript&sort=name
```
