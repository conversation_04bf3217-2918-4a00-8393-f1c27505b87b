# Node Reference Quick Guide

A concise reference for all available workflow nodes with their essential configuration properties.

## Trigger Nodes

### [`rest-api-trigger`](./manual-workflow-creation.md#rest-api-trigger)

Exposes workflow as REST API endpoint

```json
{
  "method": "POST|GET|PUT|DELETE|PATCH",
  "path": "/api/endpoint",
  "authentication": true|false,
  "validation": true|false
}
```

### [`mcp-function-trigger`](./manual-workflow-creation.md#mcp-function-trigger)

Triggers via MCP function calls

```json
{
  "functionName": "processData",
  "description": "Function description"
}
```

### [`timer-trigger`](./manual-workflow-creation.md#timer-trigger)

Time-based scheduling

```json
{
  "schedule": "0 */5 * * * *",
  "timezone": "UTC",
  "enabled": true
}
```

### [`webhook-trigger`](./manual-workflow-creation.md#webhook-trigger)

Webhook endpoints

```json
{
  "path": "/webhook/service",
  "method": "POST",
  "secretValidation": true
}
```

## Action Nodes

### [`javascript-action`](./manual-workflow-creation.md#javascript-action)

Custom JavaScript execution

```json
{
  "script": "const result = input.data * 2; return { doubled: result };",
  "timeout": 30000,
  "enableConsole": true,
  "allowAsync": true
}
```

**Available Context:**

- `input` - Input data (direct access)
- `workflowContext.variables` - Workflow variables
- `workflowContext.nodeResults` - Previous node results
- `context.workflowId`, `context.executionId`

### [`http-action`](./manual-workflow-creation.md#http-action)

HTTP requests to external APIs

```json
{
  "url": "https://api.example.com/data",
  "method": "POST",
  "headers": { "Authorization": "Bearer ${token}" },
  "body": { "data": "${input.payload}" },
  "timeout": 30000,
  "retryCount": 3
}
```

**Variable Substitution:**

- `${input.field}` - From input data
- `${variables.name}` - From context variables
- `${nodeResults.nodeId.result}` - From previous nodes

### [`sql-action`](./manual-workflow-creation.md#sql-action)

Database operations

```json
{
  "query": "SELECT * FROM users WHERE id = $1",
  "parameters": ["${input.userId}"],
  "queryType": "SELECT|INSERT|UPDATE|DELETE",
  "timeout": 30000
}
```

### [`redis-action`](./manual-workflow-creation.md#redis-action)

Redis operations

```json
{
  "operation": "SET|GET|DEL|HSET|HGET",
  "key": "${input.cacheKey}",
  "value": "${input.data}",
  "expiration": 3600
}
```

### [`litellm-action`](./manual-workflow-creation.md#litellm-action)

LLM integrations

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [{ "role": "user", "content": "Process: ${input.text}" }],
  "temperature": 0.7,
  "maxTokens": 1000
}
```

## Logic Nodes

### [`if-else-logic`](./manual-workflow-creation.md#if-else-logic)

Conditional branching

```json
{
  "condition": "input.status == 'active'",
  "trueOutput": { "result": "activated" },
  "falseOutput": { "result": "inactive" },
  "conditionType": "simple|expression|javascript"
}
```

**Condition Types:**

- **Simple**: `input.value > 10`, `status == 'active'`
- **Expression**: `input.price * 1.2 > variables.maxPrice`
- **JavaScript**: `input.items.some(item => item.category === 'premium')`

**Utility Functions:**

- `utils.isNull()`, `utils.isEmpty()`, `utils.isArray()`
- `utils.length()`, `utils.includes()`, `utils.startsWith()`
- `utils.regex(str, pattern, flags)`

### [`switch-logic`](./manual-workflow-creation.md#switch-logic)

Multi-branch switching

```json
{
  "switchValue": "${input.type}",
  "cases": {
    "user": { "action": "process_user" },
    "order": { "action": "process_order" }
  },
  "defaultCase": { "action": "process_default" }
}
```

### [`merge-logic`](./manual-workflow-creation.md#merge-logic)

Data merging

```json
{
  "mergeStrategy": "deep|shallow",
  "sources": ["${nodeResults.node1}", "${nodeResults.node2}"],
  "conflictResolution": "last_wins|first_wins"
}
```

### [`loop-logic`](./manual-workflow-creation.md#loop-logic)

Iterative processing

```json
{
  "iterable": "${input.items}",
  "itemVariable": "currentItem",
  "indexVariable": "currentIndex",
  "maxIterations": 100,
  "collectResults": true
}
```

## Terminator Nodes

### [`response-terminator`](./manual-workflow-creation.md#response-terminator)

Standard response formatting

```json
{
  "responseTemplate": {
    "success": true,
    "data": "${input}",
    "metadata": { "workflowId": "${workflowId}" }
  },
  "statusCode": 200,
  "headers": { "Content-Type": "application/json" }
}
```

### [`mcp-response-terminator`](./manual-workflow-creation.md#mcp-response-terminator)

MCP protocol response

```json
{
  "responseFormat": "mcp",
  "includeMetadata": true,
  "dataTransformation": {
    "result": "${input.result}",
    "status": "${input.status}"
  }
}
```

### [`error-terminator`](./manual-workflow-creation.md#error-terminator)

Error handling

```json
{
  "errorTemplate": {
    "error": true,
    "message": "${error.message}",
    "code": "${error.code}"
  },
  "statusCode": 500,
  "logError": true
}
```

## Common Patterns

### Variable Access

- **Input Data**: `${input.fieldName}`
- **Context Variables**: `${variables.variableName}`
- **Node Results**: `${nodeResults.nodeId.fieldName}`
- **Workflow Info**: `${workflowId}`, `${executionId}`

### JavaScript Context

```javascript
// Available in javascript-action nodes
input; // Input from previous node (direct access)
workflowContext.variables; // Workflow variables
workflowContext.nodeResults; // All previous node results
context.workflowId; // Current workflow ID
context.executionId; // Current execution ID

// Modify context
workflowContext.variables.myVar = value; // Set variable
console.log('Debug info'); // Logging
```

### Error Handling

```json
{
  "retry_config": {
    "maxRetries": 3,
    "retryDelay": 1000,
    "retryBackoffMultiplier": 2,
    "retryableErrors": ["NetworkError", "TimeoutError"]
  }
}
```

### Edge Conditions

```json
{
  "id": "conditional-edge",
  "source": "source-node",
  "target": "target-node",
  "condition": "result.success == true"
}
```

## Quick Start Template

```json
{
  "id": "my-workflow",
  "name": "My Workflow",
  "description": "Workflow description",
  "version": "1.0.0",
  "enabled": true,
  "input_schema": {
    "type": "object",
    "properties": {
      "data": { "type": "object" }
    },
    "required": ["data"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "process-input",
        "type": "javascript-action",
        "name": "Process Input",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": "return { processed: true, data: input };"
        }
      },
      {
        "id": "format-response",
        "type": "response-terminator",
        "name": "Format Response",
        "position": { "x": 400, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "result": "${input}"
          }
        }
      }
    ],
    "edges": [
      {
        "id": "edge-1",
        "source": "process-input",
        "target": "format-response"
      }
    ]
  }
}
```

## Best Practices

### 🎯 **Design**

- Keep workflows focused on single purposes
- Use descriptive node IDs and names
- Include clear descriptions and comments

### 🔧 **Configuration**

- Set appropriate timeouts for external calls
- Use variable substitution for dynamic values
- Enable retries for network operations

### 🚨 **Error Handling**

- Include error-terminator nodes
- Validate inputs early in the workflow
- Use try-catch in JavaScript nodes

### 📊 **Performance**

- Cache expensive operations with Redis
- Minimize external API calls
- Set realistic timeout values

### 🔒 **Security**

- Validate all inputs with schemas
- Use environment variables for secrets
- Enable authentication on public triggers

### 🧪 **Testing**

- Test with various input scenarios
- Verify error handling works correctly
- Monitor workflows in production

## See Also

- **[Complete Documentation](./manual-workflow-creation.md)** - Detailed node specifications
- **[Workflow Examples](./workflow-examples.md)** - Real-world implementation patterns
- **[API Documentation](./api/README.md)** - REST API reference
