# Infinite Loop Fix - Release Pipeline

Tento dokument popisuje opravu nekonečného loopu v release pipeline.

## Problém: Nekonečný loop

### Scén<PERSON>ř problému:
1. **Developer push** do main branch
2. **Create release job** se spustí → vytvoří verzi 1.0.5
3. **Semantic release** vytvoří commit: `chore(release): 1.0.5`
4. **Commit do main** → znovu spustí create release job
5. **Create release** znovu běží → vytvo<PERSON><PERSON> verzi 1.0.6
6. **Znovu commit** → znovu create release
7. **NEKONEČNÝ LOOP!** 🔄

### Důsledky:
- Nekonečné spouštění pipeline
- Nekonečné generování verzí
- Vyčerpání CI/CD minut
- Spam v Git historii

## Řešení: Vyloučení release commitů

### Přidání exclude rule pro create release job:

```yaml
create release:
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /^chore\(release\):/'
      when: never  # ❌ NIKDY nespouštět pro release commity
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: always # ✅ Spustit pro ostatní commity do main
```

## Kompletní pipeline logic

### Create Release Job
```yaml
rules:
  - if: '$CI_COMMIT_MESSAGE =~ /^chore\(release\):/'
    when: never                    # Vyloučit release commity
  - if: '$CI_COMMIT_REF_NAME == "main"'
    when: always                   # Spustit pro dev commity
```

### Docker Build Jobs
```yaml
rules:
  - if: '$CI_COMMIT_TAG'
    when: always                   # Spustit pro Git tagy
  - if: '$CI_COMMIT_MESSAGE =~ /^chore\(release\):/'
    when: always                   # Spustit pro release commity
  - if: '$CI_COMMIT_REF_NAME == "main"'
    when: manual                   # Manual fallback
```

## Nový správný workflow

### 1. Developer commit
```bash
git commit -m "feat: add new feature"
git push origin main
```

**Pipeline:**
- ✅ create release (spustí se)
- ❌ docker builds (nespustí se - žádný tag)

### 2. Semantic release commit
```bash
# Semantic release vytvoří:
git commit -m "chore(release): 1.0.5"
git tag v1.0.5
git push origin main
git push origin v1.0.5
```

**Pipeline:**
- ❌ create release (nespustí se - release commit)
- ✅ docker builds (spustí se - release commit + tag)

## Diagram workflow

```
Developer Push
      │
      ▼
┌─────────────┐
│   feat:     │ ◄── Normální commit
│   fix:      │
│   docs:     │
└─────┬───────┘
      │
      ▼
┌─────────────┐
│ create      │ ◄── Spustí se
│ release     │
└─────┬───────┘
      │
      ▼
┌─────────────┐
│chore(release│ ◄── Release commit
│   ): 1.0.5  │
└─────┬───────┘
      │
      ▼
┌─────────────┐    ┌─────────────┐
│   backend   │    │  frontend   │ ◄── Spustí se
│    build    │    │   build     │
└─────────────┘    └─────────────┘
      │                    │
      ▼                    ▼
   KONEC               KONEC
```

## Regex patterns

### Release commit detection:
```regex
^chore\(release\):
```

**Matches:**
- ✅ `chore(release): 1.0.5`
- ✅ `chore(release): 2.1.0`

**Doesn't match:**
- ❌ `feat: add feature`
- ❌ `fix: resolve bug`
- ❌ `chore: update deps`

### Git tag detection:
```bash
$CI_COMMIT_TAG
```

**Matches:**
- ✅ `v1.0.5`
- ✅ `v2.1.0`

## Testování

### Test 1: Normální development
```bash
git commit -m "feat: add feature"
git push origin main
```

**Očekávaný výsledek:**
- ✅ create release spustí
- ✅ vytvoří tag v1.0.5
- ✅ docker builds spustí
- ❌ create release se nespustí znovu

### Test 2: Hotfix
```bash
git commit -m "fix: critical bug"
git push origin main
```

**Očekávaný výsledek:**
- ✅ create release spustí
- ✅ vytvoří tag v1.0.6
- ✅ docker builds spustí
- ❌ create release se nespustí znovu

### Test 3: Manual release commit
```bash
git commit -m "chore(release): 1.0.7"
git push origin main
```

**Očekávaný výsledek:**
- ❌ create release se nespustí
- ✅ docker builds spustí (pokud je tag)

## Monitoring

### GitLab Pipeline view
```
Normal commit:
├── create release ✅
└── (waits for tag)

Release commit:
├── create release ❌ (skipped)
├── backend build ✅
└── frontend build ✅
```

### Git history
```
* chore(release): 1.0.5 (tag: v1.0.5) ◄── No new pipeline
* feat: add new feature              ◄── Triggered pipeline
* fix: resolve bug                   ◄── Triggered pipeline
```

## Výhody řešení

### ✅ Zabraňuje nekonečnému loopu
- Release commity nespouštějí nové release
- Jasná separace mezi dev a release commity

### ✅ Efektivní využití CI/CD
- Žádné zbytečné spouštění pipeline
- Úspora CI/CD minut

### ✅ Čistá Git historie
- Žádné spam release commity
- Logické verzování

### ✅ Spolehlivost
- Předvídatelné chování pipeline
- Žádné race conditions

## Závěr

Přidání exclude rule `when: never` pro release commity v create release job úplně eliminuje riziko nekonečného loopu a zajišťuje správné fungování automatizované release pipeline.
