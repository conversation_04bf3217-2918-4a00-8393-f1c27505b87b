# Workflow Engine Documentation

The Dynamic MCP Server includes a powerful workflow engine that allows you to create complex, multi-step processes using various node types.

## Quick Links

- **[📚 Manual Workflow Creation Guide](../manual-workflow-creation.md)** - Complete guide for creating workflows manually
- **[🎯 Node Reference Quick Guide](../node-reference-quick.md)** - Quick reference for all node types
- **[💡 Workflow Examples](../workflow-examples.md)** - Real-world workflow examples and patterns
- **[🔧 API Documentation](../api/README.md)** - REST API reference

## Overview

The workflow engine provides:

- **Visual Workflow Design**: Define workflows using nodes and edges
- **Parallel Execution**: Nodes without dependencies run in parallel
- **Error Handling**: Comprehensive error handling and retry mechanisms
- **State Management**: Redis-based workflow memory for persistence
- **Observability**: Built-in metrics and logging
- **Hot Reload**: Dynamic configuration updates without restart

## Supported Node Types

The workflow engine supports the following node categories:

### 🚀 Trigger Nodes

- **rest-api-trigger** - HTTP REST API endpoints
- **mcp-function-trigger** - MCP function calls
- **timer-trigger** - Time-based scheduling
- **webhook-trigger** - Webhook endpoints

### ⚡ Action Nodes

- **javascript-action** - Custom JavaScript execution
- **http-action** - HTTP requests to external APIs
- **sql-action** - Database operations
- **redis-action** - Redis operations
- **litellm-action** - LLM integrations

### 🧠 Logic Nodes

- **if-else-logic** - Conditional branching
- **switch-logic** - Multi-branch switching
- **merge-logic** - Data merging
- **loop-logic** - Iterative processing

### 🏁 Terminator Nodes

- **response-terminator** - Standard response formatting
- **mcp-response-terminator** - MCP protocol response
- **error-terminator** - Error handling

> **For detailed configuration options and examples, see the [Manual Workflow Creation Guide](../manual-workflow-creation.md)**

## Quick Start

### Basic Workflow Structure

```json
{
  "id": "my-workflow",
  "name": "My Workflow",
  "description": "A sample workflow",
  "version": "1.0.0",
  "enabled": true,
  "input_schema": {
    "type": "object",
    "properties": {
      "data": { "type": "object" }
    },
    "required": ["data"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "process-input",
        "type": "javascript-action",
        "name": "Process Input",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": "return { processed: true, data: input };"
        }
      },
      {
        "id": "format-response",
        "type": "response-terminator",
        "name": "Format Response",
        "position": { "x": 400, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "result": "${input}"
          }
        }
      }
    ],
    "edges": [
      {
        "id": "edge-1",
        "source": "process-input",
        "target": "format-response"
      }
    ]
  }
}
```

### Schema Definitions

#### Input Schema

Defines the expected structure of workflow input data:

```json
{
  "input_schema": {
    "type": "object",
    "properties": {
      "userId": {
        "type": "string",
        "description": "User identifier"
      },
      "amount": {
        "type": "number",
        "minimum": 0,
        "description": "Transaction amount"
      },
      "metadata": {
        "type": "object",
        "properties": {
          "source": { "type": "string" },
          "timestamp": { "type": "string", "format": "date-time" }
        }
      }
    },
    "required": ["userId", "amount"]
  }
}
```

#### Output Schema

Defines the expected structure of workflow output:

```json
{
  "output_schema": {
    "type": "object",
    "properties": {
      "success": { "type": "boolean" },
      "transactionId": { "type": "string" },
      "result": {
        "type": "object",
        "properties": {
          "status": { "type": "string" },
          "message": { "type": "string" }
        }
      }
    },
    "required": ["success"]
  }
}
```

### Nodes Configuration

#### Node Definition

```json
{
  "id": "unique_node_id",
  "type": "javascript-action|http-action|sql-action|redis-action|litellm-action",
  "name": "Human Readable Name",
  "description": "Optional description",
  "position": { "x": 100, "y": 100 },
  "config": {
    // Node-specific configuration
  }
}
```

#### Edge Definition

```json
{
  "id": "unique_edge_id",
  "source": "source_node_id",
  "target": "target_node_id",
  "condition": "optional_condition_expression"
}
```

## Execution Flow

### 1. Input Validation

The workflow engine validates input against the input schema:

```javascript
// Input validation example
const input = {
  userId: 'user123',
  amount: 100.5,
  metadata: {
    source: 'web',
    timestamp: '2024-01-01T12:00:00Z'
  }
};

// Validated against input_schema
```

### 2. Node Execution

Nodes are executed based on their dependencies:

```mermaid
graph TD
    A[Start] --> B[Validate Input]
    B --> C[Fetch User Data]
    B --> D[Check Permissions]
    C --> E[Process Transaction]
    D --> E
    E --> F[Send Notification]
    F --> G[End]
```

### 3. Data Flow

Data flows between nodes through the workflow context:

```json
{
  "executionId": "exec_123",
  "workflowId": "workflow_456",
  "input": {
    /* original input */
  },
  "output": null,
  "nodeResults": {
    "validate": { "valid": true, "userId": "user123" },
    "fetch_user": { "user": { "id": "user123", "name": "John" } }
  },
  "variables": {
    "userId": "user123",
    "userName": "John"
  }
}
```

### 4. Variable Interpolation

Variables can be used in node configurations:

```json
{
  "type": "http-action",
  "config": {
    "url": "https://api.example.com/users/${userId}",
    "headers": {
      "Authorization": "Bearer ${apiToken}"
    },
    "body": {
      "userName": "${nodeResults.fetch_user.user.name}",
      "amount": "${input.amount}"
    }
  }
}
```

## Error Handling and Retries

### Retry Configuration

```json
{
  "retry_config": {
    "maxRetries": 5,
    "retryDelay": 2000,
    "retryBackoffMultiplier": 1.5,
    "retryableErrors": ["NetworkError", "TimeoutError", "ServiceUnavailable"]
  }
}
```

### Error Types

- **NetworkError**: Network connectivity issues
- **TimeoutError**: Request timeout
- **ValidationError**: Data validation failures
- **ScriptError**: JavaScript execution errors
- **DatabaseError**: Database operation failures

### Circuit Breaker

The workflow engine includes a circuit breaker pattern:

```json
{
  "circuit_breaker": {
    "failureThreshold": 5,
    "recoveryTimeout": 60000,
    "monitoringPeriod": 10000
  }
}
```

## Redis Workflow Memory

### Purpose

Redis workflow memory provides:

- **State Persistence**: Workflow state survives server restarts
- **Distributed Execution**: Multiple server instances can share state
- **Performance**: Fast in-memory storage for workflow data
- **Scalability**: Horizontal scaling support

### Configuration

```json
{
  "redis": {
    "host": "localhost",
    "port": 6379,
    "password": "optional_password",
    "db": 0,
    "keyPrefix": "mcp:workflow:",
    "ttl": 3600
  }
}
```

### Data Structure

```redis
# Workflow execution state
mcp:workflow:exec:123 = {
  "executionId": "exec_123",
  "workflowId": "workflow_456",
  "status": "running",
  "input": { ... },
  "nodeResults": { ... },
  "variables": { ... },
  "startTime": "2024-01-01T12:00:00Z",
  "lastUpdate": "2024-01-01T12:01:30Z"
}

# Node execution results
mcp:workflow:exec:123:node:validate = {
  "nodeId": "validate",
  "status": "completed",
  "result": { ... },
  "startTime": "2024-01-01T12:00:05Z",
  "endTime": "2024-01-01T12:00:06Z",
  "duration": 1000
}
```

## Performance Considerations

### Parallel Execution

Nodes without dependencies execute in parallel:

```json
{
  "nodes": [
    {
      "id": "fetch_user",
      "type": "sql-action",
      "config": { ... }
    },
    {
      "id": "fetch_permissions",
      "type": "sql-action",
      "config": { ... }
    },
    {
      "id": "process",
      "type": "javascript-action",
      "config": { ... }
    }
  ],
  "edges": [
    { "source": "fetch_user", "target": "process" },
    { "source": "fetch_permissions", "target": "process" }
  ]
}
```

In this example, `fetch_user` and `fetch_permissions` run in parallel.

### Optimization Tips

1. **Minimize Node Dependencies**: Reduce sequential execution
2. **Use Caching**: Cache expensive operations with Redis nodes
3. **Optimize Queries**: Use efficient SQL queries
4. **Batch Operations**: Process multiple items together
5. **Monitor Performance**: Use built-in metrics

### Resource Limits

```json
{
  "resource_limits": {
    "maxExecutionTime": 300000,
    "maxMemoryUsage": "512MB",
    "maxConcurrentNodes": 10
  }
}
```

## Observability

### Metrics Collection

The workflow engine automatically collects metrics:

- **Execution Count**: Number of workflow executions
- **Success Rate**: Percentage of successful executions
- **Duration**: Execution time statistics
- **Error Rate**: Error frequency by type
- **Node Performance**: Individual node metrics

### Logging

Structured logging with correlation IDs:

```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "info",
  "message": "Workflow execution started",
  "executionId": "exec_123",
  "workflowId": "workflow_456",
  "correlationId": "corr_789"
}
```

### Monitoring

Integration with Prometheus for monitoring:

```prometheus
# Workflow execution metrics
workflow_executions_total{workflow_id="workflow_456",status="success"} 150
workflow_executions_total{workflow_id="workflow_456",status="error"} 5
workflow_execution_duration_seconds{workflow_id="workflow_456"} 2.5

# Node execution metrics
node_executions_total{node_type="javascript-action",status="success"} 300
node_execution_duration_seconds{node_type="http-action"} 1.2
```

## Best Practices

### 1. Workflow Design

- **Keep workflows focused**: One workflow per business process
- **Use descriptive names**: Clear node and workflow names
- **Document complex logic**: Add descriptions for complex nodes
- **Plan for errors**: Design error handling paths

### 2. Performance

- **Minimize dependencies**: Allow parallel execution where possible
- **Cache expensive operations**: Use Redis for caching
- **Optimize database queries**: Use efficient SQL
- **Monitor resource usage**: Track memory and CPU usage

### 3. Error Handling

- **Configure retries appropriately**: Different retry strategies for different errors
- **Use circuit breakers**: Prevent cascading failures
- **Log errors comprehensively**: Include context for debugging
- **Plan fallback strategies**: Alternative paths for critical failures

### 4. Security

- **Validate inputs**: Always validate workflow inputs
- **Secure credentials**: Use data sources for API keys
- **Audit workflow changes**: Track configuration changes
- **Monitor access**: Log workflow executions

### 5. Testing

- **Test with sample data**: Use the Admin API test endpoints
- **Test error scenarios**: Verify error handling works
- **Performance testing**: Test with realistic data volumes
- **Integration testing**: Test end-to-end workflows

## Additional Resources

### Documentation

- **[Manual Workflow Creation Guide](../manual-workflow-creation.md)** - Complete guide with all node types and configurations
- **[Node Reference Quick Guide](../node-reference-quick.md)** - Quick reference for development
- **[Workflow Examples](../workflow-examples.md)** - Real-world implementation patterns

### Examples

- **[Simple REST API Processing](../workflow-examples.md#example-1-simple-rest-api-processing)**
- **[Conditional Processing with External API](../workflow-examples.md#example-2-conditional-processing-with-external-api)**
- **[Database and Caching Workflow](../workflow-examples.md#example-3-database-and-caching-workflow)**
- **[Comprehensive Error Handling](../workflow-examples.md#example-4-comprehensive-error-handling)**
- **[E-commerce Order Processing](../workflow-examples.md#example-6-e-commerce-order-processing)**

### Tools

- **Admin API**: REST endpoints for workflow management
- **MCP Protocol**: Integration with MCP-compatible clients
- **Visual Editor**: (Coming in future releases)

## Getting Started

1. **Read the [Manual Workflow Creation Guide](../manual-workflow-creation.md)** for complete documentation
2. **Check the [Node Reference Quick Guide](../node-reference-quick.md)** for quick lookups
3. **Explore [Workflow Examples](../workflow-examples.md)** for implementation patterns
4. **Use the Admin API** to create and test your workflows
5. **Monitor and optimize** using the built-in observability features

For questions and support, refer to the documentation or check the project's issue tracker.
