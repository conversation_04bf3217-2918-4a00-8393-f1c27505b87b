# Workflow Examples Collection

This document provides additional practical examples for creating workflows manually. Each example demonstrates specific patterns and use cases.

## Table of Contents

1. [Data Processing Patterns](#data-processing-patterns)
2. [Integration Patterns](#integration-patterns)
3. [Error Handling Patterns](#error-handling-patterns)
4. [Advanced Logic Patterns](#advanced-logic-patterns)
5. [Real-World Use Cases](#real-world-use-cases)

## Data Processing Patterns

### Example 1: Data Validation and Enrichment Pipeline

```json
{
  "id": "data-validation-enrichment",
  "name": "Data Validation and Enrichment",
  "description": "Validates incoming data and enriches it with external information",
  "version": "1.0.0",
  "enabled": true,
  "input_schema": {
    "type": "object",
    "properties": {
      "users": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "email": { "type": "string" },
            "name": { "type": "string" },
            "age": { "type": "number" }
          }
        }
      }
    },
    "required": ["users"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "validate-input",
        "type": "javascript-action",
        "name": "Validate User Data",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { users } = input;
const validUsers = [];
const invalidUsers = [];

users.forEach((user, index) => {
  const errors = [];
  
  // Email validation
  if (!user.email || !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(user.email)) {
    errors.push('Invalid email format');
  }
  
  // Name validation
  if (!user.name || user.name.trim().length < 2) {
    errors.push('Name must be at least 2 characters');
  }
  
  // Age validation
  if (!user.age || user.age < 0 || user.age > 150) {
    errors.push('Age must be between 0 and 150');
  }
  
  if (errors.length === 0) {
    validUsers.push({ ...user, index });
  } else {
    invalidUsers.push({ ...user, index, errors });
  }
});

console.log(\`Validated \${validUsers.length} valid users, \${invalidUsers.length} invalid users\`);

return {
  validUsers,
  invalidUsers,
  totalCount: users.length,
  validCount: validUsers.length,
  invalidCount: invalidUsers.length
};
          `
        }
      },
      {
        "id": "enrich-users",
        "type": "javascript-action",
        "name": "Enrich User Data",
        "position": { "x": 400, "y": 100 },
        "config": {
          "script": `
const { validUsers } = input;
const enrichedUsers = [];

for (const user of validUsers) {
  // Enrich with additional data
  const enrichedUser = {
    ...user,
    id: \`user_\${Date.now()}_\${user.index}\`,
    emailDomain: user.email.split('@')[1],
    ageGroup: user.age < 18 ? 'minor' : user.age < 65 ? 'adult' : 'senior',
    nameLength: user.name.length,
    processedAt: new Date().toISOString(),
    metadata: {
      source: 'workflow',
      version: '1.0',
      enrichmentTimestamp: Date.now()
    }
  };
  
  enrichedUsers.push(enrichedUser);
}

console.log(\`Enriched \${enrichedUsers.length} users\`);

return {
  enrichedUsers,
  originalValidUsers: validUsers,
  enrichmentSummary: {
    count: enrichedUsers.length,
    domains: [...new Set(enrichedUsers.map(u => u.emailDomain))],
    ageGroups: enrichedUsers.reduce((acc, u) => {
      acc[u.ageGroup] = (acc[u.ageGroup] || 0) + 1;
      return acc;
    }, {})
  }
};
          `
        }
      },
      {
        "id": "store-results",
        "type": "redis-action",
        "name": "Cache Enriched Data",
        "position": { "x": 700, "y": 100 },
        "config": {
          "operation": "SET",
          "key": "enriched_users_${executionId}",
          "value": "${input.enrichedUsers}",
          "expiration": 3600
        }
      },
      {
        "id": "format-response",
        "type": "response-terminator",
        "name": "Format Response",
        "position": { "x": 1000, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "data": {
              "enrichedUsers": "${nodeResults.enrich-users.enrichedUsers}",
              "invalidUsers": "${nodeResults.validate-input.invalidUsers}",
              "summary": {
                "total": "${nodeResults.validate-input.totalCount}",
                "valid": "${nodeResults.validate-input.validCount}",
                "invalid": "${nodeResults.validate-input.invalidCount}",
                "enrichmentSummary": "${nodeResults.enrich-users.enrichmentSummary}"
              }
            },
            "metadata": {
              "executionId": "${executionId}",
              "processedAt": "${variables.timestamp}",
              "cacheKey": "enriched_users_${executionId}"
            }
          }
        }
      }
    ],
    "edges": [
      { "id": "e1", "source": "validate-input", "target": "enrich-users" },
      { "id": "e2", "source": "enrich-users", "target": "store-results" },
      { "id": "e3", "source": "store-results", "target": "format-response" }
    ]
  }
}
```

### Example 2: Batch Processing with Loop Logic

```json
{
  "id": "batch-processing-loop",
  "name": "Batch Data Processing",
  "description": "Processes large datasets in batches using loop logic",
  "version": "1.0.0",
  "input_schema": {
    "type": "object",
    "properties": {
      "items": { "type": "array" },
      "batchSize": { "type": "number", "default": 10 }
    },
    "required": ["items"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "prepare-batches",
        "type": "javascript-action",
        "name": "Create Batches",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { items, batchSize = 10 } = input;
const batches = [];

for (let i = 0; i < items.length; i += batchSize) {
  const batch = items.slice(i, i + batchSize);
  batches.push({
    batchId: Math.floor(i / batchSize) + 1,
    items: batch,
    startIndex: i,
    endIndex: Math.min(i + batchSize - 1, items.length - 1)
  });
}

workflowContext.variables.totalBatches = batches.length;
workflowContext.variables.totalItems = items.length;
workflowContext.variables.batchSize = batchSize;

console.log(\`Created \${batches.length} batches for \${items.length} items\`);

return {
  batches,
  metadata: {
    totalBatches: batches.length,
    totalItems: items.length,
    batchSize
  }
};
          `
        }
      },
      {
        "id": "process-batches",
        "type": "loop-logic",
        "name": "Process Each Batch",
        "position": { "x": 400, "y": 100 },
        "config": {
          "iterable": "${input.batches}",
          "itemVariable": "currentBatch",
          "indexVariable": "batchIndex",
          "maxIterations": 1000,
          "collectResults": true
        }
      },
      {
        "id": "process-single-batch",
        "type": "javascript-action",
        "name": "Process Batch Items",
        "position": { "x": 700, "y": 100 },
        "config": {
          "script": `
const batch = workflowContext.variables.currentBatch;
const batchIndex = workflowContext.variables.batchIndex;

console.log(\`Processing batch \${batch.batchId} with \${batch.items.length} items\`);

const processedItems = batch.items.map((item, itemIndex) => {
  // Custom processing logic here
  return {
    ...item,
    processed: true,
    processedAt: new Date().toISOString(),
    batchId: batch.batchId,
    itemIndex: itemIndex,
    globalIndex: batch.startIndex + itemIndex
  };
});

// Simulate some processing time for demonstration
const processingTime = Math.random() * 100 + 50;

return {
  batchId: batch.batchId,
  processedItems,
  processingTime,
  itemCount: processedItems.length,
  status: 'completed'
};
          `
        }
      },
      {
        "id": "aggregate-results",
        "type": "javascript-action",
        "name": "Aggregate All Results",
        "position": { "x": 1000, "y": 100 },
        "config": {
          "script": `
const loopResults = input; // Results from loop-logic node
const allProcessedItems = [];
let totalProcessingTime = 0;
let totalItemsProcessed = 0;

loopResults.forEach(result => {
  if (result.processedItems) {
    allProcessedItems.push(...result.processedItems);
    totalProcessingTime += result.processingTime || 0;
    totalItemsProcessed += result.itemCount || 0;
  }
});

const summary = {
  totalBatches: workflowContext.variables.totalBatches,
  totalItems: workflowContext.variables.totalItems,
  totalItemsProcessed,
  batchSize: workflowContext.variables.batchSize,
  averageProcessingTime: totalProcessingTime / loopResults.length,
  totalProcessingTime
};

console.log(\`Aggregated results: \${allProcessedItems.length} items processed\`);

return {
  processedItems: allProcessedItems,
  summary,
  batchResults: loopResults
};
          `
        }
      }
    ],
    "edges": [
      { "id": "e1", "source": "prepare-batches", "target": "process-batches" },
      { "id": "e2", "source": "process-batches", "target": "process-single-batch" },
      { "id": "e3", "source": "process-single-batch", "target": "aggregate-results" }
    ]
  }
}
```

## Integration Patterns

### Example 3: Multi-API Integration with Fallback

```json
{
  "id": "multi-api-fallback",
  "name": "Multi-API Integration with Fallback",
  "description": "Attempts multiple APIs with fallback logic",
  "version": "1.0.0",
  "input_schema": {
    "type": "object",
    "properties": {
      "query": { "type": "string" },
      "options": { "type": "object" }
    },
    "required": ["query"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "prepare-request",
        "type": "javascript-action",
        "name": "Prepare API Request",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { query, options = {} } = input;

const requestData = {
  query: query.trim(),
  timestamp: new Date().toISOString(),
  requestId: context.executionId,
  options
};

// Store in context for all API calls
workflowContext.variables.requestData = requestData;
workflowContext.variables.attempts = [];

return requestData;
          `
        }
      },
      {
        "id": "try-primary-api",
        "type": "http-action",
        "name": "Primary API Call",
        "position": { "x": 300, "y": 50 },
        "config": {
          "url": "https://api.primary.com/search",
          "method": "POST",
          "headers": {
            "Content-Type": "application/json",
            "Authorization": "Bearer ${variables.primaryApiKey}"
          },
          "body": {
            "query": "${variables.requestData.query}",
            "options": "${variables.requestData.options}"
          },
          "timeout": 5000,
          "retryCount": 1,
          "failOnError": false
        }
      },
      {
        "id": "check-primary-success",
        "type": "if-else-logic",
        "name": "Primary API Success?",
        "position": { "x": 500, "y": 50 },
        "config": {
          "condition": "input.status && input.status >= 200 && input.status < 300",
          "trueOutput": {
            "success": true,
            "source": "primary",
            "data": "${input.data}",
            "metadata": "${input}"
          },
          "falseOutput": {
            "success": false,
            "source": "primary",
            "error": "Primary API failed",
            "fallbackNeeded": true
          },
          "conditionType": "simple"
        }
      },
      {
        "id": "try-secondary-api",
        "type": "http-action",
        "name": "Secondary API Call",
        "position": { "x": 300, "y": 150 },
        "config": {
          "url": "https://api.secondary.com/query",
          "method": "GET",
          "headers": {
            "X-API-Key": "${variables.secondaryApiKey}"
          },
          "timeout": 8000,
          "retryCount": 2,
          "failOnError": false
        }
      },
      {
        "id": "check-secondary-success",
        "type": "if-else-logic",
        "name": "Secondary API Success?",
        "position": { "x": 500, "y": 150 },
        "config": {
          "condition": "input.status && input.status >= 200 && input.status < 300",
          "trueOutput": {
            "success": true,
            "source": "secondary",
            "data": "${input.data}",
            "metadata": "${input}"
          },
          "falseOutput": {
            "success": false,
            "source": "secondary",
            "error": "Secondary API failed",
            "fallbackNeeded": true
          },
          "conditionType": "simple"
        }
      },
      {
        "id": "try-local-cache",
        "type": "redis-action",
        "name": "Check Local Cache",
        "position": { "x": 300, "y": 250 },
        "config": {
          "operation": "GET",
          "key": "cache_${variables.requestData.query}"
        }
      },
      {
        "id": "check-cache-hit",
        "type": "if-else-logic",
        "name": "Cache Hit?",
        "position": { "x": 500, "y": 250 },
        "config": {
          "condition": "input !== null",
          "trueOutput": {
            "success": true,
            "source": "cache",
            "data": "${input}",
            "cached": true
          },
          "falseOutput": {
            "success": false,
            "source": "cache",
            "error": "No cached data available"
          },
          "conditionType": "simple"
        }
      },
      {
        "id": "merge-results",
        "type": "merge-logic",
        "name": "Merge API Results",
        "position": { "x": 700, "y": 150 },
        "config": {
          "mergeStrategy": "deep",
          "sources": [
            "${nodeResults.check-primary-success}",
            "${nodeResults.check-secondary-success}",
            "${nodeResults.check-cache-hit}"
          ],
          "conflictResolution": "first_success"
        }
      },
      {
        "id": "cache-successful-result",
        "type": "redis-action",
        "name": "Cache Result",
        "position": { "x": 900, "y": 100 },
        "config": {
          "operation": "SET",
          "key": "cache_${variables.requestData.query}",
          "value": "${input.data}",
          "expiration": 1800
        }
      },
      {
        "id": "format-final-response",
        "type": "response-terminator",
        "name": "Format Response",
        "position": { "x": 1100, "y": 150 },
        "config": {
          "responseTemplate": {
            "success": "${input.success}",
            "data": "${input.data}",
            "source": "${input.source}",
            "cached": "${input.cached || false}",
            "metadata": {
              "query": "${variables.requestData.query}",
              "executionId": "${executionId}",
              "attempts": [
                "${nodeResults.check-primary-success}",
                "${nodeResults.check-secondary-success}",
                "${nodeResults.check-cache-hit}"
              ]
            }
          }
        }
      }
    ],
    "edges": [
      { "id": "e1", "source": "prepare-request", "target": "try-primary-api" },
      { "id": "e2", "source": "try-primary-api", "target": "check-primary-success" },
      {
        "id": "e3",
        "source": "check-primary-success",
        "target": "try-secondary-api",
        "condition": "fallbackNeeded == true"
      },
      { "id": "e4", "source": "try-secondary-api", "target": "check-secondary-success" },
      {
        "id": "e5",
        "source": "check-secondary-success",
        "target": "try-local-cache",
        "condition": "fallbackNeeded == true"
      },
      { "id": "e6", "source": "try-local-cache", "target": "check-cache-hit" },
      { "id": "e7", "source": "check-primary-success", "target": "merge-results" },
      { "id": "e8", "source": "check-secondary-success", "target": "merge-results" },
      { "id": "e9", "source": "check-cache-hit", "target": "merge-results" },
      {
        "id": "e10",
        "source": "merge-results",
        "target": "cache-successful-result",
        "condition": "success == true && source != 'cache'"
      },
      { "id": "e11", "source": "cache-successful-result", "target": "format-final-response" },
      { "id": "e12", "source": "merge-results", "target": "format-final-response" }
    ]
  }
}
```

## Error Handling Patterns

### Example 4: Comprehensive Error Handling

```json
{
  "id": "error-handling-pattern",
  "name": "Comprehensive Error Handling",
  "description": "Demonstrates various error handling patterns",
  "version": "1.0.0",
  "input_schema": {
    "type": "object",
    "properties": {
      "operation": { "type": "string" },
      "data": { "type": "object" }
    },
    "required": ["operation", "data"]
  },
  "retry_config": {
    "maxRetries": 3,
    "retryDelay": 1000,
    "retryBackoffMultiplier": 2,
    "retryableErrors": ["NetworkError", "TimeoutError", "ServiceUnavailable"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "validate-operation",
        "type": "javascript-action",
        "name": "Validate Operation",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { operation, data } = input;

// Define valid operations
const validOperations = ['create', 'update', 'delete', 'read'];

try {
  if (!validOperations.includes(operation)) {
    throw new Error(\`Invalid operation: \${operation}. Valid operations: \${validOperations.join(', ')}\`);
  }

  if (!data || typeof data !== 'object') {
    throw new Error('Data must be a valid object');
  }

  // Operation-specific validation
  switch (operation) {
    case 'create':
    case 'update':
      if (!data.id) {
        throw new Error('ID is required for create/update operations');
      }
      break;
    case 'delete':
      if (!data.id) {
        throw new Error('ID is required for delete operation');
      }
      break;
    case 'read':
      // Read operations are more flexible
      break;
  }

  return {
    valid: true,
    operation,
    data,
    validatedAt: new Date().toISOString()
  };

} catch (error) {
  // Log error for debugging
  console.error('Validation error:', error.message);
  
  // Store error context
  workflowContext.variables.validationError = {
    message: error.message,
    operation,
    data,
    timestamp: new Date().toISOString()
  };
  
  throw error; // Re-throw to trigger error handling
}
          `
        }
      },
      {
        "id": "process-operation",
        "type": "switch-logic",
        "name": "Route by Operation",
        "position": { "x": 400, "y": 100 },
        "config": {
          "switchValue": "${input.operation}",
          "cases": {
            "create": { "action": "create", "data": "${input.data}" },
            "update": { "action": "update", "data": "${input.data}" },
            "delete": { "action": "delete", "data": "${input.data}" },
            "read": { "action": "read", "data": "${input.data}" }
          },
          "defaultCase": {
            "action": "unknown",
            "error": "Unsupported operation"
          }
        }
      },
      {
        "id": "execute-database-operation",
        "type": "sql-action",
        "name": "Database Operation",
        "position": { "x": 700, "y": 100 },
        "config": {
          "query": "SELECT execute_operation($1, $2)",
          "parameters": ["${input.action}", "${input.data}"],
          "queryType": "SELECT",
          "timeout": 10000
        }
      },
      {
        "id": "check-db-result",
        "type": "if-else-logic",
        "name": "Check DB Success",
        "position": { "x": 1000, "y": 100 },
        "config": {
          "condition": "input && input.length > 0",
          "trueOutput": {
            "success": true,
            "result": "${input}",
            "source": "database"
          },
          "falseOutput": {
            "success": false,
            "error": "Database operation returned no results",
            "needsErrorHandling": true
          },
          "conditionType": "simple"
        }
      },
      {
        "id": "handle-validation-error",
        "type": "error-terminator",
        "name": "Validation Error Handler",
        "position": { "x": 400, "y": 300 },
        "config": {
          "errorTemplate": {
            "error": true,
            "type": "ValidationError",
            "message": "${variables.validationError.message}",
            "details": {
              "operation": "${variables.validationError.operation}",
              "timestamp": "${variables.validationError.timestamp}"
            },
            "code": "VALIDATION_FAILED",
            "statusCode": 400
          },
          "statusCode": 400,
          "logError": true
        }
      },
      {
        "id": "handle-database-error",
        "type": "javascript-action",
        "name": "Database Error Handler",
        "position": { "x": 1000, "y": 300 },
        "config": {
          "script": `
const error = workflowContext.variables.lastError || input.error;
const retryCount = context.errorContext?.retryAttempts || 0;

console.error('Database operation failed:', error);

// Determine if error is retryable
const retryableErrors = ['connection_error', 'timeout', 'lock_timeout'];
const isRetryable = retryableErrors.some(err => 
  error && error.toLowerCase().includes(err)
);

if (isRetryable && retryCount < 3) {
  // Prepare for retry
  const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000);
  
  return {
    retry: true,
    retryAfter: retryDelay,
    retryCount: retryCount + 1,
    originalError: error
  };
} else {
  // Final failure
  return {
    retry: false,
    finalError: true,
    error: error,
    retryCount: retryCount,
    message: 'Database operation failed after retries'
  };
}
          `
        }
      },
      {
        "id": "final-error-response",
        "type": "error-terminator",
        "name": "Final Error Response",
        "position": { "x": 1300, "y": 300 },
        "config": {
          "errorTemplate": {
            "error": true,
            "type": "DatabaseError",
            "message": "${input.message}",
            "details": {
              "originalError": "${input.error}",
              "retryCount": "${input.retryCount}",
              "executionId": "${executionId}"
            },
            "code": "DATABASE_OPERATION_FAILED",
            "statusCode": 500
          },
          "statusCode": 500,
          "logError": true
        }
      },
      {
        "id": "success-response",
        "type": "response-terminator",
        "name": "Success Response",
        "position": { "x": 1300, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "data": "${input.result}",
            "operation": "${nodeResults.validate-operation.operation}",
            "metadata": {
              "executionId": "${executionId}",
              "processedAt": "${variables.timestamp}",
              "source": "${input.source}"
            }
          },
          "statusCode": 200
        }
      }
    ],
    "edges": [
      { "id": "e1", "source": "validate-operation", "target": "process-operation" },
      { "id": "e2", "source": "process-operation", "target": "execute-database-operation" },
      { "id": "e3", "source": "execute-database-operation", "target": "check-db-result" },
      {
        "id": "e4",
        "source": "check-db-result",
        "target": "success-response",
        "condition": "success == true"
      },
      {
        "id": "e5",
        "source": "check-db-result",
        "target": "handle-database-error",
        "condition": "needsErrorHandling == true"
      },
      {
        "id": "e6",
        "source": "handle-database-error",
        "target": "execute-database-operation",
        "condition": "retry == true"
      },
      {
        "id": "e7",
        "source": "handle-database-error",
        "target": "final-error-response",
        "condition": "finalError == true"
      },
      {
        "id": "error-1",
        "source": "validate-operation",
        "target": "handle-validation-error",
        "type": "error"
      }
    ]
  }
}
```

## Advanced Logic Patterns

### Example 5: Dynamic Workflow with Context Variables

```json
{
  "id": "dynamic-context-workflow",
  "name": "Dynamic Workflow with Context",
  "description": "Uses context variables and dynamic decision making",
  "version": "1.0.0",
  "input_schema": {
    "type": "object",
    "properties": {
      "config": { "type": "object" },
      "payload": { "type": "object" }
    }
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "initialize-context",
        "type": "javascript-action",
        "name": "Initialize Context",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { config = {}, payload = {} } = input;

// Set up dynamic context variables
workflowContext.variables.processingMode = config.mode || 'standard';
workflowContext.variables.maxIterations = config.maxIterations || 10;
workflowContext.variables.thresholds = config.thresholds || { high: 100, medium: 50, low: 10 };
workflowContext.variables.enabledFeatures = config.features || ['validation', 'transformation'];
workflowContext.variables.startTime = Date.now();
workflowContext.variables.processedItems = [];
workflowContext.variables.stats = { total: 0, processed: 0, errors: 0 };

// Parse payload based on type
let processedPayload;
if (Array.isArray(payload)) {
  processedPayload = payload;
} else if (typeof payload === 'object' && payload.items) {
  processedPayload = payload.items;
} else {
  processedPayload = [payload];
}

workflowContext.variables.originalPayload = payload;
workflowContext.variables.itemsToProcess = processedPayload;
workflowContext.variables.stats.total = processedPayload.length;

console.log(\`Initialized context with \${processedPayload.length} items, mode: \${workflowContext.variables.processingMode}\`);

return {
  initialized: true,
  itemCount: processedPayload.length,
  mode: workflowContext.variables.processingMode,
  features: workflowContext.variables.enabledFeatures
};
          `
        }
      },
      {
        "id": "feature-gate-validation",
        "type": "if-else-logic",
        "name": "Validation Feature Gate",
        "position": { "x": 400, "y": 50 },
        "config": {
          "condition": "utils.includes(variables.enabledFeatures, 'validation')",
          "trueOutput": {
            "enableValidation": true,
            "items": "${variables.itemsToProcess}"
          },
          "falseOutput": {
            "enableValidation": false,
            "items": "${variables.itemsToProcess}",
            "skipped": "validation"
          },
          "conditionType": "expression"
        }
      },
      {
        "id": "validate-items",
        "type": "javascript-action",
        "name": "Validate Items",
        "position": { "x": 700, "y": 50 },
        "config": {
          "script": `
if (!input.enableValidation) {
  console.log('Validation skipped by feature gate');
  return {
    validItems: input.items,
    invalidItems: [],
    validationSkipped: true
  };
}

const items = input.items;
const validItems = [];
const invalidItems = [];
const thresholds = workflowContext.variables.thresholds;

items.forEach((item, index) => {
  const validation = {
    hasId: !!item.id,
    hasValue: item.value !== undefined && item.value !== null,
    valueInRange: typeof item.value === 'number' && item.value >= 0,
    meetsThreshold: typeof item.value === 'number' && item.value >= thresholds.low
  };
  
  const isValid = Object.values(validation).every(v => v);
  
  if (isValid) {
    validItems.push({
      ...item,
      index,
      validation,
      priority: item.value >= thresholds.high ? 'high' : 
               item.value >= thresholds.medium ? 'medium' : 'low'
    });
  } else {
    invalidItems.push({
      ...item,
      index,
      validation,
      errors: Object.entries(validation)
        .filter(([key, value]) => !value)
        .map(([key]) => key)
    });
  }
});

// Update context stats
workflowContext.variables.stats.processed += items.length;
workflowContext.variables.stats.errors += invalidItems.length;

console.log(\`Validated \${items.length} items: \${validItems.length} valid, \${invalidItems.length} invalid\`);

return {
  validItems,
  invalidItems,
  validationStats: {
    total: items.length,
    valid: validItems.length,
    invalid: invalidItems.length
  }
};
          `
        }
      },
      {
        "id": "feature-gate-transformation",
        "type": "if-else-logic",
        "name": "Transformation Feature Gate",
        "position": { "x": 400, "y": 150 },
        "config": {
          "condition": "utils.includes(variables.enabledFeatures, 'transformation')",
          "trueOutput": {
            "enableTransformation": true,
            "items": "${nodeResults.validate-items.validItems || variables.itemsToProcess}"
          },
          "falseOutput": {
            "enableTransformation": false,
            "items": "${nodeResults.validate-items.validItems || variables.itemsToProcess}",
            "skipped": "transformation"
          },
          "conditionType": "expression"
        }
      },
      {
        "id": "transform-items",
        "type": "javascript-action",
        "name": "Transform Items",
        "position": { "x": 700, "y": 150 },
        "config": {
          "script": `
if (!input.enableTransformation) {
  console.log('Transformation skipped by feature gate');
  return {
    transformedItems: input.items,
    transformationSkipped: true
  };
}

const items = input.items;
const mode = workflowContext.variables.processingMode;
const transformedItems = [];

items.forEach(item => {
  let transformed = { ...item };
  
  // Apply mode-specific transformations
  switch (mode) {
    case 'enhanced':
      transformed.enhanced = true;
      transformed.score = (item.value || 0) * 1.5;
      transformed.category = transformed.priority || 'standard';
      transformed.metadata = {
        transformedAt: new Date().toISOString(),
        mode: 'enhanced',
        originalValue: item.value
      };
      break;
      
    case 'minimal':
      transformed = {
        id: item.id,
        value: item.value,
        priority: item.priority || 'normal'
      };
      break;
      
    case 'standard':
    default:
      transformed.processed = true;
      transformed.processedAt = new Date().toISOString();
      transformed.normalizedValue = Math.min(Math.max(item.value || 0, 0), 1000);
      break;
  }
  
  transformedItems.push(transformed);
});

console.log(\`Transformed \${transformedItems.length} items using \${mode} mode\`);

// Store transformed items in context
workflowContext.variables.processedItems = transformedItems;

return {
  transformedItems,
  transformationMode: mode,
  transformationStats: {
    itemCount: transformedItems.length,
    mode: mode
  }
};
          `
        }
      },
      {
        "id": "priority-routing",
        "type": "switch-logic",
        "name": "Route by Priority",
        "position": { "x": 1000, "y": 100 },
        "config": {
          "switchValue": "${variables.processingMode}",
          "cases": {
            "enhanced": {
              "route": "enhanced-processing",
              "items": "${nodeResults.transform-items.transformedItems}"
            },
            "minimal": {
              "route": "minimal-processing",
              "items": "${nodeResults.transform-items.transformedItems}"
            },
            "standard": {
              "route": "standard-processing",
              "items": "${nodeResults.transform-items.transformedItems}"
            }
          },
          "defaultCase": {
            "route": "default-processing",
            "items": "${nodeResults.transform-items.transformedItems}"
          }
        }
      },
      {
        "id": "finalize-results",
        "type": "javascript-action",
        "name": "Finalize Results",
        "position": { "x": 1300, "y": 100 },
        "config": {
          "script": `
const routingResult = input;
const endTime = Date.now();
const duration = endTime - workflowContext.variables.startTime;

// Gather all results
const finalResult = {
  success: true,
  route: routingResult.route,
  items: routingResult.items,
  processing: {
    mode: workflowContext.variables.processingMode,
    enabledFeatures: workflowContext.variables.enabledFeatures,
    duration: duration,
    stats: workflowContext.variables.stats
  },
  validation: context.nodeResults['validate-items']?.validationStats,
  transformation: context.nodeResults['transform-items']?.transformationStats,
  metadata: {
    executionId: context.executionId,
    workflowId: context.workflowId,
    processedAt: new Date().toISOString(),
    totalDuration: duration
  }
};

console.log(\`Workflow completed in \${duration}ms with route: \${routingResult.route}\`);

return finalResult;
          `
        }
      }
    ],
    "edges": [
      { "id": "e1", "source": "initialize-context", "target": "feature-gate-validation" },
      {
        "id": "e2",
        "source": "feature-gate-validation",
        "target": "validate-items",
        "condition": "enableValidation == true"
      },
      {
        "id": "e3",
        "source": "feature-gate-validation",
        "target": "feature-gate-transformation",
        "condition": "enableValidation == false"
      },
      { "id": "e4", "source": "validate-items", "target": "feature-gate-transformation" },
      {
        "id": "e5",
        "source": "feature-gate-transformation",
        "target": "transform-items",
        "condition": "enableTransformation == true"
      },
      {
        "id": "e6",
        "source": "feature-gate-transformation",
        "target": "priority-routing",
        "condition": "enableTransformation == false"
      },
      { "id": "e7", "source": "transform-items", "target": "priority-routing" },
      { "id": "e8", "source": "priority-routing", "target": "finalize-results" }
    ]
  }
}
```

## Real-World Use Cases

### Example 6: E-commerce Order Processing

```json
{
  "id": "ecommerce-order-processing",
  "name": "E-commerce Order Processing",
  "description": "Complete order processing workflow with payment, inventory, and fulfillment",
  "version": "1.0.0",
  "input_schema": {
    "type": "object",
    "properties": {
      "order": {
        "type": "object",
        "properties": {
          "orderId": { "type": "string" },
          "customerId": { "type": "string" },
          "items": { "type": "array" },
          "paymentMethod": { "type": "string" },
          "shippingAddress": { "type": "object" },
          "totalAmount": { "type": "number" }
        },
        "required": ["orderId", "customerId", "items", "totalAmount"]
      }
    },
    "required": ["order"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "validate-order",
        "type": "javascript-action",
        "name": "Validate Order",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { order } = input;

// Comprehensive order validation
const validation = {
  hasValidOrderId: !!order.orderId && order.orderId.length > 0,
  hasValidCustomerId: !!order.customerId && order.customerId.length > 0,
  hasItems: Array.isArray(order.items) && order.items.length > 0,
  hasValidAmount: typeof order.totalAmount === 'number' && order.totalAmount > 0,
  itemsHaveValidData: order.items.every(item => 
    item.productId && item.quantity > 0 && item.price > 0
  )
};

const isValid = Object.values(validation).every(v => v);

if (!isValid) {
  const errors = Object.entries(validation)
    .filter(([key, value]) => !value)
    .map(([key]) => key);
  
  throw new Error(\`Order validation failed: \${errors.join(', ')}\`);
}

// Calculate totals
const calculatedTotal = order.items.reduce((sum, item) => 
  sum + (item.price * item.quantity), 0
);

if (Math.abs(calculatedTotal - order.totalAmount) > 0.01) {
  throw new Error(\`Total amount mismatch: calculated \${calculatedTotal}, provided \${order.totalAmount}\`);
}

workflowContext.variables.validatedOrder = order;
workflowContext.variables.calculatedTotal = calculatedTotal;

return {
  valid: true,
  order,
  calculatedTotal,
  itemCount: order.items.length
};
          `
        }
      },
      {
        "id": "check-inventory",
        "type": "sql-action",
        "name": "Check Inventory",
        "position": { "x": 400, "y": 100 },
        "config": {
          "query": `
            SELECT 
              product_id, 
              available_quantity,
              reserved_quantity,
              (available_quantity - reserved_quantity) as free_quantity
            FROM inventory 
            WHERE product_id = ANY($1)
          `,
          "parameters": ["${variables.validatedOrder.items.map(item => item.productId)}"],
          "queryType": "SELECT"
        }
      },
      {
        "id": "validate-inventory",
        "type": "javascript-action",
        "name": "Validate Inventory",
        "position": { "x": 700, "y": 100 },
        "config": {
          "script": `
const inventoryData = input;
const orderItems = workflowContext.variables.validatedOrder.items;

const inventoryMap = {};
inventoryData.forEach(item => {
  inventoryMap[item.product_id] = item;
});

const unavailableItems = [];
const availableItems = [];

orderItems.forEach(orderItem => {
  const inventory = inventoryMap[orderItem.productId];
  
  if (!inventory) {
    unavailableItems.push({
      ...orderItem,
      reason: 'Product not found in inventory'
    });
  } else if (inventory.free_quantity < orderItem.quantity) {
    unavailableItems.push({
      ...orderItem,
      reason: \`Insufficient stock: need \${orderItem.quantity}, available \${inventory.free_quantity}\`,
      availableQuantity: inventory.free_quantity
    });
  } else {
    availableItems.push({
      ...orderItem,
      availableQuantity: inventory.free_quantity
    });
  }
});

if (unavailableItems.length > 0) {
  throw new Error(\`Inventory check failed: \${unavailableItems.length} items unavailable\`);
}

workflowContext.variables.inventoryValidated = true;
workflowContext.variables.availableItems = availableItems;

return {
  inventoryValid: true,
  availableItems,
  reservationNeeded: true
};
          `
        }
      },
      {
        "id": "reserve-inventory",
        "type": "sql-action",
        "name": "Reserve Inventory",
        "position": { "x": 1000, "y": 100 },
        "config": {
          "query": `
            UPDATE inventory 
            SET reserved_quantity = reserved_quantity + $2
            WHERE product_id = $1
            RETURNING product_id, reserved_quantity
          `,
          "parameters": ["${item.productId}", "${item.quantity}"],
          "queryType": "UPDATE"
        }
      },
      {
        "id": "process-payment",
        "type": "http-action",
        "name": "Process Payment",
        "position": { "x": 400, "y": 300 },
        "config": {
          "url": "https://payment-gateway.example.com/process",
          "method": "POST",
          "headers": {
            "Authorization": "Bearer ${variables.paymentApiKey}",
            "Content-Type": "application/json"
          },
          "body": {
            "orderId": "${variables.validatedOrder.orderId}",
            "customerId": "${variables.validatedOrder.customerId}",
            "amount": "${variables.calculatedTotal}",
            "paymentMethod": "${variables.validatedOrder.paymentMethod}",
            "metadata": {
              "itemCount": "${variables.validatedOrder.items.length}",
              "executionId": "${executionId}"
            }
          },
          "timeout": 30000,
          "retryCount": 2
        }
      },
      {
        "id": "validate-payment",
        "type": "if-else-logic",
        "name": "Payment Success?",
        "position": { "x": 700, "y": 300 },
        "config": {
          "condition": "input.status == 'success' && input.transactionId",
          "trueOutput": {
            "paymentSuccess": true,
            "transactionId": "${input.transactionId}",
            "amount": "${input.amount}"
          },
          "falseOutput": {
            "paymentSuccess": false,
            "error": "${input.error || 'Payment processing failed'}",
            "needsRollback": true
          },
          "conditionType": "simple"
        }
      },
      {
        "id": "create-fulfillment-order",
        "type": "http-action",
        "name": "Create Fulfillment Order",
        "position": { "x": 1000, "y": 300 },
        "config": {
          "url": "https://fulfillment.example.com/orders",
          "method": "POST",
          "headers": {
            "Authorization": "Bearer ${variables.fulfillmentApiKey}",
            "Content-Type": "application/json"
          },
          "body": {
            "orderId": "${variables.validatedOrder.orderId}",
            "items": "${variables.availableItems}",
            "shippingAddress": "${variables.validatedOrder.shippingAddress}",
            "priority": "standard",
            "paymentConfirmed": true,
            "transactionId": "${nodeResults.validate-payment.transactionId}"
          }
        }
      },
      {
        "id": "update-order-status",
        "type": "sql-action",
        "name": "Update Order Status",
        "position": { "x": 1300, "y": 200 },
        "config": {
          "query": `
            UPDATE orders 
            SET 
              status = 'processing',
              payment_status = 'paid',
              fulfillment_status = 'pending',
              transaction_id = $2,
              updated_at = NOW()
            WHERE order_id = $1
            RETURNING *
          `,
          "parameters": [
            "${variables.validatedOrder.orderId}",
            "${nodeResults.validate-payment.transactionId}"
          ],
          "queryType": "UPDATE"
        }
      },
      {
        "id": "send-confirmation",
        "type": "http-action",
        "name": "Send Order Confirmation",
        "position": { "x": 1600, "y": 200 },
        "config": {
          "url": "https://notification.example.com/send",
          "method": "POST",
          "headers": {
            "Authorization": "Bearer ${variables.notificationApiKey}",
            "Content-Type": "application/json"
          },
          "body": {
            "type": "order_confirmation",
            "customerId": "${variables.validatedOrder.customerId}",
            "orderId": "${variables.validatedOrder.orderId}",
            "transactionId": "${nodeResults.validate-payment.transactionId}",
            "template": "order_confirmation",
            "data": {
              "orderTotal": "${variables.calculatedTotal}",
              "itemCount": "${variables.validatedOrder.items.length}",
              "estimatedDelivery": "3-5 business days"
            }
          }
        }
      }
    ],
    "edges": [
      { "id": "e1", "source": "validate-order", "target": "check-inventory" },
      { "id": "e2", "source": "check-inventory", "target": "validate-inventory" },
      { "id": "e3", "source": "validate-inventory", "target": "reserve-inventory" },
      { "id": "e4", "source": "validate-order", "target": "process-payment" },
      { "id": "e5", "source": "process-payment", "target": "validate-payment" },
      {
        "id": "e6",
        "source": "validate-payment",
        "target": "create-fulfillment-order",
        "condition": "paymentSuccess == true"
      },
      { "id": "e7", "source": "reserve-inventory", "target": "update-order-status" },
      { "id": "e8", "source": "create-fulfillment-order", "target": "update-order-status" },
      { "id": "e9", "source": "update-order-status", "target": "send-confirmation" }
    ]
  }
}
```

This collection of examples demonstrates various patterns and techniques for creating robust, production-ready workflows. Each example shows different aspects of workflow design, from simple data processing to complex business logic with error handling and external integrations.

Use these examples as templates and modify them according to your specific requirements. Remember to:

1. **Test thoroughly** - Each workflow should be tested with various input scenarios
2. **Handle errors gracefully** - Include proper error handling and fallback mechanisms
3. **Monitor performance** - Use observability features to track workflow execution
4. **Document clearly** - Maintain clear documentation for complex business logic
5. **Follow security best practices** - Validate inputs and secure external communications

These patterns can be combined and extended to create more complex workflows that meet your specific business requirements.
