# Enhanced Observability

This document describes the observability features of the Dynamic MCP Server.

## Overview

The Dynamic MCP Server includes comprehensive observability features to monitor and troubleshoot workflow executions. These features include:

- Metrics collection for workflow and node executions
- Prometheus integration for real-time metrics
- Structured logging with correlation IDs
- Performance dashboards

## Metrics

### Workflow Metrics

The following metrics are collected for workflow executions:

- `workflow_executions_total`: Total number of workflow executions (counter)
  - Labels: `workflow_id`, `status`
- `workflow_execution_duration_milliseconds`: Duration of workflow executions in milliseconds (histogram)
  - Labels: `workflow_id`
- `active_workflows`: Number of currently active workflows (gauge)
  - Labels: `workflow_id`

### Node Metrics

The following metrics are collected for node executions:

- `node_executions_total`: Total number of node executions (counter)
  - Labels: `node_id`, `node_type`, `status`
- `node_execution_duration_milliseconds`: Duration of node executions in milliseconds (histogram)
  - Labels: `node_id`, `node_type`

### System Metrics

The following system metrics are collected:

- `system_memory_usage`: System memory usage statistics in bytes (gauge)
  - Labels: `type` (total, free, used)
- `system_cpu_usage`: System CPU usage statistics (gauge)
  - Labels: `cpu` (cpu0, cpu1, etc.)
- `system_load_average`: System load average (gauge)
  - Labels: `period` (1m, 5m, 15m)
- `system_uptime_seconds`: System uptime in seconds (gauge)

### Node.js Metrics

The following Node.js metrics are collected with the prefix `nodejs_`:

- `nodejs_eventloop_lag_seconds`: Event loop lag in seconds (gauge)
- `nodejs_active_handles`: Number of active handles (gauge)
- `nodejs_active_requests`: Number of active requests (gauge)
- `nodejs_heap_size_total_bytes`: Total heap size in bytes (gauge)
- `nodejs_heap_size_used_bytes`: Used heap size in bytes (gauge)
- `nodejs_external_memory_bytes`: External memory size in bytes (gauge)
- `nodejs_version_info`: Node.js version info (gauge)
- `nodejs_gc_duration_seconds`: Garbage collection duration (histogram)
  - Labels: `gc_type`

## Prometheus Integration

The Dynamic MCP Server exposes a Prometheus metrics endpoint at `/metrics` on port 3001. This endpoint can be scraped by Prometheus to collect metrics about workflow and node executions.

### Example Prometheus Configuration

```yaml
scrape_configs:
  - job_name: 'dynamic-mcp-server'
    scrape_interval: 15s
    static_configs:
      - targets: ['localhost:3001']
```

## Dashboards

### Grafana Dashboard

A sample Grafana dashboard is available to visualize the metrics collected by Prometheus. The dashboard includes the following panels:

- Workflow Execution Rate
- Workflow Execution Duration
- Workflow Success/Failure Rate
- Node Execution Rate
- Node Execution Duration
- Node Success/Failure Rate
- Active Workflows

### Dashboard Setup

1. Install Grafana
2. Add Prometheus as a data source
3. Import the dashboard JSON from `dashboards/dynamic-mcp-server.json`

## Logging

The Dynamic MCP Server uses structured logging with correlation IDs to track workflow and node executions. Each log entry includes the following fields:

- `timestamp`: The time the log entry was created
- `level`: The log level (debug, info, warn, error)
- `message`: The log message
- `workflow_id`: The ID of the workflow (if applicable)
- `execution_id`: The ID of the workflow execution (if applicable)
- `node_id`: The ID of the node (if applicable)
- `node_type`: The type of the node (if applicable)
- `correlation_id`: A unique ID to correlate log entries for a single request

### Log Levels

The following log levels are used:

- `debug`: Detailed information for debugging
- `info`: General information about the system operation
- `warn`: Warning messages that don't affect the system operation
- `error`: Error messages that affect the system operation

### Log Aggregation

Logs can be aggregated using tools like ELK Stack (Elasticsearch, Logstash, Kibana) or Graylog. The structured logs can be easily parsed and indexed for searching and visualization.

## Troubleshooting

### Common Issues

#### No Metrics Available

If the `/metrics` endpoint returns "No metrics available", check the following:

1. Make sure the server is running
2. Check if the API server is listening on port 3001
3. Verify that the ObservabilityManager is properly initialized

#### High Workflow Execution Duration

If workflow executions are taking longer than expected, check the following:

1. Look at the node execution durations to identify slow nodes
2. Check if there are any errors or retries
3. Verify that the database and Redis connections are working properly

#### High Error Rate

If there is a high error rate, check the following:

1. Look at the error details in the logs
2. Check if there are any issues with external services
3. Verify that the node configurations are correct

## Alerting

### Prometheus Alerting Rules

The following alerting rules can be configured in Prometheus:

```yaml
groups:
  - name: dynamic-mcp-server
    rules:
      - alert: HighWorkflowErrorRate
        expr: rate(workflow_executions_total{status="FAILED"}[5m]) / rate(workflow_executions_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: 'High workflow error rate'
          description: 'Workflow error rate is above 10% for the last 5 minutes'

      - alert: LongWorkflowExecutionDuration
        expr: histogram_quantile(0.95, rate(workflow_execution_duration_milliseconds_bucket[5m])) > 10000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: 'Long workflow execution duration'
          description: '95th percentile of workflow execution duration is above 10 seconds for the last 5 minutes'
```

### Alert Notifications

Alerts can be sent to various notification channels, such as:

- Email
- Slack
- PagerDuty
- OpsGenie
- Webhook

## API

The ObservabilityManager provides the following API for recording and retrieving metrics:

### Recording Metrics

```typescript
// Record workflow execution
await observabilityManager.recordWorkflowExecution(workflowId, executionId, {
  startTime: new Date(),
  endTime: new Date(),
  durationMs: 1000,
  status: 'COMPLETED'
});

// Record node execution
await observabilityManager.recordNodeExecution(nodeId, executionId, {
  nodeType: 'javascript',
  startTime: new Date(),
  endTime: new Date(),
  durationMs: 500,
  status: 'COMPLETED'
});
```

### Retrieving Metrics

```typescript
// Get workflow metrics
const workflowMetrics = await observabilityManager.getWorkflowMetrics(workflowId, {
  startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
  endTime: new Date()
});

// Get node metrics
const nodeMetrics = await observabilityManager.getNodeMetrics(nodeType, {
  startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
  endTime: new Date()
});
```

### Exporting Prometheus Metrics

```typescript
// Export Prometheus metrics
const metrics = observabilityManager.exportPrometheusMetrics();
```
