# Workflow JavaScript Syntax Migration Guide

## Problem

Workflows created before the syntax update contain old JavaScript code that will cause "context is not defined" errors when executed.

## Quick Solution

**The easiest solution is to delete the old workflow and create a new one using the current templates.**

## What Changed

The JavaScript syntax in workflow nodes has been updated:

### Old Syntax (Causes Errors)

```javascript
// ❌ This will cause "context is not defined" error
const result = context.input.message.toUpperCase();
context.variables.processedAt = new Date().toISOString();
return {
  processed: true,
  result: result,
  executionId: context.executionId
};
```

### New Syntax (Correct)

```javascript
// ✅ This is the correct syntax
const result = input.message.toUpperCase();
workflowContext.variables.processedAt = new Date().toISOString();
return {
  processed: true,
  result: result,
  executionId: workflowContext.executionId
};
```

## Migration Changes Required

| Old Syntax                  | New Syntax                          | Description                          |
| --------------------------- | ----------------------------------- | ------------------------------------ |
| `context.input.message`     | `input.message`                     | Access input data directly           |
| `context.variables.someVar` | `workflowContext.variables.someVar` | Use workflowContext for variables    |
| `context.executionId`       | `workflowContext.executionId`       | Use workflowContext for execution ID |
| `context.workflowId`        | `workflowContext.workflowId`        | Use workflowContext for workflow ID  |

## How to Fix

### Option 1: Delete and Recreate (Recommended)

1. Delete the old workflow
2. Create a new workflow using the "Simple Template", "HTTP Template", or "Complex Template"
3. The templates now contain the correct syntax
4. Customize the new workflow as needed

### Option 2: Manual Edit

1. Click "Edit Workflow" on the problematic workflow
2. Find JavaScript Action nodes in the workflow configuration
3. Update the script code to use the new syntax
4. Save the workflow
5. Test the workflow

## Identification

Workflows with old syntax will show a warning message:

- **Warning**: "Zastaralá syntaxe JavaScriptu"
- **Recommendation**: "Smažte tento workflow a vytvořte nový pomocí aktuálních šablon"

## Templates Available

The following templates are available with correct syntax:

1. **Simple Template**: Basic message processing
2. **HTTP Template**: API calls with validation
3. **Complex Template**: Multi-step workflow with logic

All templates use the correct `input` and `workflowContext` syntax.

## Testing

After creating a new workflow or fixing an old one:

1. Use the "Execute" button to test the workflow
2. Provide sample input data
3. Verify the workflow executes without "context is not defined" errors

## Support

If you encounter issues after following this guide, check:

- All JavaScript Action nodes use the new syntax
- No references to `context.input`, `context.variables`, etc. remain
- Test data is properly formatted JSON
