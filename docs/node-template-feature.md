# Node Template Feature

The Node Template feature provides a user-friendly way to insert pre-configured node templates into workflow configurations. This feature significantly improves the workflow creation experience by offering 16 different node types organized into 4 categories.

## Overview

### Components

1. **NodeTemplateService** (`frontend/src/services/NodeTemplateService.ts`)

   - Core service providing template definitions and utilities
   - Manages 16 node types across 4 categories
   - Generates unique IDs and creates node instances

2. **NodeTemplateSelector** (`frontend/src/components/workflows/NodeTemplateSelector.tsx`)

   - UI component for selecting and inserting node templates
   - Material-UI Select with grouped options
   - Insert button with validation

3. **Updated Workflows.tsx** (`frontend/src/pages/Workflows.tsx`)
   - Integrated node template selector into workflow creation dialog
   - Enhanced JSON editor with template insertion capabilities

## Node Categories

### 🚀 Trigger Nodes (4 types)

- **REST API Trigger**: Expose workflow as REST endpoint
- **MCP Function Trigger**: Trigger via MCP function calls
- **Timer Trigger**: Time-based scheduling
- **Webhook Trigger**: External webhook endpoints

### ⚡ Action Nodes (5 types)

- **JavaScript Action**: Custom JavaScript execution
- **HTTP Request**: Make HTTP calls to external APIs
- **SQL Database**: Execute SQL database operations
- **Redis Cache**: Redis caching operations
- **LLM Integration**: Large Language Model integration

### 🧠 Logic Nodes (4 types)

- **If-Else Condition**: Conditional branching logic
- **Switch Logic**: Multi-branch switching
- **Data Merger**: Merge data from multiple sources
- **Loop Iterator**: Iterate over data collections

### 🏁 Terminator Nodes (3 types)

- **Standard Response**: Format and return standard response
- **MCP Response**: Format response for MCP protocol
- **Error Handler**: Handle and format error responses

## Features

### Smart Template Insertion

- Templates are inserted into the nodes array of the JSON configuration
- Auto-generated unique IDs using timestamps
- Pre-configured with sensible defaults and sample data
- Maintains proper JSON formatting

### User Experience

- Grouped dropdown with category headers
- Descriptive tooltips and help text
- Visual feedback for successful insertion
- Error handling with user-friendly messages

### Template Quality

- Each template includes realistic sample configurations
- Variable substitution examples (`${input.field}`, `${variables.name}`)
- Proper error handling and retry configurations
- Best practice examples for each node type

## Usage

### For Users

1. Open the workflow creation/edit dialog
2. Navigate to the "Workflow Configuration (JSON)" section
3. Use the "Insert Node Template" selector
4. Choose a node type from the grouped dropdown
5. Click "Insert Node" to add the template
6. Modify the inserted template as needed

### For Developers

#### Adding New Templates

```typescript
// Add to appropriate category method in NodeTemplateService
{
  id: 'new-node-template',
  name: 'New Node Type',
  type: 'new-node-type',
  category: 'action', // or trigger, logic, terminator
  description: 'Description of the node functionality',
  config: {
    // Node-specific configuration
    parameter1: 'default-value',
    parameter2: 42
  }
}
```

#### Using the Service

```typescript
import { NodeTemplateService } from '../services/NodeTemplateService';

// Get all templates by category
const allTemplates = NodeTemplateService.getAllTemplates();

// Get specific template
const template = NodeTemplateService.getTemplateByType('javascript-action');

// Create node instance
const node = NodeTemplateService.createNodeFromTemplate('http-action', 'custom-id');
```

## Technical Implementation

### NodeTemplateService Class

- Static methods for template management
- Type-safe interfaces with proper TypeScript types
- Comprehensive template definitions with sample data
- Utility methods for ID generation and node creation

### Template Structure

```typescript
interface NodeTemplate {
  id: string;
  name: string;
  type: string;
  category: 'trigger' | 'action' | 'logic' | 'terminator';
  description: string;
  config: Record<string, unknown>;
  position?: { x: number; y: number };
}
```

### Integration Points

- Seamlessly integrated into existing workflow dialog
- Maintains compatibility with existing workflow format
- No breaking changes to existing functionality
- Progressive enhancement approach

## Sample Templates

### JavaScript Action Template

```json
{
  "id": "process-data",
  "type": "javascript-action",
  "name": "Process Data",
  "config": {
    "script": "const result = input.value * 2;\nreturn { processed: true, result: result };",
    "timeout": 30000,
    "enableConsole": true,
    "allowAsync": true
  }
}
```

### HTTP Request Template

```json
{
  "id": "api-call",
  "type": "http-action",
  "name": "API Call",
  "config": {
    "url": "https://api.example.com/data",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer ${variables.apiToken}"
    },
    "timeout": 30000,
    "retryCount": 3
  }
}
```

## Testing

Comprehensive test suite covers:

- Template generation and uniqueness
- Category organization
- Template retrieval by type
- Node creation from templates
- Flat list generation for UI components

Run tests:

```bash
cd frontend && npm test -- --testPathPattern=NodeTemplateService.test.ts
```

## Future Enhancements

### Planned Features

- Custom template creation and saving
- Template sharing between users
- Advanced cursor position insertion
- Template validation and linting
- Template marketplace

### Possible Improvements

- Drag-and-drop template insertion
- Visual template preview
- Template search and filtering
- Template versioning
- Import/export functionality

## Best Practices

### Template Design

- Use realistic sample data
- Include helpful comments in scripts
- Provide sensible defaults
- Follow consistent naming conventions

### User Guidance

- Clear descriptions for each template
- Examples of variable substitution
- Error handling patterns
- Performance considerations

## Conclusion

The Node Template feature significantly enhances the workflow creation experience by providing:

- Reduced learning curve for new users
- Faster workflow development
- Consistent configuration patterns
- Comprehensive node type coverage
- Extensible architecture for future enhancements

This implementation follows Material-UI design patterns, maintains type safety with TypeScript, and integrates seamlessly with the existing codebase.
