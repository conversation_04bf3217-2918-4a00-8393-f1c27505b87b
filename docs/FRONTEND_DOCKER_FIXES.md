# Frontend Docker Build Fixes

Tento dokument popisuje opravy provedené pro řešení problé<PERSON>ů s frontend Docker build.

## Problém

Frontend Docker build selhal s chybou:
```
src/services/api/client.ts(5,12): error TS2580: Cannot find name 'process'. 
Do you need to install type definitions for node? Try `npm i --save-dev @types/node`.
```

## Příčina

Frontend kód používal `process.env` pro přístup k environment variables, ale:
1. `process` objekt není dostupný v browser prostředí
2. Vite používá `import.meta.env` místo `process.env`
3. Environment variables nebyly správně nakonfigurovány v Helm values

## Provedené opravy

### 1. Oprava frontend kódu

**Soubor:** `frontend/src/services/api/client.ts`

**Před:**
```typescript
baseURL: process.env.REACT_APP_API_URL || 'http://localhost:7001/api',
```

**Po:**
```typescript
baseURL: import.meta.env.VITE_API_URL || '/api',
```

### 2. Přidání VITE environment variables do Helm values

**Soubor:** `charts/ccczautomationmcpserver/values-dev.yaml`

```yaml
frontend:
  environment:
    # Vite Environment Variables
    VITE_API_URL: "/api"
    VITE_API_BASE_URL: "http://localhost:7002"
```

**Soubor:** `charts/ccczautomationmcpserver/values-prod.yaml`

```yaml
frontend:
  environment:
    # Vite Environment Variables
    VITE_API_URL: "/api"
    VITE_API_BASE_URL: "/api"
```

### 3. Zjednodušení frontend ConfigMap

**Soubor:** `charts/ccczautomationmcpserver/templates/frontend-configmap.yaml`

**Před:**
```yaml
data:
  VITE_API_URL: "{{ .Values.frontend.apiUrl | default "/api" }}"
  VITE_WS_URL: "{{ .Values.frontend.wsUrl | default "/socket.io" }}"
  {{- range $key, $value := .Values.frontend.environment }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
```

**Po:**
```yaml
data:
  {{- range $key, $value := .Values.frontend.environment }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
```

### 4. Odstranění duplicitních konfigurací

Odstraněny `apiUrl` a `wsUrl` z frontend konfigurace v values souborech, protože jsou nyní definovány přímo v `environment` sekci.

## Environment Variables

### Frontend používá tyto VITE_ variables:

1. **VITE_API_URL** - používá se v `frontend/src/services/api/client.ts`
   - Dev: `/api`
   - Prod: `/api`

2. **VITE_API_BASE_URL** - používá se v `frontend/src/services/api.ts`
   - Dev: `http://localhost:7002` (pro lokální development)
   - Prod: `/api` (proxy přes ingress)

## Rozdíly mezi Dev a Prod

| Variable | Development | Production | Důvod |
|----------|-------------|------------|-------|
| `VITE_API_BASE_URL` | `http://localhost:7002` | `/api` | Dev používá přímé připojení, prod používá ingress proxy |

## Vite vs React Environment Variables

| Framework | Prefix | Příklad |
|-----------|--------|---------|
| Create React App | `REACT_APP_` | `REACT_APP_API_URL` |
| Vite | `VITE_` | `VITE_API_URL` |

## Ověření oprav

Po těchto opravách by měl frontend Docker build projít úspěšně:

1. ✅ TypeScript kompilace bez chyb
2. ✅ Správné environment variables v runtime
3. ✅ Funkční API komunikace

## Další kroky

1. Testování frontend build procesu
2. Ověření API komunikace v Kubernetes prostředí
3. Kontrola ingress routing pro frontend/backend komunikaci

## Poznámky

- Vite automaticky nahrazuje `import.meta.env.VITE_*` variables během build procesu
- Environment variables musí začínat prefixem `VITE_` aby byly dostupné v browser kódu
- `vite-env.d.ts` soubor poskytuje TypeScript typy pro `import.meta.env`
