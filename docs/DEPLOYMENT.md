# Deployment Guide

Tento dokument popisuje, jak nasadit aplikaci do Kubernetes pomoc<PERSON> chartů a GitLab CI/CD.

## Architektura

Aplikace se skládá z následujících komponent:

- **Backend (Node.js)**: MCP server na portu 3000
- **Frontend (React/Vite)**: Webová aplikace servírovaná přes nginx na portu 80
- **PostgreSQL**: Databáze na portu 5432
- **Redis**: Cache na portu 6379

## GitLab CI/CD Pipeline

Pipeline obsahuje následující stages:

### Test Stage (Pull Requests)

- `backend typecheck`: TypeScript kontrola pro backend
- `frontend typecheck`: TypeScript kontrola pro frontend
- `backend lint`: ESLint kontrola pro backend
- `frontend lint`: ESLint kontrola pro frontend
- `backend test`: Unit testy pro backend

### Deploy Stage (Main branch)

- `create release`: Semantic release pro automatické verzování
- `build backend docker image and push`: Build a push backend Docker image
- `build frontend docker image and push`: Build a push frontend Docker image

## Docker Images

### Backend

- **Dockerfile**: `./Dockerfile`
- **Image**: `network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-dev:VERSION`
- **Port**: 3000

### Frontend

- **Dockerfile**: `./frontend/Dockerfile`
- **Image**: `network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-dev-frontend:VERSION`
- **Port**: 80 (nginx)

## Helm Chart

Chart se nachází v `charts/ccczautomationmcpserver/` a obsahuje:

### Templates

- `deployment.yaml`: Backend deployment
- `frontend-deployment.yaml`: Frontend deployment
- `service.yaml`: Backend service
- `frontend-service.yaml`: Frontend service
- `ingress.yaml`: Ingress s routing
- `ConfigMap.env.yaml`: Backend environment variables
- `frontend-configmap.yaml`: Frontend environment variables
- `postgresql.yaml`: PostgreSQL deployment
- `redis.yaml`: Redis deployment

### Values Files

- `values-dev.yaml`: Development konfigurace
- `values-prod.yaml`: Production konfigurace

## Ingress Routing

Ingress směruje požadavky následovně:

- `/api/*` → Backend service (port 3000)
- `/socket.io/*` → Backend service (port 3000)
- `/health` → Backend service (port 3000)
- `/*` → Frontend service (port 80)

## Environment Variables

### Backend

Konfigurováno přes `ConfigMap.env.yaml`:

- `NODE_ENV` - Environment (development/production)
- `PORT` - MCP server port (3000)
- `APPLICATION_NAME` - Application name
- `HOST` - Server host (0.0.0.0)
- `API_PORT` - Admin API port (7001)
- `LOG_LEVEL` - Logging level (debug/info)

**Database Configuration:**

- `DB_HOST` - PostgreSQL host
- `DB_PORT` - PostgreSQL port (5432)
- `DB_USERNAME` - Database username
- `DB_PASSWORD` - Database password
- `DB_DATABASE` - Database name
- `DB_MIGRATIONS_RUN` - Run migrations on startup

**Redis Configuration:**

- `REDIS_HOST` - Redis host
- `REDIS_PORT` - Redis port (6379)
- `REDIS_PASSWORD` - Redis password

**Workflow Configuration:**

- `USE_REDIS_WORKFLOW_MEMORY` - Use Redis for workflow memory
- `WORKFLOW_CONTEXT_TTL` - Workflow context TTL in seconds

**Data Management:**

- `DATA_RETENTION_DAYS` - Data retention period
- `DATA_CLEANUP_INTERVAL_MINUTES` - Cleanup interval
- `DATA_CLEANUP_BATCH_SIZE` - Cleanup batch size

**Authentication:**

- `JWT_SECRET` - JWT secret key
- `JWT_EXPIRES_IN` - JWT expiration time
- `ADMIN_PORT` - Admin API port (7002)

**LiteLLM Integration:**

- `LITELLM_API_KEY` - LiteLLM API key
- `LITELLM_BASE_URL` - LiteLLM base URL

### Frontend

Konfigurováno přes `frontend-configmap.yaml`:

- `REACT_APP_API_URL`
- `REACT_APP_WS_URL`

## Deployment Process

### Automatický deployment (doporučeno)

1. **Commit do main branch** spustí semantic release
2. **Semantic release** vytvoří nový tag a verzi
3. **Automatická aktualizace** Helm values souborů s novou verzí
4. **Docker build** vytvoří a pushne oba images s novou verzí:
   - Backend: `${CI_REGISTRY_IMAGE}:${VERSION}`
     - Příklad: `network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:1.0.1`
   - Frontend: `${CI_REGISTRY_IMAGE}:frontend-${VERSION}`
     - Příklad: `network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:frontend-1.0.1`
5. **Manual trigger** pro Helm deployment v GitLab UI

### Semantic Release

Semantic release automaticky:

- Analyzuje commit messages (conventional commits)
- Generuje novou verzi podle změn
- Vytvoří CHANGELOG.md
- Aktualizuje Helm values soubory
- Vytvoří Git tag
- Pushne změny zpět do repository

### Conventional Commits

Pro správné fungování semantic release používejte conventional commits:

- `feat:` - nová funkcionalita (minor version)
- `fix:` - oprava chyby (patch version)
- `feat!:` nebo `BREAKING CHANGE:` - breaking change (major version)
- `chore:`, `docs:`, `style:`, `refactor:`, `test:` - bez změny verze

Příklady:

```
feat: add user authentication
fix: resolve database connection issue
feat!: change API response format
```

## Manual Deployment

```bash
# Build images
docker build -t backend:VERSION .
docker build -t frontend:VERSION ./frontend

# Deploy with Helm
helm upgrade --install myapp ./charts/ccczautomationmcpserver \
  --values ./charts/ccczautomationmcpserver/values-dev.yaml \
  --set image.tag=VERSION \
  --set frontend.image.tag=VERSION
```

## Monitoring

- Backend health check: `/health`
- Frontend health check: `/health`
- Prometheus metrics: Backend exportuje metriky
- Alerting: Konfigurováno v `PrometheusRule.yaml`

## Troubleshooting

### Logs

```bash
# Backend logs
kubectl logs -l app=mcp

# Frontend logs
kubectl logs -l app=frontend

# Database logs
kubectl logs -l app=postgresql
```

### Common Issues

1. **Image pull errors**: Zkontrolujte GitLab registry credentials
2. **Database connection**: Zkontrolujte PostgreSQL service a credentials
3. **Frontend routing**: Zkontrolujte ingress konfiguraci
