# REST API Trigger Authentication Analysis

## Problem Summary

The user is experiencing a 401 Unauthorized error when calling `http://localhost:3000/api/process-message` even though they have configured `authentication: false` in their REST API trigger node. This document explains why this happens and provides solutions.

## Root Cause

The issue occurs because there are **two separate authentication layers**:

1. **Server-level authentication** (Express middleware)
2. **Node-level authentication** (REST API trigger configuration)

### 1. Server-Level Authentication

Looking at the code structure:

- The application runs on port 3000 (API server) and port 7002 (Admin server)
- All routes under `/api` and `/admin` are protected by authentication middleware
- The middleware is applied globally in `src/api/routes/index.ts` and `src/api/routes/adminRoutes.ts`

```typescript
// src/api/middleware/authMiddleware.ts
export const authMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // Skip authentication in development mode
  if (process.env.NODE_ENV === 'development') {
    // Auto-authenticates as dev user
    next();
    return;
  }

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json({
      success: false,
      error: 'Access denied. No token provided.'
    });
    return;
  }
  // ... JWT validation
};
```

### 2. Node-Level Authentication

The `authentication: false` setting in the REST API trigger node only affects the node's internal validation:

```typescript
// src/workflow/nodes/trigger/RestAPITriggerNode.ts
if (this.config.authentication) {
  await this.authenticateRequest(requestData);
}
```

This check happens **after** the request has already passed through the server middleware.

## Current Workflow Endpoints

Based on the routing analysis, workflows with REST API triggers are accessed through:

1. **Admin route**: `/admin/workflows/:id/trigger` (port 7002)
2. **API route**: `/api/workflows/:id/trigger` (port 7002)

Both endpoints are protected by authentication middleware.

## Why `/api/process-message` Returns 401

The endpoint `/api/process-message` doesn't exist in the routing configuration. The application returns 401 because:

1. The request hits the API server on port 3000
2. No route matches `/api/process-message`
3. The middleware sees it's under `/api/*` and requires authentication
4. Without a valid token, it returns 401

## Solutions

### Solution 1: Use Development Mode (Quick Fix)

Set the environment variable to skip authentication:

```bash
NODE_ENV=development npm start
```

This will auto-authenticate all requests as a development user.

### Solution 2: Use the Correct Endpoint

Call the workflow trigger endpoint with the workflow ID:

```bash
# First, find your workflow ID
# Then call:
curl -X POST http://localhost:7002/api/workflows/{workflow-id}/trigger \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {your-jwt-token}" \
  -d '{"message": "ahojky"}'
```

### Solution 3: Create a Public Endpoint (Recommended)

To create truly public REST API endpoints for workflows, you need to:

1. **Create a new public routes file** (`src/api/routes/publicRoutes.ts`):

```typescript
import { Router } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../types';
import { IWorkflowEngine } from '../../core/interfaces/IWorkflowEngine';
import { IRepository } from '../../core/interfaces/IRepository';
import { Workflow } from '../../infrastructure/database/entities/Workflow.entity';

export const createPublicRoutes = (container: Container): Router => {
  const router = Router();
  const workflowEngine = container.get<IWorkflowEngine>(TYPES.WorkflowEngine);
  const workflowRepository = container.get<IRepository<Workflow>>(TYPES.WorkflowRepository);

  // Dynamic workflow REST API endpoints
  router.all('/*', async (req, res) => {
    try {
      // Find workflow by REST API path
      const workflows = await workflowRepository.findAll();
      const workflow = workflows.find((w) => {
        const trigger = w.config.nodes?.find((n) => n.type === 'rest-api-trigger');
        return (
          trigger?.config?.path === req.path &&
          trigger?.config?.method?.toUpperCase() === req.method.toUpperCase()
        );
      });

      if (!workflow) {
        return res.status(404).json({ error: 'Endpoint not found' });
      }

      // Check if authentication is required
      const trigger = workflow.config.nodes.find((n) => n.type === 'rest-api-trigger');
      if (trigger?.config?.authentication) {
        // Perform authentication check
        const authHeader = req.headers.authorization;
        if (!authHeader) {
          return res.status(401).json({ error: 'Authentication required' });
        }
      }

      // Execute workflow
      const input = {
        method: req.method,
        path: req.path,
        headers: req.headers,
        query: req.query,
        body: req.body,
        params: req.params
      };

      const result = await workflowEngine.executeWorkflow(workflow.id, input);

      // Return result based on workflow output
      if (result && typeof result === 'object' && result.statusCode) {
        res.status(result.statusCode).json(result.body || result);
      } else {
        res.status(200).json(result);
      }
    } catch (error) {
      console.error('Public endpoint error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  return router;
};
```

2. **Update the admin server** (`src/api/adminServer.ts`) to mount public routes:

```typescript
// Add this line in setupRoutes() method, BEFORE the auth middleware routes
this.app.use('/api', createPublicRoutes(this.container));

// Then add the authenticated routes
this.app.use('/auth', createAuthRoutes(this.container));
this.app.use('/admin', createDashboardRoutes(this.container));
this.app.use('/api/admin', createAdminRoutes(this.container));
```

### Solution 4: Use Optional Authentication

Modify the workflow routes to use optional authentication for trigger endpoints:

```typescript
// In src/api/routes/workflowRoutes.ts
import { optionalAuthMiddleware } from '../middleware/authMiddleware';

// Change the trigger endpoint to use optional auth
router.post('/:id/trigger', optionalAuthMiddleware, (req, res) => controller.trigger(req, res));
```

## Testing Your Workflow

Once you've implemented one of the solutions above, you can test your workflow:

```bash
# For public endpoint (Solution 3)
curl -X POST http://localhost:7002/api/process-message \
  -H "Content-Type: application/json" \
  -d '{"message": "ahojky"}'

# For workflow trigger endpoint (Solution 2)
curl -X POST http://localhost:7002/api/workflows/{workflow-id}/trigger \
  -H "Content-Type: application/json" \
  -d '{"message": "ahojky"}'
```

## Best Practices

1. **Security**: Only disable authentication for truly public endpoints
2. **Validation**: Always validate input data in your workflow nodes
3. **Rate Limiting**: Consider adding rate limiting for public endpoints
4. **Logging**: Log all requests to public endpoints for security monitoring
5. **Error Handling**: Return appropriate error messages without exposing internal details

## Summary

The `authentication: false` setting in the REST API trigger node configuration does not bypass server-level authentication. To create public REST API endpoints, you need to either:

1. Use development mode for testing
2. Implement a public routes handler that bypasses authentication middleware
3. Use optional authentication middleware for specific endpoints
4. Provide proper JWT tokens when calling protected endpoints

The recommended approach is Solution 3, which creates a dedicated public API handler that respects the workflow's authentication configuration.
