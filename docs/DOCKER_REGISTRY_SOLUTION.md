# Docker Registry Solution - Dva Images v Jednom Repository

Tento dokument popisuje řešení pro hostování dvou různých Docker images (backend a frontend) ve stejném GitLab registry.

## Problém

- Máme jeden GitLab repository pro celý projekt
- Potřebujeme dva různé Docker images: backend a frontend
- GitLab registry je sdílený s Git repository
- Frontend build selhal kvůli nesprávnému názvu image při push

## Řešení: Tag-based Naming Convention

GitLab registry umožňuje pouze jeden image name per repository. Řešením je použití různých tagů pro rozlišení komponent:

### Backend Image

```
${CI_REGISTRY_IMAGE}:${VERSION}
```

**Příklad:**

```
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:1.0.1
```

### Frontend Image

```
${CI_REGISTRY_IMAGE}:frontend-${VERSION}
```

**Příklad:**

```
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:frontend-1.0.1
```

## Oprava GitLab CI/CD

### Problém v pipeline

**Před opravou:**

```yaml
script:
  - docker build --pull -t ${CI_REGISTRY_IMAGE}-frontend:${CURRENT_VERSION_ID} -f frontend/Dockerfile ./frontend
  - docker push ${CI_REGISTRY_IMAGE}-frontend:${CURRENT_VERSION_ID} # ❌ Nový repository!
  - docker rmi ${CI_REGISTRY_IMAGE}-frontend:${CURRENT_VERSION_ID} # ❌ Nový repository!
```

**Po opravě:**

```yaml
script:
  - docker build --pull -t ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID} -f frontend/Dockerfile ./frontend
  - docker push ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID} # ✅ Stejný repository, jiný tag!
  - docker rmi ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID} # ✅ Stejný repository, jiný tag!
```

### Chyba

Frontend build vytvořil image s názvem:

```
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-frontend:1.0.1
```

Ale pokusil se pushovat image s názvem:

```
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:1.0.1
```

Což způsobilo chybu:

```
An image does not exist locally with the tag: network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver
```

## Helm Chart Konfigurace

### Backend Deployment

```yaml
image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
```

### Frontend Deployment

```yaml
image: {{ .Values.frontend.image.repository }}:{{ .Values.frontend.image.tag }}
```

### Values Configuration

**values-dev.yaml:**

```yaml
image:
  repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-dev'
  tag: '1.0.1'

frontend:
  image:
    repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-dev-frontend'
    tag: '1.0.1'
```

**values-prod.yaml:**

```yaml
image:
  repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver'
  tag: '1.0.1'

frontend:
  image:
    repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-frontend'
    tag: '1.0.1'
```

## Alternativní řešení (pro budoucnost)

### 1. Separate Repositories

- Vytvořit samostatné Git repositories pro backend a frontend
- Každý má vlastní GitLab registry
- Složitější správa, ale čistší separace

### 2. Multi-stage Dockerfile

- Jeden Dockerfile s více stages
- Build oba images v jednom procesu
- Složitější konfigurace

### 3. GitLab Package Registry

- Použít GitLab Package Registry místo Container Registry
- Umožňuje více typů artifacts v jednom repository

## Výhody současného řešení

✅ **Jednoduché**: Minimální změny v existující struktuře
✅ **Konzistentní**: Stejné verzování pro oba images
✅ **Přehledné**: Jasné rozlišení pomocí suffixu
✅ **Kompatibilní**: Funguje s existujícím GitLab registry

## Testování

Po opravě by měl frontend build projít úspěšně:

1. ✅ Docker build vytvoří image s názvem `...-frontend:VERSION`
2. ✅ Docker push pushne image se správným názvem
3. ✅ Docker rmi odstraní image se správným názvem
4. ✅ Helm deployment použije správný image název

## Monitoring

V GitLab Container Registry by měly být viditelné oba images:

- `aidcc-ccczautomationmcpserver:1.0.1`
- `aidcc-ccczautomationmcpserver-frontend:1.0.1`

## Závěr

Suffix `-frontend` je nejjednodušší a nejefektivnější způsob, jak rozlišit dva images ve stejném GitLab registry. Řešení je škálovatelné a umožňuje přidat další komponenty v budoucnosti (např. `-worker`, `-api`, atd.).
