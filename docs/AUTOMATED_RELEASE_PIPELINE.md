# Automated Release Pipeline

Tento dokument popisuje automatizaci celého release procesu od commit do main branch až po Docker build.

## Problém

**Před opravou:**

1. Push do main → semantic release vytvoří tag
2. Release commit má `[skip ci]` → pipeline se nespustí
3. <PERSON>er build jobs musí být spuštěny manuálně
4. Možnost použití staré verze při manuálním spuštění

## Řešení

### 1. Odstranění `[skip ci]` z release commit

**`.releaserc.json`:**

```json
{
  "message": "chore(release): ${nextRelease.version}\n\n${nextRelease.notes}"
}
```

**Před:** `chore(release): 1.0.5 [skip ci]`
**Po:** `chore(release): 1.0.5`

### 2. Automatick<PERSON> spuštění Docker builds

**GitLab CI/CD rules:**

```yaml
rules:
  - if: '$CI_COMMIT_TAG'
    when: always
  - if: '$CI_COMMIT_MESSAGE =~ /^chore\(release\):/'
    when: always
  - if: '$CI_COMMIT_REF_NAME == "main"'
    when: manual
```

### 3. Job dependencies

```yaml
needs:
  - job: 'create release'
    optional: true
```

## Nový automatizovaný workflow

### 1. Developer push

```bash
git commit -m "feat: add new feature"
git push origin main
```

### 2. Automatický release (create release job)

- Semantic release analyzuje commits
- Generuje novou verzi (např. 1.0.5)
- Aktualizuje soubory:
  - `version`: `1.0.5`
  - `package.json`: `"version": "1.0.5"`
  - Helm values: `tag: '1.0.5'`
  - Chart.yaml: `version: 1.0.5, appVersion: 1.0.5`
- Vytvoří commit: `chore(release): 1.0.5`
- Vytvoří Git tag: `v1.0.5`

### 3. Automatický Docker build

**Spustí se automaticky kvůli:**

- Commit message: `chore(release): 1.0.5`
- Git tag: `v1.0.5`

**Backend build:**

```bash
CURRENT_VERSION_ID="$(<version)"  # 1.0.5
docker build -t registry:1.0.5 .
docker push registry:1.0.5
```

**Frontend build:**

```bash
CURRENT_VERSION_ID="$(<version)"  # 1.0.5
docker build -t registry:frontend-1.0.5 ./frontend
docker push registry:frontend-1.0.5
```

## Pipeline stages

```
┌─────────────────┐
│   Push to main  │
└─────────┬───────┘
          │
┌─────────▼───────┐
│  create release │ (automatic)
│  - semantic     │
│  - update files │
│  - create tag   │
└─────────┬───────┘
          │
    ┌─────▼─────┐    ┌─────────────┐
    │  backend  │    │  frontend   │
    │   build   │    │    build    │ (automatic)
    │           │    │             │
    └───────────┘    └─────────────┘
```

## Trigger conditions

### Create release job

```yaml
rules:
  - if: '$CI_COMMIT_MESSAGE =~ /\[release\]/'
    when: never
  - if: '$CI_COMMIT_REF_NAME == "main"'
    when: always
```

### Docker build jobs

```yaml
rules:
  - if: '$CI_COMMIT_TAG' # Git tag trigger ONLY
    when: always
  - if: '$CI_COMMIT_REF_NAME == "main"' # Manual fallback
    when: manual
```

## Výhody automatizace

### ✅ Konzistence

- Vždy se použije správná verze ze souboru `version`
- Žádné manuální chyby při zadávání verze

### ✅ Rychlost

- Celý proces od commit do Docker images je automatický
- Žádné čekání na manuální spuštění

### ✅ Spolehlivost

- Dependency mezi jobs zajišťuje správné pořadí
- Optional dependency nevyžaduje úspěšný release job

### ✅ Transparentnost

- Jasné commit messages pro release
- Viditelné Git tagy pro každou verzi

## Monitoring

### GitLab Pipeline view

```
create release    ✅ (automatic)
├── backend build ✅ (automatic)
└── frontend build ✅ (automatic)
```

### Git history

```
* chore(release): 1.0.5 (tag: v1.0.5)
* feat: add new feature
* fix: resolve bug
```

### Container Registry

```
aidcc-ccczautomationmcpserver
├── 1.0.5 (backend)
├── frontend-1.0.5 (frontend)
├── 1.0.4 (backend)
└── frontend-1.0.4 (frontend)
```

## Troubleshooting

### Problém: Docker build se nespustí

```bash
# Zkontrolujte commit message
git log --oneline -1

# Zkontrolujte Git tag
git tag --list | tail -5

# Zkontrolujte pipeline rules
```

### Problém: Špatná verze v Docker build

```bash
# Zkontrolujte version soubor
cat version

# Zkontrolujte dependency
needs:
  - job: "create release"
    optional: true
```

### Problém: Release job selhává

```bash
# Zkontrolujte semantic release logs
yarn semantic-release --dry-run

# Zkontrolujte conventional commits
git log --oneline -10
```

## Závěr

Automatizace odstraňuje manuální kroky a zajišťuje konzistentní deployment proces. Po push do main branch se automaticky:

1. ✅ Vytvoří nová verze
2. ✅ Aktualizují se všechny soubory
3. ✅ Vytvoří se Git tag
4. ✅ Buildují se Docker images
5. ✅ Pushnou se do registry

Celý proces je nyní plně automatizovaný a spolehlivý.
