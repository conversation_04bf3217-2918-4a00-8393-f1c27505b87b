# Manual Workflow Creation Guide

This guide provides comprehensive documentation for manually creating workflows in the MCP Automation Server system. It covers all supported node types, their configuration schemas, and examples for building workflows programmatically.

## Table of Contents

1. [Workflow Structure](#workflow-structure)
2. [Node Palette Reference](#node-palette-reference)
3. [Trigger Nodes](#trigger-nodes)
4. [Action Nodes](#action-nodes)
5. [Logic Nodes](#logic-nodes)
6. [Terminator Nodes](#terminator-nodes)
7. [Complete Workflow Examples](#complete-workflow-examples)
8. [Best Practices](#best-practices)

## Workflow Structure

A workflow consists of the following main components:

### Basic Workflow Schema

```json
{
  "id": "unique-workflow-id",
  "name": "Workflow Name",
  "description": "Description of what the workflow does",
  "version": "1.0.0",
  "enabled": true,
  "tags": ["tag1", "tag2"],
  "input_schema": {
    "type": "object",
    "properties": {
      "param1": {
        "type": "string",
        "description": "Parameter description"
      }
    },
    "required": ["param1"]
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "result": {
        "type": "object",
        "description": "Output result"
      }
    }
  },
  "nodes_config": {
    "nodes": [],
    "edges": []
  },
  "retry_config": {
    "maxRetries": 3,
    "retryDelay": 1000,
    "retryBackoffMultiplier": 2,
    "retryableErrors": ["NetworkError", "TimeoutError"]
  },
  "observability_config": {
    "logLevel": "info",
    "enableMetrics": true,
    "enableTracing": true
  }
}
```

### Node Structure

Each node in the workflow has the following structure:

```json
{
  "id": "unique-node-id",
  "type": "node-type",
  "name": "Node Display Name",
  "position": { "x": 100, "y": 100 },
  "config": {
    // Node-specific configuration
  }
}
```

### Edge Structure

Edges define the connections between nodes:

```json
{
  "id": "unique-edge-id",
  "source": "source-node-id",
  "target": "target-node-id",
  "type": "default"
}
```

## Node Palette Reference

The following node types are available in the system:

### Trigger Nodes

- [`rest-api-trigger`](#rest-api-trigger) - HTTP REST API endpoints
- [`mcp-function-trigger`](#mcp-function-trigger) - MCP function calls
- [`timer-trigger`](#timer-trigger) - Time-based scheduling
- [`webhook-trigger`](#webhook-trigger) - Webhook endpoints

### Action Nodes

- [`javascript-action`](#javascript-action) - Custom JavaScript execution
- [`http-action`](#http-action) - HTTP requests to external APIs
- [`sql-action`](#sql-action) - Database operations
- [`redis-action`](#redis-action) - Redis operations
- [`litellm-action`](#litellm-action) - LLM integrations

### Logic Nodes

- [`if-else-logic`](#if-else-logic) - Conditional branching
- [`switch-logic`](#switch-logic) - Multi-branch switching
- [`merge-logic`](#merge-logic) - Data merging
- [`loop-logic`](#loop-logic) - Iterative processing

### Terminator Nodes

- [`response-terminator`](#response-terminator) - Standard response
- [`mcp-response-terminator`](#mcp-response-terminator) - MCP protocol response
- [`error-terminator`](#error-terminator) - Error handling

## Trigger Nodes

### rest-api-trigger

Exposes workflow as a REST API endpoint.

**Configuration Schema:**

```json
{
  "method": "POST",
  "path": "/api/custom-endpoint",
  "authentication": true,
  "validation": true,
  "headers": {
    "Content-Type": "application/json"
  },
  "queryParams": {
    "version": "v1"
  },
  "bodySchema": {
    "type": "object",
    "properties": {
      "data": { "type": "string" }
    }
  }
}
```

**Properties:**

- `method` (string, required): HTTP method (GET, POST, PUT, DELETE, PATCH)
- `path` (string, required): URL path (must start with "/")
- `authentication` (boolean): Enable authentication
- `validation` (boolean): Enable request validation
- `headers` (object): Default headers
- `queryParams` (object): Expected query parameters
- `bodySchema` (object): JSON schema for request body

**Example:**

```json
{
  "id": "api-trigger",
  "type": "rest-api-trigger",
  "name": "REST API Trigger",
  "position": { "x": 100, "y": 100 },
  "config": {
    "method": "POST",
    "path": "/api/process-data",
    "authentication": false,
    "validation": true,
    "bodySchema": {
      "type": "object",
      "properties": {
        "payload": { "type": "object" },
        "metadata": { "type": "object" }
      },
      "required": ["payload"]
    }
  }
}
```

### mcp-function-trigger

Triggers workflow via MCP function calls.

**Configuration Schema:**

```json
{
  "functionName": "processData",
  "description": "Process data using workflow",
  "inputValidation": true,
  "outputTransformation": true
}
```

**Properties:**

- `functionName` (string, required): MCP function name
- `description` (string): Function description
- `inputValidation` (boolean): Enable input validation
- `outputTransformation` (boolean): Transform output format

### timer-trigger

Schedules workflow execution based on time.

**Configuration Schema:**

```json
{
  "schedule": "0 */5 * * * *",
  "timezone": "UTC",
  "enabled": true,
  "maxExecutions": 100,
  "payload": {
    "type": "scheduled"
  }
}
```

**Properties:**

- `schedule` (string, required): Cron expression
- `timezone` (string): Timezone for scheduling
- `enabled` (boolean): Enable/disable scheduler
- `maxExecutions` (number): Maximum executions
- `payload` (object): Default payload data

### webhook-trigger

Receives webhook calls from external systems.

**Configuration Schema:**

```json
{
  "path": "/webhook/github",
  "method": "POST",
  "secretValidation": true,
  "secret": "webhook-secret",
  "headers": {
    "X-Hub-Signature": "signature"
  }
}
```

**Properties:**

- `path` (string, required): Webhook path
- `method` (string): HTTP method
- `secretValidation` (boolean): Enable secret validation
- `secret` (string): Webhook secret
- `headers` (object): Expected headers

## Action Nodes

### javascript-action

Executes custom JavaScript code with full context access.

**Configuration Schema:**

```json
{
  "script": "const result = input.data * 2; return { doubled: result };",
  "timeout": 30000,
  "enableConsole": true,
  "allowAsync": true,
  "contextVariables": {
    "apiKey": "{{API_KEY}}"
  }
}
```

**Properties:**

- `script` (string, required): JavaScript code to execute
- `timeout` (number): Execution timeout in milliseconds
- `enableConsole` (boolean): Allow console.log output
- `allowAsync` (boolean): Support async/await
- `contextVariables` (object): Additional context variables

**Available Context Objects:**

- `input` - Input data from previous node (direct access)
- `workflowContext.variables` - Workflow variables
- `workflowContext.nodeResults` - Results from all previous nodes
- `context.workflowId` - Current workflow ID
- `context.executionId` - Current execution ID

**Example:**

```json
{
  "id": "data-processor",
  "type": "javascript-action",
  "name": "Process Data",
  "position": { "x": 300, "y": 100 },
  "config": {
    "script": `
// Extract and validate input
const { payload, metadata } = input;

console.log('Processing payload:', payload);

// Transform data
const result = {
  id: payload.id || generateId(),
  processedData: {
    ...payload,
    processedAt: new Date().toISOString(),
    version: metadata?.version || '1.0'
  },
  status: 'processed'
};

// Store in context for next nodes
workflowContext.variables.processedResult = result;

return result;
    `,
    "timeout": 10000,
    "enableConsole": true,
    "allowAsync": false
  }
}
```

### http-action

Makes HTTP requests to external APIs with full configuration options.

**Configuration Schema:**

```json
{
  "url": "https://api.example.com/data",
  "method": "POST",
  "headers": {
    "Authorization": "Bearer ${token}",
    "Content-Type": "application/json"
  },
  "body": {
    "data": "${input.payload}"
  },
  "timeout": 30000,
  "followRedirects": true,
  "validateSSL": true,
  "retryCount": 3,
  "retryDelay": 1000,
  "returnFullResponse": false,
  "failOnError": true
}
```

**Properties:**

- `url` (string, required): Target URL
- `method` (string, required): HTTP method
- `headers` (object): Request headers
- `body` (string|object): Request body
- `timeout` (number): Request timeout
- `followRedirects` (boolean): Follow redirects
- `validateSSL` (boolean): Validate SSL certificates
- `retryCount` (number): Number of retries
- `retryDelay` (number): Delay between retries
- `returnFullResponse` (boolean): Return full response object
- `failOnError` (boolean): Fail on HTTP error codes

**Variable Substitution:**
Use `${variable}` syntax to substitute values from context:

- `${input.field}` - From input data
- `${variables.name}` - From context variables
- `${nodeResults.nodeId.result}` - From previous node results

**Example:**

```json
{
  "id": "api-call",
  "type": "http-action",
  "name": "Call External API",
  "position": { "x": 500, "y": 100 },
  "config": {
    "url": "https://api.service.com/process",
    "method": "POST",
    "headers": {
      "Authorization": "Bearer ${variables.apiToken}",
      "Content-Type": "application/json",
      "X-Request-ID": "${executionId}"
    },
    "body": {
      "payload": "${input.processedData}",
      "metadata": {
        "source": "workflow",
        "timestamp": "${variables.timestamp}"
      }
    },
    "timeout": 15000,
    "retryCount": 2,
    "retryDelay": 2000,
    "returnFullResponse": true
  }
}
```

### sql-action

Executes SQL queries against the database.

**Configuration Schema:**

```json
{
  "query": "SELECT * FROM users WHERE id = $1",
  "parameters": ["${input.userId}"],
  "queryType": "SELECT",
  "timeout": 30000,
  "transaction": false,
  "resultFormat": "rows"
}
```

**Properties:**

- `query` (string, required): SQL query
- `parameters` (array): Query parameters
- `queryType` (string): Query type (SELECT, INSERT, UPDATE, DELETE)
- `timeout` (number): Query timeout
- `transaction` (boolean): Use transaction
- `resultFormat` (string): Result format (rows, single, count)

### redis-action

Performs Redis operations.

**Configuration Schema:**

```json
{
  "operation": "SET",
  "key": "${input.cacheKey}",
  "value": "${input.data}",
  "expiration": 3600,
  "options": {
    "nx": true
  }
}
```

**Properties:**

- `operation` (string, required): Redis operation (GET, SET, DEL, etc.)
- `key` (string, required): Redis key
- `value` (any): Value for SET operations
- `expiration` (number): TTL in seconds
- `options` (object): Additional Redis options

### litellm-action

Integrates with Large Language Models via LiteLLM.

**Configuration Schema:**

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "Process this data: ${input.text}"
    }
  ],
  "temperature": 0.7,
  "maxTokens": 1000,
  "apiKey": "${variables.openaiKey}"
}
```

**Properties:**

- `model` (string, required): LLM model name
- `messages` (array, required): Chat messages
- `temperature` (number): Response creativity
- `maxTokens` (number): Maximum response tokens
- `apiKey` (string): API key for the service

## Logic Nodes

### if-else-logic

Conditional branching based on expressions.

**Configuration Schema:**

```json
{
  "condition": "input.status == 'active'",
  "trueOutput": {
    "result": "activated",
    "data": "${input}"
  },
  "falseOutput": {
    "result": "inactive",
    "reason": "status not active"
  },
  "conditionType": "simple",
  "variables": {
    "threshold": 100
  }
}
```

**Properties:**

- `condition` (string, required): Condition expression
- `trueOutput` (any, required): Output when condition is true
- `falseOutput` (any, required): Output when condition is false
- `conditionType` (string): Condition type (simple, expression, javascript)
- `variables` (object): Additional variables for condition

**Condition Types:**

1. **Simple**: Basic comparisons

   - `input.value > 10`
   - `status == 'active'`
   - `variables.count >= threshold`

2. **Expression**: Advanced expressions

   - `input.price * 1.2 > variables.maxPrice`
   - `utils.length(input.items) > 0`

3. **JavaScript**: Full JavaScript expressions
   - `input.items.some(item => item.category === 'premium')`
   - `new Date(input.date) > new Date()`

**Available Utility Functions:**

- `utils.isNull(value)`, `utils.isEmpty(value)`
- `utils.isArray(value)`, `utils.isObject(value)`
- `utils.length(value)`, `utils.includes(array, item)`
- `utils.startsWith(str, prefix)`, `utils.endsWith(str, suffix)`
- `utils.regex(str, pattern, flags)`

**Example:**

```json
{
  "id": "status-check",
  "type": "if-else-logic",
  "name": "Check Status",
  "position": { "x": 400, "y": 200 },
  "config": {
    "condition": "input.priority == 'high' && input.amount > variables.threshold",
    "trueOutput": {
      "action": "process_immediately",
      "priority": "urgent",
      "data": "${input}"
    },
    "falseOutput": {
      "action": "queue_normal",
      "priority": "normal",
      "data": "${input}"
    },
    "conditionType": "simple",
    "variables": {
      "threshold": 1000
    }
  }
}
```

### switch-logic

Multi-branch switching based on values.

**Configuration Schema:**

```json
{
  "switchValue": "${input.type}",
  "cases": {
    "user": {
      "action": "process_user",
      "template": "user_template"
    },
    "order": {
      "action": "process_order",
      "template": "order_template"
    }
  },
  "defaultCase": {
    "action": "process_default",
    "template": "default_template"
  }
}
```

**Properties:**

- `switchValue` (string, required): Value to switch on
- `cases` (object, required): Case mappings
- `defaultCase` (any): Default case when no match

### merge-logic

Merges data from multiple sources.

**Configuration Schema:**

```json
{
  "mergeStrategy": "deep",
  "sources": ["${nodeResults.node1}", "${nodeResults.node2}", "${input}"],
  "conflictResolution": "last_wins",
  "outputFormat": "object"
}
```

**Properties:**

- `mergeStrategy` (string): Merge strategy (shallow, deep)
- `sources` (array, required): Data sources to merge
- `conflictResolution` (string): How to resolve conflicts
- `outputFormat` (string): Output format

### loop-logic

Iterative processing over collections.

**Configuration Schema:**

```json
{
  "iterable": "${input.items}",
  "itemVariable": "currentItem",
  "indexVariable": "currentIndex",
  "condition": "currentIndex < 10",
  "maxIterations": 100,
  "collectResults": true
}
```

**Properties:**

- `iterable` (string, required): Collection to iterate over
- `itemVariable` (string): Variable name for current item
- `indexVariable` (string): Variable name for current index
- `condition` (string): Continue condition
- `maxIterations` (number): Maximum iterations
- `collectResults` (boolean): Collect all iteration results

## Terminator Nodes

### response-terminator

Formats and returns the final workflow response.

**Configuration Schema:**

```json
{
  "responseTemplate": {
    "success": true,
    "data": "${input}",
    "timestamp": "${variables.timestamp}",
    "metadata": {
      "workflowId": "${workflowId}",
      "executionId": "${executionId}"
    }
  },
  "statusCode": 200,
  "headers": {
    "Content-Type": "application/json"
  }
}
```

**Properties:**

- `responseTemplate` (object, required): Response template
- `statusCode` (number): HTTP status code
- `headers` (object): Response headers

### mcp-response-terminator

Formats response for MCP protocol compliance.

**Configuration Schema:**

```json
{
  "responseFormat": "mcp",
  "includeMetadata": true,
  "dataTransformation": {
    "type": "object",
    "properties": {
      "result": "${input.result}",
      "status": "${input.status}"
    }
  }
}
```

### error-terminator

Handles errors and formats error responses.

**Configuration Schema:**

```json
{
  "errorTemplate": {
    "error": true,
    "message": "${error.message}",
    "code": "${error.code}",
    "details": "${error.details}"
  },
  "statusCode": 500,
  "logError": true
}
```

## Complete Workflow Examples

### Example 1: Simple REST API Processing

```json
{
  "id": "rest-api-workflow",
  "name": "REST API Data Processing",
  "description": "Processes REST API requests with validation and transformation",
  "version": "1.0.0",
  "enabled": true,
  "tags": ["api", "processing"],
  "input_schema": {
    "type": "object",
    "properties": {
      "payload": {
        "type": "object",
        "description": "Request payload"
      }
    },
    "required": ["payload"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "api-trigger",
        "type": "rest-api-trigger",
        "name": "API Endpoint",
        "position": { "x": 100, "y": 100 },
        "config": {
          "method": "POST",
          "path": "/api/process",
          "authentication": false,
          "validation": true
        }
      },
      {
        "id": "validate-data",
        "type": "javascript-action",
        "name": "Validate Input",
        "position": { "x": 300, "y": 100 },
        "config": {
          "script": `
const { payload } = input;

if (!payload || typeof payload !== 'object') {
  throw new Error('Invalid payload: must be an object');
}

// Validation logic
const requiredFields = ['id', 'type'];
for (const field of requiredFields) {
  if (!payload[field]) {
    throw new Error(\`Missing required field: \${field}\`);
  }
}

return {
  validated: true,
  data: payload,
  timestamp: new Date().toISOString()
};
          `
        }
      },
      {
        "id": "process-data",
        "type": "javascript-action",
        "name": "Process Data",
        "position": { "x": 500, "y": 100 },
        "config": {
          "script": `
const { data } = input;

// Transform data
const processedData = {
  ...data,
  processed: true,
  processedAt: new Date().toISOString(),
  processingId: context.executionId
};

return processedData;
          `
        }
      },
      {
        "id": "format-response",
        "type": "response-terminator",
        "name": "Format Response",
        "position": { "x": 700, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "data": "${input}",
            "message": "Data processed successfully"
          },
          "statusCode": 200
        }
      }
    ],
    "edges": [
      {
        "id": "edge-1",
        "source": "api-trigger",
        "target": "validate-data"
      },
      {
        "id": "edge-2",
        "source": "validate-data",
        "target": "process-data"
      },
      {
        "id": "edge-3",
        "source": "process-data",
        "target": "format-response"
      }
    ]
  }
}
```

### Example 2: Conditional Processing with External API

```json
{
  "id": "conditional-api-workflow",
  "name": "Conditional API Processing",
  "description": "Conditionally processes data with external API calls",
  "version": "1.0.0",
  "enabled": true,
  "input_schema": {
    "type": "object",
    "properties": {
      "data": { "type": "object" },
      "priority": { "type": "string" }
    }
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "input-processor",
        "type": "javascript-action",
        "name": "Process Input",
        "position": { "x": 100, "y": 100 },
        "config": {
          "script": `
const { data, priority } = input;
return {
  originalData: data,
  priority: priority || 'normal',
  timestamp: new Date().toISOString()
};
          `
        }
      },
      {
        "id": "priority-check",
        "type": "if-else-logic",
        "name": "Check Priority",
        "position": { "x": 300, "y": 100 },
        "config": {
          "condition": "input.priority == 'high'",
          "trueOutput": {
            "route": "high-priority",
            "data": "${input}"
          },
          "falseOutput": {
            "route": "normal-priority",
            "data": "${input}"
          },
          "conditionType": "simple"
        }
      },
      {
        "id": "high-priority-api",
        "type": "http-action",
        "name": "High Priority API",
        "position": { "x": 500, "y": 50 },
        "config": {
          "url": "https://api.priority.com/high",
          "method": "POST",
          "headers": {
            "Content-Type": "application/json"
          },
          "body": {
            "data": "${input.data}",
            "priority": "high"
          }
        }
      },
      {
        "id": "normal-priority-api",
        "type": "http-action",
        "name": "Normal Priority API",
        "position": { "x": 500, "y": 150 },
        "config": {
          "url": "https://api.standard.com/process",
          "method": "POST",
          "headers": {
            "Content-Type": "application/json"
          },
          "body": {
            "data": "${input.data}",
            "priority": "normal"
          }
        }
      },
      {
        "id": "merge-results",
        "type": "merge-logic",
        "name": "Merge Results",
        "position": { "x": 700, "y": 100 },
        "config": {
          "mergeStrategy": "deep",
          "sources": ["${nodeResults.high-priority-api}", "${nodeResults.normal-priority-api}"],
          "conflictResolution": "last_wins"
        }
      },
      {
        "id": "final-response",
        "type": "response-terminator",
        "name": "Final Response",
        "position": { "x": 900, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "result": "${input}",
            "metadata": {
              "workflowId": "${workflowId}",
              "executionId": "${executionId}"
            }
          }
        }
      }
    ],
    "edges": [
      {
        "id": "edge-1",
        "source": "input-processor",
        "target": "priority-check"
      },
      {
        "id": "edge-2",
        "source": "priority-check",
        "target": "high-priority-api",
        "condition": "route == 'high-priority'"
      },
      {
        "id": "edge-3",
        "source": "priority-check",
        "target": "normal-priority-api",
        "condition": "route == 'normal-priority'"
      },
      {
        "id": "edge-4",
        "source": "high-priority-api",
        "target": "merge-results"
      },
      {
        "id": "edge-5",
        "source": "normal-priority-api",
        "target": "merge-results"
      },
      {
        "id": "edge-6",
        "source": "merge-results",
        "target": "final-response"
      }
    ]
  }
}
```

### Example 3: Database and Caching Workflow

```json
{
  "id": "database-cache-workflow",
  "name": "Database with Redis Caching",
  "description": "Stores data in database and caches in Redis",
  "version": "1.0.0",
  "enabled": true,
  "input_schema": {
    "type": "object",
    "properties": {
      "action": {
        "type": "string",
        "enum": ["store", "retrieve"]
      },
      "data": { "type": "object" },
      "key": { "type": "string" }
    },
    "required": ["action"]
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "action-router",
        "type": "switch-logic",
        "name": "Route by Action",
        "position": { "x": 100, "y": 100 },
        "config": {
          "switchValue": "${input.action}",
          "cases": {
            "store": {
              "operation": "store",
              "data": "${input.data}",
              "key": "${input.key}"
            },
            "retrieve": {
              "operation": "retrieve",
              "key": "${input.key}"
            }
          },
          "defaultCase": {
            "operation": "unknown",
            "error": "Invalid action"
          }
        }
      },
      {
        "id": "cache-check",
        "type": "redis-action",
        "name": "Check Cache",
        "position": { "x": 300, "y": 150 },
        "config": {
          "operation": "GET",
          "key": "${input.key}"
        }
      },
      {
        "id": "cache-hit-check",
        "type": "if-else-logic",
        "name": "Cache Hit?",
        "position": { "x": 500, "y": 150 },
        "config": {
          "condition": "input !== null",
          "trueOutput": {
            "source": "cache",
            "data": "${input}"
          },
          "falseOutput": {
            "source": "database",
            "needsQuery": true
          }
        }
      },
      {
        "id": "database-query",
        "type": "sql-action",
        "name": "Query Database",
        "position": { "x": 700, "y": 200 },
        "config": {
          "query": "SELECT * FROM items WHERE id = $1",
          "parameters": ["${variables.originalInput.key}"],
          "queryType": "SELECT"
        }
      },
      {
        "id": "store-in-cache",
        "type": "redis-action",
        "name": "Cache Result",
        "position": { "x": 900, "y": 200 },
        "config": {
          "operation": "SET",
          "key": "${variables.originalInput.key}",
          "value": "${input}",
          "expiration": 3600
        }
      },
      {
        "id": "store-data",
        "type": "sql-action",
        "name": "Store in Database",
        "position": { "x": 300, "y": 50 },
        "config": {
          "query": "INSERT INTO items (id, data, created_at) VALUES ($1, $2, NOW()) ON CONFLICT (id) DO UPDATE SET data = $2",
          "parameters": ["${input.key}", "${input.data}"],
          "queryType": "INSERT"
        }
      },
      {
        "id": "cache-stored-data",
        "type": "redis-action",
        "name": "Cache Stored Data",
        "position": { "x": 500, "y": 50 },
        "config": {
          "operation": "SET",
          "key": "${variables.originalInput.key}",
          "value": "${variables.originalInput.data}",
          "expiration": 3600
        }
      },
      {
        "id": "format-response",
        "type": "response-terminator",
        "name": "Format Response",
        "position": { "x": 1100, "y": 100 },
        "config": {
          "responseTemplate": {
            "success": true,
            "data": "${input}",
            "metadata": {
              "source": "${input.source || 'store'}",
              "timestamp": "${variables.timestamp}"
            }
          }
        }
      }
    ],
    "edges": [
      {
        "id": "route-to-cache-check",
        "source": "action-router",
        "target": "cache-check",
        "condition": "operation == 'retrieve'"
      },
      {
        "id": "route-to-store",
        "source": "action-router",
        "target": "store-data",
        "condition": "operation == 'store'"
      },
      {
        "id": "cache-to-hit-check",
        "source": "cache-check",
        "target": "cache-hit-check"
      },
      {
        "id": "cache-miss-to-db",
        "source": "cache-hit-check",
        "target": "database-query",
        "condition": "needsQuery == true"
      },
      {
        "id": "db-to-cache",
        "source": "database-query",
        "target": "store-in-cache"
      },
      {
        "id": "store-to-cache",
        "source": "store-data",
        "target": "cache-stored-data"
      },
      {
        "id": "cache-hit-to-response",
        "source": "cache-hit-check",
        "target": "format-response",
        "condition": "source == 'cache'"
      },
      {
        "id": "cached-result-to-response",
        "source": "store-in-cache",
        "target": "format-response"
      },
      {
        "id": "stored-cache-to-response",
        "source": "cache-stored-data",
        "target": "format-response"
      }
    ]
  }
}
```

## Best Practices

### 1. Workflow Design

- **Keep workflows focused**: Each workflow should have a single, clear purpose
- **Use descriptive names**: Node and edge IDs should be meaningful
- **Handle errors gracefully**: Include error-terminator nodes for error scenarios
- **Validate inputs early**: Use validation nodes at the beginning of workflows
- **Document your workflows**: Include clear descriptions and comments

### 2. Node Configuration

- **Use variable substitution**: Leverage `${variable}` syntax for dynamic values
- **Set appropriate timeouts**: Configure realistic timeout values for external calls
- **Enable retries for flaky operations**: Use retry configuration for network calls
- **Cache frequently used data**: Use Redis for caching expensive operations
- **Log important steps**: Use console.log in JavaScript nodes for debugging

### 3. Performance Optimization

- **Minimize external calls**: Batch operations when possible
- **Use caching strategically**: Cache expensive computations and API responses
- **Set connection limits**: Configure appropriate timeouts and retry policies
- **Monitor execution times**: Use observability configuration to track performance
- **Optimize SQL queries**: Use indexes and limit result sets appropriately

### 4. Security Considerations

- **Validate all inputs**: Use input schemas and validation nodes
- **Sanitize data**: Clean user inputs in JavaScript nodes
- **Use environment variables**: Store secrets in environment variables, not config
- **Enable authentication**: Use authentication on public-facing triggers
- **Limit permissions**: Use least-privilege access for database and Redis operations

### 5. Testing and Debugging

- **Start simple**: Build workflows incrementally, testing each node
- **Use console logging**: Add debug output in JavaScript nodes
- **Test error scenarios**: Ensure error handling works correctly
- **Validate schemas**: Test with various input formats
- **Monitor in production**: Use observability features to track workflow health

### 6. Variable and Context Usage

- **Store intermediate results**: Use `workflowContext.variables` to pass data between nodes
- **Access previous results**: Use `nodeResults.nodeId` to access specific node outputs
- **Use utility functions**: Leverage built-in utility functions in logic nodes
- **Handle null values**: Check for null/undefined values in conditions
- **Type checking**: Validate data types in JavaScript nodes

### 7. Edge Cases and Error Handling

- **Handle empty inputs**: Check for empty or missing data
- **Timeout handling**: Set appropriate timeouts for all external operations
- **Network failures**: Use retry policies for network-dependent operations
- **Data validation**: Validate data at each critical step
- **Graceful degradation**: Provide fallback behaviors when possible

This guide provides a comprehensive reference for manually creating workflows in the MCP Automation Server. Use these examples and patterns as starting points for building your own custom workflows.
