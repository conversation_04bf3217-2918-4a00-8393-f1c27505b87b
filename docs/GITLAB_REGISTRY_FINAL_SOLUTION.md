# GitLab Registry Final Solution - Tag-based Approach

Tento dokument popisuje finální řešení problému s hostováním dvou Docker images v jednom GitLab repository.

## Problém

GitLab Container Registry má omezení:
- **Jeden repository = jeden image name**
- <PERSON>kus o push image s jiným názvem (např. `-frontend` suffix) vytváří nový repository
- Chyba: `denied: requested access to the resource is denied`

## Původní ne<PERSON>pěšný přístup

```bash
# Backend (funguje)
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:1.0.2

# Frontend (nefunguje - pokus o nový repository)
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-frontend:1.0.2
```

## Finální řešení: Tag-based Naming

Používá<PERSON> s<PERSON> repository, ale různé tagy:

```bash
# Backend
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:1.0.2

# Frontend
network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver:frontend-1.0.2
```

## Implementace

### 1. GitLab CI/CD (.gitlab-ci.yml)

**Backend build:**
```yaml
script:
  - docker build --pull -t ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID} .
  - docker push ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
  - docker rmi ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
```

**Frontend build:**
```yaml
script:
  - docker build --pull -t ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID} -f frontend/Dockerfile ./frontend
  - docker push ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID}
  - docker rmi ${CI_REGISTRY_IMAGE}:frontend-${CURRENT_VERSION_ID}
```

### 2. Helm Chart Templates

**Backend deployment:**
```yaml
image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
```

**Frontend deployment:**
```yaml
image: {{ .Values.frontend.image.repository }}:frontend-{{ .Values.frontend.image.tag }}
```

### 3. Helm Values

**values-dev.yaml:**
```yaml
image:
  repository: "network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-dev"
  tag: "1.0.2"

frontend:
  image:
    repository: "network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver-dev"
    tag: "1.0.2"
```

**values-prod.yaml:**
```yaml
image:
  repository: "network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver"
  tag: "1.0.2"

frontend:
  image:
    repository: "network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver"
    tag: "1.0.2"
```

## Výsledek v GitLab Registry

V Container Registry budou viditelné oba tagy:

```
aidcc-ccczautomationmcpserver
├── 1.0.2 (backend)
└── frontend-1.0.2 (frontend)
```

## Výhody tohoto řešení

✅ **Kompatibilní s GitLab omezením**: Jeden repository, více tagů
✅ **Jednoduché**: Minimální změny v kódu
✅ **Konzistentní verzování**: Stejná verze pro oba komponenty
✅ **Přehledné**: Jasné rozlišení pomocí tag prefixu
✅ **Škálovatelné**: Lze přidat další komponenty (worker-1.0.2, api-1.0.2)

## Testování

Po implementaci by měl frontend build projít úspěšně:

1. ✅ Docker build vytvoří image s tagem `frontend-VERSION`
2. ✅ Docker push pushne do stejného repository s jiným tagem
3. ✅ Žádné permission denied chyby
4. ✅ Helm deployment použije správný image s prefixem

## Alternativní tag formáty

Můžeme použít různé formáty tagů:

### Prefix approach (zvoleno)
```
1.0.2           # backend
frontend-1.0.2  # frontend
```

### Suffix approach
```
1.0.2-backend   # backend
1.0.2-frontend  # frontend
```

### Component approach
```
backend/1.0.2   # backend
frontend/1.0.2  # frontend
```

## Monitoring

V GitLab Container Registry → Packages & Registries → Container Registry:
- Jeden repository: `aidcc-ccczautomationmcpserver`
- Více tagů: `1.0.2`, `frontend-1.0.2`, `1.0.1`, `frontend-1.0.1`, atd.

## Závěr

Tag-based approach je nejjednodušší a nejkompatibilnější řešení pro GitLab Container Registry omezení. Umožňuje hostovat více komponent v jednom repository bez nutnosti vytváření nových repositories nebo složitých workaroundů.
