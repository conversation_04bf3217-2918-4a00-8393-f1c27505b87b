ingressRoute:
  dnsSuffix: "Your K8s 'CLUSTER_NAME' and 'BASE_DOMAIN', that is available as 'CLUSTER_URI' within the CI/CD Pipeline runtime; i.e. 'aks-ppbe.network.cz.o2'"
nginx:
  image:
    registry: network.git.cz.o2:5005
    repository: deployments/aidcc-ccczautomationmcpserver/nginx
    tag: 1.25.1-debian-11-r39
    pullPolicy: Always
    pullSecrets:
      - aidcc-ccczautomationmcpserver-dev-gitlab-dockerconfig
  autoscaling:
    enabled: false
  service:
    type: ClusterIP
    ports:
      http: 8080
    targetPort:
      http: 8080
  transport:
    enabled: false
    listener:
      name: tcp-1234
      protocol: TCP
    upstream:
      name: nginx-tcp-1234
      port: 1234
      service: nginx-demo-transportserver
      protocol: TCP
  staticSiteConfigmap: index-html-configmap
  resources:
   limits:
      cpu: 100m
      memory: 128Mi
   requests:
      cpu: 100m
      memory: 128Mi
  livenessProbe:
    enabled: true
    initialDelaySeconds: 30
    timeoutSeconds: 5
    periodSeconds: 10
    failureThreshold: 6
    successThreshold: 1
  readinessProbe:
    enabled: true
    initialDelaySeconds: 5
    timeoutSeconds: 3
    periodSeconds: 5
    failureThreshold: 3
    successThreshold: 1
  metrics:
    enabled: true
    image:
      registry: network.git.cz.o2:5005
      repository: deployments/aidcc-ccczautomationmcpserver/nginx-exporter
      tag: 0.11.0-debian-11-r300
      pullSecrets:
        - aidcc-ccczautomationmcpserver-dev-gitlab-dockerconfig
    resources:
       limits:
          cpu: 100m
          memory: 128Mi
       Examples:
       requests:
          cpu: 100m
          memory: 128Mi
    serviceMonitor:
      enabled: true
    prometheusRule:
      enabled: true
      rules:
        - alert: LowInstance
          expr: up{service="{{ template "common.names.fullname" . }}"} < 1
          for: 1m
          labels:
            severity: critical
          annotations:
            description: Service {{ template "common.names.fullname" . }} Tomcat is down since 1m.
            summary: Tomcat instance is down.

