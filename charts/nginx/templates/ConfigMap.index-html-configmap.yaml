apiVersion: v1
kind: ConfigMap
metadata:
  name: index-html-configmap
data:
  index.html: |
    <!doctype html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>NTW Azure</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css" rel="stylesheet"
              integrity="sha384-gH2yIJqKdNHPEq0n4Mqa/HGKIhSkIHeL5AyhkYV8i59U5AR6csBvApHHNl/vI1Bx"
              crossorigin="anonymous">
    </head>
    <body>
        <div class="container">
        <header class="d-flex flex-wrap justify-content-center py-3 mb-4 border-bottom">
            <a href=class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-dark text-decoration-none">
            <span class="fs-4">NTW Azure</span>
            </a>

            <ul class="nav nav-pills">
                <li class="nav-item"><a href="https://portal.azure.com/" target="_blank" class="nav-link">Azure</a></li>
                <li class="nav-item"><a href="https://network.git.cz.o2/" target="_blank" class="nav-link">Gitlab</a>
                </li>
                <li class="nav-item"><a href="https://grafana.network.o2.cz/" target="_blank"
                                        class="nav-link">Grafana</a></li>
                <li class="nav-item"><a href="https://kibana1.cem.cz.o2/" target="_blank" class="nav-link">Kibana</a>
                </li>
            </ul>
        </header>
    </div>

        <div class="container px-4 py-5" id="featured-3">
        <h2 class="pb-2 border-bottom">Návody</h2>
        <div class="row g-4 py-5 row-cols-1 row-cols-lg-4">
            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#collection"/>
                    </svg>
                </div>
                <h2>Tooling</h2>
                <p>Tooly potřebné ke správě v Azure a kubernetes.</p>
                <a href="https://confluence.cz.o2/display/PORTALS/Tooling+Installation" target="_blank"
                   class="icon-link d-inline-flex align-items-center">
                    Tooling
                </a>
            </div>
            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#people-circle"/>
                    </svg>
                </div>
                <h2>CI/CD Pipelines</h2>
                <p>Informace o Pipelinenách. Jak fungují a jak je používat ve svých deploymentech.</p>
                <a href="https://confluence.cz.o2/pages/viewpage.action?pageId=103517195" target="_blank"
                   class="icon-link d-inline-flex align-items-center">
                    Pipelines
                </a>
            </div>
            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#toggles2"/>
                    </svg>
                </div>
                <h2>Featured title</h2>
                <p>Paragraph of text beneath the heading to explain the heading. We'll add onto it with another sentence
                    and probably just keep going until we run out of words.</p>
                <a href="#" target="_blank" class="icon-link d-inline-flex align-items-center">
                    Call to action
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#chevron-right"/>
                    </svg>
                </a>
            </div>
            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#toggles2"/>
                    </svg>
                </div>
                <h2>Featured title</h2>
                <p>Paragraph of text beneath the heading to explain the heading. We'll add onto it with another sentence
                    and probably just keep going until we run out of words.</p>
                <a href="#" target="_blank" class="icon-link d-inline-flex align-items-center">
                    Call to action
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#chevron-right"/>
                    </svg>
                </a>
            </div>
        </div>
    </div>

        <div class="container px-4 py-5" id="featured-3">
        <h2 class="pb-2 border-bottom">Manuály</h2>
        <div class="row g-4 py-5 row-cols-1 row-cols-lg-3">
            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#collection"/>
                    </svg>
                </div>
                <h2>WSL</h2>
                <p>Pro jednodušší instalaci a správu toolů je lepší používat OS Linux. Jedna z možností je integrovaný
                    Linux ve Windows -> WSL.</p>
                <a href="https://docs.microsoft.com/en-us/windows/wsl/install" target="_blank"
                   class="icon-link d-inline-flex align-items-center">
                    WSL
                </a>
            </div>

            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#toggles2"/>
                    </svg>
                </div>
                <h2>PowerShell</h2>
                <p>Cli pro Windows.</p>
                <a href="https://docs.microsoft.com/cs-cz/powershell/" target="_blank"
                   class="icon-link d-inline-flex align-items-center">
                    PowerShell
                </a>
            </div>

            <div class="feature col">
                <div class="feature-icon d-inline-flex align-items-center justify-content-center text-bg-primary bg-gradient fs-2 mb-3">
                    <svg class="bi" width="1em" height="1em">
                        <use xlink:href="#people-circle"/>
                    </svg>
                </div>
                <h2>HELM</h2>
                <p>Package Manager pro k8s.</p>
                <a href="https://helm.sh/" target="_blank" class="icon-link d-inline-flex align-items-center">
                    HELM
                </a>
            </div>


        </div>
    </div>
    </body>
    </html>