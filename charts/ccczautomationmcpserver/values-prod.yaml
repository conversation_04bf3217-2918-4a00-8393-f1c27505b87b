# Declare variables to be passed into your templates.

# -------------------------------------
# Prometheus Alerting Thresholds
# -------------------------------------
alerting:
  enabled: true
  critical:
    enabled: true
    CPUsageHigh:
      percent: 90
    MemoryUsageHigh:
      percent: 80
    PodFrequentRestarts:
      count: 2
    PersistentVolume:
      freeBytes: 0
      percentFull: 80
  warning:
    enabled: true
    CPUsageElevated:
      percent: 80
    MemoryUsageElevated:
      percent: 70
    PersistentVolumeElevated:
      percentFull: 70
  info:
    enabled: false
    CPUsageLow:
      percent: 20
    MemoryUsageLow:
      percent: 20

# -------------------------------------
# Image Configuration
# -------------------------------------
image:
  repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver'
  tag: '1.5.0'

# -------------------------------------
# mcp Configuration
# -------------------------------------
mcp:
  PORT: 7000
  APPLICATION_NAME: ccczautomationmcpserver
  NODE_ENV: production
  REDIS_PASSWORD: redis123
  REDIS_PORT: 6379

# -------------------------------------
# Frontend Configuration
# -------------------------------------
frontend:
  NODE_ENV: production
  image:
    repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver'
    tag: '1.5.0'
  service:
    type: ClusterIP
    port: 80
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi
  environment:
    # Runtime Environment Variables
    NODE_ENV: 'production'
    VITE_API_URL: '/api'
    VITE_API_BASE_URL: 'http://aidcc-ccczautomationmcpserver-mcp:7002'

# -------------------------------------
# Environment Variables
# -------------------------------------
environment:
  # Server Configuration
  HOST: '0.0.0.0'
  API_PORT: '7001'
  LOG_LEVEL: 'info'

  # Database Configuration
  DB_HOST: 'aidcc-ccczautomationmcpserver-db-service'
  DB_PORT: '5432'
  DB_USERNAME: 'postgres'
  DB_PASSWORD: 'password'
  DB_DATABASE: 'mcp_server'

  # Redis Configuration
  REDIS_HOST: 'aidcc-ccczautomationmcpserver-redis-service'
  REDIS_PORT: '6379'
  REDIS_PASSWORD: 'redis123'

  # Workflow Configuration
  USE_REDIS_WORKFLOW_MEMORY: 'true'
  WORKFLOW_CONTEXT_TTL: '3600'

  # Data Retention Configuration
  DATA_RETENTION_DAYS: '30'
  DATA_CLEANUP_INTERVAL_MINUTES: '60'
  DATA_CLEANUP_BATCH_SIZE: '1000'

  # LiteLLM Configuration
  LITELLM_API_KEY: ''
  LITELLM_BASE_URL: 'http://localhost:8000'

  # JWT Configuration
  JWT_SECRET: 'production-secret-key-change-this'
  JWT_EXPIRES_IN: '24h'
  ADMIN_PORT: '7002'

# -------------------------------------
# Service Configuration
# -------------------------------------
service:
  type: ClusterIP
  port: 7000

# -------------------------------------
# Ingress Configuration
# -------------------------------------
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    # HTTP Streaming Support for MCP Server
    nginx.ingress.kubernetes.io/proxy-read-timeout: '0'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '0'
    nginx.ingress.kubernetes.io/proxy-buffering: 'off'
    nginx.ingress.kubernetes.io/proxy-request-buffering: 'off'
    nginx.ingress.kubernetes.io/upstream-keepalive-timeout: '3600'
    nginx.ingress.kubernetes.io/backend-protocol: 'HTTP'
    nginx.ingress.kubernetes.io/proxy-http-version: '1.1'
  ingressClassName: nginx
  domainname: aidcc-ccczautomationmcpserver.aks-bf.network.cz.o2
  hosts:
    - host: aidcc-ccczautomationmcpserver.aks-bf.network.cz.o2
      paths:
        - path: /
          pathType: Prefix
  tls:
    enabled: true

# -------------------------------------
# Resources Configuration
# -------------------------------------
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 200m
    memory: 256Mi

# -------------------------------------
# Redis Configuration
# -------------------------------------
redis:
  resources:
    limits:
      cpu: 1
      memory: 1Gi
    requests:
      cpu: 200m
      memory: 256Mi

# -------------------------------------
# Affinity Configuration
# -------------------------------------
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app
                operator: In
                values:
                  - mcp
          topologyKey: kubernetes.io/hostname
