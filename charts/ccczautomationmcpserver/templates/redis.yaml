apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Chart.Name }}-redis
  labels:
    app.kubernetes.io/name: {{ .Chart.Name }}-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Chart.Name }}-redis
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}-redis
    spec:
      imagePullSecrets:
        - name: {{ .Release.Name }}-gitlab-dockerconfig
      containers:
        - image: redis:7.0.8-alpine
          imagePullPolicy: Always
          name: {{ .Chart.Name }}-redis
          ports:
          - name: redis
            containerPort: 6379
          resources:
            {{- toYaml .Values.redis.resources | nindent 12 }}
