apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-frontend
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}-frontend
spec:
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      terminationGracePeriodSeconds: 60
      nodeSelector:
        kubernetes.io/arch: amd64
      imagePullSecrets:
        - name: {{ .Release.Name }}-gitlab-dockerconfig
      containers:
        - image: {{ .Values.frontend.image.repository | default "network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver" }}:frontend-{{ .Values.frontend.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: Always
          name: frontend
          ports:
            - containerPort: 80
          resources:
            {{- toYaml .Values.frontend.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 3
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: 80
            initialDelaySeconds: 3
            periodSeconds: 10
          env:
            - name: NODE_ENV
              value: {{ .Values.frontend.environment.NODE_ENV | quote }}
            - name: VITE_API_BASE_URL
              value: {{ .Values.frontend.environment.VITE_API_BASE_URL | quote }}
            - name: VITE_API_URL
              value: {{ .Values.frontend.environment.VITE_API_URL | quote }}
          envFrom:
            - configMapRef:
                name: {{ .Release.Name }}-frontend-env
