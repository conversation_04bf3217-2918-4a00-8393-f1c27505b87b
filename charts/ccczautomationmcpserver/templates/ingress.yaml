{{- if .Values.ingress.enabled -}}
{{- $fullName := .Release.Name -}}
{{- $svcPort := .Values.service.port -}}
{{- $frontendSvcPort := .Values.frontend.service.port -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Chart.Name }}
  labels:
    app: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
  - host: {{ .Values.ingress.domainname }}
    http:
      paths:
      # API routes go to backend
      - backend:
          service:
            name: {{ .Chart.Name }}-mcp
            port:
              number: {{ $svcPort }}
        path: /api
        pathType: Prefix
      # WebSocket routes go to backend
      - backend:
          service:
            name: {{ .Chart.Name }}-mcp
            port:
              number: {{ $svcPort }}
        path: /socket.io
        pathType: Prefix
      # Health check for backend
      - backend:
          service:
            name: {{ .Chart.Name }}-mcp
            port:
              number: {{ $svcPort }}
        path: /health
        pathType: Prefix
      # Everything else goes to frontend
      - backend:
          service:
            name: {{ .Release.Name }}-frontend
            port:
              number: {{ $frontendSvcPort }}
        path: /
        pathType: Prefix
  {{- if .Values.ingress.tls.enabled }}
  tls:
  - hosts:
    - {{ .Values.ingress.domainname }}
  {{- end }}
{{- end }}
