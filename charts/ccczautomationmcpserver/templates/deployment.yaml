apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-mcp
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}-mcp
spec:
  selector:
    matchLabels:
      app: mcp
  template:
    metadata:
      labels:
        app: mcp
    spec:
      terminationGracePeriodSeconds: 60
      nodeSelector:
        kubernetes.io/arch: amd64
      imagePullSecrets:
        - name: {{ .Release.Name }}-gitlab-dockerconfig
      containers:
        - image: {{ .Values.image.repository | default "network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver" }}:{{ .Values.image.tag | default .Chart.AppVersion }}
          imagePullPolicy: Always
          name: mcp
          ports:
            - containerPort: 7000
            - containerPort: 7001
            - containerPort: 7002
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /health
              port: 7000
            initialDelaySeconds: 3
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: 7000
            initialDelaySeconds: 3
            periodSeconds: 10
          env:
            - name: NODE_ENV
              value: {{ .Values.mcp.NODE_ENV | default "production" }}
            - name: PORT
              value: "{{ .Values.mcp.PORT | default "3000" }}"
            - name: APPLICATION_NAME
              value: {{ .Values.mcp.APPLICATION_NAME | default "CCCZAutomationLlmMcp" }}
          envFrom:
            - configMapRef:
                name: {{ .Release.Name }}-env
