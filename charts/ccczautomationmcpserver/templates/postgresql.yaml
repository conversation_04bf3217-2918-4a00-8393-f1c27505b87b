apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ .Chart.Name }}-db
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Chart.Name }}-db
  serviceName: {{ .Chart.Name }}-db-service
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}-db
    spec:
      terminationGracePeriodSeconds: 10
      imagePullSecrets:
        - name: {{ .Release.Name }}-gitlab-dockerconfig
      containers:
        - args:
            - '-c'
            - max_connections=1000
            - '-c'
            - shared_buffers=1024MB
          image: postgres:15
          imagePullPolicy: Always
          name: postgresql
          ports:
            - name: db
              containerPort: 5432
          env:
            - name: POSTGRES_PASSWORD
              value: {{ .Values.postresql.environments.POSTGRES_PASSWORD }}
            - name: POSTGRES_DB
              value: mcp_server
          volumeMounts:
            - name: {{ .Chart.Name }}-db-volume
              mountPath: /var/lib/postgresql/data
              subPath: postgres
          resources:
            {{- toYaml .Values.postresql.resources | nindent 12 }}

      volumes:
        - name: {{ .Chart.Name }}-db-volume
          persistentVolumeClaim:
            claimName: {{ .Chart.Name }}-db-pvc
