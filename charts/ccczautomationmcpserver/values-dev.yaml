# Declare variables to be passed into your templates.

# -------------------------------------
# Prometheus Alerting Thresholds
# -------------------------------------
alerting:
  enabled: false

# -------------------------------------
# Image Configuration
# -------------------------------------
image:
  repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver'
  tag: '1.5.0'

# -------------------------------------
# mcp Configuration
# -------------------------------------
mcp:
  PORT: 7000
  APPLICATION_NAME: ccczautomationmcpserver
  NODE_ENV: development

# -------------------------------------
# Frontend Configuration
# -------------------------------------
frontend:
  NODE_ENV: development
  image:
    repository: 'network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver'
    tag: '1.5.0'
  service:
    type: ClusterIP
    port: 80
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 64Mi
  environment:
    # Runtime Environment Variables
    NODE_ENV: 'development'
    VITE_API_URL: '/api'
    VITE_API_BASE_URL: 'http://aidcc-ccczautomationmcpserver-mcp:7002'

# -------------------------------------
# Environment Variables
# -------------------------------------
environment:
  # Server Configuration
  HOST: '0.0.0.0'
  API_PORT: '7001'
  LOG_LEVEL: 'debug'

  # Database Configuration
  DB_HOST: 'aidcc-ccczautomationmcpserver-db-service'
  DB_PORT: '5432'
  DB_USERNAME: 'postgres'
  DB_PASSWORD: 'password'
  DB_DATABASE: 'mcp_server'

  # Redis Configuration
  REDIS_HOST: 'aidcc-ccczautomationmcpserver-redis-service'
  REDIS_PORT: '6379'
  REDIS_PASSWORD: ''

  # Workflow Configuration
  USE_REDIS_WORKFLOW_MEMORY: 'false'
  WORKFLOW_CONTEXT_TTL: '3600'

  # Data Retention Configuration
  DATA_RETENTION_DAYS: '5'
  DATA_CLEANUP_INTERVAL_MINUTES: '5'
  DATA_CLEANUP_BATCH_SIZE: '1000'

  # LiteLLM Configuration
  LITELLM_API_KEY: ''
  LITELLM_BASE_URL: 'http://localhost:8000'

  # JWT Configuration (add for admin API)
  JWT_SECRET: 'your-secret-key-change-in-production'
  JWT_EXPIRES_IN: '24h'
  ADMIN_PORT: '7002'

# -------------------------------------
# Service Configuration
# -------------------------------------
service:
  type: ClusterIP
  port: 7000

# -------------------------------------
# Ingress Configuration
# -------------------------------------
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    # HTTP Streaming Support for MCP Server
    nginx.ingress.kubernetes.io/proxy-read-timeout: '0'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '0'
    nginx.ingress.kubernetes.io/proxy-buffering: 'off'
    nginx.ingress.kubernetes.io/proxy-request-buffering: 'off'
    nginx.ingress.kubernetes.io/upstream-keepalive-timeout: '3600'
    nginx.ingress.kubernetes.io/backend-protocol: 'HTTP'
    nginx.ingress.kubernetes.io/proxy-http-version: '1.1'
  ingressClassName: nginx
  domainname: aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2
  hosts:
    - host: aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2
      paths:
        - path: /
          pathType: Prefix
  tls:
    enabled: true

# -------------------------------------
# Resources Configuration
# -------------------------------------
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

# -------------------------------------
# Redis Configuration
# -------------------------------------
redis:
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi

# -------------------------------------
# Postgresql Configuration
# -------------------------------------
postresql:
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi
  environments:
    POSTGRES_PASSWORD: password
