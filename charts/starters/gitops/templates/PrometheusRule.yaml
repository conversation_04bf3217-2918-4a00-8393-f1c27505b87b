{{- if .Values.alerting.enabled }}
{{- $critical := .Values.alerting.critical }}
{{- $warning := .Values.alerting.warning }}
{{- $info := .Values.alerting.info }}
{{- $clusterName := .Values.myClusterName }}
{{- $namespace := .Release.Namespace }}
{{- $valuePercent := `{{ $value | humanizePercentage }}` }}
{{- $valueCount := `{{ $value | humanize }}` }}
{{- $messageHeader := printf "[%s] %s"  $clusterName .Release.Name }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: gitops-alerting
spec:
    groups:
    {{- if $critical.enabled }}
    - name: gitops-critical
      rules:
      - alert: {{ $namespace }}-CPUsageHigh
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod CPU saturated above {{ $critical.CPUsageHigh.percent }}% during the last 5m"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' has critical CPU usage. Threshold/current: {{ $critical.CPUsageHigh.percent }}% / {{ $valuePercent }}"
        labels:
          severity: critical
        expr: sum by (pod, container_name) (rate(container_cpu_usage_seconds_total{namespace="{{ $namespace }}"}[5m])) / sum by (pod, container_name) (container_spec_cpu_quota{namespace="{{ $namespace }}"} / container_spec_cpu_period{namespace="{{ $namespace }}"}) > {{ divf $critical.CPUsageHigh.percent 100 }}
        for: 5m
      - alert: {{ $namespace }}-MemoryUsageHigh
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod Memory saturated above {{ $critical.MemoryUsageHigh.percent }}% during the las 2m"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' has high Memory usage. Threshold/current: {{ $critical.MemoryUsageHigh.percent }}% / {{ $valuePercent }}"
        labels:
          severity: critical
        expr: sum by (pod, container_name) (container_memory_working_set_bytes{namespace="{{ $namespace }}"}) / sum by (pod, container_name) (container_spec_memory_limit_bytes{namespace="{{ $namespace }}"} > 0) > {{ divf $critical.MemoryUsageHigh.percent 100 }}
        for: 2m
      - alert: {{ $namespace }}-PersistentVolumeFull
        annotations:
          summary: "{{ $messageHeader }} | PersistentVolume '{{`{{`}}$labels.persistentvolumeclaim{{`}}`}}' is full for the last 5m"
          description: "PersistentVolume '{{`{{`}}$labels.persistentvolumeclaim{{`}}`}}' is full and needs immediate action!"
        labels:
          severity: critical
        expr: kubelet_volume_stats_available_bytes{namespace="{{ $namespace }}"} == {{ $critical.PersistentVolume.freeBytes }}
        for: 5m
      - alert: {{ $namespace }}-PersistentVolumeAlmostFull
        annotations:
          summary: "{{ $messageHeader }} | PersistentVolume '{{`{{`}}$labels.persistentvolumeclaim{{`}}`}}' was more than {{ $critical.PersistentVolume.percentFull }}% saturated during the last 2m"
          description: "PersistentVolume '{{`{{`}}$labels.persistentvolumeclaim{{`}}`}}' is filling up. Threshold/current: {{ $critical.PersistentVolume.percentFull }}% / {{ $valuePercent }}"
        labels:
          severity: critical
        expr: kubelet_volume_stats_used_bytes{namespace="{{ $namespace }}"} / kubelet_volume_stats_capacity_bytes{namespace="{{ $namespace }}"} > {{ divf $critical.PersistentVolume.percentFull 100 }}
        for: 2m
      - alert: {{ $namespace }}-PodFrequentRestarts
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod is restarting frequently for the last hour"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' was restarted more than {{ $critical.PodFrequentRestarts.count }}x within the last hour. Threshold/current: {{ $critical.PodFrequentRestarts.count }} / {{ $valueCount }}"
        labels:
          severity: critical
        expr: increase(kube_pod_container_status_restarts_total{namespace="{{ $namespace }}"}[1h]) >= {{ $critical.PodFrequentRestarts.count }}
        for: 10m
    {{- end }}
    {{- if $warning.enabled }}
    - name: gitops-warning
      rules:
      - alert: {{ $namespace }}-CPUsageElevated
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod CPU saturated above {{ $warning.CPUsageElevated.percent }}% during the last 5m"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' has elevated CPU usage. Threshold/current: {{ $warning.CPUsageElevated.percent }}% / {{ $valuePercent }}"
        labels:
          severity: warning
        expr: sum by (pod, container_name) (rate(container_cpu_usage_seconds_total{namespace="{{ $namespace }}"}[5m])) / sum by (pod, container_name) (container_spec_cpu_quota{namespace="{{ $namespace }}"} / container_spec_cpu_period{namespace="{{ $namespace }}"}) > {{ divf $warning.CPUsageElevated.percent 100 }}
        for: 5m
      - alert: {{ $namespace }}-MemoryUsageElevated
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod Memory saturated above {{ $warning.MemoryUsageElevated.percent }}% during the last 2m"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' has elevated Memory usage. Threshold/current: {{ $warning.MemoryUsageElevated.percent }}% / {{ $valuePercent }}"
        labels:
          severity: warning
        expr: sum by (pod, container_name) (container_memory_working_set_bytes{namespace="{{ $namespace }}"}) / sum by (pod, container_name) (container_spec_memory_limit_bytes{namespace="{{ $namespace }}"} > 0) > {{ divf $warning.MemoryUsageElevated.percent 100 }}
        for: 2m
      - alert: {{ $namespace }}-PersistentVolumeElevated
        annotations:
          summary: "{{ $messageHeader }} | PersistentVolume '{{`{{`}}$labels.persistentvolumeclaim{{`}}`}}' was more than {{ $warning.PersistentVolumeElevated.percentFull }}% saturated during the last 2m"
          description: "PersistentVolume '{{`{{`}}$labels.persistentvolumeclaim{{`}}`}}' is filling up. Threshold/current: {{ $warning.MemoryUsageElevated.percent }}% / {{ $valuePercent }}"
        labels:
          severity: warning
        expr: kubelet_volume_stats_used_bytes{namespace="{{ $namespace }}"} / kubelet_volume_stats_capacity_bytes{namespace="{{ $namespace }}"} > {{ divf $warning.PersistentVolumeElevated.percentFull 100 }}
        for: 2m
    {{- end }}
    {{- if $info.enabled }}
    - name: gitops-info
      rules:
      - alert: {{ $namespace }}-CPUsageLow
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod CPU saturated less than {{ $info.CPUsageLow.percent }}%"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' has low CPU usage for more than a day. Threshold/current: {{ $info.CPUsageLow.percent }}% / {{ $valuePercent }}"
        labels:
          severity: info
        expr: sum by (pod, name) (rate(container_cpu_usage_seconds_total{namespace="{{ $namespace }}"}[3m])) < {{ $info.CPUsageLow.percent }}
        for: 30h
      - alert: {{ $namespace }}-MemoryUsageLow
        annotations:
          summary: "{{ $messageHeader }}/{{`{{`}}$labels.pod{{`}}`}} | Pod Memory saturated less than {{ $info.MemoryUsageLow.percent }}%"
          description: "Pod '{{`{{`}}$labels.pod{{`}}`}}' has low Memory usage for more than a day. Threshold/current: {{ $info.MemoryUsageLow.percent }}% / {{ $valuePercent }}"
        labels:
          severity: info
        expr: (sum by (pod, name) (container_memory_working_set_bytes{namespace="{{ $namespace }}"}) / sum by (pod, name) (container_spec_memory_limit_bytes{namespace="{{ $namespace }}"} > 0)) < {{ $info.MemoryUsageLow.percent }} / 100
        for: 30h
    {{- end }}
{{- end }}