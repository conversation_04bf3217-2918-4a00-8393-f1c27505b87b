{{- if $.Values.ingress.enabled -}}
{{- range $bitswan, $values := .Values.sites }}
{{- range $instance, $data := $values }}
{{- $bitsafe := cat $bitswan "-" $instance | replace "_" "" | nospace -}}
{{- $fullName := include "initial.fullname" $ -}}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $.Release.Name }}-{{ $bitsafe }}
  labels:
    {{- include "initial.labels" $ | nindent 4 }}
  {{- with $.Values.ingress.annotations }}
  annotations:
    {{- toYaml $ | nindent 4 }}
  {{- end }}
spec:
  ingressClassName: {{ $.Values.ingress.className }}
  {{- if $.Values.ingress.tls.enabled }}
  tls:
    - hosts:
        - "{{ $.Release.Name }}-{{ $bitsafe }}.{{ $.Values.ingress.baseDomain }}"
  {{- end }}
  rules:
    {{- range $.Values.ingress.hosts }}
    - host: "{{ $.Release.Name }}-{{ $bitsafe }}.{{ $.Values.ingress.baseDomain }}"
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ $.Release.Name }}-{{ $bitsafe }}
                port:
                  number: {{ $.Values.service.port }}
         {{- end }}
   {{- end }}
{{- end }}
{{- end }}
{{- end }}