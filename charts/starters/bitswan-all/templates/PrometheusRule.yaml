{{- if .Values.alerting }}
{{- $critical := .Values.alerting.critical }}
{{- $warning := .Values.alerting.warning }}
{{- $info := .Values.alerting.info }}
{{- $clusterName := substr 0 7 .Values.ingress.baseDomain }}
{{- $namespace := `{{$labels.namespace}}` }}
{{- $valuePercent := `{{ $value | humanizePercentage }}` }}
{{- $valueCount := `{{ $value | humanize }}` }}
{{- $clusterNamespace := printf "%s/%s"  $clusterName `{{$labels.namespace}}` }}
{{- $namespacePod := printf "%s/%s" `{{$labels.namespace}}` `{{$labels.pod}}` }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: bitswan
spec:
    groups:
    {{- if $critical.enabled }}
    - name: bitswan-critical
      rules:
      - alert: {{ .Release.Namespace }}-CPUsageHigh
        expr: SUM(rate(container_cpu_usage_seconds_total[5m])) BY (pod_name, container_name) / SUM(container_spec_cpu_quota/container_spec_cpu_period) BY (pod_name, container_name) > {{ divf $critical.CPUsageHigh.percent 100 }}
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes | {{ $clusterName }}/{{ $namespace }}: {{`{{$labels.instance}}`}}. Container CPU saturated more than {{ $critical.CPUsageHigh.percent }}% during the last 5m"
          description: "CPU usage for {{`{{$labels.instance}}`}} is high. Threshold/current: {{ $critical.CPUsageHigh.percent }}% / {{ $valuePercent }}"
      - alert: {{ .Release.Namespace }}-MemoryUsageHigh
        expr: SUM(container_memory_working_set_bytes) BY (instance, name) / SUM(container_spec_memory_limit_bytes > 0) BY (instance, name) > {{ divf $critical.MemoryUsageHigh.percent 100 }}
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes | {{ $clusterName }}/{{ $namespace }}: {{`{{$labels.instance}}`}}. Container memory saturated above {{ $critical.MemoryUsageHigh.percent }}%"
          description: "Memory usage for {{`{{$labels.instance}}`}} is high. Threshold/current: {{ $critical.MemoryUsageHigh.percent }}% / {{ $valuePercent }}"
      - alert: {{ .Release.Namespace }}-PersistentVolumeFull
        expr: kubelet_volume_stats_available_bytes == {{ $critical.PersistentVolume.freeBytes }}
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes | {{ $clusterName }}/{{ $namespace }}: PersistentVolume is full"
          description: "PersistentVolume {{`{{$labels.persistentvolumeclaim}}`}} is full and needs immediate action"
      - alert: {{ .Release.Namespace }}-PersistentVolumeAlmostFull
        expr: (kubelet_volume_stats_available_bytes - kubelet_volume_stats_capacity_bytes) / kubelet_volume_stats_capacity_bytes > {{ divf $critical.PersistentVolume.percentFull 100 }}
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes | {{ $clusterName }}/{{ $namespace }}: PersistentVolume is more than {{ $critical.PersistentVolume.percentFull }}% full"
          description: "PersistentVolume {{`{{$labels.persistentvolumeclaim}}`}} is ALMOST full. Threshold/current: {{ $critical.PersistentVolume.percentFull }}% / {{ $valuePercent }}"
      - alert: {{ .Release.Namespace }}-PodFrequentRestarts
        expr: increase(kube_pod_container_status_restarts_total[1h]) >= {{ $critical.PodFrequentRestarts.count }}
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Kubernetes | {{ $clusterNamespace }}/{{`{{$labels.pod}}`}} is restarting frequently"
          description: "Pod {{ $namespacePod }} was restarted more than twice within the last hour. Threshold/current: {{ $critical.PodFrequentRestarts.count }} / {{ $valueCount }}"
    {{- end }}
    {{- if $warning.enabled }}
    - name: bitswan-warning
      rules:
      - alert: CPUsageElevated
        expr: SUM(rate(container_cpu_usage_seconds_total[5m])) BY (pod_name, container_name) / SUM(container_spec_cpu_quota/container_spec_cpu_period) BY (pod_name, container_name) > {{ divf $warning.CPUsageElevated.percent 100 }}
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "More than {{ $warning.CPUsageElevated.percent }}% of CPU is used by {{`{{$labels.job}}`}}/{{`{{$labels.instance}}`}} during the last 5m"
          description: "Too high CPU usage may be a sign of insufficient resources and make process unstable. Consider to either increase available CPU resources or decrease the load on the process"
      - alert: MemoryUsageElevated
        expr: SUM(container_memory_working_set_bytes) BY (instance, name) / SUM(container_spec_memory_limit_bytes > 0) BY (instance, name) > {{ divf $warning.MemoryUsageElevated.percent 100 }}
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: Container High Memory usage (instance {{`{{$labels.instance}}`}})
          description: "Container Memory usage is above {{ $warning.MemoryUsageElevated }}%. Value: {{`{{$value}}`}}; Labels: {{`{{$labels}}`}}"
      - alert: PersistentVolumeElevated
        expr: kubelet_volume_stats_available_bytes / kubelet_volume_stats_capacity_bytes < {{ $warning.PersistentVolumeElevated.percentFull }}
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Persistent Volume Claim {{`{{$labels.persistentvolumeclaim}}`}} is more than {{ $warning.PersistentVolumeElevated.percentFull }}% full"
          description: "Persistent Volume Claim {{`{{$labels.persistentvolumeclaim}}`}} is less than {{ $warning.PersistentVolumeElevated.percentFull }}% full and may require attention"
    {{- end }}
    {{- if $info.enabled }}
    - name: bitswan-info
      rules:
      - alert: CPUsageLow
        expr: SUM(rate(container_cpu_usage_seconds_total[3m])) BY (instance, name) < {{ $info.CPUsageLow.percent }}
        for: 30h
        labels:
          severity: info
        annotations:
          summary: "Container Low CPU utilization (instance {{`{{$labels.instance}}`}})"
          description: "Container CPU utilization is under {{ $info.CPUsageLow.percent }}% for more than a day. Consider reducing the allocated CPU. Value: {{`{{$value}}`}} Labels: {{`{{$labels}}`}}"
      - alert: MemoryUsageLow
        expr: (SUM(container_memory_working_set_bytes) BY (instance, name) / SUM(container_spec_memory_limit_bytes > 0) BY (instance, name) * 100) < {{ $info.MemoryUsageLow.percent }}
        for: 30h
        labels:
          severity: info
        annotations:
          summary: "Container Low Memory usage (instance {{`{{$labels.instance}}`}})"
          description: "Container Memory usage is under 20% for more than a day. Consider reducing the allocated memory. Value: {{`{{$value}}`}}; Labels: {{`{{$labels}}`}}"
    {{- end }}
{{- end }}