{{- range $bitswan, $values := .Values.sites }}
{{- range $instance, $data := $values }}
{{- $bitsafe := cat $bitswan "-" $instance | replace "_" "" | nospace -}}
{{- $global := $.Values.default }}
{{- $autoscaling := mergeOverwrite ($global.autoscaling  | deepCopy) (default dict $data.autoscaling) }}
{{- if $autoscaling.enabled }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $bitsafe }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: {{ $bitsafe }}
  minReplicas: {{ $autoscaling.minReplicas }}
  maxReplicas: {{ $autoscaling.maxReplicas }}
  metrics:
  {{- if $autoscaling.cpuBound }}
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ $autoscaling.targetCPUUtilizationPercentage }}
  {{- end }}
  {{- if $autoscaling.memoryBound }}
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: {{ $autoscaling.targetMemoryUtilizationPercentage }}
  {{- end }}
{{- end }}
{{- end }}
{{- end }}
