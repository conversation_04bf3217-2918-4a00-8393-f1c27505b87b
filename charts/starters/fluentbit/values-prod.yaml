# Configuration of the Fluent-Bit O2CZ GitOps Deployment
# Source: https://network.git.cz.o2/ntwcl/gitopsntw/-/tree/main/templates/helm/starters/fluentbit
# -------------------------------------------------
# General include files for LUA functions and Fluent-Bit Parsers
code:
  - scripts/functions.lua
  - parsers/default_parsers.conf

# Configurations for Fluent-Bit runtime pipeline

configurations:
  - your_pipelines_here.conf
# -------------------------------------------------
# Fluent-Bit Helm Chart values customizations
# Source: https://github.com/fluent/helm-charts/tree/main/charts/fluent-bit
fluent-bit:
  kind: Deployment
  replicaCount: 1
  image:
    repository: network.git.cz.o2:5005/ntwcl/gitopsntw/fluent-bit
    tag: 3.1.7
  testFramework:
    enabled: false
  imagePullSecrets:
    - name: ${APP_PROJECT}-${APP_NAME}-prod-gitlab-dockerconfig
  openShift:
    enabled: true
  resources:
     limits:
       cpu: 1100m
       memory: 1280Mi
     requests:
       cpu: 100m
       memory: 128Mi
  envFrom:
    - secretRef:
        name: ${APP_PROJECT}-${APP_NAME}-prod-credentials
  existingConfigMap: configurations
  extraVolumeMounts:
    - mountPath: /fluent-bit/etc/files
      name: files
  extraVolumes:
    - configMap:
        name: files
        defaultMode: 420
      name: files
  hotReload:
    enabled: true
    extraWatchVolumes:
      - files
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 100m
        memory: 128Mi