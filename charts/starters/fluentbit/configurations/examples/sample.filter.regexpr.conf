[PARSER]
    Name   custom_parser
    Format  regex
    Regex  (?i)user \[(?<user_name>[^\]]+)\] with roles \[(?<user_role>[^\]]+)\] from host\[(?<user_ip>[^\]]+)\] and port \[(?<user_port>\d+)\].*application \[(?<application_name>[^\]]+)\] at host \[(?<host_ip>[^\]]+)\] and port \[(?<host_port>[^\]]+)\]
    Time_Key   timestamp
    Time_Format %Y-%m-%dT%H:%M:%S
    Type       log

# PARSER: Vněj<PERSON><PERSON> zp<PERSON> (syslog zpráva s vloženou zprávou)
[PARSER]
    Name          syslog_outer_regex_parser
    Format        regex
    Regex         <(?<pri>\d+)> (?<timestamp>\w+ \d+ \d+:\d+:\d+) (?<hostname>[^\s]+) (?<appname>[^\s]+) (<.*)
    Time_Key      timestamp

# PARSER: Vnitřn<PERSON> zpr<PERSON>va (druhá syslog zpráva)
[PARSER]
    Name          syslog_inner_regex_parser
    Format        regex
    Regex         <(?<inner_pri>\d+)>1 (?<iso_timestamp>[^ ]+) (?<log_dn>[^ ]+) (?<log_source>[^\s]+) (?<log_unknown>[^\s]+) (?<log_details>.*)
    Time_Key      iso_timestamp

# PARSER: sudo zpráva (klíč-hodnota)
[PARSER]
    Name          syslog_sudo_regex_parser
    Format        regex
    Regex         <(?<pri>\d+)> (?<timestamp>\w+ \d+ \d+:\d+:\d+) (?<hostname>[^\s]+) (?<appname>[^\s]+\[\d+\]): (?<log_content>.*)
    Time_Key      timestamp

# PARSER: Key-Value parser pro sudo klíč-hodnota páry
[PARSER]
    Name              kv_parser_sudo
    Format            kv
    KV_Separator      =
    KV_Pair_Separator \s*;\s*
    KV_Allow_No_Value On
