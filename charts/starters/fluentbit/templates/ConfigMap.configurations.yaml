apiVersion: v1
kind: ConfigMap
metadata:
  name: configurations
data:
# Main Fluent-bit configuration file 'fluent-bit.conf'
{{ ($.Files.Glob "configurations/fluent-bit.conf").AsConfig | indent 2 }}
# All included Fluent-Bit configuration files
  _includes.conf: |-
{{- range $filename := $.Values.configurations }}
{{- $getFile := printf "@INCLUDE %s" $filename}}
{{ $getFile | indent 4 }}
{{- end }}
# All included Fluent-Bit configuration files
{{- range $filename := $.Values.configurations }}
{{- $getFile := printf "%s/%s" "configurations" $filename}}
{{ ($.Files.Glob $getFile).AsConfig | indent 2 }}
{{- end }}
# LUA and Parser configuration files
{{- range $filename := $.Values.code }}
{{- $getFile := printf "%s/%s" "configurations" $filename}}
{{ ($.Files.Glob $getFile).AsConfig | indent 2 }}
{{- end }}
