# Declare variables to be passed into your templates.
fluentd:
  replicaCount: 1
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 2
  image:
    #repository: "fluent/fluentd-kubernetes-daemonset"
    repository: "network.git.cz.o2:5005/ntwcl/gitopsntw/fluentd"
    pullPolicy: "IfNotPresent"
    tag: "v1.17-O2-v10"
  imagePullSecrets:
    - name: nmrep-fluentdgrafanaaudit-prod-gitlab-dockerconfig
  securityContext:
    allowPrivilegeEscalation: false
    runAsNonRoot: true
  resources:
     requests:
       cpu: 500m
       memory: 512Mi
     limits:
       cpu: 1
       memory: 2Gi
  volumes:
  - name: varlog
    hostPath:
      path: /var/log
  - name: varlibdockercontainers
    hostPath:
      path: /var/lib/docker/containers
  - name: etcfluentd-main
    configMap:
      name: 'fluentd-main'
      defaultMode: 0777
  - name: etcfluentd-config
    configMap:
      name: 'fluentd-config'
      defaultMode: 0777
  # INCLUDE  
  #- name: include-avas
  #  configMap:
  #    name: 'include-avas'
  #    defaultMode: 0777
  volumeMounts:
  - name: varlog
    mountPath: /var/log
  - name: varlibdockercontainers
    mountPath: /var/lib/docker/containers
    readOnly: true
  - name: etcfluentd-main
    mountPath: /etc/fluent
  - name: etcfluentd-config
    mountPath: /etc/fluent/config.d/  
  # INCLUDE
  #- name: include-avas
  #  mountPath: /etc/fluent/config.d/includes/include-avas
  ## Fluentd service
  ##
  service:
    type: "ClusterIP"
    annotations: {}
    # loadBalancerIP:
    # externalTrafficPolicy: Local
    ports: {}
  #   - name: "forwarder"
  #     protocol: TCP
  #     containerPort: 5140

  ## Prometheus Monitoring
  ##
  metrics:
    serviceMonitor:
      enabled: enabled
     

    prometheusRule:
      enabled: true


  plugins: []

  configMapConfigs: 
    - fluentd-prometheus-conf

  fileConfigs:
    01_sources.conf: |-

      ## match tag=debug.** and dump to console
      <match debug.**>
        @type stdout
        @id output_stdout
      </match>

      <source>
        @type monitor_agent
        bind 0.0.0.0
        port 24220
      </source>

      # INCLUDE
      #@include includes/*/* 

     
