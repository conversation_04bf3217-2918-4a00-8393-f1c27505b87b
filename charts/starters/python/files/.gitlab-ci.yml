# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.
# ---------------------------------------------------------------
# Source: https://network.git.cz.o2/ntwcl/gitopsntw/-/tree/main/templates/repository/gitops/.gitlab-ci.yml
# Examples of extending this CI/CD configuration: https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_CICD_MODS.md
# ---------------------------------------------------------------
include:
  - project: "ntwcl/gitopsntw"
    ref: HEAD
    file:
      - "/templates/cicd/gitlab-ci-deployment_form.yml"
      - "/templates/cicd/gitlab-ci-deployment.yml"
# ---------------------------------------------------------------

docker:build:on_tag:
  stage: build
  tags:
    - shell
  before_script:
    - docker login -u "${CI_REGISTRY_USER}" -p "${CI_REGISTRY_PASSWORD}" "${CI_REGISTRY}"
  script:
    - docker build -f Dockerfile.single -t ${CI_REGISTRY_IMAGE}/python_application:${CI_COMMIT_TAG} .
    # - docker build -f Dockerfile.multi -t ${CI_REGISTRY_IMAGE}/python_application:${CI_COMMIT_TAG} .
    - docker push ${CI_REGISTRY_IMAGE}/python_application:${CI_COMMIT_TAG}
  rules:
    - if: $CI_COMMIT_TAG
      when: always
    - when: never

docker:synchronize:images:
  stage: build
  tags:
    - shell
  before_script:
    - docker login -u "${SOURCE_PULL_USERNAME}" -p "${SOURCE_PULL_PASSWORD}" "${CI_REGISTRY}"
  script:
    - ./registry_sync.sh "${SOURCE_VERSION}"
  rules:
    - if: $DOCKER_SYNC == 'True'
      when: always
    - when: never
