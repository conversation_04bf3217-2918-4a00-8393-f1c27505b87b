#!/usr/bin/env bash
# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.

set -e

function loggie() {
    LOG_SEVERITY=${1}
    LOG_LINE="${2}"
    TIMESTAMP=$(date +'%Y-%m-%d %H:%M:%S')

    if [[ "${LOG_LEVEL}" == "DEBUG" ]]; then
        # Print everything
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "ERROR" && "${LOG_SEVERITY}" == "ERROR" ]]; then
        # Print only ERROR messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "WARNING" && ( "${LOG_SEVERITY}" != "INFO" && "${LOG_SEVERITY}" != "DEBUG" ) ]]; then
        # Print only WARNING and ERROR messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    elif [[ "${LOG_LEVEL}" == "INFO" ]]; then
        # Print INFO+ messages
        echo -e "${TIMESTAMP} | ${LOG_SEVERITY} | ${LOG_LINE}"
    fi
}

# Some definitions
LOG_LEVEL="INFO"

SOURCE_REPO="network.git.cz.o2:5005/<group>/<project>"
# Delete this afterwards
# >>>
echo "Set the 'SOURCE_REPO' variable of this shell-script first: '${SOURCE_REPO}'"
exit 0
# <<<
SOURCE_TAG="${1}"
SOURCE_IMAGE="${SOURCE_REPO}:${SOURCE_TAG}"
DESTINATION_IMAGE=${CI_REGISTRY_IMAGE}:${SOURCE_TAG}

if DOCKER_CLI_EXPERIMENTAL=enabled docker manifest inspect "${DESTINATION_IMAGE}" >/dev/null; then

    # Image found, no syncing
    loggie "INFO" "Docker Image already present: '${DESTINATION_IMAGE}'"
    exit 1

else

  loggie "INFO" "Pulling source Docker Image: '${SOURCE_IMAGE}'"
  docker pull "${SOURCE_IMAGE}"
  loggie "INFO" "Pushing as destination Docker Image: '${DESTINATION_IMAGE}'"
  docker tag "${SOURCE_IMAGE}" "${DESTINATION_IMAGE}"
  docker push "${DESTINATION_IMAGE}"
  loggie "INFO" "Syncing Docker Image OK"
fi

loggie "INFO" "All done"

