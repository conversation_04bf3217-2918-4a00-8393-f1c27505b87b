# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.
# ---------------------------------------------------------------

docker:images:
  stage: build
  tags:
    - shell
  before_script:
    - docker login -u "${CI_REGISTRY_USER}" -p "${CI_REGISTRY_PASSWORD}" "${CI_REGISTRY}"
  script:
    # Build & Store Docker Image
    - docker build -t ${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG} .
    - docker push ${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG}
    # Trigger Remote CI/CD Pipeline to sync image into the Deployments Repository (you need to create the Trigger Token first)
    # CI/CD Settings / Pipeline trigger tokens
    - curl -X POST -F token="${DOCKER_SYNC_TRIGGER_TOKEN}" -F "ref=main" -F "variables[DOCKER_SYNC]=True" -F "variables[SOURCE_VERSION]=${CI_COMMIT_TAG}" https://network.git.cz.o2/api/v4/projects/<REMOTE_PROJECT_ID>/trigger/pipeline
  rules:
    - if: $CI_COMMIT_TAG
      when: always
    - when: never
