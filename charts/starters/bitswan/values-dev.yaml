# Declare variables to be passed into your templates.

# -------------------------------------
# BitSwan Worker configuration
# Documentation: https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/BITSWAN_HELM.md
# -------------------------------------
# Common Template (YAML Anchor)
commonConfig: &commonConfig
  imageVersion: 1.0.0
# -------------------------------------
bitswan:
  sites:
    <domain>-<project>:
      <instance>:
        configuration: site_test.conf
  # -------------------------------------
  # Connectivity Pod
  # Source Code: https://network.git.cz.o2/deployments/nmrep-dbtester/
  # Documentation: https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/K8S_NETWORK_TESTS.md
  # -------------------------------------
  connectivityPod:
    enabled: false
    imageVersion: latest

  # -------------------------------------
  # BitSwan Configuration Defaults
  # -------------------------------------
  default:
    autoscaling:
      enabled: false
      cpuBound: false
      memoryBound: false
      minReplicas: 1
      maxReplicas: 4
      targetCPUUtilizationPercentage: 80
      targetMemoryUtilizationPercentage: 80
    extra_configs:
    debug: false
    enabled: true
    environment: production
    hostAliases:
      enabled: false
      records:
        *******: dns.google
    livenessProbe:
      initialDelaySeconds: 240
      periodSeconds: 10
    nodeSelector: {}
    pvc:
      enabled: false
      accessModes:
        - ReadWriteOncePod
      resources:
        requests:
          storage: 300Mi
      storageClassName: csi-cephfs-sc
    replicas: 1
    resources:
      limits:
        cpu: 1100m
        memory: 2Gi
      requests:
        cpu: 100m
        memory: 128Mi
    service:
      enabled: false
      port: 8080
      name: http
    sla: 0
    startupProbe:
      failureThreshold: 30
      periodSeconds: 10
    terminationGracePeriodSeconds: 180

  # -------------------------------------
  # BitSwan Alerting Thresholds
  # IMPORTANT: Enabling Alerting PrometheusRules on '-dev' Namespace
  # is strongly discouraged: generated Alerts will be routed
  # directly into the CNFM Zabbix infrastructure.
  # -------------------------------------
  alerting:
    enabled: false
