# Dynamic MCP Server - Project Idea

## <PERSON><PERSON><PERSON><PERSON>

Vytvořit vysoce výkonnou, flexibilní a škálovatelnou platformu, která funguje jako **runtime-configurable workflow execution engine**. Tato platforma slouží jako backend pro MCP (Model Context Protocol) klienty a zároveň poskytuje REST API rozhraní. Veškerá business logika, včetně obsluhy MCP funkcí a REST API endpointů, je definována a vykonávána jako dynamicky konfigurovatelná workflow. Tyto workflow jsou sestavena z jednotlivých uzlů (nodes), kde každý uzel provádí specifickou operaci (např. volán<PERSON> databáze, externího API, spuštění JavaScriptu).

Platforma je navržena pro horizontální <PERSON> (běh ve více instancích, např. Kubernetes pody) a klade silný důraz na detailní dohledování (observability) a monitoring od samého počátku vývoje. Využívá standardní MCP SDK pro zajištění kompatibility.

## 🎯 Klíčové Koncepty

### Workflow-First Execution

- Jádrem systému je workflow engine. I jednoduché operace, jako jsou standardní MCP funkce (getDateTime, echo) nebo obsluha REST endpointů, jsou interně realizovány jako workflow.
- Workflow je sekvence propojených uzlů, kterými protékají data.

### Node-Based Architecture (Stavební Bloky Workflow)

#### Trigger Nodes

Spouštějí workflow. Mohou to být:

- Příchozí MCP volání funkce.
- Příchozí HTTP požadavek na REST API endpoint.
- Časovač (cron-like).
- Volání z jiného workflow.

#### Action Nodes

Provádějí konkrétní akce. Příklady typů uzlů:

- **JavaScript Node**: Vykonává uživatelsky definovaný JavaScript kód v bezpečném sandboxu (např. VM2, IsolateVM).
- **SQL Node**: Vykonává SQL příkazy proti nakonfigurované databázi (primárně PostgreSQL, potenciálně Vector DB).
- **REST API Node**: Volá externí REST API služby.
- **SOAP Node**: Volá externí SOAP služby.
- **Redis Node**: Provádí operace nad Redis databází.
- **LiteLLM Node**: Komunikuje s LiteLLM proxy serverem pro volání AI modelů.
- **Call Workflow Node**: Spouští jiné, již definované, workflow (sub-workflow).

#### Logic Nodes (pro budoucí rozšíření)

Uzly pro řízení toku, jako IF/ELSE, Switch, Merge. Pro MVP se zaměříme na sekvenční tok.

### Workflow Memory (Execution Context)

- Každé spuštění workflow má svůj vlastní, izolovaný paměťový kontext – dynamický objekt, který drží data relevantní pro daný běh.
- Tento kontext je perzistován v Redis po dobu vykonávání workflow (a případně déle pro asynchronní operace nebo pro účely debugování).
- Jednotlivé uzly workflow mohou číst data z tohoto kontextu (např. pomocí JSONPath notace) a zapisovat/modifikovat data v něm.
- Tímto způsobem si uzly předávají data.

### Configuration as Data (Správa přes Admin API)

- Definice všech workflow (tj. seznam uzlů, jejich konfigurace, propojení mezi nimi, typ triggeru) jsou uloženy jako strukturovaná data (JSONB) v PostgreSQL databázi.
- Konfigurace datových zdrojů (připojovací údaje k externím DB, API klíče atd.) jsou také uloženy v PostgreSQL, přičemž citlivé údaje jsou šifrovány.
- Správa (CRUD operace) těchto definic a konfigurací probíhá výhradně přes verzované Admin REST API. Toto API umožňuje dynamicky měnit chování serveru bez nutnosti redeploye.

### Horizontální Škálovatelnost

- Aplikace je navržena tak, aby mohla běžet v několika identických instancích (podech).
- Statelessness (kde je to možné): REST API handlery jsou primárně stateless.
- MCP Session Affinity: Pro MCP komunikaci (HTTP streaming) se spoléhá na mechanismy load balanceru/ingress controlleru (např. sticky sessions), které zajistí, že všechny požadavky v rámci jedné MCP session směřují na stejný pod. Aplikace sama neřeší komplexní externalizaci MCP session stavu napříč pody v MVP.
- Sdílený Backend: Všechny instance sdílejí stejnou PostgreSQL databázi (pro konfigurace a dohledávací data) a stejnou Redis instanci (pro workflow memory a cachování).

### Interface-Driven a Decoupled Design

- Všechny klíčové komponenty systému (workflow engine, node handlery, data providery, služby) jsou definovány skrze jasná rozhraní (TypeScript interfaces).
- Pro propojení komponent se využívá Dependency Injection (DI), což usnadňuje testování, údržbu a rozšiřitelnost.

### Self-Describing Components pro AI

- Každá MCP funkce (která je workflow) a potenciálně i jednotlivé uzly mají metadata (uložená v PostgreSQL jako součást definice workflow/uzlu).
- Tato metadata popisují účel, vstupní parametry (JSON Schema), výstupní formát a případné vedlejší efekty.
- To umožňuje LLM modelům lépe pochopit, co daná funkce/uzel dělá, a efektivněji je využívat.

### Detailní Dohledování (Observability) a Monitoring

#### Strukturované Logování

Všechny komponenty produkují strukturované logy (JSON) s kontextuálními informacemi (např. workflow_execution_id, node_id).

#### Perzistentní Dohledávací Záznamy

- Pro každý běh workflow se do PostgreSQL ukládá detailní záznam do tabulky workflow_executions (kdy začal/skončil, stav, vstup, výstup, chyba).
- Pro každý vykonaný uzel v rámci workflow se do PostgreSQL ukládá záznam do tabulky workflow_node_executions (kdy začal/skončil, doba trvání, vstupní data pro uzel, výstupní data z uzlu, chyba).
- Tato data umožňují detailní audit, debugging a analýzu výkonu.

#### Prometheus Metriky

- Server exportuje metriky na endpointu /metrics ve formátu Prometheus.
- Zahrnuje standardní Node.js metriky, metriky HTTP serveru.
- Specifické aplikační metriky: počet spuštěných/dokončených/selhaných workflow (s labely pro název workflow), doba trvání workflow (histogram), počet volání jednotlivých typů uzlů, počet volání a doba trvání operací data providerů (s labely pro typ providera, cílový systém), chybovost.

#### OpenTelemetry (Budoucí Směr)

Pro pokročilé distribuované trasování napříč komponentami a workflow kroky.

## 🚀 Hlavní Cíle a Přínosy

- **Rychlost a Výkon**: Cílem jsou odpovědi v řádu desítek milisekund pro synchronní operace. Toho se dosahuje efektivním workflow enginem, optimalizovanými data providery a agresivním cachováním (v Redis).

- **Flexibilita a Agilita**: Možnost dynamicky vytvářet, testovat a nasazovat novou business logiku (jako workflow) přes Admin API bez nutnosti modifikace kódu serveru a jeho restartu.

- **Rozšiřitelnost**: Snadné přidávání nových typů uzlů pro workflow a nových data providerů díky interface-driven designu.

- **Škálovatelnost**: Schopnost obsloužit rostoucí zátěž přidáním dalších instancí serveru.

- **Provozní Robustnost**: Díky detailnímu dohledávání a monitoringu je možné efektivně identifikovat problémy, analyzovat výkon a zajišťovat stabilitu v produkčním prostředí.

- **Jednoduchost (MVP přístup)**: Začít s nejnutnější sadou funkcí a postupně přidávat komplexitu, ale vždy s důrazem na udržení co nejjednoduššího a nejčistšího designu.

- **MCP Kompatibilita**: Plná podpora standardního MCP protokolu pro snadnou integraci s AI agenty a jinými MCP-kompatibilními systémy.

## ⚙️ Klíčové Technologické Komponenty a Závislosti

- **Runtime**: Node.js s TypeScriptem.

- **Databáze**:

  - PostgreSQL: Pro ukládání konfigurací (workflow, datové zdroje) a detailních dohledávacích dat.
  - Redis: Pro ukládání workflow memory (execution context), cachování a potenciálně pro správu session (pokud by bylo nutné nad rámec sticky sessions).

- **Komunikace**:

  - HTTP/1.1 nebo HTTP/2 pro REST API a MCP (HTTP streaming).

- **Workflow Engine**: Vlastní implementace, inspirovaná n8n, ale zjednodušená pro MVP.

- **JavaScript Sandbox**: VM2 nebo IsolateVM.

- **MCP Interakce**: @modelcontextprotocol/sdk.

- **Monitoring**: prom-client pro Prometheus metriky.

- **Logování**: Pino nebo Winston.

- **HTTP Framework**: Express.js nebo Fastify.

- **DI Kontejner**: InversifyJS nebo nativní DI z NestJS (pokud by se zvažoval NestJS jako framework).

## 🎪 Příklad Užití

### Definice Služby přes Admin API

1. Administrátor definuje nový datový zdroj, např. "CRM_API" typu REST, s base URL a autentizačními údaji (uloženo šifrovaně).

2. Administrátor vytvoří workflow wf_fetchCustomerDetails:
   - Trigger: MCP Function Call fetchCustomerDetails.
   - Metadata: Popis funkce, JSON Schema pro vstup (customerId: string), JSON Schema pro výstup.
   - Uzel 1 (JavaScript): Validuje customerId.
   - Uzel 2 (REST API): Volá "CRM_API" s customerId pro získání detailů zákazníka. Výsledek uloží do workflow memory jako customerDataFromCRM.
   - Uzel 3 (SQL): Na základě customerId dotáže interní PostgreSQL databázi pro historii objednávek. Výsledek uloží jako orderHistory.
   - Uzel 4 (JavaScript): Zkombinuje customerDataFromCRM a orderHistory do finálního objektu. Tento objekt je výstupem workflow.

### Volání MCP Funkce Klientem

1. MCP klient (např. LLM orchestrátor) se připojí k serveru (na libovolný pod, session affinity zajistí směrování).

2. Klient volá MCP funkci fetchCustomerDetails s argumentem customerId: "123".

3. Pod, který přijal požadavek, spustí instanci workflow wf_fetchCustomerDetails:
   - workflow_execution_id je vygenerováno.
   - Záznam o startu workflow je uložen do workflow_executions v PostgreSQL.
   - Workflow se vykonává:
     - Uzel 1 (JS): Provede validaci. Záznam o jeho běhu (vstup, výstup, doba trvání) se uloží do workflow_node_executions. Data v Redis (workflow memory) jsou aktualizována.
     - Uzel 2 (REST): Volá CRM. Záznam o jeho běhu se uloží. Data v Redis jsou aktualizována.
     - Uzel 3 (SQL): Dotazuje DB. Záznam o jeho běhu se uloží. Data v Redis jsou aktualizována.
     - Uzel 4 (JS): Kombinuje data. Záznam o jeho běhu se uloží.
   - Finální výsledek z workflow (zkompletovaná data o zákazníkovi) je vrácen MCP klientovi.
   - Záznam o dokončení workflow (stav, doba trvání, finální výsledek) je aktualizován v workflow_executions.

### Monitoring a Dohledávání

1. Během celého procesu se generují logy a metriky.

2. Operátor může v Grafaně sledovat dashboardy s metrikami (doba trvání wf_fetchCustomerDetails, úspěšnost volání CRM_API, atd.).

3. V případě problému může operátor prohledat logy podle workflow_execution_id nebo se podívat do tabulek workflow_executions a workflow_node_executions pro detailní trasu a data každého kroku.

## 📅 Implementační Fáze

Vzhledem k workflow-centrické architektuře je potřeba upravit původní plán fází vývoje:

### Fáze 0: Základní Setup s Workflow Foundation (Týden 1-2)

- Core MCP server s SDK integrací
- Základní workflow engine se sekvenčním vykonáváním
- Jednoduché typy uzlů (JavaScript, HTTP)
- Databázové schéma pro workflow
- Redis integrace pro workflow memory
- Základní observability (logování, jednoduché metriky)

### Fáze 1: Rozšířené Typy Uzlů a Admin API (Týden 3-4)

- Další typy uzlů (SQL, Redis, atd.)
- Admin API pro správu workflow
- Rozšířená observability (detailní logování, Prometheus metriky)
- Základní error handling a retries
- Hot-reload konfigurace

### Fáze 2: Pokročilé Workflow Funkce a Datové Zdroje (Týden 5-6)

- Podmíněná logika a větvení
- Paralelní vykonávání
- Datové transformace
- Integrace externích datových zdrojů
- Verzování

### Fáze 3+: Multi-tenancy, Admin UI a Optimalizace (Týden 7+)

- Plná multi-tenancy
- Admin UI pro správu workflow
- Výkonnostní optimalizace
- Pokročilý monitoring a alerting
- Rozšířené bezpečnostní funkce

## 🔄 Integrace s Existujícím Kódem

Existující implementace MCP serveru bude sloužit jako základ, na kterém bude workflow engine postaven:

1. **MCP Server Core**: Zůstane zachován, bude rozšířen o integraci s workflow enginem.
2. **Dynamic Service Loader**: Bude rozšířen o podporu workflow-based nástrojů.
3. **JavaScript VM**: Bude využit pro JavaScript uzly v workflow.
4. **Databázová infrastruktura**: Bude rozšířena o tabulky pro workflow a jejich vykonávání.

## 🛠️ Další Kroky

1. Detailní návrh workflow engine a jeho komponent
2. Definice rozhraní pro různé typy uzlů
3. Implementace základního workflow engine
4. Integrace s MCP serverem
5. Vytvoření prvních testovacích workflow
6. Implementace Admin API pro správu workflow
