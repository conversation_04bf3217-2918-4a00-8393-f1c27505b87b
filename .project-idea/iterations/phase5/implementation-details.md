# Phase 5: Implementation Details - Advanced Admin UI & Visual Workflow Editor

## 🎯 Detailní Implementační <PERSON>pecifikace

### 1. ReactFlow Integration Architecture

#### 1.1 Core ReactFlow Setup

**Package Dependencies:**
```
@xyflow/react: ^12.0.0
@monaco-editor/react: ^4.6.0
@mui/material: ^6.0.0
@mui/x-data-grid: ^7.0.0
react-redux: ^9.0.0
@reduxjs/toolkit: ^2.0.0
```

**Canvas Configuration:**
- Viewport: Unlimited canvas s pan/zoom
- Grid: Snap-to-grid s 20px spacing
- Minimap: Collapsible minimap pro navigation
- Controls: Zoom, fit view, fullscreen
- Background: Dot pattern s theme integration

#### 1.2 Custom Node Architecture

**Base Node Interface:**
```typescript
interface WorkflowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
  style?: NodeStyle;
}

interface NodeData {
  label: string;
  config: NodeConfig;
  validation: ValidationState;
  execution?: ExecutionState;
}
```

**Node Categories:**
- **Triggers**: REST API, MCP Function, Timer, Webhook, Manual
- **Actions**: JavaScript, SQL, Redis, REST Call, LiteLLM, Email
- **Logic**: IF/ELSE, Switch, Merge, Loop, Parallel
- **Terminators**: Response, MCP Response, Error Handler, Success/Failure

### 2. Visual Node Implementation

#### 2.1 Node Component Structure

**Custom Node Components:**
- `TriggerNode`: Entry points pro workflows
- `ActionNode`: Processing a external calls
- `LogicNode`: Conditional a flow control
- `TerminatorNode`: Workflow completion

**Node Features:**
- Dynamic port generation based na configuration
- Visual status indicators (idle, running, success, error)
- Inline validation messages
- Collapsible configuration panels
- Context menu s node-specific actions

#### 2.2 Node Configuration System

**Dynamic Form Generation:**
- JSON Schema-based form rendering
- Real-time validation s error highlighting
- Conditional field visibility
- Custom input components pro specific data types
- Configuration templates a presets

**Monaco Editor Integration:**
- JavaScript code editing s IntelliSense
- SQL syntax highlighting a autocomplete
- Error detection a linting
- Code formatting a beautification
- Snippet library pro common operations

### 3. Workflow Context Management

#### 3.1 Context Architecture

**Context Structure:**
```typescript
interface WorkflowContext {
  executionId: string;
  workflowId: string;
  tenantId: string;
  variables: Record<string, any>;
  nodeResults: Record<string, any>;
  metadata: ContextMetadata;
}
```

**Redis Implementation:**
- Key pattern: `workflow:context:{executionId}`
- TTL: Configurable (default 24 hours)
- Serialization: JSON s compression
- Atomic operations pro concurrent access

#### 3.2 Context Visualization

**Context Inspector Panel:**
- Real-time context updates via WebSocket
- JSONPath query interface
- Variable history a change tracking
- Data type visualization
- Export/import context data

**Debug Mode Features:**
- Step-by-step execution s breakpoints
- Variable inspection na každém kroku
- Execution timeline s performance metrics
- Error stack trace s context state
- Replay capability pro debugging

### 4. Use Case Implementations

#### 4.1 Use Case 1: REST API to JavaScript Processing

**Workflow Structure:**
1. **REST API Trigger Node**
   - Endpoint configuration: POST /api/process
   - Payload validation schema
   - Authentication requirements
   - Rate limiting settings

2. **Payload Extraction Node**
   - JSONPath expressions pro data extraction
   - Data type conversion
   - Validation rules
   - Default values

3. **JavaScript Processing Node**
   - Monaco editor s context IntelliSense
   - Available context variables: `context.payload.*`
   - Error handling a try/catch
   - Performance monitoring

4. **Response Formatter Node**
   - Response template configuration
   - Data mapping z context
   - HTTP status code setting
   - Headers configuration

5. **REST Response Node**
   - Final response generation
   - Content-Type handling
   - Error response formatting
   - Logging a metrics

#### 4.2 Use Case 2: Database Integration with Redis Caching

**Workflow Structure:**
1. **REST API Trigger Node**
   - Similar to Use Case 1
   - Parameter extraction z URL/body

2. **SQL Query Node**
   - Query builder interface
   - Parameter binding visualization
   - Connection pool management
   - Query performance monitoring

3. **Data Transformation Node**
   - JavaScript transformation logic
   - Data mapping a filtering
   - Format conversion (SQL → JSON)
   - Validation a sanitization

4. **Redis Cache Node**
   - Key generation strategy
   - TTL configuration
   - Cache invalidation rules
   - Conflict resolution

5. **Response Node**
   - Success response (201 Created)
   - Error handling
   - Audit logging

#### 4.3 Use Case 3: MCP Function with Redis Lookup

**Workflow Structure:**
1. **MCP Function Trigger Node**
   - Function metadata definition
   - Parameter schema validation
   - Documentation generation
   - Version management

2. **Redis Lookup Node**
   - Key pattern matching
   - Data retrieval a deserialization
   - Cache miss handling
   - Performance optimization

3. **Data Processing Node**
   - Business logic implementation
   - Data enrichment
   - Validation a transformation
   - Error handling

4. **MCP Response Node**
   - Response schema validation
   - Format compliance
   - Error response handling
   - Metrics collection

### 5. Modern UI/UX Implementation

#### 5.1 Design System Upgrade

**Material Design 3 Features:**
- Dynamic color theming
- Improved typography scale
- Enhanced component variants
- Better accessibility support
- Dark/light mode transitions

**Custom Theme Configuration:**
```typescript
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: { main: '#1976d2' },
    secondary: { main: '#dc004e' },
    background: {
      default: '#fafafa',
      paper: '#ffffff'
    }
  },
  typography: {
    fontFamily: 'Inter, Roboto, sans-serif'
  },
  components: {
    // Custom component overrides
  }
});
```

#### 5.2 Performance Optimization

**Canvas Performance:**
- Virtual scrolling pro large node lists
- Viewport-based rendering
- Efficient re-render strategies
- Memory management pro large workflows

**State Management:**
- Redux Toolkit s RTK Query
- Normalized state structure
- Selective component updates
- Memoization strategies

**Bundle Optimization:**
- Code splitting pro workflow editor
- Lazy loading pro heavy components
- Tree shaking optimization
- Asset optimization

### 6. Backend API Extensions

#### 6.1 Workflow Editor API

**New Endpoints:**
- `GET /api/workflows/{id}/visual` - Visual workflow definition
- `POST /api/workflows/{id}/visual` - Save visual workflow
- `GET /api/workflows/templates` - Workflow templates
- `POST /api/workflows/{id}/test` - Test workflow execution
- `GET /api/workflows/{id}/context/{executionId}` - Execution context

#### 6.2 Real-Time Features

**WebSocket Implementation:**
- Real-time workflow execution updates
- Collaborative editing support
- Context change notifications
- Performance metrics streaming

**Event Types:**
- `workflow.execution.started`
- `workflow.execution.completed`
- `workflow.node.executed`
- `workflow.context.updated`
- `workflow.error.occurred`

### 7. Testing Strategy

#### 7.1 Component Testing

**React Component Tests:**
- Jest + React Testing Library
- Visual regression testing s Storybook
- Accessibility testing s axe-core
- Performance testing s React DevTools Profiler

#### 7.2 Integration Testing

**Workflow Editor Tests:**
- End-to-end workflow creation
- Node configuration validation
- Context management testing
- API integration testing

#### 7.3 Performance Testing

**Metrics to Monitor:**
- Canvas rendering performance (target: 60fps)
- Memory usage s large workflows
- Bundle size impact
- API response times
- WebSocket message latency

### 8. Migration Strategy

#### 8.1 Backward Compatibility

**JSON Workflow Support:**
- Import existing JSON workflows
- Export visual workflows to JSON
- API compatibility maintenance
- Gradual migration path

#### 8.2 Data Migration

**Migration Tools:**
- Automated workflow conversion
- Configuration mapping
- Validation a testing
- Rollback capabilities

This implementation plan provides detailed technical specifications for Phase 5, ensuring successful delivery of the advanced Admin UI with visual workflow editor while maintaining system reliability and performance.
