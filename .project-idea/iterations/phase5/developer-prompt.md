# Phase 5 Developer Prompt - Visual Workflow Editor

## Úkol

Implementujte Phase 5 Dynamic MCP Server projektu - Advanced Admin UI s vizuálním workflow editorem. Transformujte současné základní Admin UI na moderní platformu s drag&drop workflow editorem.

## Hlavní Cíle

### PRIORITA 1: Visual Workflow Management
- Vizuální workflow editor s ReactFlow.dev
- Canvas s drag&drop funkcionalitou pro workflow nodes
- Workflow kontext management s Redis persistencí
- Implementace 3 konkrétních use cases

### PRIORITA 2: Modern UI/UX
- Material Design 3 upgrade
- Odstranění demo dat
- Responsive design optimalizace

## Klíčové Use Cases k Implementaci

1. **REST API → JavaScript Processing** - POST endpoint s payload processing
2. **Database + Redis Caching** - SQL query s Redis cache integration
3. **MCP Function + Redis Lookup** - MCP funkce s Redis data lookup

## Technické <PERSON>ky

- ReactFlow.dev pro visual editor
- Monaco Editor pro JavaScript/SQL editing
- Material-UI v6+ s Material Design 3
- Performance: <100ms response, 60fps rendering
- 90% test coverage pro nové komponenty
- Backward compatibility s existujícími workflows

## Implementační Timeline

- **Týden 1**: ReactFlow integration & basic canvas
- **Týden 2**: Advanced node features & configuration
- **Týden 3**: Use cases implementation & testing
- **Týden 4**: UI/UX modernization & polish

## 📚 Detailní Dokumentace

Před implementací prostudujte následující dokumenty v `.project-idea/iterations/phase5/`:

### Povinné k prostudování:
- **design.md** - Kompletní architektonický návrh a implementační plán
- **postulates.md** - Základní principy a development guidelines
- **todo.md** - Detailní implementační checklist s prioritami
- **implementation-details.md** - Technické specifikace a API extensions
- **README.md** - Přehled Phase 5 a kontext

### Souvisící dokumentace:
- `.project-idea/idea.md` - Celková vize projektu
- `.project-idea/development-principles.md` - Development standards
- `.project-idea/iterations/phase0-4/` - Existující implementace k využití

## Success Criteria

Implementace bude úspěšná, když:
- Visual workflow editor bude plně funkční s drag&drop
- Všechny 3 use cases budou implementovány a testovány
- Performance requirements budou splněny
- Modern UI/UX bude nasazeno s pozitivním user feedback
- Backward compatibility bude zachována
- Test coverage dosáhne 90%

## Poznámky

- Využijte existující komponenty z Phase 0-4
- Neduplicujte kód, rozšiřujte existující struktury
- Postupujte podle todo.md checklistu
- Pravidelně aktualizujte progress
- V případě nejasností konzultujte design.md a implementation-details.md

Začněte prostudováním dokumentace a poté postupujte podle implementačního plánu v todo.md.
