# Phase 5 Implementation Prompt - Advanced Admin UI & Visual Workflow Editor

Implementujte Phase 5 Dynamic MCP Server projektu podle následující specifikace. Tato fáze se zaměřuje na vytvoření pokročilého Admin UI s vizuálním workflow editorem.

## Kontext a Cíle

R<PERSON>řte současné Admin UI o vizuální workflow editor s drag&drop funkcionalitou. Hlavním cílem je umožnit administrátorům vytvářet workflow bez znalosti JSON struktury pomocí intuitivního vizuálního rozhraní.

## Prioritní Požadavky

### PRIORITA 1: Visual Workflow Management

Implementujte vizuální workflow editor s následujícími funkcemi:

- ReactFlow.dev integration pro canvas s drag&drop
- Node palette s kategorizovanými workflow komponenty
- Workflow kontext management s Redis persistencí
- Implementace 3 specifických use cases
- Real-time debugging a context visualization

### PRIORITA 2: Modern UI/UX Design

Modernizujte design a user experience:

- Material Design 3 upgrade s custom theming
- Odstranění demo dat a implementace reálných funkcí
- Responsive design optimalizace
- Dark/light mode support

## Klíčové Use Cases k Implementaci

### Use Case 1: REST API to JavaScript Processing
- REST API endpoint trigger s payload extraction
- JavaScript processing node s context access
- Response formatting a validation
- Complete end-to-end workflow

### Use Case 2: Database Integration with Redis Caching
- SQL query execution s parameter binding
- Data transformation pipeline
- Redis caching integration
- Performance monitoring

### Use Case 3: MCP Function with Redis Lookup
- MCP function trigger configuration
- Redis data lookup s pattern matching
- Data processing a response formatting
- Function metadata management

## Technické Specifikace

### Frontend Requirements

Rozšiřte současnou React aplikaci o:

- ReactFlow.dev pro visual workflow editor
- Monaco Editor pro JavaScript a SQL editing
- Material-UI v6+ s Material Design 3
- Redux Toolkit pro advanced state management
- WebSocket integration pro real-time updates

### Backend Extensions

Rozšiřte Admin API o:

- Visual workflow editor endpoints
- Workflow context management API
- Real-time WebSocket server
- Enhanced workflow execution engine
- Performance monitoring endpoints

### Node Types k Implementaci

Vytvořte následující kategorie nodes:

**Trigger Nodes:**
- REST API Endpoint Node
- MCP Function Call Node
- Timer/Cron Node
- Webhook Node
- Manual Trigger Node

**Action Nodes:**
- JavaScript Execution Node
- SQL Query Node
- Redis Operation Node
- REST API Call Node
- LiteLLM Node
- Email/Notification Node

**Logic Nodes:**
- IF/ELSE Conditional Node
- Switch/Case Node
- Merge/Join Node
- Loop/Iteration Node
- Parallel Execution Node

**Terminator Nodes:**
- Response Node
- MCP Response Node
- Error Handler Node
- Success/Failure Node

## Implementační Požadavky

### Performance Requirements

- Workflow editor response time pod 100ms
- Canvas rendering na 60fps pro workflows s 100+ nodes
- Memory optimization pro large workflows
- Bundle size increase maximálně 20%

### User Experience Requirements

- Zero JSON knowledge requirement pro basic workflows
- Real-time validation a error prevention
- Intuitive drag&drop interface
- Comprehensive help a documentation
- Workflow creation time reduction o 70%

### Technical Requirements

- 90% test coverage pro nové komponenty
- Backward compatibility s existujícími workflows
- Migration tools pro JSON workflows
- Security compliance s existing standards
- Accessibility compliance WCAG 2.1

## Dokumentace k Prostudování

Před implementací prostudujte následující dokumenty v .project-idea/iterations/phase5/:

- design.md - Kompletní architektonický návrh
- postulates.md - Základní principy a guidelines
- todo.md - Detailní implementační checklist
- implementation-details.md - Technické specifikace
- README.md - Přehled a kontext Phase 5

Také prostudujte související dokumenty z předchozích fází:

- .project-idea/idea.md - Celková vize projektu
- .project-idea/development-principles.md - Development guidelines
- .project-idea/iterations/phase0-4/ - Existující implementace

## Implementační Postup

### Týden 1: ReactFlow Integration
- Nastavte ReactFlow.dev v frontend projektu
- Implementujte základní canvas s pan/zoom
- Vytvořte node palette s drag&drop
- Implementujte základní node types

### Týden 2: Advanced Node Features
- Dokončete všechny node types
- Implementujte node configuration system
- Integrujte Monaco Editor pro code editing
- Vytvořte context management system

### Týden 3: Use Cases Implementation
- Implementujte všechny 3 use cases
- Vytvořte workflow testing framework
- Implementujte debug mode a error handling
- Optimalizujte performance

### Týden 4: UI/UX Modernization
- Upgradujte na Material Design 3
- Implementujte responsive design
- Odstraňte demo data
- Dokončete testing a documentation

## Kvalitní Standardy

Dodržujte development principles definované v .project-idea/development-principles.md:

- Interface-first development approach
- Comprehensive testing s mocky
- Documentation as code
- Security by design
- Performance awareness
- Modular architecture s max 300 lines per file

## Success Criteria

Implementace bude považována za úspěšnou, když:

- Visual workflow editor bude plně funkční
- Všechny 3 use cases budou implementovány a testovány
- Performance requirements budou splněny
- Modern UI/UX bude nasazeno
- Backward compatibility bude zachována
- Test coverage dosáhne 90%
- Documentation bude kompletní

## Poznámky k Implementaci

- Využijte existující komponenty z Phase 0-4 kde je to možné
- Neduplicujte kód, rozšiřujte existující struktury
- Udržujte API backward compatibility
- Implementujte postupně s možností rollback
- Testujte průběžně s real-world scenarios

Při implementaci postupujte podle todo.md checklistu a pravidelně aktualizujte progress. V případě technických otázek nebo nejasností konzultujte design.md a implementation-details.md dokumenty.
