# Workflow Engine Implementation Details

This document provides detailed implementation guidance for the workflow engine component of the Dynamic MCP Server.

## Core Components

### 1. Workflow Engine

The `WorkflowEngine` is the central component responsible for executing workflows. It coordinates the execution of nodes and manages the workflow context.

```typescript
// src/workflow/engine/WorkflowEngine.ts
import { injectable, inject } from 'inversify';
import { IWorkflowEngine } from '../../types/workflow';
import { INodeRegistry } from '../../types/workflow';
import { IWorkflowMemory } from '../../types/workflow';
import { WorkflowRepository } from '../../database/repositories/WorkflowRepository';
import { WorkflowExecutionRepository } from '../../database/repositories/WorkflowExecutionRepository';
import { WorkflowNodeExecutionRepository } from '../../database/repositories/WorkflowNodeExecutionRepository';
import { ILogger } from '../../types/logger';
import { v4 as uuidv4 } from 'uuid';

@injectable()
export class WorkflowEngine implements IWorkflowEngine {
  constructor(
    @inject('WorkflowRepository') private workflowRepository: WorkflowRepository,
    @inject('WorkflowExecutionRepository')
    private workflowExecutionRepository: WorkflowExecutionRepository,
    @inject('WorkflowNodeExecutionRepository')
    private workflowNodeExecutionRepository: WorkflowNodeExecutionRepository,
    @inject('NodeRegistry') private nodeRegistry: INodeRegistry,
    @inject('WorkflowMemory') private workflowMemory: IWorkflowMemory,
    @inject('Logger') private logger: ILogger
  ) {}

  async executeWorkflow(workflowId: string, input: any): Promise<any> {
    // 1. Load workflow configuration from database
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // 2. Create execution record
    const executionId = uuidv4();
    await this.workflowExecutionRepository.create({
      id: executionId,
      workflow_id: workflowId,
      status: 'RUNNING',
      input_data: input,
      started_at: new Date()
    });

    // 3. Initialize workflow context
    const context = {
      executionId,
      workflowId,
      input,
      output: null,
      nodeResults: {},
      variables: {}
    };
    await this.workflowMemory.updateContext(executionId, context);

    try {
      // 4. Execute nodes in sequence
      const { nodes, edges } = workflow.nodes_config;

      // Find start node (node with no incoming edges)
      const startNodeId = this.findStartNode(nodes, edges);
      if (!startNodeId) {
        throw new Error('No start node found in workflow');
      }

      // Execute nodes in sequence
      let currentNodeId = startNodeId;
      let result = input;

      while (currentNodeId) {
        const node = nodes.find((n) => n.id === currentNodeId);
        if (!node) {
          throw new Error(`Node with ID ${currentNodeId} not found`);
        }

        // Log node execution start
        this.logger.info(`Executing node ${node.id} (${node.type}) in workflow ${workflowId}`);

        // Create node execution record
        const nodeExecutionId = uuidv4();
        const nodeStartTime = new Date();
        await this.workflowNodeExecutionRepository.create({
          id: nodeExecutionId,
          workflow_execution_id: executionId,
          node_id: node.id,
          node_type: node.type,
          status: 'RUNNING',
          input_data: result,
          started_at: nodeStartTime
        });

        try {
          // Get node implementation
          const nodeImpl = this.nodeRegistry.getNode(node.type, node.config);

          // Execute node
          const nodeContext = await this.workflowMemory.getContext(executionId);
          result = await nodeImpl.execute(result, nodeContext);

          // Update node results in context
          nodeContext.nodeResults[node.id] = result;
          await this.workflowMemory.updateContext(executionId, nodeContext);

          // Update node execution record
          const nodeEndTime = new Date();
          await this.workflowNodeExecutionRepository.update(nodeExecutionId, {
            status: 'COMPLETED',
            output_data: result,
            completed_at: nodeEndTime,
            execution_time_ms: nodeEndTime.getTime() - nodeStartTime.getTime()
          });
        } catch (error) {
          // Handle node execution error
          await this.workflowNodeExecutionRepository.update(nodeExecutionId, {
            status: 'FAILED',
            error_details: {
              message: error.message,
              stack: error.stack
            },
            completed_at: new Date()
          });
          throw error;
        }

        // Find next node
        const nextEdge = edges.find((e) => e.source === currentNodeId);
        currentNodeId = nextEdge ? nextEdge.target : null;
      }

      // 5. Update workflow execution record
      const endTime = new Date();
      await this.workflowExecutionRepository.update(executionId, {
        status: 'COMPLETED',
        output_data: result,
        completed_at: endTime,
        execution_time_ms: endTime.getTime() - new Date(context.started_at).getTime()
      });

      // 6. Return final result
      return result;
    } catch (error) {
      // Handle workflow execution error
      await this.workflowExecutionRepository.update(executionId, {
        status: 'FAILED',
        error_details: {
          message: error.message,
          stack: error.stack
        },
        completed_at: new Date()
      });
      throw error;
    } finally {
      // Clean up workflow context (optional for in-memory implementation)
      // await this.workflowMemory.deleteContext(executionId);
    }
  }

  private findStartNode(nodes: any[], edges: any[]): string | null {
    // Find node that has no incoming edges
    const nodesWithIncomingEdges = new Set(edges.map((e) => e.target));
    const startNode = nodes.find((n) => !nodesWithIncomingEdges.has(n.id));
    return startNode ? startNode.id : null;
  }
}
```

### 2. Node Registry

The `NodeRegistry` is responsible for registering and providing node implementations.

```typescript
// src/workflow/nodes/NodeRegistry.ts
import { injectable } from 'inversify';
import { INodeRegistry, INode, NodeFactory } from '../../types/workflow';

@injectable()
export class NodeRegistry implements INodeRegistry {
  private nodeFactories: Map<string, NodeFactory> = new Map();

  registerNode(type: string, nodeFactory: NodeFactory): void {
    this.nodeFactories.set(type, nodeFactory);
  }

  getNode(type: string, config: Record<string, any>): INode {
    const factory = this.nodeFactories.get(type);
    if (!factory) {
      throw new Error(`Node type '${type}' is not registered`);
    }
    return factory(config);
  }

  getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodeFactories.keys());
  }
}
```

### 3. Workflow Memory

The `WorkflowMemory` manages the execution context for workflows. For Phase 0, this will be an in-memory implementation.

```typescript
// src/workflow/engine/WorkflowMemory.ts
import { injectable } from 'inversify';
import { IWorkflowMemory, WorkflowContext } from '../../types/workflow';

@injectable()
export class InMemoryWorkflowMemory implements IWorkflowMemory {
  private contexts: Map<string, WorkflowContext> = new Map();

  async getContext(executionId: string): Promise<WorkflowContext> {
    const context = this.contexts.get(executionId);
    if (!context) {
      throw new Error(`Workflow context for execution ID ${executionId} not found`);
    }
    return { ...context }; // Return a copy to prevent direct modification
  }

  async updateContext(executionId: string, context: WorkflowContext): Promise<void> {
    this.contexts.set(executionId, { ...context }); // Store a copy
  }

  async deleteContext(executionId: string): Promise<void> {
    this.contexts.delete(executionId);
  }
}
```

### 4. Base Node

The `BaseNode` provides common functionality for all node types.

```typescript
// src/workflow/nodes/BaseNode.ts
import { INode, WorkflowContext } from '../../types/workflow';
import { ILogger } from '../../types/logger';

export abstract class BaseNode implements INode {
  protected config: Record<string, any>;
  protected logger: ILogger;

  constructor(config: Record<string, any>, logger: ILogger) {
    this.config = config;
    this.logger = logger;
    this.validateConfig();
  }

  abstract execute(input: any, context: WorkflowContext): Promise<any>;

  protected validateConfig(): void {
    // Base validation logic
    if (!this.config) {
      throw new Error('Node configuration is required');
    }
  }
}
```

### 5. JavaScript Node

The `JavaScriptNode` executes JavaScript code in a sandboxed environment.

```typescript
// src/workflow/nodes/JavaScriptNode.ts
import { BaseNode } from './BaseNode';
import { WorkflowContext } from '../../types/workflow';
import { IScriptEngine } from '../../types/script';
import { ILogger } from '../../types/logger';
import { inject, injectable } from 'inversify';

@injectable()
export class JavaScriptNode extends BaseNode {
  constructor(
    config: Record<string, any>,
    @inject('ScriptEngine') private scriptEngine: IScriptEngine,
    @inject('Logger') logger: ILogger
  ) {
    super(config, logger);
  }

  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing JavaScript node with input: ${JSON.stringify(input)}`);

    // Create script execution context
    const scriptContext = {
      args: input,
      logger: this.logger,
      workflowContext: {
        variables: context.variables,
        nodeResults: context.nodeResults
      }
    };

    // Execute script
    const result = await this.scriptEngine.execute(this.config.script, scriptContext);

    this.logger.debug(`JavaScript node execution completed with result: ${JSON.stringify(result)}`);
    return result;
  }

  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.script) {
      throw new Error('JavaScript node requires a script property');
    }

    if (typeof this.config.script !== 'string') {
      throw new Error('Script property must be a string');
    }
  }
}

// Factory function for creating JavaScript nodes
export const createJavaScriptNode = (container: any) => {
  return (config: Record<string, any>) => {
    return new JavaScriptNode(config, container.get('ScriptEngine'), container.get('Logger'));
  };
};
```

### 6. HTTP Node

The `HTTPNode` makes HTTP requests to external services.

```typescript
// src/workflow/nodes/HTTPNode.ts
import { BaseNode } from './BaseNode';
import { WorkflowContext } from '../../types/workflow';
import { ILogger } from '../../types/logger';
import { inject, injectable } from 'inversify';
import axios, { AxiosRequestConfig, Method } from 'axios';

@injectable()
export class HTTPNode extends BaseNode {
  constructor(config: Record<string, any>, @inject('Logger') logger: ILogger) {
    super(config, logger);
  }

  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing HTTP node with input: ${JSON.stringify(input)}`);

    // Prepare request configuration
    const method = (this.config.method || 'GET').toUpperCase() as Method;
    const url = this.replaceVariables(this.config.url, input, context);

    const requestConfig: AxiosRequestConfig = {
      method,
      url,
      headers: this.config.headers || {},
      timeout: this.config.timeout || 30000 // Default timeout: 30 seconds
    };

    // Add request body for POST, PUT, PATCH
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      requestConfig.data = this.config.body
        ? this.replaceVariables(this.config.body, input, context)
        : input;
    }

    // Add query parameters for GET, DELETE
    if (['GET', 'DELETE'].includes(method) && this.config.params) {
      requestConfig.params = this.replaceVariables(this.config.params, input, context);
    }

    try {
      // Execute HTTP request
      const response = await axios(requestConfig);

      // Process response
      const result = this.config.returnFullResponse ? response : response.data;

      this.logger.debug(`HTTP node execution completed with status: ${response.status}`);
      return result;
    } catch (error) {
      this.logger.error(`HTTP node execution failed: ${error.message}`);

      if (this.config.failOnError !== false) {
        throw error;
      }

      // Return error information if failOnError is false
      return {
        error: true,
        message: error.message,
        status: error.response?.status,
        data: error.response?.data
      };
    }
  }

  protected validateConfig(): void {
    super.validateConfig();

    if (!this.config.url) {
      throw new Error('HTTP node requires a url property');
    }

    if (
      this.config.method &&
      !['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'].includes(
        this.config.method.toUpperCase()
      )
    ) {
      throw new Error(`Invalid HTTP method: ${this.config.method}`);
    }
  }

  private replaceVariables(template: any, input: any, context: WorkflowContext): any {
    if (typeof template === 'string') {
      // Replace ${input} with the input value
      return (
        template
          .replace(/\${input}/g, JSON.stringify(input))
          // Replace ${variables.xyz} with context variables
          .replace(/\${variables\.([^}]+)}/g, (match, key) => {
            return JSON.stringify(context.variables[key]);
          })
          // Replace ${nodeResults.xyz} with node results
          .replace(/\${nodeResults\.([^}]+)}/g, (match, key) => {
            return JSON.stringify(context.nodeResults[key]);
          })
      );
    } else if (typeof template === 'object' && template !== null) {
      // Recursively process object properties
      const result = Array.isArray(template) ? [] : {};
      for (const key in template) {
        result[key] = this.replaceVariables(template[key], input, context);
      }
      return result;
    }

    return template;
  }
}

// Factory function for creating HTTP nodes
export const createHTTPNode = (container: any) => {
  return (config: Record<string, any>) => {
    return new HTTPNode(config, container.get('Logger'));
  };
};
```

### 7. Workflow-Based Tool

The `WorkflowBasedTool` is an MCP tool implementation that executes a workflow.

```typescript
// src/workflow/tools/WorkflowBasedTool.ts
import { inject, injectable } from 'inversify';
import { Tool } from '@modelcontextprotocol/sdk';
import { IWorkflowEngine } from '../../types/workflow';
import { ILogger } from '../../types/logger';

@injectable()
export class WorkflowBasedTool implements Tool {
  constructor(
    private config: {
      name: string;
      description: string;
      input_schema: any;
      workflow_id: string;
    },
    @inject('WorkflowEngine') private workflowEngine: IWorkflowEngine,
    @inject('Logger') private logger: ILogger
  ) {}

  getName(): string {
    return this.config.name;
  }

  getDescription(): string {
    return this.config.description;
  }

  getParameterSchema(): any {
    return this.config.input_schema;
  }

  async call(params: any): Promise<any> {
    this.logger.info(
      `Executing workflow-based tool ${this.config.name} with workflow ID ${this.config.workflow_id}`
    );

    try {
      // Execute the workflow
      const result = await this.workflowEngine.executeWorkflow(this.config.workflow_id, params);

      this.logger.info(`Workflow-based tool ${this.config.name} execution completed`);
      return result;
    } catch (error) {
      this.logger.error(
        `Workflow-based tool ${this.config.name} execution failed: ${error.message}`
      );
      throw error;
    }
  }
}

// Factory function for creating workflow-based tools
export const createWorkflowBasedTool = (container: any) => {
  return (config: {
    name: string;
    description: string;
    input_schema: any;
    workflow_id: string;
  }) => {
    return new WorkflowBasedTool(config, container.get('WorkflowEngine'), container.get('Logger'));
  };
};
```

## Integration with Dynamic Service Loader

To integrate the workflow engine with the existing Dynamic Service Loader, we need to modify the loader to support workflow-based tools:

```typescript
// src/services/DynamicServiceLoader.ts
import { injectable, inject } from 'inversify';
import { Tool } from '@modelcontextprotocol/sdk';
import { FunctionRepository } from '../database/repositories/FunctionRepository';
import { WorkflowRepository } from '../database/repositories/WorkflowRepository';
import { ILogger } from '../types/logger';
import { IScriptEngine } from '../types/script';
import { createWorkflowBasedTool } from '../workflow/tools/WorkflowBasedTool';
import { Container } from 'inversify';

@injectable()
export class DynamicServiceLoader {
  private tools: Map<string, Tool> = new Map();
  private workflowBasedToolFactory: ReturnType<typeof createWorkflowBasedTool>;

  constructor(
    @inject('FunctionRepository') private functionRepository: FunctionRepository,
    @inject('WorkflowRepository') private workflowRepository: WorkflowRepository,
    @inject('ScriptEngine') private scriptEngine: IScriptEngine,
    @inject('Logger') private logger: ILogger,
    @inject('Container') private container: Container
  ) {
    // Create factory for workflow-based tools
    this.workflowBasedToolFactory = createWorkflowBasedTool(container);
  }

  async loadTools(): Promise<void> {
    this.logger.info('Loading tools from database');

    // Clear existing tools
    this.tools.clear();

    // Load function configurations
    const functions = await this.functionRepository.findAll();

    for (const func of functions) {
      if (!func.enabled) {
        continue;
      }

      try {
        let tool: Tool;

        if (func.handler_config.type === 'script') {
          // Create script-based tool
          tool = this.createScriptBasedTool(func);
        } else if (func.handler_config.type === 'workflow') {
          // Create workflow-based tool
          tool = await this.createWorkflowBasedTool(func);
        } else {
          this.logger.warn(
            `Unknown handler type: ${func.handler_config.type} for function ${func.name}`
          );
          continue;
        }

        this.tools.set(func.name, tool);
        this.logger.info(`Loaded tool: ${func.name}`);
      } catch (error) {
        this.logger.error(`Failed to load tool ${func.name}: ${error.message}`);
      }
    }

    this.logger.info(`Loaded ${this.tools.size} tools`);
  }

  getTools(): Tool[] {
    return Array.from(this.tools.values());
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  private createScriptBasedTool(func: any): Tool {
    // Implementation of script-based tool creation
    // ...
  }

  private async createWorkflowBasedTool(func: any): Promise<Tool> {
    const workflowId = func.handler_config.workflow_id;

    // Verify that the workflow exists
    const workflow = await this.workflowRepository.findById(workflowId);
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }

    // Create workflow-based tool
    return this.workflowBasedToolFactory({
      name: func.name,
      description: func.description,
      input_schema: func.input_schema,
      workflow_id: workflowId
    });
  }
}
```

## Example Workflow

Here's an example of a simple workflow that could be used for testing:

### Echo Workflow

This workflow simply echoes back the input with a timestamp.

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "echo",
  "description": "Echo the input with a timestamp",
  "version": 1,
  "input_schema": {
    "type": "object",
    "properties": {
      "message": {
        "type": "string",
        "description": "Message to echo"
      }
    },
    "required": ["message"]
  },
  "output_schema": {
    "type": "object",
    "properties": {
      "message": {
        "type": "string"
      },
      "timestamp": {
        "type": "string",
        "format": "date-time"
      }
    }
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "input",
        "type": "javascript",
        "name": "Process Input",
        "config": {
          "script": "return args;"
        }
      },
      {
        "id": "addTimestamp",
        "type": "javascript",
        "name": "Add Timestamp",
        "config": {
          "script": "return { message: args.message, timestamp: new Date().toISOString() };"
        }
      }
    ],
    "edges": [
      {
        "source": "input",
        "target": "addTimestamp"
      }
    ]
  },
  "enabled": true
}
```

### HTTP Request Workflow

This workflow makes an HTTP request to an external API and returns the result.

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "name": "getWeather",
  "description": "Get weather information for a city",
  "version": 1,
  "input_schema": {
    "type": "object",
    "properties": {
      "city": {
        "type": "string",
        "description": "City name"
      }
    },
    "required": ["city"]
  },
  "output_schema": {
    "type": "object"
  },
  "nodes_config": {
    "nodes": [
      {
        "id": "validateInput",
        "type": "javascript",
        "name": "Validate Input",
        "config": {
          "script": "if (!args.city) throw new Error('City is required'); return args;"
        }
      },
      {
        "id": "fetchWeather",
        "type": "http",
        "name": "Fetch Weather",
        "config": {
          "url": "https://api.weatherapi.com/v1/current.json",
          "method": "GET",
          "params": {
            "key": "YOUR_API_KEY",
            "q": "${input.city}"
          }
        }
      },
      {
        "id": "formatResponse",
        "type": "javascript",
        "name": "Format Response",
        "config": {
          "script": "return { city: args.location.name, temperature: args.current.temp_c, condition: args.current.condition.text };"
        }
      }
    ],
    "edges": [
      {
        "source": "validateInput",
        "target": "fetchWeather"
      },
      {
        "source": "fetchWeather",
        "target": "formatResponse"
      }
    ]
  },
  "enabled": true
}
```

## Conclusion

This implementation provides a solid foundation for the workflow engine in Phase 0. It includes:

1. A workflow engine that can execute sequential workflows
2. A node registry for managing different node types
3. JavaScript and HTTP node implementations
4. In-memory workflow context management
5. Integration with the existing Dynamic Service Loader

The implementation is designed to be extensible, allowing for additional node types and more advanced workflow features in future phases. The workflow engine follows the interface-driven design principle, making it easy to test and maintain.

In subsequent phases, the following enhancements can be added:

1. Redis integration for workflow memory
2. Conditional branching and parallel execution
3. Additional node types (SQL, Redis, etc.)
4. Error handling and retries
5. Admin API for workflow management
6. Performance optimizations
