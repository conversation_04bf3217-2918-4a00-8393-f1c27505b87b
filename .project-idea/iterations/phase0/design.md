# Phase 0: Basic Setup with Workflow Foundation (MVP - Week 1-2)

## Overview

Phase 0 focuses on establishing the foundational components of the Dynamic MCP Server with a workflow-centric approach. This phase will deliver a Minimum Viable Product (MVP) that demonstrates the core functionality of dynamically loading and executing MCP functions as workflows from a database. The goal is to have a working MCP server that can load and execute at least one simple workflow from the database when called by a client.

## Architecture

The architecture for Phase 0 will include the following components:

```mermaid
graph TD
    subgraph Client
        MCPClient["MCP Client (e.g., MCP Inspector)"]
    end

    subgraph "MCP Server (Phase 0)"
        MCPCore["MCP Server Core"]
        ToolsHandler["Tools Handler"]
        DynamicLoader["Dynamic Service Loader"]

        subgraph "Workflow Engine"
            WorkflowEngine["Workflow Engine"]
            NodeRegistry["Node Registry"]
            WorkflowMemory["Workflow Memory"]
        end

        subgraph "Node Types"
            JSNode["JavaScript Node"]
            HTTPNode["HTTP Node"]
        end

        ScriptEngine["JavaScript VM (Sandbox)"]
        DB["PostgreSQL Database"]
    end

    MCPClient -- "1. MCP Request (tools/list, tools/call)" --> MC<PERSON>ore
    MCPCore -- "2. Route Request" --> <PERSON><PERSON><PERSON><PERSON><PERSON>
    ToolsHandler -- "3. Get Available Tools" --> DynamicLoader
    DynamicLoader -- "4. Load Functions/Workflows" --> DB
    DynamicLoader -- "5. Execute Workflow" --> WorkflowEngine
    WorkflowEngine -- "6. Get Node Implementations" --> NodeRegistry
    WorkflowEngine -- "7. Store/Retrieve Context" --> WorkflowMemory
    NodeRegistry -- "8. Use Node Types" --> JSNode
    NodeRegistry -- "8. Use Node Types" --> HTTPNode
    JSNode -- "9. Execute JavaScript" --> ScriptEngine
    MCPCore -- "10. MCP Response" --> MCPClient
```

## Component Descriptions

### 1. MCP Server Core

The core server component that implements the MCP protocol and handles client connections. It will:

- Accept and validate MCP JSON-RPC requests
- Route requests to appropriate handlers
- Return responses to clients
- Manage client sessions

Implementation will use the `@modelcontextprotocol/sdk` package for MCP protocol compliance.

### 2. Tools Handler

Responsible for handling MCP tool-related requests:

- `tools/list`: Returns a list of available tools
- `tools/call`: Executes a specific tool with provided arguments

The handler will interact with the Dynamic Service Loader to get available tools and execute them.

### 3. Dynamic Service Loader

A key component that loads function and workflow definitions from the database and creates executable tool instances:

- Queries the database for active function and workflow configurations
- Creates tool instances from configurations (JavaScript tools or workflow-based tools)
- Provides access to loaded tools for the Tools Handler

### 4. Workflow Engine

The core component for executing workflows:

- Manages the sequential execution of workflow nodes
- Maintains workflow state and context
- Coordinates data flow between nodes
- Tracks execution metrics and logs

For Phase 0, the workflow engine will support only sequential execution of nodes.

### 5. Node Registry

Manages the available node types and their implementations:

- Registers node type implementations
- Provides node implementations to the workflow engine
- Validates node configurations

### 6. Workflow Memory

Manages the execution context for workflows:

- Stores and retrieves data between node executions
- Maintains isolation between different workflow executions
- For Phase 0, this will be an in-memory implementation with preparation for Redis integration in later phases

### 7. Node Types

#### JavaScript Node

- Executes JavaScript code in a sandboxed environment
- Provides access to workflow context data
- Returns execution results to the workflow engine

#### HTTP Node

- Makes HTTP requests to external services
- Supports GET, POST, PUT, DELETE methods
- Handles response parsing and error handling

### 8. JavaScript VM (Sandbox)

A secure execution environment for running user-defined JavaScript code:

- Executes JavaScript code in a sandboxed environment
- Provides a controlled execution context with access to workflow data
- Prevents malicious code execution

### 9. PostgreSQL Database

Stores function and workflow configurations:

- Function metadata (name, description)
- Workflow definitions (nodes, connections)
- Input/output schemas (JSON Schema)
- Execution history and metrics

## Implementation Details

### Database Schema

```sql
-- Function definitions
CREATE TABLE mcp_functions (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    input_schema JSONB NOT NULL,
    handler_config JSONB NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Workflow definitions
CREATE TABLE mcp_workflows (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    version INT NOT NULL DEFAULT 1,
    input_schema JSONB NOT NULL,
    output_schema JSONB,
    nodes_config JSONB NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Workflow execution history
CREATE TABLE mcp_workflow_executions (
    id UUID PRIMARY KEY,
    workflow_id UUID NOT NULL REFERENCES mcp_workflows(id),
    status VARCHAR(50) NOT NULL,
    input_data JSONB,
    output_data JSONB,
    error_details JSONB,
    started_at TIMESTAMPTZ NOT NULL,
    completed_at TIMESTAMPTZ,
    execution_time_ms INTEGER
);

-- Node execution history
CREATE TABLE mcp_workflow_node_executions (
    id UUID PRIMARY KEY,
    workflow_execution_id UUID NOT NULL REFERENCES mcp_workflow_executions(id),
    node_id VARCHAR(255) NOT NULL,
    node_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    input_data JSONB,
    output_data JSONB,
    error_details JSONB,
    started_at TIMESTAMPTZ NOT NULL,
    completed_at TIMESTAMPTZ,
    execution_time_ms INTEGER
);
```

The `handler_config` JSON structure for JavaScript functions:

```json
{
  "type": "script",
  "script_content": "// JavaScript code here"
}
```

The `handler_config` JSON structure for workflow-based functions:

```json
{
  "type": "workflow",
  "workflow_id": "uuid-of-workflow"
}
```

The `nodes_config` JSON structure for workflows:

```json
{
  "nodes": [
    {
      "id": "node1",
      "type": "javascript",
      "name": "Validate Input",
      "config": {
        "script": "// JavaScript code here"
      }
    },
    {
      "id": "node2",
      "type": "http",
      "name": "Call External API",
      "config": {
        "url": "https://api.example.com/data",
        "method": "GET",
        "headers": {
          "Content-Type": "application/json"
        }
      }
    }
  ],
  "edges": [
    {
      "source": "node1",
      "target": "node2"
    }
  ]
}
```

### TypeScript Interfaces

```typescript
// Function configuration interface
interface DynamicFunctionConfig {
  id: string;
  name: string;
  description: string;
  input_schema: JSONSchema;
  handler_config: {
    type: 'script' | 'workflow';
    script_content?: string;
    workflow_id?: string;
  };
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

// Workflow configuration interface
interface WorkflowConfig {
  id: string;
  name: string;
  description: string;
  version: number;
  input_schema: JSONSchema;
  output_schema?: JSONSchema;
  nodes_config: {
    nodes: WorkflowNode[];
    edges: WorkflowEdge[];
  };
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
}

// Workflow node interface
interface WorkflowNode {
  id: string;
  type: string;
  name: string;
  config: Record<string, any>;
}

// Workflow edge interface
interface WorkflowEdge {
  source: string;
  target: string;
}

// Workflow engine interface
interface IWorkflowEngine {
  executeWorkflow(workflowId: string, input: any): Promise<any>;
}

// Node interface
interface INode {
  execute(input: any, context: WorkflowContext): Promise<any>;
}

// Node registry interface
interface INodeRegistry {
  registerNode(type: string, nodeFactory: NodeFactory): void;
  getNode(type: string, config: Record<string, any>): INode;
}

type NodeFactory = (config: Record<string, any>) => INode;

// Workflow memory interface
interface IWorkflowMemory {
  getContext(executionId: string): Promise<WorkflowContext>;
  updateContext(executionId: string, context: WorkflowContext): Promise<void>;
  deleteContext(executionId: string): Promise<void>;
}

// Workflow context interface
interface WorkflowContext {
  executionId: string;
  workflowId: string;
  input: any;
  output: any;
  nodeResults: Record<string, any>;
  variables: Record<string, any>;
}

// Database interface
interface IDatabase {
  query<T>(sql: string, params?: any[]): Promise<T[]>;
  queryOne<T>(sql: string, params?: any[]): Promise<T | null>;
  execute(sql: string, params?: any[]): Promise<void>;
}

// Script engine interface
interface IScriptEngine {
  execute(script: string, context: ScriptExecutionContext): Promise<any>;
}

interface ScriptExecutionContext {
  args: any;
  logger: ILogger;
  // For workflow nodes, additional context is provided
  workflowContext?: {
    variables: Record<string, any>;
    nodeResults: Record<string, any>;
  };
}
```

### Directory Structure

```
src/
├── config/                  # Configuration files
│   ├── database.ts          # Database configuration
│   ├── server.ts            # Server configuration
│   └── logger.ts            # Logging configuration
│
├── database/                # Database related code
│   ├── entities/            # TypeORM entities
│   │   ├── Function.ts      # Function entity
│   │   ├── Workflow.ts      # Workflow entity
│   │   ├── WorkflowExecution.ts
│   │   └── WorkflowNodeExecution.ts
│   ├── migrations/          # Database migrations
│   ├── repositories/        # Database repositories
│   └── Database.ts          # Database service
│
├── mcp/                     # MCP protocol implementation
│   ├── MCPServer.ts         # MCP server implementation
│   ├── ToolsHandler.ts      # Handler for tools/list and tools/call
│   └── SessionManager.ts    # MCP session management
│
├── services/                # Core services
│   ├── DynamicServiceLoader.ts  # Loads functions from DB
│   ├── ScriptEngine.ts      # JavaScript VM for executing code
│   └── LoggerService.ts     # Logging service
│
├── workflow/                # Workflow engine components
│   ├── engine/
│   │   ├── WorkflowEngine.ts    # Main workflow engine
│   │   ├── WorkflowExecutor.ts  # Executes workflow steps
│   │   └── WorkflowMemory.ts    # Manages workflow context
│   ├── nodes/
│   │   ├── NodeRegistry.ts      # Registry of node types
│   │   ├── BaseNode.ts          # Base class for nodes
│   │   ├── JavaScriptNode.ts    # JavaScript node implementation
│   │   └── HTTPNode.ts          # HTTP node implementation
│   ├── models/
│   │   ├── WorkflowConfig.ts    # Workflow configuration model
│   │   ├── WorkflowContext.ts   # Workflow execution context
│   │   └── WorkflowNode.ts      # Node configuration model
│   └── tools/
│       └── WorkflowBasedTool.ts # MCP tool backed by workflow
│
├── utils/                   # Utility functions
│   ├── errors.ts            # Error handling utilities
│   ├── validation.ts        # Input validation utilities
│   └── schema.ts            # JSON Schema utilities
│
├── types/                   # TypeScript type definitions
│   ├── mcp.ts               # MCP related types
│   ├── function.ts          # Function related types
│   └── workflow.ts          # Workflow related types
│
├── app.ts                   # Application setup
└── index.ts                 # Entry point
```

## Implementation Approach

### 1. Project Setup

1. Initialize a new Node.js project with TypeScript
2. Set up ESLint, Prettier, and Jest for code quality and testing
3. Configure TypeORM for database access
4. Set up logging with Winston or Pino
5. Configure environment variables with dotenv

### 2. Database Implementation

1. Create database entities for functions, workflows, and execution history
2. Implement database migrations
3. Create repositories for database access
4. Implement database service for connection management

### 3. MCP Protocol Implementation

1. Implement MCP server using the `@modelcontextprotocol/sdk` package
2. Create handlers for MCP requests (tools/list, tools/call)
3. Implement session management for MCP clients

### 4. Dynamic Function Loading

1. Implement the DynamicServiceLoader to load functions from the database
2. Create base classes for different function types (script-based, workflow-based)
3. Implement caching for loaded functions

### 5. JavaScript VM (Sandbox)

1. Implement a secure JavaScript execution environment using VM2 or similar
2. Create a context object for script execution
3. Implement error handling and timeout mechanisms

### 6. Workflow Engine

1. Implement the core workflow engine for sequential execution
2. Create the node registry for managing node types
3. Implement the workflow memory for context management
4. Create base classes for workflow nodes
5. Implement JavaScript and HTTP node types

### 7. Testing and Documentation

1. Write unit tests for all components
2. Create integration tests for the complete flow
3. Document the API and internal architecture
4. Create example workflows for testing

## Expected Outputs

By the end of Phase 0, we will have:

1. A working MCP server that implements the core MCP protocol
2. A database schema for storing function and workflow configurations
3. A Dynamic Service Loader that can load functions and workflows from the database
4. A workflow engine for executing workflow-based tools
5. JavaScript and HTTP node implementations
6. A JavaScript VM for executing JavaScript code
7. At least one simple test workflow stored in the database and executable via MCP

The MVP will support:

- MCP initialization
- Listing available tools
- Calling a tool with arguments (both script-based and workflow-based)
- Sequential workflow execution
- Returning results to the client

## Limitations for Phase 0

1. Only sequential workflow execution (no branching or parallel execution)
2. Limited node types (only JavaScript and HTTP)
3. In-memory workflow context (no Redis integration yet)
4. No admin API for managing workflows (manual database insertion for testing)
5. No advanced error handling or retries
6. No performance optimizations

These limitations will be addressed in subsequent phases of the project.
