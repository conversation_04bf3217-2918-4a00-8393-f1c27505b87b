# Dynamic MCP Server - Phase 0 Implementation

This document describes the implementation of Phase 0 of the Dynamic MCP Server, which provides a foundation for dynamically loading and executing MCP functions from a database.

## Architecture

The Dynamic MCP Server is built with the following components:

```mermaid
graph TD
    subgraph Client
        MCPClient["MCP Client (e.g., MCP Inspector)"]
    end

    subgraph "MCP Server (Phase 0)"
        MCPCore["MCP Server Core"]
        ToolsHandler["Tools Handler"]
        DynamicLoader["Dynamic Service Loader"]
        ScriptEngine["JavaScript VM (Sandbox)"]
        DB["PostgreSQL Database"]
    end

    MCPClient -- "1. MCP Request (tools/list, tools/call)" --> MCPCore
    MCPCore -- "2. Route Request" --> ToolsHandler
    ToolsHandler -- "3. Get Available Tools" --> DynamicLoader
    DynamicLoader -- "4. Load Functions" --> <PERSON>ler -- "5. Execute Tool" --> ScriptEngine
    MCPCore -- "6. MCP Response" --> MC<PERSON>lient
```

## Technology Stack

- **Node.js**: Version 22 LTS
- **TypeScript**: Version 5.x
- **PostgreSQL**: For storing function configurations
- **Redis**: For caching (future use)
- **Express.js**: For HTTP server
- **@modelcontextprotocol/sdk**: For MCP protocol implementation
- **VM2**: For secure JavaScript execution
- **TypeORM**: For database access
- **InversifyJS**: For dependency injection
- **Pino**: For logging

## Setup Instructions

### Prerequisites

- Node.js 22 LTS
- PostgreSQL (running on default port 5432)
- Redis (running on default port 6379)

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd aidcc-ccczautomationmcpserver
   ```

2. Install dependencies:
   ```bash
   yarn install
   ```

3. Create a `.env` file with the following content:
   ```
   DB_HOST=localhost
   DB_PORT=5432
   DB_USERNAME=postgres
   DB_PASSWORD=postgres
   DB_DATABASE=mcp_server
   NODE_ENV=development
   LOG_LEVEL=debug
   PORT=3000
   HOST=0.0.0.0
   ```

4. Create the database:
   ```bash
   createdb mcp_server
   ```

5. Run database migrations:
   ```bash
   yarn migration:run
   ```

6. Create a sample function:
   ```bash
   yarn create-sample
   ```

7. Build the project:
   ```bash
   yarn build
   ```

8. Start the server:
   ```bash
   yarn start
   ```

### Testing with MCP Inspector

To test the server with the MCP Inspector:

1. In one terminal, start the server:
   ```bash
   yarn dev
   ```

2. In another terminal, run the MCP Inspector:
   ```bash
   npx @modelcontextprotocol/inspector http://localhost:3000/mcp
   ```

3. Use the Inspector to list available tools and call them.

## Implementation Details

### Database Schema

The database schema for Phase 0 includes a single table:

```sql
CREATE TABLE mcp_functions (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    input_schema JSONB NOT NULL,
    handler_config JSONB NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Core Components

1. **MCPServer**: Implements the MCP protocol using the SDK.
2. **DynamicServiceLoader**: Loads function configurations from the database.
3. **JavaScriptTool**: Executes JavaScript code in a secure sandbox.
4. **PostgresDatabase**: Provides access to the PostgreSQL database.
5. **MessageRouter**: Routes MCP messages to the appropriate handlers.

### Dependency Injection

The project uses InversifyJS for dependency injection, which makes it easy to:

- Manage service lifecycles
- Swap implementations for testing
- Decouple components

### Security Considerations

- JavaScript code is executed in a VM2 sandbox for security.
- Database access is restricted to read-only operations for function execution.
- Input validation is performed using JSON Schema.

## Next Steps

After completing Phase 0, the next steps are:

1. Implement prompt templates (Phase 1)
2. Add data sources and connection management (Phase 2)
3. Implement workflows and multi-tenancy (Phase 3)

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Ensure PostgreSQL is running
   - Check database credentials in `.env`
   - Verify database exists

2. **MCP Inspector Connection Issues**:
   - Ensure server is running
   - Check the URL (http://localhost:3000/mcp)
   - Verify no firewall blocking connections

3. **Function Execution Errors**:
   - Check JavaScript syntax in function handler
   - Verify input parameters match schema
   - Check for timeout issues (default 10s)
