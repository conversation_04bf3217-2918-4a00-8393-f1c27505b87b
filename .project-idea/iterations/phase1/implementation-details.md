# Phase 1: Implementation Details

This document provides detailed implementation guidance for Phase 1 of the Dynamic MCP Server project, focusing on extending the workflow engine with additional node types, implementing Redis-based workflow memory, adding error handling and retries, and enhancing observability.

## Extended Node Types

### SQL Node

The SQL Node allows workflows to execute SQL queries against databases.

```typescript
// src/workflow/nodes/SQLNode.ts
import { BaseNode } from './BaseNode';
import { ISQLNode } from '../../core/interfaces/ISQLNode';
import { WorkflowContext } from '../../core/interfaces/IWorkflowEngine';
import { ILogger } from '../../core/interfaces/ILogger';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../types';
import { IDatabase } from '../../core/interfaces/IDatabase';
import { SQLOptions, SQLTransaction } from '../../core/interfaces/ISQLNode';
import { RetryPolicy } from '../retry/RetryPolicy';

@injectable()
export class SQLNode extends BaseNode implements ISQLNode {
  private retryPolicy: RetryPolicy;

  constructor(
    config: Record<string, any>,
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) logger: ILogger
  ) {
    super(config, logger);
    this.retryPolicy = new RetryPolicy(config.retry || {});
  }

  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing SQL node with input: ${JSON.stringify(input)}`);

    try {
      // Extract query and parameters from input or config
      const query = this.config.query || input.query;
      const params = this.config.params || input.params || [];
      const options = this.config.options || {};

      // Execute query with retry
      const result = await this.retryPolicy.execute(() => 
        this.executeQuery(query, params, options)
      );

      this.logger.debug(`SQL node execution completed with result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`SQL node execution failed: ${error.message}`);
      throw error;
    }
  }

  async executeQuery(query: string, params: any[], options?: SQLOptions): Promise<any> {
    // Get connection from pool
    const connection = await this.database.getConnection();
    
    try {
      // Execute query
      const result = await connection.query(query, params);
      return result;
    } finally {
      // Release connection back to pool
      await connection.release();
    }
  }

  async beginTransaction(): Promise<SQLTransaction> {
    const connection = await this.database.getConnection();
    await connection.beginTransaction();
    
    return {
      query: async (query: string, params: any[]) => {
        return await connection.query(query, params);
      },
      commit: async () => {
        await connection.commit();
        await connection.release();
      },
      rollback: async () => {
        await connection.rollback();
        await connection.release();
      }
    };
  }

  protected validateConfig(): void {
    super.validateConfig();
    
    // Validate data source configuration
    if (!this.config.dataSource && !this.config.connectionString) {
      throw new Error('SQL node requires either a dataSource or connectionString property');
    }
  }
}
```

### Redis Node

The Redis Node allows workflows to interact with Redis for caching and data storage.

```typescript
// src/workflow/nodes/RedisNode.ts
import { BaseNode } from './BaseNode';
import { IRedisNode } from '../../core/interfaces/IRedisNode';
import { WorkflowContext } from '../../core/interfaces/IWorkflowEngine';
import { ILogger } from '../../core/interfaces/ILogger';
import { inject, injectable } from 'inversify';
import { TYPES } from '../../types';
import { IRedisClient } from '../../core/interfaces/IRedisClient';
import { RetryPolicy } from '../retry/RetryPolicy';

@injectable()
export class RedisNode extends BaseNode implements IRedisNode {
  private retryPolicy: RetryPolicy;

  constructor(
    config: Record<string, any>,
    @inject(TYPES.RedisClient) private redisClient: IRedisClient,
    @inject(TYPES.Logger) logger: ILogger
  ) {
    super(config, logger);
    this.retryPolicy = new RetryPolicy(config.retry || {});
  }

  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.debug(`Executing Redis node with input: ${JSON.stringify(input)}`);

    try {
      // Extract command and arguments from input or config
      const command = this.config.command || input.command;
      const args = this.config.args || input.args || [];

      // Execute command with retry
      const result = await this.retryPolicy.execute(() => 
        this.executeCommand(command, args)
      );

      this.logger.debug(`Redis node execution completed with result: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Redis node execution failed: ${error.message}`);
      throw error;
    }
  }

  async executeCommand(command: string, args: any[]): Promise<any> {
    return await this.redisClient.executeCommand(command, args);
  }

  async getCached<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T> {
    // Try to get from cache first
    const cached = await this.redisClient.get(key);
    if (cached) {
      return JSON.parse(cached);
    }

    // If not in cache, fetch and store
    const result = await fetchFn();
    await this.redisClient.set(key, JSON.stringify(result), ttl);
    return result;
  }

  protected validateConfig(): void {
    super.validateConfig();
    
    // Validate Redis configuration
    if (!this.config.command && !this.config.cacheKey) {
      throw new Error('Redis node requires either a command or cacheKey property');
    }
  }
}
```

## Redis Workflow Memory

The Redis Workflow Memory provides a distributed and persistent storage for workflow execution contexts.

```typescript
// src/workflow/engine/RedisWorkflowMemory.ts
import { injectable, inject } from 'inversify';
import { IRedisWorkflowMemory, WorkflowContext } from '../../core/interfaces/IWorkflowEngine';
import { IRedisClient } from '../../core/interfaces/IRedisClient';
import { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';

@injectable()
export class RedisWorkflowMemory implements IRedisWorkflowMemory {
  private readonly keyPrefix = 'workflow:context:';
  
  constructor(
    @inject(TYPES.RedisClient) private redisClient: IRedisClient,
    @inject(TYPES.Logger) private logger: ILogger
  ) {}

  async getContext(executionId: string): Promise<WorkflowContext> {
    const key = this.getKey(executionId);
    const data = await this.redisClient.get(key);
    
    if (!data) {
      throw new Error(`Workflow context for execution ID ${executionId} not found`);
    }
    
    return JSON.parse(data);
  }

  async updateContext(executionId: string, context: WorkflowContext): Promise<void> {
    const key = this.getKey(executionId);
    await this.redisClient.set(key, JSON.stringify(context));
    
    // Set default TTL if not already set
    const ttl = await this.redisClient.ttl(key);
    if (ttl < 0) {
      await this.setContextTTL(executionId, 3600); // Default: 1 hour
    }
  }

  async deleteContext(executionId: string): Promise<void> {
    const key = this.getKey(executionId);
    await this.redisClient.del(key);
  }

  async setContextTTL(executionId: string, ttlSeconds: number): Promise<void> {
    const key = this.getKey(executionId);
    await this.redisClient.expire(key, ttlSeconds);
  }

  private getKey(executionId: string): string {
    return `${this.keyPrefix}${executionId}`;
  }
}
```

## Error Handling and Retries

The Retry Policy provides configurable retry mechanisms for handling transient errors.

```typescript
// src/workflow/retry/RetryPolicy.ts
import { injectable } from 'inversify';

export interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  retryBackoffMultiplier?: number;
  retryableErrors?: string[];
}

@injectable()
export class RetryPolicy {
  private maxRetries: number;
  private retryDelay: number;
  private retryBackoffMultiplier: number;
  private retryableErrors: string[];

  constructor(options: RetryOptions = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.retryBackoffMultiplier = options.retryBackoffMultiplier || 2;
    this.retryableErrors = options.retryableErrors || [];
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    let lastError: Error;
    let delay = this.retryDelay;

    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        // Check if error is retryable
        if (!this.isRetryable(error as Error)) {
          throw error;
        }
        
        // If this was the last attempt, throw the error
        if (attempt === this.maxRetries) {
          throw error;
        }
        
        // Wait before retrying
        await this.sleep(delay);
        
        // Increase delay for next attempt
        delay *= this.retryBackoffMultiplier;
      }
    }

    throw lastError;
  }

  private isRetryable(error: Error): boolean {
    // If no specific errors are defined, all errors are retryable
    if (this.retryableErrors.length === 0) {
      return true;
    }
    
    // Check if error name or message matches any retryable error
    return this.retryableErrors.some(retryableError => 
      error.name === retryableError || 
      error.message.includes(retryableError)
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## Enhanced Observability

The Observability Manager provides comprehensive monitoring and metrics collection for workflows.

```typescript
// src/workflow/observability/ObservabilityManager.ts
import { injectable, inject } from 'inversify';
import { IObservabilityManager } from '../../core/interfaces/IObservabilityManager';
import { ILogger } from '../../core/interfaces/ILogger';
import { TYPES } from '../../types';
import { IDatabase } from '../../core/interfaces/IDatabase';
import { WorkflowMetrics, NodeMetrics, TimeRange, WorkflowMetricsSummary, NodeMetricsSummary } from '../../core/interfaces/IObservabilityManager';
import { Counter, Histogram, Gauge } from 'prom-client';

@injectable()
export class ObservabilityManager implements IObservabilityManager {
  // Prometheus metrics
  private workflowExecutionCounter: Counter;
  private workflowExecutionDuration: Histogram;
  private nodeExecutionCounter: Counter;
  private nodeExecutionDuration: Histogram;
  private activeWorkflows: Gauge;

  constructor(
    @inject(TYPES.Database) private database: IDatabase,
    @inject(TYPES.Logger) private logger: ILogger
  ) {
    // Initialize Prometheus metrics
    this.initializeMetrics();
  }

  async recordWorkflowExecution(workflowId: string, executionId: string, metrics: WorkflowMetrics): Promise<void> {
    try {
      // Record in database
      await this.database.getRepository('workflow_metrics').save({
        workflow_id: workflowId,
        execution_id: executionId,
        start_time: metrics.startTime,
        end_time: metrics.endTime,
        duration_ms: metrics.durationMs,
        status: metrics.status,
        error: metrics.error
      });

      // Update Prometheus metrics
      this.workflowExecutionCounter.inc({ workflow_id: workflowId, status: metrics.status });
      this.workflowExecutionDuration.observe({ workflow_id: workflowId }, metrics.durationMs);
      
      if (metrics.status === 'RUNNING') {
        this.activeWorkflows.inc({ workflow_id: workflowId });
      } else {
        this.activeWorkflows.dec({ workflow_id: workflowId });
      }
    } catch (error) {
      this.logger.error(`Failed to record workflow execution metrics: ${error.message}`);
    }
  }

  async recordNodeExecution(nodeId: string, executionId: string, metrics: NodeMetrics): Promise<void> {
    try {
      // Record in database
      await this.database.getRepository('node_metrics').save({
        node_id: nodeId,
        execution_id: executionId,
        node_type: metrics.nodeType,
        start_time: metrics.startTime,
        end_time: metrics.endTime,
        duration_ms: metrics.durationMs,
        status: metrics.status,
        error: metrics.error
      });

      // Update Prometheus metrics
      this.nodeExecutionCounter.inc({ node_id: nodeId, node_type: metrics.nodeType, status: metrics.status });
      this.nodeExecutionDuration.observe({ node_id: nodeId, node_type: metrics.nodeType }, metrics.durationMs);
    } catch (error) {
      this.logger.error(`Failed to record node execution metrics: ${error.message}`);
    }
  }

  async getWorkflowMetrics(workflowId: string, timeRange?: TimeRange): Promise<WorkflowMetricsSummary> {
    // Query database for workflow metrics
    const query = this.database.getRepository('workflow_metrics')
      .createQueryBuilder('wm')
      .where('wm.workflow_id = :workflowId', { workflowId });
    
    if (timeRange) {
      query.andWhere('wm.start_time >= :startTime', { startTime: timeRange.startTime })
        .andWhere('wm.start_time <= :endTime', { endTime: timeRange.endTime });
    }
    
    const metrics = await query.getMany();
    
    // Calculate summary statistics
    return this.calculateWorkflowMetricsSummary(metrics);
  }

  async getNodeMetrics(nodeId: string, timeRange?: TimeRange): Promise<NodeMetricsSummary> {
    // Query database for node metrics
    const query = this.database.getRepository('node_metrics')
      .createQueryBuilder('nm')
      .where('nm.node_id = :nodeId', { nodeId });
    
    if (timeRange) {
      query.andWhere('nm.start_time >= :startTime', { startTime: timeRange.startTime })
        .andWhere('nm.start_time <= :endTime', { endTime: timeRange.endTime });
    }
    
    const metrics = await query.getMany();
    
    // Calculate summary statistics
    return this.calculateNodeMetricsSummary(metrics);
  }

  private initializeMetrics(): void {
    // Initialize Prometheus metrics
    this.workflowExecutionCounter = new Counter({
      name: 'workflow_executions_total',
      help: 'Total number of workflow executions',
      labelNames: ['workflow_id', 'status']
    });
    
    this.workflowExecutionDuration = new Histogram({
      name: 'workflow_execution_duration_milliseconds',
      help: 'Duration of workflow executions in milliseconds',
      labelNames: ['workflow_id'],
      buckets: [10, 50, 100, 500, 1000, 5000, 10000, 30000, 60000]
    });
    
    this.nodeExecutionCounter = new Counter({
      name: 'node_executions_total',
      help: 'Total number of node executions',
      labelNames: ['node_id', 'node_type', 'status']
    });
    
    this.nodeExecutionDuration = new Histogram({
      name: 'node_execution_duration_milliseconds',
      help: 'Duration of node executions in milliseconds',
      labelNames: ['node_id', 'node_type'],
      buckets: [1, 5, 10, 50, 100, 500, 1000, 5000, 10000]
    });
    
    this.activeWorkflows = new Gauge({
      name: 'active_workflows',
      help: 'Number of currently active workflows',
      labelNames: ['workflow_id']
    });
  }

  private calculateWorkflowMetricsSummary(metrics: any[]): WorkflowMetricsSummary {
    // Calculate summary statistics
    // Implementation details omitted for brevity
    return {} as WorkflowMetricsSummary;
  }

  private calculateNodeMetricsSummary(metrics: any[]): NodeMetricsSummary {
    // Calculate summary statistics
    // Implementation details omitted for brevity
    return {} as NodeMetricsSummary;
  }
}
```

## Conclusion

This document provides implementation guidance for the key components of Phase 1. The actual implementation should follow the principles outlined in the development postulates and adhere to the project's coding standards. Additional components not covered in detail here should be implemented following similar patterns and principles.
