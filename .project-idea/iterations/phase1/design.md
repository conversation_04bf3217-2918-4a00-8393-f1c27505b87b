# Phase 1: Extended Node Types, Admin API, and Enhanced Observability (MVP+ - Week 3-4)

## Overview

Phase 1 builds upon the workflow-centric foundation established in Phase 0 to create a more robust and feature-rich Dynamic MCP Server. This phase introduces additional node types for workflows (SQL, Redis, etc.), a comprehensive Admin API for workflow management, enhanced error handling with retries, and improved observability. The goal is to have a server that can dynamically load and execute complex workflows with various node types, manage these workflows through an Admin API, and provide detailed insights into workflow execution.

## Architecture

The architecture for Phase 1 extends the Phase 0 architecture with the following additional components:

```mermaid
graph TD
    subgraph Clients
        MCPClient["MCP Client"]
        AdminClient["Admin Client (REST)"]
    end

    subgraph "MCP Server (Phase 1)"
        MCPCore["MCP Server Core"]
        ToolsHandler["Tools Handler"]
        DynamicLoader["Dynamic Service Loader"]
        WorkflowEngine["Workflow Engine"]

        subgraph "Node Types"
            JSNode["JavaScript Node"]
            HTTPNode["HTTP Node"]
            SQLNode["SQL Node (New)"]
            RedisNode["Redis Node (New)"]
            LiteLLMNode["LiteLLM Node (New)"]
        end

        ScriptEngine["JavaScript VM (Sandbox)"]
        DB["PostgreSQL Database"]
        Redis["Redis (New)"]
        AdminAPI["Admin REST API"]
        ConfigWatcher["Config Watcher"]
        NotificationMgr["Notification Manager"]
        ObservabilityMgr["Observability Manager (Enhanced)"]
    end

    MCPClient -- "MCP Requests" --> MCPCore
    MCPCore -- "Route Request" --> ToolsHandler
    ToolsHandler -- "Get/Execute Tools" --> DynamicLoader
    DynamicLoader -- "Execute Workflow" --> WorkflowEngine

    WorkflowEngine -- "Execute Node" --> JSNode
    WorkflowEngine -- "Execute Node" --> HTTPNode
    WorkflowEngine -- "Execute Node" --> SQLNode
    WorkflowEngine -- "Execute Node" --> RedisNode
    WorkflowEngine -- "Execute Node" --> LiteLLMNode

    JSNode -- "Execute Script" --> ScriptEngine
    SQLNode -- "Execute Query" --> DB
    RedisNode -- "Execute Command" --> Redis

    DynamicLoader -- "Load Configs" --> DB
    WorkflowEngine -- "Store Execution Data" --> DB
    WorkflowEngine -- "Store Context" --> Redis

    AdminClient -- "REST Requests" --> AdminAPI
    AdminAPI -- "CRUD Operations" --> DB
    DB -- "Notify Changes" --> ConfigWatcher
    ConfigWatcher -- "Reload Configs" --> DynamicLoader
    ConfigWatcher -- "Signal Change" --> NotificationMgr
    NotificationMgr -- "Send Notifications" --> MCPClient

    WorkflowEngine -- "Report Metrics" --> ObservabilityMgr
    ObservabilityMgr -- "Store Metrics" --> DB
```

## Component Descriptions

### 1. Extended Node Types

#### SQL Node

A node type for executing SQL queries against databases:

- Supports parameterized queries
- Handles connection pooling
- Supports multiple database types (primarily PostgreSQL)
- Includes transaction support
- Provides error handling and retry capabilities

#### Redis Node

A node type for interacting with Redis:

- Supports all common Redis commands
- Handles connection management
- Provides caching capabilities
- Supports pub/sub operations
- Includes error handling and retry capabilities

#### LiteLLM Node

A node type for communicating with AI models via LiteLLM:

- Supports multiple AI providers
- Handles API key management
- Provides prompt templating
- Includes response parsing
- Features error handling and retry capabilities

### 2. Workflow Memory with Redis

An enhanced implementation of workflow memory using Redis:

- Persists workflow context across server restarts
- Supports distributed execution
- Provides TTL-based expiration
- Includes serialization/deserialization of complex objects
- Features atomic operations for concurrent access

### 3. Admin REST API

A comprehensive REST API for managing workflows and functions:

- Create, read, update, delete workflows and functions
- Test workflows and functions before activation
- Manage node configurations
- View execution history and metrics
- Secure access with authentication

### 4. Error Handling and Retries

A robust error handling system for workflows:

- Configurable retry policies per node
- Circuit breaker pattern for external services
- Error logging and reporting
- Recovery strategies for failed workflows
- Timeout handling

### 5. Enhanced Observability

Comprehensive monitoring and observability features:

- Detailed execution metrics
- Performance dashboards
- Execution history and audit logs
- Node-level performance tracking
- Integration with Prometheus and other monitoring tools

## Implementation Details

### Database Schema

In addition to the existing tables from Phase 0, we'll add or modify:

```sql
-- Add retry_config to mcp_workflows table
ALTER TABLE mcp_workflows ADD COLUMN retry_config JSONB;

-- Add observability_config to mcp_workflows table
ALTER TABLE mcp_workflows ADD COLUMN observability_config JSONB;

-- Add node_config table for storing reusable node configurations
CREATE TABLE mcp_node_configs (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    node_type VARCHAR(50) NOT NULL,
    config JSONB NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add data_source table for storing connection information
CREATE TABLE mcp_data_sources (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    connection_config_encrypted TEXT NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### TypeScript Interfaces

```typescript
// Node interfaces
interface INode {
  execute(input: any, context: WorkflowContext): Promise<any>;
}

interface ISQLNode extends INode {
  executeQuery(query: string, params: any[], options?: SQLOptions): Promise<any>;
  beginTransaction(): Promise<SQLTransaction>;
}

interface IRedisNode extends INode {
  executeCommand(command: string, args: any[]): Promise<any>;
  getCached<T>(key: string, fetchFn: () => Promise<T>, ttl?: number): Promise<T>;
}

interface ILiteLLMNode extends INode {
  generateCompletion(prompt: string, options?: LiteLLMOptions): Promise<string>;
  generateEmbedding(text: string): Promise<number[]>;
}

// Retry configuration
interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  retryBackoffMultiplier: number;
  retryableErrors?: string[];
}

// Workflow memory with Redis
interface IRedisWorkflowMemory extends IWorkflowMemory {
  getContext(executionId: string): Promise<WorkflowContext>;
  updateContext(executionId: string, context: WorkflowContext): Promise<void>;
  deleteContext(executionId: string): Promise<void>;
  setContextTTL(executionId: string, ttlSeconds: number): Promise<void>;
}

// Enhanced observability
interface IObservabilityManager {
  recordWorkflowExecution(
    workflowId: string,
    executionId: string,
    metrics: WorkflowMetrics
  ): Promise<void>;
  recordNodeExecution(nodeId: string, executionId: string, metrics: NodeMetrics): Promise<void>;
  getWorkflowMetrics(workflowId: string, timeRange?: TimeRange): Promise<WorkflowMetricsSummary>;
  getNodeMetrics(nodeId: string, timeRange?: TimeRange): Promise<NodeMetricsSummary>;
}

// Data source configuration
interface DataSourceConfig {
  id: string;
  name: string;
  type: string;
  connectionConfigEncrypted: string;
  enabled: boolean;
}
```

### Directory Structure Additions

```
src/
├── core/
│   ├── interfaces/
│   │   ├── INode.ts
│   │   ├── ISQLNode.ts
│   │   ├── IRedisNode.ts
│   │   ├── ILiteLLMNode.ts
│   │   ├── IObservabilityManager.ts
│   │   ├── IRedisWorkflowMemory.ts
│   │   └── ...
│   └── ...
├── workflow/
│   ├── engine/
│   │   ├── RedisWorkflowMemory.ts
│   │   └── ...
│   ├── nodes/
│   │   ├── SQLNode.ts
│   │   ├── RedisNode.ts
│   │   ├── LiteLLMNode.ts
│   │   └── ...
│   ├── retry/
│   │   ├── RetryPolicy.ts
│   │   ├── CircuitBreaker.ts
│   │   └── ...
│   └── observability/
│       ├── ObservabilityManager.ts
│       ├── MetricsCollector.ts
│       └── ...
├── infrastructure/
│   ├── database/
│   │   ├── entities/
│   │   │   ├── NodeConfig.entity.ts
│   │   │   ├── DataSource.entity.ts
│   │   │   └── ...
│   │   └── ...
│   ├── redis/
│   │   ├── RedisClient.ts
│   │   └── ...
│   └── ...
├── api/
│   ├── controllers/
│   │   ├── WorkflowController.ts
│   │   ├── NodeController.ts
│   │   ├── DataSourceController.ts
│   │   └── ...
│   ├── validators/
│   │   ├── WorkflowValidator.ts
│   │   ├── NodeValidator.ts
│   │   └── ...
│   ├── routes/
│   │   ├── workflowRoutes.ts
│   │   ├── nodeRoutes.ts
│   │   └── ...
│   └── middleware/
│       ├── authMiddleware.ts
│       └── ...
└── ...
```

## Database Triggers

To support the hot-reload mechanism, we'll add database triggers:

```sql
-- Function to notify about configuration changes
CREATE OR REPLACE FUNCTION notify_config_change()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM pg_notify('mcp_config_updates', json_build_object(
        'entity_type', TG_TABLE_NAME,
        'id', NEW.id,
        'name', NEW.name,
        'action', TG_OP
    )::text);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for mcp_functions
CREATE TRIGGER mcp_functions_notify
AFTER INSERT OR UPDATE OR DELETE ON mcp_functions
FOR EACH ROW EXECUTE FUNCTION notify_config_change();

-- Trigger for mcp_workflows
CREATE TRIGGER mcp_workflows_notify
AFTER INSERT OR UPDATE OR DELETE ON mcp_workflows
FOR EACH ROW EXECUTE FUNCTION notify_config_change();

-- Trigger for mcp_node_configs
CREATE TRIGGER mcp_node_configs_notify
AFTER INSERT OR UPDATE OR DELETE ON mcp_node_configs
FOR EACH ROW EXECUTE FUNCTION notify_config_change();

-- Trigger for mcp_data_sources
CREATE TRIGGER mcp_data_sources_notify
AFTER INSERT OR UPDATE OR DELETE ON mcp_data_sources
FOR EACH ROW EXECUTE FUNCTION notify_config_change();
```

## Admin API Endpoints

The Admin API will include the following endpoints:

### Workflows

- `GET /admin/workflows` - List all workflows
- `GET /admin/workflows/:id` - Get a specific workflow
- `POST /admin/workflows` - Create a new workflow
- `PUT /admin/workflows/:id` - Update a workflow
- `DELETE /admin/workflows/:id` - Delete a workflow
- `POST /admin/workflows/test` - Test a workflow without saving
- `GET /admin/workflows/:id/executions` - Get execution history for a workflow
- `GET /admin/workflows/:id/metrics` - Get performance metrics for a workflow

### Functions

- `GET /admin/functions` - List all functions
- `GET /admin/functions/:id` - Get a specific function
- `POST /admin/functions` - Create a new function
- `PUT /admin/functions/:id` - Update a function
- `DELETE /admin/functions/:id` - Delete a function
- `POST /admin/functions/test` - Test a function without saving

### Node Configurations

- `GET /admin/nodes` - List all node configurations
- `GET /admin/nodes/:id` - Get a specific node configuration
- `POST /admin/nodes` - Create a new node configuration
- `PUT /admin/nodes/:id` - Update a node configuration
- `DELETE /admin/nodes/:id` - Delete a node configuration
- `POST /admin/nodes/test` - Test a node configuration without saving

### Data Sources

- `GET /admin/datasources` - List all data sources
- `GET /admin/datasources/:id` - Get a specific data source
- `POST /admin/datasources` - Create a new data source
- `PUT /admin/datasources/:id` - Update a data source
- `DELETE /admin/datasources/:id` - Delete a data source
- `POST /admin/datasources/test` - Test a data source connection without saving

## Expected Outcomes

By the end of Phase 1, we will have:

1. A Dynamic MCP Server with extended workflow capabilities
2. Additional node types (SQL, Redis, LiteLLM) for more complex workflows
3. Redis-based workflow memory for improved persistence and scalability
4. Robust error handling and retry mechanisms
5. Enhanced observability for monitoring workflow performance
6. A comprehensive Admin REST API for managing workflows and configurations
7. A hot-reload mechanism for configuration changes
8. MCP notifications for configuration changes

This enhanced version will enable:

- Creation of more complex and powerful workflows
- Integration with databases, caching systems, and AI models
- Improved reliability through error handling and retries
- Better monitoring and troubleshooting through enhanced observability
- Dynamic management of workflows and configurations
- Real-time updates to configurations without server restart
- Testing of configurations before activation
- Notification of clients about configuration changes

These features provide a solid foundation for the more advanced workflow capabilities, data source management, and versioning features planned for Phase 2.
