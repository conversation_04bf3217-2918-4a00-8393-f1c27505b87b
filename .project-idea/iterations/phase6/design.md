# Phase 6: Visual Workflow Editor - Clean Node Type Migration

## 🎯 Cíle Phase 6

Phase 6 implementuje **čistou migraci na NodePalette typy** bez backward compatibility. <PERSON><PERSON><PERSON> je nahradit současné backend node typy novými NodePalette typy a vytvořit jednotný systém pro visual i JSON editor.

## 🚨 Identifikovaný Problém

### Současný Stav
- **Frontend NodePalette** používá descriptive typy: `rest-api-trigger`, `javascript-action`, `sql-action`, `if-else-logic`
- **Backend NodeRegistry** podporuje legacy typy: `javascript`, `http`, `sql`, `redis`, `litellm`, `if-else`, `switch`, `merge`
- **Výsledek**: 
  - Workflow vytvořené ve vizuálním editoru nelze uložit
  - Nekonzistentní experience mezi JSON a visual editorem
  - Dva různé systémy node typů

### Root Cause Analysis
1. **Legacy Node Types**: Backend používá zastaralé simple názvy
2. **Missing Node Categories**: Backend nemá trigger a terminator kategorie
3. **Incomplete Coverage**: Backend nepodporuje všechny NodePalette typy

## 🔧 Clean Migration Strategy

### 1. Complete Node Type Replacement

**Nahradit VŠECHNY backend node typy novými NodePalette typy:**

```typescript
// Nová čistá sada node typů (NodePalette jako jediný standard)
const NEW_NODE_TYPES = {
  // Trigger Nodes (NOVÉ)
  'rest-api-trigger': RestAPITriggerNode,
  'mcp-function-trigger': MCPFunctionTriggerNode,
  'webhook-trigger': WebhookTriggerNode,
  'timer-trigger': TimerTriggerNode,
  
  // Action Nodes (NAHRAZENÉ)
  'javascript-action': JavaScriptActionNode,  // nahrazuje 'javascript'
  'sql-action': SQLActionNode,                // nahrazuje 'sql'
  'redis-action': RedisActionNode,            // nahrazuje 'redis'
  'http-action': HTTPActionNode,              // nahrazuje 'http'
  'litellm-action': LiteLLMActionNode,        // nahrazuje 'litellm'
  
  // Logic Nodes (NAHRAZENÉ)
  'if-else-logic': IfElseLogicNode,           // nahrazuje 'if-else'
  'switch-logic': SwitchLogicNode,            // nahrazuje 'switch'
  'merge-logic': MergeLogicNode,              // nahrazuje 'merge'
  'loop-logic': LoopLogicNode,                // NOVÝ
  
  // Terminator Nodes (NOVÉ)
  'response-terminator': ResponseTerminatorNode,
  'mcp-response-terminator': MCPResponseTerminatorNode,
  'error-terminator': ErrorTerminatorNode
};
```

### 2. Simplified NodeRegistry

**Odstranit alias complexity - pouze primary types:**

```typescript
// src/workflow/nodes/NodeRegistry.ts - Simplified version
@injectable()
export class NodeRegistry implements INodeRegistry {
  private nodeFactories: Map<string, NodeFactory> = new Map();
  
  registerNode(type: string, nodeFactory: NodeFactory): void {
    this.nodeFactories.set(type, nodeFactory);
  }
  
  getNode(type: string, config: Record<string, any>): INode {
    const factory = this.nodeFactories.get(type);
    if (!factory) {
      throw new Error(`Node type '${type}' is not registered. Supported types: ${this.getRegisteredNodeTypes().join(', ')}`);
    }
    return factory(config);
  }
  
  getRegisteredNodeTypes(): string[] {
    return Array.from(this.nodeFactories.keys());
  }
}
```

### 3. Direct Frontend Integration

**Frontend používá přímo NodePalette typy bez transformace:**

```typescript
// frontend/src/components/workflow/VisualWorkflowEditor.tsx
const handleSaveSubmit = async () => {
  const workflowData = {
    name: saveForm.name,
    description: saveForm.description,
    enabled: saveForm.enabled,
    input_schema: { type: 'object', properties: {} },
    nodes_config: {
      nodes: nodes.map((node) => ({
        id: node.id,
        type: node.data.type, // Direct NodePalette type - no mapping needed
        name: node.data.label,
        config: node.data.config || {},
        position: node.position
      })),
      edges: edges.map((edge) => ({
        source: edge.source,
        target: edge.target
      }))
    }
  };

  // Direct API call - no transformation needed
  await apiService.createWorkflow(workflowData);
};
```

## 📋 Implementační Plán

### Phase 6.1: Legacy Cleanup (Týden 1)

#### 1.1 Remove Legacy Node Types
- **Odstranit staré node implementations**
  - Smazat `JavaScriptNode` (nahradit `JavaScriptActionNode`)
  - Smazat `HTTPNode` (nahradit `HTTPActionNode`)
  - Smazat `SQLNode` (nahradit `SQLActionNode`)
  - Smazat `RedisNode` (nahradit `RedisActionNode`)
  - Smazat `LiteLLMNode` (nahradit `LiteLLMActionNode`)
  - Smazat `IfElseNode` (nahradit `IfElseLogicNode`)
  - Smazat `SwitchNode` (nahradit `SwitchLogicNode`)
  - Smazat `MergeNode` (nahradit `MergeLogicNode`)

#### 1.2 Clean NodeRegistry Registration
- **Aktualizovat inversify.config.ts**
  - Odstranit registrace legacy typů
  - Připravit pro nové NodePalette typy

### Phase 6.2: New Node Type Implementation (Týden 1-2)

#### 2.1 Trigger Nodes (NOVÉ)
- **RestAPITriggerNode** (`rest-api-trigger`)
- **MCPFunctionTriggerNode** (`mcp-function-trigger`)
- **WebhookTriggerNode** (`webhook-trigger`)
- **TimerTriggerNode** (`timer-trigger`)

#### 2.2 Enhanced Action Nodes (NAHRAZENÉ)
- **JavaScriptActionNode** (`javascript-action`) - enhanced verze JavaScriptNode
- **SQLActionNode** (`sql-action`) - enhanced verze SQLNode
- **RedisActionNode** (`redis-action`) - enhanced verze RedisNode
- **HTTPActionNode** (`http-action`) - enhanced verze HTTPNode
- **LiteLLMActionNode** (`litellm-action`) - enhanced verze LiteLLMNode

#### 2.3 Enhanced Logic Nodes (NAHRAZENÉ + NOVÉ)
- **IfElseLogicNode** (`if-else-logic`) - enhanced verze IfElseNode
- **SwitchLogicNode** (`switch-logic`) - enhanced verze SwitchNode
- **MergeLogicNode** (`merge-logic`) - enhanced verze MergeNode
- **LoopLogicNode** (`loop-logic`) - NOVÝ node type

#### 2.4 Terminator Nodes (NOVÉ)
- **ResponseTerminatorNode** (`response-terminator`)
- **MCPResponseTerminatorNode** (`mcp-response-terminator`)
- **ErrorTerminatorNode** (`error-terminator`)

### Phase 6.3: Registration & Testing (Týden 2-3)

#### 3.1 Complete Registration
```typescript
// src/inversify.config.ts - Clean registration
const nodeRegistry = container.get<NodeRegistry>(TYPES.NodeRegistry);

// Register ALL NodePalette types as primary
nodeRegistry.registerNode('rest-api-trigger', createRestAPITriggerFactory(container));
nodeRegistry.registerNode('mcp-function-trigger', createMCPFunctionTriggerFactory(container));
nodeRegistry.registerNode('webhook-trigger', createWebhookTriggerFactory(container));
nodeRegistry.registerNode('timer-trigger', createTimerTriggerFactory(container));

nodeRegistry.registerNode('javascript-action', createJavaScriptActionFactory(container));
nodeRegistry.registerNode('sql-action', createSQLActionFactory(container));
nodeRegistry.registerNode('redis-action', createRedisActionFactory(container));
nodeRegistry.registerNode('http-action', createHTTPActionFactory(container));
nodeRegistry.registerNode('litellm-action', createLiteLLMActionFactory(container));

nodeRegistry.registerNode('if-else-logic', createIfElseLogicFactory(container));
nodeRegistry.registerNode('switch-logic', createSwitchLogicFactory(container));
nodeRegistry.registerNode('merge-logic', createMergeLogicFactory(container));
nodeRegistry.registerNode('loop-logic', createLoopLogicFactory(container));

nodeRegistry.registerNode('response-terminator', createResponseTerminatorFactory(container));
nodeRegistry.registerNode('mcp-response-terminator', createMCPResponseTerminatorFactory(container));
nodeRegistry.registerNode('error-terminator', createErrorTerminatorFactory(container));
```

#### 3.2 Comprehensive Testing
- **Unit tests** pro všechny nové node typy
- **Integration tests** s NodeRegistry
- **End-to-end tests** visual editor → backend
- **Workflow execution tests** s novými typy

## 🎯 Výhody Clean Migration

### ✅ Simplicity
- **Jeden node type systém** - žádné aliasy nebo mapping
- **Clean codebase** - žádný legacy code
- **Straightforward development** - jasné naming convention

### ✅ Consistency
- **Unified experience** - stejné typy všude
- **Predictable behavior** - žádné edge cases s aliasy
- **Clear documentation** - jeden set typů k dokumentaci

### ✅ Performance
- **No mapping overhead** - direct usage
- **No alias resolution** - přímý lookup
- **Simplified validation** - jednodušší error handling

### ✅ Maintainability
- **Single source of truth** - NodePalette definuje všechny typy
- **Easy extensions** - přidání nového typu je straightforward
- **Clear architecture** - žádná complexity s backward compatibility

## 🔄 Migration Path

### For Developers
1. **Delete existing workflows** s legacy node typy
2. **Recreate workflows** s novými NodePalette typy
3. **Use visual editor** pro intuitive workflow creation
4. **Manual JSON editing** stále možné s novými typy

### For System
1. **Clean slate approach** - žádné migrace dat
2. **Fresh start** s novými node typy
3. **Simplified testing** - pouze nové typy
4. **Clear documentation** - pouze aktuální typy

## 📊 Risk Mitigation

### Eliminated Risks
- **No backward compatibility issues** - žádné legacy workflow
- **No mapping bugs** - žádné transformace
- **No alias confusion** - pouze primary typy
- **No performance overhead** - direct usage

### Remaining Risks
- **Development time** - implementace všech node typů
- **Testing complexity** - comprehensive coverage needed
- **Documentation effort** - dokumentace všech nových typů

### Low Risk
- **User adoption** - visual editor je intuitivnější než JSON
- **System stability** - clean architecture bez legacy baggage
