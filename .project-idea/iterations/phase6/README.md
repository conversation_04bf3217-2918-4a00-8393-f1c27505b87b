# Phase 6: Visual Workflow Editor - Clean Node Type Migration

## 🚨 Kritický Problém

Vizuální workflow editor v Phase 5 má zásadní problém - **nemůže ukládat workflow**, protože frontend používá jiné názvy node typů než backend podporuje.

### Problém Detail
- **Frontend NodePalette**: `rest-api-trigger`, `javascript-action`, `sql-action`, `if-else-logic`
- **Backend NodeRegistry**: `javascript`, `http`, `sql`, `redis`, `litellm`, `if-else`, `switch`, `merge`
- **Výsledek**: 
  - Server vrací error "unsupported node type" při pokusu o uložení
  - Nekonzistentní experience mezi JSON a visual editorem
  - Dva různé systémy node typů

## 🎯 Cíle Phase 6

### CLEAN MIGRATION APPROACH: Bez Backward Compatibility
1. **Odstranit VŠECHNY legacy node typy** z backendu
2. **Implementovat VŠECHNY NodePalette typy** jako primary types
3. **Ž<PERSON><PERSON><PERSON> aliasy, žádné mapping** - pouze clean NodePalette systém

### Výhody Clean Migration
- **Simplicity** - jeden node type systém
- **Consistency** - stejné typy všude
- **Performance** - no mapping overhead
- **Maintainability** - clean codebase bez legacy baggage

## 📁 Struktura Dokumentace

### Klíčové Dokumenty

1. **[design.md](./design.md)** - Clean migration strategy
   - Complete node type replacement
   - Simplified NodeRegistry
   - Direct frontend integration
   - Implementation plan

2. **[todo.md](./todo.md)** - Detailní implementační plán
   - Legacy cleanup tasks
   - New node type implementation
   - Clean registration process
   - Testing a documentation

3. **[postulates.md](./postulates.md)** - Základní principy Phase 6
   - Clean slate approach
   - No backward compatibility
   - Simplicity over complexity
   - Performance priorities

4. **[implementation-details.md](./implementation-details.md)** - Technické detaily
   - Konkrétní kód examples
   - Node implementations
   - Testing strategies
   - Migration instructions

## 🔧 Technické Řešení

### Clean Node Type Architecture

```
Frontend NodePalette → Direct Usage → Backend NodeRegistry
     ↓                      ↓                    ↓
'javascript-action'  →  'javascript-action'  →  JavaScriptActionNode
'rest-api-trigger'   →  'rest-api-trigger'   →  RestAPITriggerNode
'if-else-logic'      →  'if-else-logic'      →  IfElseLogicNode

NO LEGACY SUPPORT - Clean slate approach
```

### Nové Backend Node Typy (Complete Replacement)

- **Trigger Nodes**: `rest-api-trigger`, `mcp-function-trigger`, `webhook-trigger`, `timer-trigger`
- **Enhanced Action Nodes**: `javascript-action`, `sql-action`, `redis-action`, `http-action`, `litellm-action`
- **Enhanced Logic Nodes**: `if-else-logic`, `switch-logic`, `merge-logic`, `loop-logic`
- **Terminator Nodes**: `response-terminator`, `mcp-response-terminator`, `error-terminator`

### Legacy Removal Strategy

- **Delete Legacy Implementations**: Odstranit všechny staré node classes
- **Clean NodeRegistry**: Simplified bez alias support
- **Direct Integration**: Frontend používá přímo NodePalette typy

## 📊 Implementation Timeline

### Týden 1: Legacy Cleanup (KRITICKÉ)

- Remove všechny legacy node implementations
- Clean NodeRegistry simplification
- Prepare infrastructure pro nové typy

### Týden 2: New Node Types Implementation (VYSOKÁ PRIORITA)

- Implementovat všechny NodePalette typy
- Complete registration v NodeRegistry
- Backend validation updates

### Týden 3: Frontend Simplification & Testing (VYSOKÁ PRIORITA)

- Remove mapping complexity
- Direct NodePalette type usage
- Comprehensive testing

## 🎉 Expected Impact

Po dokončení Phase 6:

### ✅ Immediate Benefits
- **Visual editor plně funkční** - všechny nodes z palette lze uložit
- **Unified experience** - stejné typy ve visual i JSON editoru
- **No mapping complexity** - direct usage NodePalette typů

### ✅ Long-term Benefits
- **Clean architecture** - žádný legacy code
- **Better performance** - no mapping overhead
- **Easy maintenance** - simplified codebase
- **Future extensibility** - straightforward node additions

## 🚀 Getting Started

Pro implementaci Phase 6:

1. **Review dokumentace** v tomto adresáři
2. **Začít s legacy cleanup** (nejvyšší priorita)
3. **Implementovat postupně** podle todo.md
4. **Testovat kontinuálně** každý krok
5. **Validovat end-to-end** před dokončením

## 🔄 Migration Instructions

### For Developers
1. **Delete existing workflows** s legacy node typy
2. **Recreate workflows** using visual editor s NodePalette typy
3. **Manual JSON editing** možné s novými typy
4. **Full compatibility** mezi visual a JSON editorem

### For System
1. **Clean slate approach** - žádné migrace dat
2. **Fresh start** s NodePalette typy
3. **Simplified testing** - pouze nové typy
4. **Clear documentation** - pouze aktuální typy

## 🔗 Souvislosti s Předchozími Fázemi

- **Phase 0-4**: Poskytují solid foundation (workflow engine, API, infrastructure)
- **Phase 5**: Vytvořil vizuální editor, ale s compatibility issues
- **Phase 6**: Clean migration na NodePalette typy - final solution

## 📈 Success Metrics

### Technical KPIs
- [ ] 100% NodePalette typů podporováno na backendu
- [ ] Zero legacy code v codebase
- [ ] Direct usage NodePalette typů všude
- [ ] Simplified architecture bez mapping
- [ ] 95%+ test coverage pro nové komponenty

### User Experience KPIs
- [ ] Visual editor může uložit všechny node typy z palette
- [ ] JSON editor používá stejné typy jako visual editor
- [ ] Clear feedback při validation errors
- [ ] Intuitive workflow creation experience
- [ ] No complexity pro end users

## 🎯 Key Advantages of Clean Migration

### vs. Mapping Approach
- **No complexity** - žádné transformace nebo mapping
- **Better performance** - direct node type usage
- **Easier debugging** - clear error messages
- **Simpler testing** - pouze jeden set typů

### vs. Alias Approach
- **No confusion** - pouze primary typy
- **Clear documentation** - jeden set typů k dokumentaci
- **Easier maintenance** - žádné edge cases s aliasy
- **Future-proof** - extensible bez legacy baggage

---

**Phase 6 implementuje clean slate approach pro node typy - nejjednodušší a nejelegantnější řešení problému s vizuálním editorem.**
