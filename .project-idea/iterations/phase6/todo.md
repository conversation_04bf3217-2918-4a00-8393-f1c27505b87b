# Phase 6: Visual Workflow Editor - Clean Node Type Migration - Todo List

## 📋 Implementační Postup

**FOKUS: <PERSON><PERSON><PERSON> migrace na NodePalette typy bez backward compatibility**

**AKTUÁLNÍ STAV:** 🚨 KRITICKÝ PROBLÉM - Vizuální editor ne<PERSON><PERSON><PERSON><PERSON> ukládat workflow

**CLEAN MIGRATION APPROACH:**

1. Odstranit VŠECHNY legacy node typy z backendu
2. Implementovat VŠECHNY NodePalette typy jako primary types
3. Žádné aliasy, žádné mapping - pouze clean NodePalette systém

## PRIORITA 1: Legacy Cleanup (Týden 1) 🚨 KRITICKÉ

### 1.1 Remove Legacy Node Implementations ⚡ VYSOKÁ PRIORITA

- [x] **Smazat staré node classes** ✅ HOTOVO

  - [x] Odstranit `src/workflow/nodes/JavaScriptNode.ts` ✅
  - [x] Odstranit `src/workflow/nodes/HTTPNode.ts` ✅
  - [x] Odstranit `src/workflow/nodes/SQLNodeFactory.ts` a související ✅
  - [x] Odstranit `src/workflow/nodes/RedisNodeFactory.ts` a související ✅
  - [x] Odstranit `src/workflow/nodes/LiteLLMNodeFactory.ts` a související ✅
  - [x] Odstranit `src/workflows/nodes/logic/IfElseNode.ts` ✅
  - [x] Odstranit `src/workflows/nodes/logic/SwitchNode.ts` ✅
  - [x] Odstranit `src/workflows/nodes/logic/MergeNode.ts` ✅

- [x] **Vyčistit factory registrations** ⚠️ ČÁSTEČNĚ HOTOVO
  - [x] Odstranit legacy registrace z `src/inversify.config.ts` ✅
  - [x] Odstranit `createJavaScriptNodeFactory` ✅
  - [x] Odstranit `createHTTPNodeFactory` ✅
  - [x] Odstranit `createSQLNodeFactory` ✅
  - [x] Odstranit `createRedisNodeFactory` ✅
  - [x] Odstranit `createLiteLLMNodeFactory` ✅
  - [x] `registerLogicNodes` - vyprázdněn, pouze log message ✅
  - [ ] `registerActionNodes` - stále registruje VectorDB, GraphQL, SOAP nodes ⚠️
  - [ ] `registerTransformNodes` - stále registruje transform nodes ⚠️

### 1.2 Clean NodeRegistry ⚡ VYSOKÁ PRIORITA

- [x] **Simplifikovat NodeRegistry** ✅ HOTOVO

  - [x] NodeRegistry používá pouze direct lookup ✅
  - [x] Žádné aliasy nejsou podporovány ✅
  - [x] Error messages používají `getRegisteredNodeTypes()` ✅

- [ ] **Aktualizovat INodeRegistry interface**
  - [ ] Odstranit alias-related metody
  - [ ] Simplifikovat type definitions
  - [ ] Aktualizovat JSDoc dokumentaci

## PRIORITA 2: New Node Type Implementation (Týden 1-2) 🔥 VYSOKÁ PRIORITA

### 2.1 Trigger Nodes Implementation (NOVÉ)

- [x] **RestAPITriggerNode** (type: `rest-api-trigger`) ✅ HOTOVO

  - [x] Vytvořit `IRestAPITriggerConfig` interface ✅
  - [x] Implementovat `RestAPITriggerNode` class ✅
  - [x] Přidat REST API specific configuration ✅
  - [x] Implementovat HTTP endpoint handling ✅
  - [x] Vytvořit `createRestAPITriggerFactory` function ✅

- [x] **MCPFunctionTriggerNode** (type: `mcp-function-trigger`) ✅ HOTOVO

  - [x] Vytvořit `IMCPFunctionTriggerConfig` interface ✅
  - [x] Implementovat `MCPFunctionTriggerNode` class ✅
  - [x] Přidat MCP function call configuration ✅
  - [x] Implementovat MCP integration logic ✅
  - [x] Vytvořit `createMCPFunctionTriggerFactory` function ✅

- [x] **WebhookTriggerNode** (type: `webhook-trigger`) ✅ HOTOVO

  - [x] Vytvořit `IWebhookTriggerConfig` interface ✅
  - [x] Implementovat `WebhookTriggerNode` class ✅
  - [x] Přidat webhook specific configuration ✅
  - [x] Implementovat external webhook handling ✅
  - [x] Vytvořit `createWebhookTriggerFactory` function ✅

- [x] **TimerTriggerNode** (type: `timer-trigger`) ✅ HOTOVO
  - [x] Vytvořit `ITimerTriggerConfig` interface ✅
  - [x] Implementovat `TimerTriggerNode` class ✅
  - [x] Přidat cron expression validation ✅
  - [x] Implementovat scheduling logic ✅
  - [x] Vytvořit `createTimerTriggerFactory` function ✅

### 2.2 Enhanced Action Nodes Implementation (NAHRAZENÉ)

- [x] **JavaScriptActionNode** (type: `javascript-action`) ✅ HOTOVO

  - [x] Vytvořit nový `IJavaScriptActionConfig` interface ✅
  - [x] Implementovat `JavaScriptActionNode` class (enhanced verze) ✅
  - [x] Přidat enhanced configuration options ✅
  - [x] Implementovat better error handling ✅
  - [x] Vytvořit `createJavaScriptActionFactory` function ✅

- [x] **SQLActionNode** (type: `sql-action`) ✅ HOTOVO

  - [x] Vytvořit nový `ISQLActionConfig` interface ✅
  - [x] Implementovat `SQLActionNode` class (enhanced verze) ✅
  - [x] Přidat enhanced query capabilities ✅
  - [x] Implementovat better connection management ✅
  - [x] Vytvořit `createSQLActionFactory` function ✅

- [x] **RedisActionNode** (type: `redis-action`) ✅ HOTOVO

  - [x] Vytvořit nový `IRedisActionConfig` interface ✅
  - [x] Implementovat `RedisActionNode` class (enhanced verze) ✅
  - [x] Přidat enhanced Redis operations ✅
  - [x] Implementovat better caching strategies ✅
  - [x] Vytvořit `createRedisActionFactory` function ✅

- [x] **HTTPActionNode** (type: `http-action`) ✅ HOTOVO

  - [x] Vytvořit nový `IHTTPActionConfig` interface ✅
  - [x] Implementovat `HTTPActionNode` class (enhanced verze) ✅
  - [x] Přidat enhanced HTTP capabilities ✅
  - [x] Implementovat better request/response handling ✅
  - [x] Vytvořit `createHTTPActionFactory` function ✅

- [x] **LiteLLMActionNode** (type: `litellm-action`) ✅ HOTOVO
  - [x] Vytvořit nový `ILiteLLMActionConfig` interface ✅
  - [x] Implementovat `LiteLLMActionNode` class (enhanced verze) ✅
  - [x] Přidat enhanced LLM capabilities ✅
  - [x] Implementovat better model management ✅
  - [x] Vytvořit `createLiteLLMActionFactory` function ✅

### 2.3 Enhanced Logic Nodes Implementation (NAHRAZENÉ + NOVÉ)

- [x] **IfElseLogicNode** (type: `if-else-logic`) ✅ HOTOVO

  - [x] Vytvořit nový `IIfElseLogicConfig` interface ✅
  - [x] Implementovat `IfElseLogicNode` class (enhanced verze) ✅
  - [x] Přidat enhanced conditional logic ✅
  - [x] Implementovat better expression evaluation ✅
  - [x] Vytvořit `createIfElseLogicFactory` function ✅

- [x] **SwitchLogicNode** (type: `switch-logic`) ✅ HOTOVO

  - [x] Vytvořit nový `ISwitchLogicConfig` interface ✅
  - [x] Implementovat `SwitchLogicNode` class (enhanced verze) ✅
  - [x] Přidat enhanced switch capabilities ✅
  - [x] Implementovat better case handling ✅
  - [x] Vytvořit `createSwitchLogicFactory` function ✅

- [x] **MergeLogicNode** (type: `merge-logic`) ✅ HOTOVO

  - [x] Vytvořit nový `IMergeLogicConfig` interface ✅
  - [x] Implementovat `MergeLogicNode` class (enhanced verze) ✅
  - [x] Přidat enhanced merge strategies ✅
  - [x] Implementovat better data combination ✅
  - [x] Vytvořit `createMergeLogicFactory` function ✅

- [x] **LoopLogicNode** (type: `loop-logic`) - NOVÝ ✅ HOTOVO
  - [x] Vytvořit `ILoopLogicConfig` interface ✅
  - [x] Implementovat `LoopLogicNode` class ✅
  - [x] Přidat loop/iteration capabilities ✅
  - [x] Implementovat forEach, while, for loops ✅
  - [x] Vytvořit `createLoopLogicFactory` function ✅

### 2.4 Terminator Nodes Implementation (NOVÉ)

- [x] **ResponseTerminatorNode** (type: `response-terminator`) ✅ HOTOVO

  - [x] Vytvořit `IResponseTerminatorConfig` interface ✅
  - [x] Implementovat `ResponseTerminatorNode` class ✅
  - [x] Přidat HTTP response formatting ✅
  - [x] Implementovat status code handling ✅
  - [x] Vytvořit `createResponseTerminatorFactory` function ✅

- [x] **MCPResponseTerminatorNode** (type: `mcp-response-terminator`) ✅ HOTOVO

  - [x] Vytvořit `IMCPResponseTerminatorConfig` interface ✅
  - [x] Implementovat `MCPResponseTerminatorNode` class ✅
  - [x] Přidat MCP response formatting ✅
  - [x] Implementovat schema validation ✅
  - [x] Vytvořit `createMCPResponseTerminatorFactory` function ✅

- [x] **ErrorTerminatorNode** (type: `error-terminator`) ✅ HOTOVO
  - [x] Vytvořit `IErrorTerminatorConfig` interface ✅
  - [x] Implementovat `ErrorTerminatorNode` class ✅
  - [x] Přidat error formatting logic ✅
  - [x] Implementovat error logging ✅
  - [x] Vytvořit `createErrorTerminatorFactory` function ✅

## PRIORITA 3: Clean Registration (Týden 2) 🔥 VYSOKÁ PRIORITA

### 3.1 Complete NodePalette Registration

- [x] **Aktualizovat inversify.config.ts** ✅ HOTOVO

  - [x] Registrovat všechny trigger nodes ✅
  - [x] Registrovat všechny enhanced action nodes ✅
  - [x] Registrovat všechny enhanced logic nodes ✅
  - [x] Registrovat všechny terminator nodes ✅
  - [x] Ověřit, že ŽÁDNÉ legacy typy nejsou registrované ✅

- [ ] **Aktualizovat TYPES konstanty**
  - [ ] Přidat nové factory types
  - [ ] Odstranit legacy factory types
  - [ ] Aktualizovat dokumentaci

### 3.2 Backend Validation Updates

- [ ] **Aktualizovat NodeValidator** ⚠️ ČÁSTEČNĚ HOTOVO

  - [x] `validateNodeType()` používá `getRegisteredNodeTypes()` ✅
  - [x] Clear error messages s supported types ✅
  - [ ] Odstranit legacy type validation (stále obsahuje `javascript`, `http`, `sql`, `redis`, `litellm`) ⚠️

- [ ] **Aktualizovat WorkflowValidator**
  - [ ] Rozšířit validaci pro trigger/terminator kombinace
  - [ ] Implementovat workflow structure validation
  - [ ] Přidat validation pro nové node categories

## PRIORITA 4: Frontend Simplification (Týden 2-3) 🔥 VYSOKÁ PRIORITA

### 4.1 Remove Mapping Complexity

- [x] **Simplifikovat VisualWorkflowEditor** ✅ HOTOVO

  - [x] Frontend používá čisté node typy (trigger, action, logic, terminator) ✅
  - [x] Žádná mapping logic není přítomna ✅
  - [x] Direct usage NodePalette typů ✅
  - [x] Simplifikované error handling ✅

- [ ] **Aktualizovat API Service**
  - [ ] Odstranit WorkflowTransformer (pokud existuje)
  - [ ] Direct API calls bez transformace
  - [ ] Simplifikovat error handling

### 4.2 Enhanced User Experience

- [ ] **Implementovat better validation feedback**
  - [ ] Real-time validation ve visual editoru
  - [ ] Clear error messages pro unsupported nodes
  - [ ] Success feedback pro successful saves
  - [ ] Loading states pro API operations

## 🚨 ZBÝVAJÍCÍ ÚKOLY - DOKONČENO ✅

### Legacy Code Cleanup - HOTOVO ✅

- [x] **Odstranit zbývající legacy factory calls z `inversify.config.ts`** ✅

  - [x] Odstranit `registerActionNodes(container)` call (řádek 174) ✅
  - [x] Odstranit `registerTransformNodes(container)` call (řádek 175) ✅
  - [x] Odstranit importy `registerLogicNodes`, `registerActionNodes`, `registerTransformNodes` (řádky 68-70) ✅

- [x] **Aktualizovat NodeValidator pro NodePalette typy** ✅
  - [x] Odstranit legacy type validation z `validateNodeConfig()` metody ✅
  - [x] Odstranit case statements pro `javascript`, `http`, `sql`, `redis`, `litellm` ✅
  - [x] Přidat case statements pro NodePalette typy (`javascript-action`, `http-action`, atd.) ✅
  - [x] Aktualizovat legacy validation metody na NodePalette názvy ✅

### Phase 2 Extended Nodes - ROZHODNUTO ✅

- [x] **Rozhodnuto o Phase 2 nodes (VectorDB, GraphQL, SOAP)** ✅
  - [x] Dočasně zakomentovány pro udržení čisté NodePalette migrace ✅
  - [x] TODO přidáno pro Phase 2 - převést na NodePalette naming convention ✅
  - [x] `registerActionNodes()` funkce prázdná s informativní zprávou ✅

## PRIORITA 5: Testing & Documentation (Týden 3) 📚 STŘEDNÍ PRIORITA

### 5.1 Comprehensive Testing

- [ ] **Unit Tests pro všechny nové node typy**

  - [ ] Test všech trigger nodes
  - [ ] Test všech enhanced action nodes
  - [ ] Test všech enhanced logic nodes
  - [ ] Test všech terminator nodes

- [ ] **Integration Tests**
  - [ ] Test NodeRegistry s novými typy
  - [ ] Test workflow execution s NodePalette typy
  - [ ] End-to-end visual editor tests
  - [ ] API integration tests

### 5.2 Documentation Updates

- [ ] **API Documentation**

  - [ ] Dokumentovat všechny nové node typy
  - [ ] Aktualizovat workflow API docs
  - [ ] Vytvořit migration guide (delete & recreate)
  - [ ] Dokumentovat supported node types

- [ ] **Developer Documentation**
  - [ ] Aktualizovat README s novými node typy
  - [ ] Dokumentovat development workflow
  - [ ] Přidat examples a best practices
  - [ ] Vytvořit troubleshooting guide

## 🎯 Milestones a Deliverables

### Milestone 1: Legacy Cleanup Complete (Týden 1)

- ✅ Všechny legacy node types odstraněny
- ✅ NodeRegistry simplified
- ✅ Clean foundation připravena

### Milestone 2: All NodePalette Types Implemented (Týden 2)

- ✅ Všechny NodePalette typy implementovány
- ✅ Complete registration v NodeRegistry
- ✅ Backend validation aktualizován

### Milestone 3: Frontend Simplified (Týden 2-3)

- ✅ Visual editor používá direct NodePalette typy
- ✅ Žádná mapping complexity
- ✅ Seamless workflow creation/editing

### Milestone 4: Production Ready (Týden 3)

- ✅ Comprehensive testing complete
- ✅ Documentation aktualizována
- ✅ Clean architecture verified

## 📊 Success Criteria

### Technical Requirements

- [ ] 100% NodePalette typů podporováno na backendu
- [ ] Zero legacy code v codebase
- [ ] Direct usage NodePalette typů všude
- [ ] Simplified architecture bez mapping
- [ ] 95%+ test coverage pro nové komponenty

### User Experience Requirements

- [ ] Visual editor může uložit všechny node typy z palette
- [ ] JSON editor používá stejné typy jako visual editor
- [ ] Clear feedback při validation errors
- [ ] Intuitive workflow creation experience
- [ ] No complexity pro end users

## 🚀 Phase 6 Success Definition

Phase 6 bude úspěšně dokončena když:

1. ✅ **Clean Node Type System**: Pouze NodePalette typy podporovány
2. ✅ **No Legacy Code**: NodeValidator a factory calls vyčištěny
3. ✅ **Visual Editor Fully Functional**: Všechny nodes z palette lze použít
4. ✅ **Unified Experience**: Stejné typy ve visual i JSON editoru
5. ✅ **Simplified Architecture**: Žádné mapping nebo aliasy
6. [ ] **Comprehensive Testing**: 95%+ coverage a end-to-end testy (neotestováno)
7. [ ] **Clean Documentation**: Pouze aktuální node typy dokumentovány (neotestováno)

## 🔄 Migration Instructions for Developers

### Before Phase 6

- Existující workflow používají legacy typy (`javascript`, `http`, `sql`, atd.)
- Visual editor nefunguje

### After Phase 6

1. **Delete all existing workflows** s legacy node typy
2. **Recreate workflows** using visual editor s NodePalette typy
3. **Manual JSON editing** možné s novými typy (`javascript-action`, `http-action`, atd.)
4. **Full compatibility** mezi visual a JSON editorem

### Benefits

- **Clean slate** - žádné legacy baggage
- **Consistent experience** - stejné typy všude
- **Better maintainability** - jednodušší codebase
- **Future-proof** - extensible architecture
