# Phase 6 Implementation Prompt - Clean Node Type Migration

## 🎯 Project Context

You are implementing Phase 6 of the Dynamic MCP Server project - a critical fix for the visual workflow editor that currently cannot save workflows due to node type incompatibility between frontend and backend.

## 🚨 Current Problem

The visual workflow editor in Phase 5 has a fundamental issue:
- **Frontend NodePalette** uses descriptive types: `rest-api-trigger`, `javascript-action`, `sql-action`, `if-else-logic`
- **Backend NodeRegistry** supports legacy types: `javascript`, `http`, `sql`, `redis`, `litellm`, `if-else`, `switch`, `merge`
- **Result**: Visual editor cannot save workflows - server returns "unsupported node type" errors

## 🔧 Solution Approach

**CLEAN MIGRATION WITHOUT BACKWARD COMPATIBILITY**

We are implementing a clean slate approach:
1. **Remove ALL legacy node types** from backend
2. **Implement ALL NodePalette types** as primary types
3. **No aliases, no mapping** - only clean NodePalette system

## 📋 Implementation Tasks

### PRIORITY 1: Legacy Cleanup (Week 1)

#### 1.1 Remove Legacy Node Implementations
Delete these files and their registrations:
- `src/workflow/nodes/JavaScriptNode.ts`
- `src/workflow/nodes/HTTPNode.ts`
- `src/workflow/nodes/SQLNodeFactory.ts` and related
- `src/workflow/nodes/RedisNodeFactory.ts` and related
- `src/workflow/nodes/LiteLLMNodeFactory.ts` and related
- `src/workflows/nodes/logic/IfElseNode.ts`
- `src/workflows/nodes/logic/SwitchNode.ts`
- `src/workflows/nodes/logic/MergeNode.ts`

#### 1.2 Clean NodeRegistry
- Remove legacy registrations from `src/inversify.config.ts`
- Simplify NodeRegistry (no alias support needed)
- Update error messages for clear feedback

### PRIORITY 2: New Node Type Implementation (Week 1-2)

#### 2.1 Trigger Nodes (NEW)
Implement these new node types:
- **RestAPITriggerNode** (type: `rest-api-trigger`)
- **MCPFunctionTriggerNode** (type: `mcp-function-trigger`)
- **WebhookTriggerNode** (type: `webhook-trigger`)
- **TimerTriggerNode** (type: `timer-trigger`)

#### 2.2 Enhanced Action Nodes (REPLACEMENTS)
Replace legacy nodes with enhanced versions:
- **JavaScriptActionNode** (type: `javascript-action`) - replaces JavaScriptNode
- **SQLActionNode** (type: `sql-action`) - replaces SQLNode
- **RedisActionNode** (type: `redis-action`) - replaces RedisNode
- **HTTPActionNode** (type: `http-action`) - replaces HTTPNode
- **LiteLLMActionNode** (type: `litellm-action`) - replaces LiteLLMNode

#### 2.3 Enhanced Logic Nodes (REPLACEMENTS + NEW)
- **IfElseLogicNode** (type: `if-else-logic`) - replaces IfElseNode
- **SwitchLogicNode** (type: `switch-logic`) - replaces SwitchNode
- **MergeLogicNode** (type: `merge-logic`) - replaces MergeNode
- **LoopLogicNode** (type: `loop-logic`) - NEW node type

#### 2.4 Terminator Nodes (NEW)
- **ResponseTerminatorNode** (type: `response-terminator`)
- **MCPResponseTerminatorNode** (type: `mcp-response-terminator`)
- **ErrorTerminatorNode** (type: `error-terminator`)

### PRIORITY 3: Registration & Testing (Week 2-3)

#### 3.1 Complete Registration
Update `src/inversify.config.ts` to register ALL NodePalette types:

```typescript
// Register ALL NodePalette types as primary
nodeRegistry.registerNode('rest-api-trigger', createRestAPITriggerFactory(container));
nodeRegistry.registerNode('mcp-function-trigger', createMCPFunctionTriggerFactory(container));
nodeRegistry.registerNode('webhook-trigger', createWebhookTriggerFactory(container));
nodeRegistry.registerNode('timer-trigger', createTimerTriggerFactory(container));

nodeRegistry.registerNode('javascript-action', createJavaScriptActionFactory(container));
nodeRegistry.registerNode('sql-action', createSQLActionFactory(container));
nodeRegistry.registerNode('redis-action', createRedisActionFactory(container));
nodeRegistry.registerNode('http-action', createHTTPActionFactory(container));
nodeRegistry.registerNode('litellm-action', createLiteLLMActionFactory(container));

nodeRegistry.registerNode('if-else-logic', createIfElseLogicFactory(container));
nodeRegistry.registerNode('switch-logic', createSwitchLogicFactory(container));
nodeRegistry.registerNode('merge-logic', createMergeLogicFactory(container));
nodeRegistry.registerNode('loop-logic', createLoopLogicFactory(container));

nodeRegistry.registerNode('response-terminator', createResponseTerminatorFactory(container));
nodeRegistry.registerNode('mcp-response-terminator', createMCPResponseTerminatorFactory(container));
nodeRegistry.registerNode('error-terminator', createErrorTerminatorFactory(container));
```

#### 3.2 Frontend Simplification
The frontend should already work without changes since it uses NodePalette types directly. Verify that:
- `VisualWorkflowEditor.tsx` uses `node.data.type` directly
- No mapping or transformation is applied
- API calls send NodePalette types directly to backend

### PRIORITY 4: Testing (Week 3)

#### 4.1 Unit Tests
Create comprehensive unit tests for all new node types:
- Test node construction and configuration validation
- Test execute() methods
- Test error handling

#### 4.2 Integration Tests
- Test NodeRegistry with new types
- Test workflow execution with NodePalette types
- End-to-end visual editor tests

## 📚 Reference Documentation

Study these files in `.project-idea/iterations/phase6/`:
- **design.md** - Complete architectural design
- **todo.md** - Detailed task breakdown
- **postulates.md** - Implementation principles
- **README.md** - Project overview

## 🔧 Technical Guidelines

### Node Implementation Pattern
Each new node should follow this pattern:

```typescript
// Interface definition
export interface INewNodeConfig {
  // Node-specific configuration
}

// Node implementation
export class NewNode extends BaseNode {
  private config: INewNodeConfig;
  
  constructor(config: INewNodeConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }
  
  protected validateConfig(): void {
    super.validateConfig();
    // Node-specific validation
  }
  
  async execute(input: any, context: WorkflowContext): Promise<any> {
    // Node-specific execution logic
  }
}

// Factory function
export function createNewNodeFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new NewNode(config as INewNodeConfig, logger);
  };
}
```

### Directory Structure
Organize new nodes in these directories:
- `src/workflow/nodes/trigger/` - Trigger nodes
- `src/workflow/nodes/action/` - Action nodes (enhanced versions)
- `src/workflow/nodes/logic/` - Logic nodes (enhanced versions)
- `src/workflow/nodes/terminator/` - Terminator nodes

### Error Handling
Provide clear error messages that include:
- Supported node types list
- Specific validation failures
- Actionable guidance for users

## 🎯 Success Criteria

### Technical Requirements
- [ ] 100% NodePalette types supported on backend
- [ ] Zero legacy code in codebase
- [ ] Direct usage of NodePalette types everywhere
- [ ] Simplified architecture without mapping
- [ ] 95%+ test coverage for new components

### User Experience Requirements
- [ ] Visual editor can save all node types from palette
- [ ] JSON editor uses same types as visual editor
- [ ] Clear feedback on validation errors
- [ ] Intuitive workflow creation experience

## 🚀 Expected Outcome

After Phase 6 completion:
1. **Visual editor fully functional** - all nodes from palette can be saved
2. **Unified experience** - same types in visual and JSON editor
3. **Clean architecture** - no legacy code or mapping complexity
4. **Future-proof** - easy to add new node types

## 🔄 Migration Instructions for Users

Inform users that:
1. **Existing workflows with legacy types must be deleted**
2. **Recreate workflows using visual editor with NodePalette types**
3. **Manual JSON editing possible with new types**
4. **Full compatibility between visual and JSON editor**

## ⚠️ Important Notes

- **No backward compatibility** - this is intentional for simplicity
- **Clean slate approach** - delete and recreate existing workflows
- **Direct implementation** - no mapping or transformation layers
- **NodePalette is authoritative** - backend follows frontend naming exactly

## 📞 Support

If you encounter issues or need clarification:
1. Review the detailed documentation in `.project-idea/iterations/phase6/`
2. Check existing node implementations for patterns
3. Ensure you understand the clean migration approach
4. Focus on simplicity over complexity

**Remember: This is a clean migration without backward compatibility. The goal is a simple, unified node type system across the entire application.**
