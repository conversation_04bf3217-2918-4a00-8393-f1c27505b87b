# Phase 6: Implementation Details - Node Type Synchronization

## 🔧 Detailní Implementační Specifikace

### 1. Frontend Mapping Layer Implementation

#### 1.1 NodeTypeMapper Service

```typescript
// frontend/src/services/nodeTypeMapper.ts
export interface NodeTypeMapping {
  frontendType: string;
  backendType: string;
  category: 'trigger' | 'action' | 'logic' | 'terminator';
  requiresSpecialHandling?: boolean;
}

export const NODE_TYPE_MAPPINGS: NodeTypeMapping[] = [
  // Trigger Nodes
  { frontendType: 'rest-api-trigger', backendType: 'http', category: 'trigger' },
  { frontendType: 'mcp-function-trigger', backendType: 'mcp-function', category: 'trigger' },
  { frontendType: 'webhook-trigger', backendType: 'http', category: 'trigger' },
  { frontendType: 'timer-trigger', backendType: 'timer', category: 'trigger' },
  
  // Action Nodes
  { frontendType: 'javascript-action', backendType: 'javascript', category: 'action' },
  { frontendType: 'sql-action', backendType: 'sql', category: 'action' },
  { frontendType: 'redis-action', backendType: 'redis', category: 'action' },
  { frontendType: 'http-action', backendType: 'http', category: 'action' },
  { frontendType: 'litellm-action', backendType: 'litellm', category: 'action' },
  
  // Logic Nodes
  { frontendType: 'if-else-logic', backendType: 'if-else', category: 'logic' },
  { frontendType: 'switch-logic', backendType: 'switch', category: 'logic' },
  { frontendType: 'merge-logic', backendType: 'merge', category: 'logic' },
  
  // Terminator Nodes
  { frontendType: 'response-terminator', backendType: 'response', category: 'terminator' },
  { frontendType: 'mcp-response-terminator', backendType: 'mcp-response', category: 'terminator' },
  { frontendType: 'error-terminator', backendType: 'error-handler', category: 'terminator' }
];

export class NodeTypeMapper {
  private frontendToBackendMap: Map<string, string>;
  private backendToFrontendMap: Map<string, string>;
  
  constructor() {
    this.initializeMappings();
  }
  
  private initializeMappings(): void {
    this.frontendToBackendMap = new Map();
    this.backendToFrontendMap = new Map();
    
    NODE_TYPE_MAPPINGS.forEach(mapping => {
      this.frontendToBackendMap.set(mapping.frontendType, mapping.backendType);
      this.backendToFrontendMap.set(mapping.backendType, mapping.frontendType);
    });
  }
  
  mapToBackend(frontendType: string): string {
    const backendType = this.frontendToBackendMap.get(frontendType);
    if (!backendType) {
      console.warn(`No backend mapping found for frontend type: ${frontendType}`);
      return frontendType; // Fallback to original type
    }
    return backendType;
  }
  
  mapToFrontend(backendType: string): string {
    const frontendType = this.backendToFrontendMap.get(backendType);
    if (!frontendType) {
      console.warn(`No frontend mapping found for backend type: ${backendType}`);
      return backendType; // Fallback to original type
    }
    return frontendType;
  }
  
  validateMapping(frontendType: string): boolean {
    return this.frontendToBackendMap.has(frontendType);
  }
  
  getSupportedFrontendTypes(): string[] {
    return Array.from(this.frontendToBackendMap.keys());
  }
  
  getSupportedBackendTypes(): string[] {
    return Array.from(this.backendToFrontendMap.keys());
  }
}
```

#### 1.2 Workflow Transformation Service

```typescript
// frontend/src/services/workflowTransformer.ts
import { NodeTypeMapper } from './nodeTypeMapper';

export interface WorkflowNode {
  id: string;
  type: string;
  name: string;
  config: Record<string, any>;
  position?: { x: number; y: number };
}

export interface WorkflowData {
  name: string;
  description: string;
  enabled: boolean;
  input_schema: any;
  nodes_config: {
    nodes: WorkflowNode[];
    edges: Array<{ source: string; target: string }>;
  };
}

export class WorkflowTransformer {
  private nodeTypeMapper: NodeTypeMapper;
  
  constructor() {
    this.nodeTypeMapper = new NodeTypeMapper();
  }
  
  transformForBackend(frontendWorkflow: WorkflowData): WorkflowData {
    return {
      ...frontendWorkflow,
      nodes_config: {
        ...frontendWorkflow.nodes_config,
        nodes: frontendWorkflow.nodes_config.nodes.map(node => ({
          ...node,
          type: this.nodeTypeMapper.mapToBackend(node.type)
        }))
      }
    };
  }
  
  transformForFrontend(backendWorkflow: WorkflowData): WorkflowData {
    return {
      ...backendWorkflow,
      nodes_config: {
        ...backendWorkflow.nodes_config,
        nodes: backendWorkflow.nodes_config.nodes.map(node => ({
          ...node,
          type: this.nodeTypeMapper.mapToFrontend(node.type)
        }))
      }
    };
  }
  
  validateWorkflow(workflow: WorkflowData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    workflow.nodes_config.nodes.forEach((node, index) => {
      if (!this.nodeTypeMapper.validateMapping(node.type)) {
        errors.push(`Node ${index + 1} (${node.name}): Unsupported node type '${node.type}'`);
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

### 2. Backend Node Implementation

#### 2.1 Trigger Nodes

```typescript
// src/workflow/nodes/trigger/MCPFunctionTriggerNode.ts
import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import { ILogger } from '../../../core/interfaces/ILogger';

export interface MCPFunctionTriggerConfig {
  functionName: string;
  inputSchema: Record<string, any>;
  outputSchema: Record<string, any>;
  timeout?: number;
}

export class MCPFunctionTriggerNode extends BaseNode {
  private config: MCPFunctionTriggerConfig;
  
  constructor(config: MCPFunctionTriggerConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }
  
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.functionName) {
      throw new Error('MCP Function Trigger: functionName is required');
    }
    
    if (!this.config.inputSchema) {
      throw new Error('MCP Function Trigger: inputSchema is required');
    }
  }
  
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info(`Executing MCP Function Trigger: ${this.config.functionName}`);
    
    try {
      // MCP function triggers pass through the input data
      // The actual MCP function call is handled by the workflow engine
      return {
        functionName: this.config.functionName,
        input: input,
        timestamp: new Date().toISOString(),
        context: context.executionId
      };
    } catch (error) {
      this.logger.error(`MCP Function Trigger execution failed: ${error.message}`);
      throw error;
    }
  }
}
```

```typescript
// src/workflow/nodes/trigger/TimerTriggerNode.ts
import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import { ILogger } from '../../../core/interfaces/ILogger';

export interface TimerTriggerConfig {
  cronExpression: string;
  timezone?: string;
  enabled: boolean;
  maxExecutions?: number;
}

export class TimerTriggerNode extends BaseNode {
  private config: TimerTriggerConfig;
  
  constructor(config: TimerTriggerConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }
  
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.cronExpression) {
      throw new Error('Timer Trigger: cronExpression is required');
    }
    
    // Basic cron validation (can be enhanced)
    const cronParts = this.config.cronExpression.split(' ');
    if (cronParts.length !== 5) {
      throw new Error('Timer Trigger: Invalid cron expression format');
    }
  }
  
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info(`Executing Timer Trigger: ${this.config.cronExpression}`);
    
    try {
      return {
        triggerType: 'timer',
        cronExpression: this.config.cronExpression,
        executionTime: new Date().toISOString(),
        timezone: this.config.timezone || 'UTC',
        input: input,
        context: context.executionId
      };
    } catch (error) {
      this.logger.error(`Timer Trigger execution failed: ${error.message}`);
      throw error;
    }
  }
}
```

#### 2.2 Terminator Nodes

```typescript
// src/workflow/nodes/terminator/ResponseTerminatorNode.ts
import { BaseNode } from '../BaseNode';
import { WorkflowContext } from '../../../core/types/WorkflowContext';
import { ILogger } from '../../../core/interfaces/ILogger';

export interface ResponseTerminatorConfig {
  statusCode: number;
  headers: Record<string, string>;
  body: string; // Template string with variable substitution
  contentType?: string;
}

export class ResponseTerminatorNode extends BaseNode {
  private config: ResponseTerminatorConfig;
  
  constructor(config: ResponseTerminatorConfig, logger: ILogger) {
    super(config, logger);
    this.config = config;
  }
  
  protected validateConfig(): void {
    super.validateConfig();
    
    if (!this.config.statusCode || this.config.statusCode < 100 || this.config.statusCode > 599) {
      throw new Error('Response Terminator: Valid statusCode (100-599) is required');
    }
  }
  
  async execute(input: any, context: WorkflowContext): Promise<any> {
    this.logger.info(`Executing Response Terminator with status: ${this.config.statusCode}`);
    
    try {
      // Process template variables in body
      let processedBody = this.config.body;
      
      // Replace ${result} with input data
      processedBody = processedBody.replace(/\$\{result\}/g, JSON.stringify(input));
      
      // Replace ${context.variable} with context variables
      Object.keys(context.variables || {}).forEach(key => {
        const regex = new RegExp(`\\$\\{context\\.${key}\\}`, 'g');
        processedBody = processedBody.replace(regex, context.variables[key]);
      });
      
      return {
        statusCode: this.config.statusCode,
        headers: {
          'Content-Type': this.config.contentType || 'application/json',
          ...this.config.headers
        },
        body: processedBody
      };
    } catch (error) {
      this.logger.error(`Response Terminator execution failed: ${error.message}`);
      throw error;
    }
  }
}
```

#### 2.3 Node Factory Registration

```typescript
// src/workflow/nodes/trigger/TriggerNodeFactory.ts
import { Container } from 'inversify';
import { INode } from '../../../core/interfaces/INode';
import { ILogger } from '../../../core/interfaces/ILogger';
import { TYPES } from '../../../types';
import { MCPFunctionTriggerNode, MCPFunctionTriggerConfig } from './MCPFunctionTriggerNode';
import { TimerTriggerNode, TimerTriggerConfig } from './TimerTriggerNode';

export function createMCPFunctionTriggerFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new MCPFunctionTriggerNode(config as MCPFunctionTriggerConfig, logger);
  };
}

export function createTimerTriggerFactory(container: Container) {
  return (config: Record<string, any>): INode => {
    const logger = container.get<ILogger>(TYPES.Logger);
    return new TimerTriggerNode(config as TimerTriggerConfig, logger);
  };
}

export function registerTriggerNodes(container: Container): void {
  const nodeRegistry = container.get(TYPES.NodeRegistry);
  
  // Register trigger nodes
  (nodeRegistry as any).registerNode('mcp-function', createMCPFunctionTriggerFactory(container));
  (nodeRegistry as any).registerNode('timer', createTimerTriggerFactory(container));
  
  console.log('Trigger nodes registered successfully');
}
```

### 3. Integration Points

#### 3.1 API Service Updates

```typescript
// frontend/src/services/api.ts - Updated methods
import { WorkflowTransformer } from './workflowTransformer';

class ApiService {
  private workflowTransformer: WorkflowTransformer;
  
  constructor() {
    this.workflowTransformer = new WorkflowTransformer();
  }
  
  async createWorkflow(workflow: any): Promise<any> {
    // Validate workflow before transformation
    const validation = this.workflowTransformer.validateWorkflow(workflow);
    if (!validation.isValid) {
      throw new Error(`Workflow validation failed: ${validation.errors.join(', ')}`);
    }
    
    // Transform for backend
    const backendWorkflow = this.workflowTransformer.transformForBackend(workflow);
    
    const response = await this.api.post('/api/workflows', backendWorkflow);
    return response.data;
  }
  
  async getWorkflow(id: string): Promise<any> {
    const response = await this.api.get(`/api/workflows/${id}`);
    
    // Transform for frontend
    const frontendWorkflow = this.workflowTransformer.transformForFrontend(response.data);
    
    return frontendWorkflow;
  }
  
  async testWorkflow(workflow: any, input: any): Promise<any> {
    // Validate and transform workflow
    const validation = this.workflowTransformer.validateWorkflow(workflow);
    if (!validation.isValid) {
      throw new Error(`Workflow validation failed: ${validation.errors.join(', ')}`);
    }
    
    const backendWorkflow = this.workflowTransformer.transformForBackend(workflow);
    
    const response = await this.api.post('/api/workflows/test', { 
      workflow: backendWorkflow, 
      input 
    });
    return response.data;
  }
}
```

#### 3.2 Backend Validation Updates

```typescript
// src/api/validators/NodeValidator.ts - Enhanced validation
export class NodeValidator {
  private nodeRegistry: INodeRegistry;
  
  private validateNodeType(nodeType: string): void {
    try {
      const availableNodeTypes = this.nodeRegistry.getRegisteredNodeTypes();
      if (!availableNodeTypes.includes(nodeType)) {
        // Provide helpful suggestions
        const suggestions = this.findSimilarNodeTypes(nodeType, availableNodeTypes);
        const suggestionText = suggestions.length > 0 
          ? ` Did you mean: ${suggestions.join(', ')}?`
          : '';
          
        throw new ValidationError('Validation failed', {
          errors: [
            {
              field: 'node_type',
              message: `Unsupported node type: ${nodeType}.${suggestionText}`,
              code: ErrorCode.INVALID_VALUE,
              value: nodeType,
              expected: availableNodeTypes.join(', ')
            }
          ]
        });
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError('Failed to validate node type', {
        errors: [
          {
            field: 'node_type',
            message: 'Failed to validate node type',
            code: ErrorCode.VALIDATION_ERROR,
            value: nodeType
          }
        ]
      });
    }
  }
  
  private findSimilarNodeTypes(input: string, available: string[]): string[] {
    // Simple similarity matching
    return available.filter(type => 
      type.includes(input) || input.includes(type) || 
      this.levenshteinDistance(input, type) <= 2
    ).slice(0, 3);
  }
  
  private levenshteinDistance(str1: string, str2: string): number {
    // Implementation of Levenshtein distance algorithm
    // ... (standard implementation)
  }
}
```

### 4. Testing Strategy

#### 4.1 Frontend Tests

```typescript
// frontend/src/services/__tests__/nodeTypeMapper.test.ts
import { NodeTypeMapper } from '../nodeTypeMapper';

describe('NodeTypeMapper', () => {
  let mapper: NodeTypeMapper;
  
  beforeEach(() => {
    mapper = new NodeTypeMapper();
  });
  
  describe('mapToBackend', () => {
    it('should map frontend trigger types to backend types', () => {
      expect(mapper.mapToBackend('rest-api-trigger')).toBe('http');
      expect(mapper.mapToBackend('mcp-function-trigger')).toBe('mcp-function');
      expect(mapper.mapToBackend('timer-trigger')).toBe('timer');
    });
    
    it('should map frontend action types to backend types', () => {
      expect(mapper.mapToBackend('javascript-action')).toBe('javascript');
      expect(mapper.mapToBackend('sql-action')).toBe('sql');
      expect(mapper.mapToBackend('redis-action')).toBe('redis');
    });
    
    it('should return original type for unknown mappings', () => {
      expect(mapper.mapToBackend('unknown-type')).toBe('unknown-type');
    });
  });
  
  describe('mapToFrontend', () => {
    it('should map backend types to frontend types', () => {
      expect(mapper.mapToFrontend('http')).toBe('rest-api-trigger');
      expect(mapper.mapToFrontend('javascript')).toBe('javascript-action');
      expect(mapper.mapToFrontend('if-else')).toBe('if-else-logic');
    });
  });
  
  describe('validateMapping', () => {
    it('should validate known frontend types', () => {
      expect(mapper.validateMapping('javascript-action')).toBe(true);
      expect(mapper.validateMapping('unknown-type')).toBe(false);
    });
  });
});
```

#### 4.2 Backend Tests

```typescript
// src/workflow/nodes/trigger/__tests__/MCPFunctionTriggerNode.test.ts
import { MCPFunctionTriggerNode } from '../MCPFunctionTriggerNode';
import { createMockLogger } from '../../../../__tests__/mocks/mockLogger';

describe('MCPFunctionTriggerNode', () => {
  let node: MCPFunctionTriggerNode;
  let mockLogger: any;
  
  beforeEach(() => {
    mockLogger = createMockLogger();
  });
  
  describe('constructor', () => {
    it('should create node with valid config', () => {
      const config = {
        functionName: 'testFunction',
        inputSchema: { type: 'object' },
        outputSchema: { type: 'object' }
      };
      
      expect(() => {
        node = new MCPFunctionTriggerNode(config, mockLogger);
      }).not.toThrow();
    });
    
    it('should throw error for missing functionName', () => {
      const config = {
        inputSchema: { type: 'object' },
        outputSchema: { type: 'object' }
      };
      
      expect(() => {
        node = new MCPFunctionTriggerNode(config as any, mockLogger);
      }).toThrow('MCP Function Trigger: functionName is required');
    });
  });
  
  describe('execute', () => {
    beforeEach(() => {
      const config = {
        functionName: 'testFunction',
        inputSchema: { type: 'object' },
        outputSchema: { type: 'object' }
      };
      node = new MCPFunctionTriggerNode(config, mockLogger);
    });
    
    it('should execute successfully with valid input', async () => {
      const input = { test: 'data' };
      const context = {
        executionId: 'test-execution',
        workflowId: 'test-workflow',
        input,
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };
      
      const result = await node.execute(input, context);
      
      expect(result).toEqual({
        functionName: 'testFunction',
        input: input,
        timestamp: expect.any(String),
        context: 'test-execution'
      });
    });
  });
});
```
