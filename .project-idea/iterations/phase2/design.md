# Phase 2: Pokročilé Workflow Funkce a <PERSON><PERSON><PERSON> (Týden 5-6)

## Overview

Phase 2 builds upon the workflow-centric foundation established in Phases 0 and 1 to introduce advanced workflow capabilities that transform the Dynamic MCP Server into a powerful workflow execution engine:

1. **Podmíněná logika a větvení**: Logic Nodes (IF/ELSE, Switch, Merge) pro řízení toku workflow
2. **Paralelní vykonávání**: Možnost spouštět uzly paralelně pro lepší výkon
3. **Datové transformace**: Pokročilé transformace dat mezi uzly workflow
4. **Integrace externích datových zdrojů**: Rozšíření stávajících uzlů o dalš<PERSON> typy (Vector DB, GraphQL, SOAP)
5. **Verzování workflow**: Podpora více verzí workflow s historií a aktivací

These enhancements will transform the server into a true runtime-configurable workflow execution engine, enabling complex business logic to be defined and executed as dynamic workflows.

## Architecture

The architecture for Phase 2 extends the workflow-centric foundation with advanced workflow capabilities:

```mermaid
graph TD
    subgraph Clients
        MCPClient["MCP Client"]
        AdminClient["Admin Client (REST)"]
    end

    subgraph "Dynamic MCP Server (Phase 2)"
        MCPCore["MCP Server Core"]

        subgraph "Workflow Engine (Enhanced)"
            WorkflowEngine["Workflow Engine"]
            ExecutionCoordinator["Execution Coordinator"]
            ParallelExecutor["Parallel Executor"]
            ConditionalProcessor["Conditional Processor"]
            DataTransformer["Data Transformer"]
        end

        subgraph "Node Types (Extended)"
            TriggerNodes["Trigger Nodes"]
            ActionNodes["Action Nodes"]
            LogicNodes["Logic Nodes (NEW)"]
            TransformNodes["Transform Nodes (NEW)"]
        end

        subgraph "Enhanced Node Implementations"
            JSNode["JavaScript Node"]
            SQLNode["SQL Node"]
            RESTNode["REST API Node"]
            RedisNode["Redis Node"]
            LiteLLMNode["LiteLLM Node"]
            VectorDBNode["Vector DB Node (NEW)"]
            GraphQLNode["GraphQL Node (NEW)"]
            SOAPNode["SOAP Node (NEW)"]
            IFNode["IF/ELSE Node (NEW)"]
            SwitchNode["Switch Node (NEW)"]
            MergeNode["Merge Node (NEW)"]
        end

        subgraph "Workflow Memory & Context"
            RedisMemory["Redis Workflow Memory"]
            ContextManager["Context Manager"]
            DataPipeline["Data Pipeline"]
        end

        subgraph "Versioning & Management"
            VersionManager["Workflow Version Manager"]
            HistoryTracker["History Tracker"]
            AdminAPI["Admin REST API"]
        end

        DB["PostgreSQL Database"]
        Redis["Redis"]
    end

    MCPClient -- "MCP Requests" --> MCPCore
    AdminClient -- "REST Requests" --> AdminAPI

    MCPCore -- "Execute Workflow" --> WorkflowEngine
    WorkflowEngine -- "Coordinate" --> ExecutionCoordinator
    ExecutionCoordinator -- "Parallel Execution" --> ParallelExecutor
    ExecutionCoordinator -- "Conditional Logic" --> ConditionalProcessor
    ExecutionCoordinator -- "Data Transform" --> DataTransformer

    WorkflowEngine -- "Load Nodes" --> ActionNodes
    WorkflowEngine -- "Process Logic" --> LogicNodes
    WorkflowEngine -- "Transform Data" --> TransformNodes

    ActionNodes -- "Memory Access" --> RedisMemory
    LogicNodes -- "Context Access" --> ContextManager
    TransformNodes -- "Pipeline Process" --> DataPipeline

    VersionManager -- "Store Versions" --> DB
    HistoryTracker -- "Track Changes" --> DB
    AdminAPI -- "CRUD Operations" --> DB

    RedisMemory -- "Store/Retrieve" --> Redis
    ContextManager -- "Manage Context" --> Redis
```

## Component Descriptions

### 1. Enhanced Workflow Engine

#### Execution Coordinator

- Orchestrates the execution of workflow nodes
- Manages execution order and dependencies
- Handles parallel execution coordination
- Implements conditional logic processing

#### Parallel Executor

- Executes multiple nodes simultaneously when possible
- Manages resource allocation for parallel tasks
- Handles synchronization of parallel branches
- Optimizes execution performance

#### Conditional Processor

- Processes IF/ELSE logic nodes
- Evaluates conditions based on workflow memory
- Routes execution flow based on conditions
- Supports complex boolean expressions

#### Data Transformer

- Handles data transformations between nodes
- Supports JSONPath expressions for data extraction
- Implements data mapping and conversion
- Provides built-in transformation functions

### 2. Logic Nodes (NEW)

#### IF/ELSE Node

- Evaluates boolean conditions
- Routes workflow execution based on condition results
- Supports multiple condition types (equals, greater than, contains, etc.)
- Allows nested conditions with AND/OR logic

#### Switch Node

- Multi-way branching based on value matching
- Supports default case handling
- Efficient for multiple condition scenarios
- Type-aware value comparison

#### Merge Node

- Combines multiple execution branches
- Waits for all parallel branches to complete
- Merges data from multiple sources
- Handles branch synchronization

### 3. Transform Nodes (NEW)

#### Data Mapping Node

- Maps data between different schemas
- Supports field renaming and restructuring
- Handles type conversions
- Provides validation of mapped data

#### Aggregation Node

- Performs data aggregation operations
- Supports sum, count, average, min, max operations
- Groups data by specified fields
- Handles large datasets efficiently

### 4. Extended Action Nodes

#### Vector DB Node

- Connects to vector databases (Pinecone, Weaviate, etc.)
- Performs similarity searches
- Handles vector embeddings
- Supports metadata filtering

#### GraphQL Node

- Executes GraphQL queries and mutations
- Handles GraphQL schema introspection
- Supports variables and fragments
- Manages GraphQL subscriptions

#### SOAP Node

- Calls SOAP web services
- Handles WSDL parsing
- Supports various authentication methods
- Manages SOAP envelope construction

### 5. Workflow Versioning System

#### Version Manager

- Manages multiple versions of workflows
- Handles version activation/deactivation
- Supports version rollback capabilities
- Maintains version metadata and history

#### History Tracker

- Tracks all changes to workflow definitions
- Records who made changes and when
- Maintains audit trail for compliance
- Supports change comparison and diff views

## Implementation Details

### Database Schema

Enhanced workflow tables and versioning support:

```sql
-- Enhanced workflow definitions table with versioning
ALTER TABLE workflow_definitions ADD COLUMN version INT NOT NULL DEFAULT 1;
ALTER TABLE workflow_definitions ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE workflow_definitions ADD COLUMN tenant_id UUID;
ALTER TABLE workflow_definitions ADD COLUMN created_by VARCHAR(255);

-- Workflow version history
CREATE TABLE workflow_version_history (
    log_id SERIAL PRIMARY KEY,
    workflow_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    version INT NOT NULL,
    definition JSONB NOT NULL,
    is_active BOOLEAN NOT NULL,
    tenant_id UUID,
    enabled BOOLEAN NOT NULL,
    changed_at TIMESTAMPTZ NOT NULL,
    changed_by VARCHAR(255),
    change_type VARCHAR(50) NOT NULL,
    FOREIGN KEY (workflow_id) REFERENCES workflow_definitions(id)
);

-- Enhanced node definitions for new node types
CREATE TABLE workflow_node_types (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50) NOT NULL, -- 'trigger', 'action', 'logic', 'transform'
    implementation_class VARCHAR(255) NOT NULL,
    config_schema JSONB NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert new node types for Phase 2
INSERT INTO workflow_node_types (id, name, category, implementation_class, config_schema, description) VALUES
(gen_random_uuid(), 'if-else', 'logic', 'IfElseNode', '{"type": "object", "properties": {"condition": {"type": "string"}, "trueNodes": {"type": "array"}, "falseNodes": {"type": "array"}}}', 'Conditional branching node'),
(gen_random_uuid(), 'switch', 'logic', 'SwitchNode', '{"type": "object", "properties": {"expression": {"type": "string"}, "cases": {"type": "object"}, "defaultCase": {"type": "array"}}}', 'Multi-way branching node'),
(gen_random_uuid(), 'merge', 'logic', 'MergeNode', '{"type": "object", "properties": {"waitForAll": {"type": "boolean"}, "mergeStrategy": {"type": "string"}}}', 'Branch merging node'),
(gen_random_uuid(), 'data-mapping', 'transform', 'DataMappingNode', '{"type": "object", "properties": {"mappings": {"type": "array"}, "validation": {"type": "object"}}}', 'Data transformation and mapping node'),
(gen_random_uuid(), 'aggregation', 'transform', 'AggregationNode', '{"type": "object", "properties": {"operations": {"type": "array"}, "groupBy": {"type": "array"}}}', 'Data aggregation node'),
(gen_random_uuid(), 'vector-db', 'action', 'VectorDBNode', '{"type": "object", "properties": {"provider": {"type": "string"}, "operation": {"type": "string"}, "config": {"type": "object"}}}', 'Vector database operations node'),
(gen_random_uuid(), 'graphql', 'action', 'GraphQLNode', '{"type": "object", "properties": {"endpoint": {"type": "string"}, "query": {"type": "string"}, "variables": {"type": "object"}}}', 'GraphQL query/mutation node'),
(gen_random_uuid(), 'soap', 'action', 'SOAPNode', '{"type": "object", "properties": {"wsdl": {"type": "string"}, "operation": {"type": "string"}, "parameters": {"type": "object"}}}', 'SOAP web service node');

-- Parallel execution tracking
CREATE TABLE workflow_parallel_executions (
    id UUID PRIMARY KEY,
    execution_id UUID NOT NULL,
    branch_id VARCHAR(100) NOT NULL,
    node_ids JSONB NOT NULL,
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    result JSONB,
    error_message TEXT,
    FOREIGN KEY (execution_id) REFERENCES workflow_executions(id)
);
```

### TypeScript Interfaces

```typescript
// Enhanced workflow interfaces for Phase 2
interface IExecutionCoordinator {
  executeWorkflow(
    workflow: WorkflowDefinition,
    context: WorkflowExecutionContext
  ): Promise<WorkflowResult>;
  executeParallel(
    nodes: WorkflowNode[],
    context: WorkflowExecutionContext
  ): Promise<ParallelExecutionResult>;
  evaluateCondition(
    condition: ConditionalExpression,
    context: WorkflowExecutionContext
  ): Promise<boolean>;
}

interface IParallelExecutor {
  executeBranches(
    branches: ExecutionBranch[],
    context: WorkflowExecutionContext
  ): Promise<BranchResult[]>;
  synchronizeBranches(branchResults: BranchResult[]): Promise<MergedResult>;
}

interface IConditionalProcessor {
  evaluateCondition(
    expression: ConditionalExpression,
    context: WorkflowExecutionContext
  ): Promise<boolean>;
  routeExecution(
    condition: boolean,
    trueNodes: WorkflowNode[],
    falseNodes: WorkflowNode[]
  ): WorkflowNode[];
}

interface IDataTransformer {
  transformData(data: any, transformation: DataTransformation): Promise<any>;
  mapFields(data: any, mapping: FieldMapping[]): Promise<any>;
  aggregateData(data: any[], operations: AggregationOperation[]): Promise<any>;
}

// Logic node interfaces
interface ILogicNode extends IWorkflowNode {
  evaluateLogic(context: WorkflowExecutionContext): Promise<LogicResult>;
}

interface IIfElseNode extends ILogicNode {
  condition: ConditionalExpression;
  trueNodes: string[];
  falseNodes: string[];
}

interface ISwitchNode extends ILogicNode {
  expression: string;
  cases: Record<string, string[]>;
  defaultCase: string[];
}

interface IMergeNode extends ILogicNode {
  waitForAll: boolean;
  mergeStrategy: 'combine' | 'override' | 'append';
}

// Transform node interfaces
interface ITransformNode extends IWorkflowNode {
  transformData(data: any, context: WorkflowExecutionContext): Promise<any>;
}

interface IDataMappingNode extends ITransformNode {
  mappings: FieldMapping[];
  validation?: ValidationSchema;
}

interface IAggregationNode extends ITransformNode {
  operations: AggregationOperation[];
  groupBy?: string[];
}

// Extended action node interfaces
interface IVectorDBNode extends IWorkflowNode {
  provider: 'pinecone' | 'weaviate' | 'qdrant';
  operation: 'search' | 'insert' | 'update' | 'delete';
  config: VectorDBConfig;
}

interface IGraphQLNode extends IWorkflowNode {
  endpoint: string;
  query: string;
  variables?: Record<string, any>;
  headers?: Record<string, string>;
}

interface ISOAPNode extends IWorkflowNode {
  wsdl: string;
  operation: string;
  parameters: Record<string, any>;
  authentication?: SOAPAuthentication;
}

// Versioning interfaces
interface IWorkflowVersionManager {
  createVersion(
    workflowId: string,
    definition: WorkflowDefinition,
    createdBy: string
  ): Promise<WorkflowVersion>;
  activateVersion(workflowId: string, version: number): Promise<void>;
  deactivateVersion(workflowId: string, version: number): Promise<void>;
  getVersionHistory(workflowId: string): Promise<WorkflowVersion[]>;
  rollbackToVersion(workflowId: string, version: number): Promise<void>;
}

interface WorkflowVersion {
  id: string;
  workflowId: string;
  version: number;
  definition: WorkflowDefinition;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  changeType: 'create' | 'update' | 'activate' | 'deactivate';
}

// Execution context enhancements
interface EnhancedWorkflowExecutionContext extends WorkflowExecutionContext {
  parallelBranches: Map<string, BranchExecutionContext>;
  conditionalResults: Map<string, boolean>;
  transformationCache: Map<string, any>;
  versionInfo: WorkflowVersionInfo;
}
```

### Directory Structure Additions

```
src/
├── core/
│   ├── interfaces/
│   │   ├── IExecutionCoordinator.ts
│   │   ├── IParallelExecutor.ts
│   │   ├── IConditionalProcessor.ts
│   │   ├── IDataTransformer.ts
│   │   ├── IWorkflowVersionManager.ts
│   │   └── ...
│   └── ...
├── workflows/
│   ├── engine/
│   │   ├── ExecutionCoordinator.ts
│   │   ├── ParallelExecutor.ts
│   │   ├── ConditionalProcessor.ts
│   │   └── DataTransformer.ts
│   ├── nodes/
│   │   ├── logic/
│   │   │   ├── IfElseNode.ts
│   │   │   ├── SwitchNode.ts
│   │   │   └── MergeNode.ts
│   │   ├── transform/
│   │   │   ├── DataMappingNode.ts
│   │   │   └── AggregationNode.ts
│   │   └── action/
│   │       ├── VectorDBNode.ts
│   │       ├── GraphQLNode.ts
│   │       └── SOAPNode.ts
│   ├── versioning/
│   │   ├── WorkflowVersionManager.ts
│   │   └── HistoryTracker.ts
│   └── ...
├── services/
│   └── ...
└── ...
```

## Data Provider Implementation

### PostgreSQL Data Provider

```typescript
class PostgresDataProvider implements IDataProvider {
  private connectionConfig: PostgresConnectionConfig;
  private pool: Pool;

  constructor(connectionConfig: PostgresConnectionConfig) {
    this.connectionConfig = connectionConfig;
    this.pool = new Pool(connectionConfig);
  }

  getType(): string {
    return 'postgres';
  }

  async query(queryConfig: PostgresQueryConfig, options?: QueryOptions): Promise<any> {
    const { sql, params } = queryConfig;

    // Validate SQL to prevent injection
    this.validateSql(sql);

    try {
      const result = await this.pool.query(sql, params);
      return result.rows;
    } catch (error) {
      throw new DataProviderError('PostgreSQL query failed', error);
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      client.release();
      return true;
    } catch (error) {
      return false;
    }
  }

  private validateSql(sql: string): void {
    // Basic validation to prevent dangerous operations
    const dangerousCommands = ['DROP', 'TRUNCATE', 'DELETE FROM', 'UPDATE', 'ALTER'];

    if (dangerousCommands.some((cmd) => sql.toUpperCase().includes(cmd))) {
      throw new DataProviderError('Potentially dangerous SQL command detected');
    }
  }
}
```

### REST API Data Provider

```typescript
class RestApiDataProvider implements IDataProvider {
  private connectionConfig: RestApiConnectionConfig;
  private httpClient: any; // Axios, node-fetch, etc.

  constructor(connectionConfig: RestApiConnectionConfig) {
    this.connectionConfig = connectionConfig;
    this.httpClient = this.createHttpClient();
  }

  getType(): string {
    return 'rest-api';
  }

  async query(queryConfig: RestApiQueryConfig, options?: QueryOptions): Promise<any> {
    const { method, path, body, headers } = queryConfig;
    const url = this.buildUrl(path);

    try {
      const response = await this.httpClient.request({
        method,
        url,
        data: body,
        headers: { ...this.connectionConfig.defaultHeaders, ...headers }
      });

      return response.data;
    } catch (error) {
      throw new DataProviderError('REST API request failed', error);
    }
  }
}
```

## Expected Outcomes

By the end of Phase 2, we will have:

1. **Advanced Workflow Capabilities**: Logic nodes (IF/ELSE, Switch, Merge) enabling conditional workflow execution
2. **Parallel Execution**: Ability to execute multiple workflow branches simultaneously for improved performance
3. **Data Transformation**: Sophisticated data mapping and aggregation capabilities between workflow nodes
4. **Extended Node Types**: New action nodes for Vector DB, GraphQL, and SOAP integrations
5. **Workflow Versioning**: Complete versioning system with history tracking and rollback capabilities

These enhancements will enable:

- **Complex Business Logic**: Workflows can now implement sophisticated conditional logic and branching
- **High Performance**: Parallel execution reduces overall workflow execution time
- **Data Processing**: Advanced data transformation capabilities for complex data pipelines
- **External Integrations**: Seamless integration with modern data sources and APIs
- **Change Management**: Robust versioning system for workflow governance and rollback capabilities
- **Scalable Architecture**: Foundation for enterprise-grade workflow execution engine

Phase 2 transforms the Dynamic MCP Server into a true runtime-configurable workflow execution engine, capable of handling complex business processes and data transformations while maintaining high performance and reliability.
