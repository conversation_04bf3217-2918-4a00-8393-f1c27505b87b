# Phase 3+: Multi-tenancy, Admin UI a Optimalizace

## Overview

Phase 3 představuje pokročilou fázi vývoje Dynamic MCP Server, která staví na robustní workflow-first architektuře implementované v předchozích fázích. Tato fáze zavádí enterprise-grade funkce pro produkční nasazení:

1. **Full Multi-tenancy**: Kompletní izolace tenantů pro všechny aspekty systému včetně workflow, dat a konfigurací
2. **Admin UI**: Webové rozhraní pro správu workflow, datov<PERSON><PERSON> zdro<PERSON><PERSON>, tenantů a monitorování
3. **Performance Optimizations**: Vylepšení výkonu pro škálovatelnost a responzivitu v produkčním prostředí
4. **Enterprise Features**: Pokročilé funkce pro enterprise nasazení (audit, security, monitoring)

Tyto funkce transformují Dynamic MCP Server do kompletní enterprise-grade platformy schopné podporovat komplexní business procesy napříč více tenantů s plnou izolací a správou.

## Architecture

Architektura pro Phase 3 rozšiřuje workflow-first architekturu z předchozích fází o enterprise komponenty:

```mermaid
graph TD
    subgraph Clients
        MCPClient["MCP Client"]
        AdminClient["Admin UI / API Client"]
    end

    subgraph "Dynamic MCP Server (Phase 3)"
        MCPCore["MCP Server Core"]
        ToolsHandler["Tools Handler"]
        PromptsHandler["Prompts Handler"]

        subgraph "Workflow System (Phases 0-2)"
            WorkflowEngine["Workflow Engine"]
            WorkflowRegistry["Workflow Registry"]
            WorkflowExecutor["Workflow Executor"]
            NodeHandlers["Node Handlers (JS, HTTP, SQL, Redis, LiteLLM, Logic)"]
        end

        DynamicLoader["Dynamic Service Loader"]
        ScriptEngine["JavaScript VM"]
        TemplateEngine["Template Engine"]

        subgraph "Data Access Layer (Phases 1-2)"
            DataProviderRegistry["Data Provider Registry"]
            DataProviders["Data Providers (SQL, Redis, Vector, GraphQL, SOAP)"]
        end

        subgraph "Multi-tenancy (NEW)"
            TenantManager["Tenant Manager"]
            TenantContext["Tenant Context"]
            TenantResolver["Tenant Resolver"]
            TenantIsolation["Tenant Isolation Layer"]
        end

        subgraph "Admin System (NEW)"
            AdminAPI["Admin REST API (Extended)"]
            AdminUI["Admin UI Server"]
            AuthService["Authentication Service"]
            UIAssets["UI Assets & Routing"]
        end

        DB["PostgreSQL Database"]
        Redis["Redis Cache & Workflow Memory"]
        ConfigWatcher["Config Watcher"]
        NotificationMgr["Notification Manager"]

        subgraph "Enhanced Monitoring (NEW)"
            MetricsCollector["Enhanced Metrics Collector"]
            HealthChecker["Health Checker"]
            PerformanceOptimizer["Performance Optimizer"]
            AlertManager["Alert Manager"]
        end
    end

    MCPClient -- "MCP Requests" --> MCPCore
    AdminClient -- "UI/API Requests" --> AdminUI
    AdminUI -- "API Calls" --> AdminAPI

    MCPCore -- "Route Request" --> ToolsHandler
    ToolsHandler -- "Execute Workflow" --> DynamicLoader

    DynamicLoader -- "Execute with Tenant Context" --> WorkflowEngine
    WorkflowEngine -- "Load Tenant-Specific Workflows" --> WorkflowRegistry
    WorkflowEngine -- "Execute with Isolation" --> WorkflowExecutor

    TenantResolver -- "Resolve from Request" --> TenantManager
    TenantManager -- "Set Context" --> TenantContext
    TenantIsolation -- "Filter Data" --> DataProviders

    MetricsCollector -- "Tenant-Specific Metrics" --> PerformanceOptimizer
    AlertManager -- "Monitor Thresholds" --> MetricsCollector
```

## Component Descriptions

### 1. Workflow System (Již implementováno v Phases 0-2)

Workflow systém je již plně implementován v předchozích fázích s následujícími komponentami:

#### Workflow Engine

- ✅ Orchestruje vykonávání workflow s podporou sekvenčního i paralelního běhu
- ✅ Spravuje workflow stav a kontext v Redis
- ✅ Implementuje error handling a retry mechanismy
- ✅ Podporuje všechny typy uzlů (JavaScript, HTTP, SQL, Redis, LiteLLM, Logic)

#### Workflow Registry

- ✅ Načítá workflow definice z databáze
- ✅ Validuje workflow konfigurace
- ✅ Poskytuje přístup k workflow podle jména/ID
- ✅ Podporuje verzování workflow s historií

#### Workflow Executor

- ✅ Vykonává jednotlivé workflow kroky
- ✅ Spravuje tok dat mezi uzly
- ✅ Implementuje podmíněnou logiku a větvení (IF/ELSE, Switch, Merge)
- ✅ Podporuje paralelní vykonávání

#### Node Handlers (Kompletní sada)

- ✅ JavaScript Node: Vykonává JavaScript kód v sandboxu
- ✅ HTTP Node: Volá externí REST API
- ✅ SQL Node: Vykonává SQL dotazy
- ✅ Redis Node: Operace s Redis databází
- ✅ LiteLLM Node: Komunikace s AI modely
- ✅ Logic Nodes: IF/ELSE, Switch, Merge pro řízení toku
- ✅ Vector DB Node: Práce s vektorovými databázemi
- ✅ GraphQL Node: GraphQL dotazy
- ✅ SOAP Node: SOAP webové služby

### 2. Multi-tenancy System (NOVÉ pro Phase 3)

Multi-tenancy systém je klíčovou novou komponentou Phase 3, která poskytuje kompletní izolaci tenantů:

#### Tenant Manager

- Spravuje konfigurace tenantů a jejich nastavení
- Vynucuje izolaci tenantů na všech úrovních systému
- Implementuje tenant-specific limity a kvóty
- Podporuje provisioning a deprovisioning tenantů
- Spravuje tenant-specific encryption keys

#### Tenant Context

- Udržuje kontext tenanta během zpracování požadavků
- Poskytuje tenant informace všem komponentám
- Zajišťuje správnou izolaci tenantů v celém systému
- Podporuje tenant-specific customizace a nastavení
- Implementuje async context propagation

#### Tenant Resolver

- Rozpoznává tenanta z informací v požadavku (header, subdomain, path)
- Podporuje různé strategie identifikace tenantů
- Implementuje tenant authentication a authorization
- Spravuje tenant sessions a caching
- Validuje tenant permissions

#### Tenant Isolation Layer

- Filtruje všechny databázové dotazy podle tenant_id
- Zajišťuje izolaci workflow a jejich execution
- Implementuje tenant-specific Redis namespacing
- Kontroluje přístup k datovým zdrojům podle tenant permissions

### 3. Admin UI (NOVÉ pro Phase 3)

Admin UI je kompletně nová webová aplikace pro správu celého systému:

#### Admin UI Server

- Poskytuje React-based single-page application
- Implementuje UI-specific routes a middleware
- Spravuje UI assets a resources (bundling, minifikace)
- Podporuje client-side state management (Redux/Zustand)
- Implementuje real-time updates přes WebSocket

#### Authentication Service

- Spravuje user authentication a sessions
- Podporuje různé authentication metody (local, OAuth, SAML)
- Implementuje role-based authorization a permissions
- Integruje s externími identity providery
- Poskytuje JWT token management

#### UI Components & Features

- **Dashboard**: System metrics, status indicators, activity feed
- **Workflow Management**: Visual workflow editor, testing, monitoring
- **Tenant Management**: Tenant provisioning, settings, resource limits
- **User Management**: User accounts, roles, permissions
- **Data Source Management**: Connection testing, credential management
- **System Settings**: Global configuration, backup/restore
- **Monitoring**: Real-time metrics, logs, alerts

### 4. Enhanced Monitoring & Optimization (Rozšířené pro Phase 3)

Rozšíření existujícího monitoring systému o enterprise-grade funkce:

#### Enhanced Metrics Collector

- ✅ Rozšiřuje existující Prometheus metrics o tenant-specific metriky
- Implementuje advanced performance tracking
- Přidává business metrics (workflow success rates, tenant usage)
- Podporuje custom metrics definované uživateli
- Implementuje metrics aggregation a retention policies

#### Health Checker

- ✅ Rozšiřuje existující health checks o tenant-specific monitoring
- Implementuje dependency health verification
- Přidává proactive health monitoring s alerting
- Podporuje custom health check definice
- Poskytuje detailed health status API s tenant isolation

#### Performance Optimizer

- Implementuje dynamic resource allocation based on metrics
- Optimalizuje cache usage s tenant-specific strategies
- Automaticky tuní database connection pools
- Implementuje intelligent query optimization
- Podporuje auto-scaling recommendations

#### Alert Manager (NOVÉ)

- Implementuje configurable alerting rules
- Podporuje multiple notification channels (email, Slack, webhook)
- Poskytuje tenant-specific alert configurations
- Implementuje alert escalation a acknowledgment
- Integruje s external monitoring systems (PagerDuty, OpsGenie)

## Implementation Details

### Multi-tenancy Database Schema (NOVÉ)

Rozšíření existujícího databázového schématu o multi-tenancy podporu:

```sql
-- Nová tabulka pro tenants
CREATE TABLE mcp_tenants (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    settings JSONB NOT NULL DEFAULT '{}',
    encryption_key_id VARCHAR(255),
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(255)
);

-- Nová tabulka pro tenant users
CREATE TABLE mcp_tenant_users (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL REFERENCES mcp_tenants(id) ON DELETE CASCADE,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255),
    roles JSONB NOT NULL DEFAULT '[]',
    enabled BOOLEAN NOT NULL DEFAULT true,
    last_login_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(tenant_id, username),
    UNIQUE(tenant_id, email)
);

-- Rozšíření existujících tabulek o tenant_id (již implementováno v předchozích fázích)
-- ALTER TABLE mcp_workflows ADD COLUMN tenant_id UUID REFERENCES mcp_tenants(id);
-- ALTER TABLE mcp_functions ADD COLUMN tenant_id UUID REFERENCES mcp_tenants(id);
-- ALTER TABLE mcp_prompts ADD COLUMN tenant_id UUID REFERENCES mcp_tenants(id);
-- ALTER TABLE mcp_data_sources ADD COLUMN tenant_id UUID REFERENCES mcp_tenants(id);

-- Indexy pro výkon
CREATE INDEX idx_mcp_tenants_name ON mcp_tenants(name);
CREATE INDEX idx_mcp_tenant_users_tenant_id ON mcp_tenant_users(tenant_id);
CREATE INDEX idx_mcp_tenant_users_username ON mcp_tenant_users(tenant_id, username);
```

### Multi-tenancy TypeScript Interfaces

```typescript
// Tenant Management
interface Tenant {
  id: string;
  name: string;
  display_name: string;
  settings: TenantSettings;
  encryption_key_id?: string;
  enabled: boolean;
  created_at: Date;
  updated_at: Date;
  created_by?: string;
}

interface TenantSettings {
  // Resource limits
  max_concurrent_workflows: number;
  max_script_execution_time: number;
  max_memory_usage: number;
  max_storage_usage: number;

  // Feature flags
  allowed_node_types: string[];
  allowed_data_sources: string[];
  enable_custom_functions: boolean;
  enable_external_apis: boolean;

  // Security settings
  require_2fa: boolean;
  session_timeout: number;
  allowed_ip_ranges?: string[];

  // Monitoring settings
  enable_detailed_logging: boolean;
  metrics_retention_days: number;
  alert_thresholds: Record<string, number>;
}

// User Management
interface TenantUser {
  id: string;
  tenant_id: string;
  username: string;
  email: string;
  roles: UserRole[];
  enabled: boolean;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

interface UserRole {
  name: string;
  permissions: Permission[];
}

interface Permission {
  resource: string; // 'workflows', 'functions', 'data_sources', etc.
  actions: string[]; // 'read', 'write', 'delete', 'execute'
  conditions?: Record<string, any>; // Additional conditions
}
```

### Service Interfaces pro Multi-tenancy

```typescript
// Core tenant management services
interface ITenantManager {
  getTenant(tenantId: string): Promise<Tenant | null>;
  getTenantByName(name: string): Promise<Tenant | null>;
  createTenant(tenant: Partial<Tenant>): Promise<Tenant>;
  updateTenant(tenantId: string, updates: Partial<Tenant>): Promise<Tenant>;
  deleteTenant(tenantId: string): Promise<boolean>;
  listTenants(pagination?: PaginationOptions): Promise<PaginatedResult<Tenant>>;
  validateTenantSettings(settings: TenantSettings): Promise<ValidationResult>;
}

interface ITenantContext {
  getCurrentTenant(): Tenant | null;
  setCurrentTenant(tenant: Tenant): void;
  clearCurrentTenant(): void;
  withTenant<T>(tenant: Tenant, fn: () => Promise<T>): Promise<T>;
  getTenantId(): string | null;
  requireTenant(): Tenant; // Throws if no tenant
}

interface ITenantResolver {
  resolveTenant(request: any): Promise<Tenant | null>;
  resolveFromHeader(headerValue: string): Promise<Tenant | null>;
  resolveFromSubdomain(hostname: string): Promise<Tenant | null>;
  resolveFromPath(path: string): Promise<Tenant | null>;
}

interface ITenantIsolation {
  filterQuery(query: any, tenantId: string): any;
  validateAccess(resource: string, action: string, tenantId: string): Promise<boolean>;
  getRedisNamespace(tenantId: string): string;
  encryptSensitiveData(data: any, tenantId: string): Promise<any>;
  decryptSensitiveData(encryptedData: any, tenantId: string): Promise<any>;
}
```

### Admin UI Architecture

Admin UI je React-based single-page application s následující architekturou:

#### Frontend Stack

- **React 18** s TypeScript pro type safety
- **Redux Toolkit** pro state management
- **React Router** pro routing a navigation
- **Material-UI** nebo **Ant Design** pro UI komponenty
- **React Query** pro API data fetching a caching
- **WebSocket** pro real-time updates

#### Key Features

- **Dashboard**: Real-time system metrics, tenant overview, activity feed
- **Workflow Management**: Visual workflow editor s drag-and-drop, testing, monitoring
- **Tenant Management**: Tenant provisioning, settings, resource monitoring
- **User Management**: User accounts, role assignment, permission management
- **Data Source Management**: Connection testing, credential management, usage monitoring
- **System Settings**: Global configuration, backup/restore, system maintenance
- **Monitoring & Alerts**: Real-time metrics, log viewer, alert configuration

## Expected Outcomes

Po dokončení Phase 3 budeme mít:

1. **Enterprise-Ready Multi-tenancy**: Kompletní izolace tenantů s podporou pro tisíce tenantů

   - Tenant-specific konfigurace a limity
   - Bezpečná izolace dat a workflow
   - Flexibilní user management s role-based permissions

2. **Professional Admin UI**: Moderní webové rozhraní pro správu celého systému

   - Intuitivní dashboard s real-time metrikami
   - Visual workflow editor pro non-technical uživatele
   - Kompletní tenant a user management

3. **Production-Grade Performance**: Optimalizace pro enterprise nasazení

   - Advanced monitoring a alerting
   - Intelligent resource optimization
   - Scalable architecture pro vysokou zátěž

4. **Complete Platform**: Plně funkční enterprise platforma
   - ✅ Workflow-first execution engine (Phases 0-2)
   - ✅ Kompletní sada node typů (Phases 1-2)
   - ✅ Advanced workflow features (Phase 2)
   - 🆕 Multi-tenancy a enterprise security
   - 🆕 Professional admin interface
   - 🆕 Production monitoring a optimization

Tyto pokročilé funkce transformují Dynamic MCP Server do kompletní enterprise-grade platformy schopné podporovat komplexní business procesy napříč více tenantů s plnou governance, škálovatelností a použitelností pro produkční nasazení.
