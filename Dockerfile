# Build stage
FROM node:20-slim AS builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy package files and install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy source code and build
COPY tsconfig.json ./
COPY src ./src
RUN yarn build

# Production stage
FROM node:20-slim AS production

WORKDIR /app

# Copy package files and install production dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile --production && yarn cache clean

# Copy built files from builder stage
COPY --from=builder /app/dist ./dist

# Expose all server ports
# Main MCP server
EXPOSE 7000
# API server
EXPOSE 7001
# Admin API server
EXPOSE 7002

# Start server
CMD ["node", "dist/index.js"]
