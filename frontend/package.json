{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "yarn tsc -b && yarn vite build", "lint": "yarn eslint .", "preview": "yarn vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@monaco-editor/react": "^4.7.0", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^8.4.0", "@reduxjs/toolkit": "^2.8.2", "@xyflow/react": "^12.6.4", "axios": "^1.9.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}