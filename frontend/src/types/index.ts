// Core types for Admin UI

export interface Tenant {
  id: string;
  name: string;
  displayName: string;
  enabled: boolean;
  settings: TenantSettings;
  encryptionKeyId?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

export interface TenantSettings {
  max_concurrent_workflows: number;
  max_script_execution_time: number;
  max_memory_usage: number;
  max_storage_usage: number;
  allowed_node_types: string[];
  allowed_data_sources: string[];
  enable_custom_functions: boolean;
  enable_external_apis: boolean;
  require_2fa: boolean;
  session_timeout: number;
  enable_detailed_logging: boolean;
  metrics_retention_days: number;
  alert_thresholds: Record<string, number>;
}

export interface TenantUser {
  id: string;
  tenantId: string;
  username: string;
  email: string;
  enabled: boolean;
  roles: UserRole[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserRole {
  name: string;
  permissions: string[];
}

export interface CreateTenantRequest {
  name: string;
  displayName: string;
  settings?: Partial<TenantSettings>;
  createdBy?: string;
}

export interface UpdateTenantRequest {
  name?: string;
  displayName?: string;
  enabled?: boolean;
  settings?: Partial<TenantSettings>;
}

export interface CreateTenantUserRequest {
  username: string;
  email: string;
  password?: string;
  passwordHash?: string;
  roles?: UserRole[];
  enabled?: boolean;
}

export interface UpdateTenantUserRequest {
  email?: string;
  enabled?: boolean;
  roles?: UserRole[];
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Authentication types
export interface LoginRequest {
  tenantName: string;
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: TenantUser;
  tenant: Tenant;
  expiresAt: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  token?: string;
  user?: TenantUser;
  tenant?: Tenant;
  loading: boolean;
  error?: string;
}

// Dashboard types
export interface DashboardStats {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  activeUsers: number;
  totalWorkflows: number;
  runningWorkflows: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  lastUpdated: string;
}

export interface TenantMetrics {
  tenantId: string;
  tenantName: string;
  userCount: number;
  workflowCount: number;
  activeWorkflows: number;
  memoryUsage: number;
  storageUsage: number;
  lastActivity: string;
}

// Workflow types (basic for display)
export interface Workflow {
  id: string;
  tenantId: string;
  name: string;
  description?: string;
  status: 'draft' | 'active' | 'paused' | 'archived';
  createdAt: string;
  updatedAt: string;
  lastRunAt?: string;
  createdBy: string;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  tenantId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  error?: string;
  triggeredBy: string;
}

// Settings validation
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// UI State types
export interface UIState {
  sidebarOpen: boolean;
  currentPage: string;
  loading: boolean;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// Table column definitions
export interface TableColumn<T = any> {
  id: keyof T;
  label: string;
  minWidth?: number;
  align?: 'left' | 'center' | 'right';
  format?: (value: any) => string;
  sortable?: boolean;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'boolean' | 'select' | 'multiselect';
  required?: boolean;
  validation?: (value: any) => string | undefined;
  options?: { value: any; label: string }[];
  placeholder?: string;
  helperText?: string;
}

// Theme types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Real-time types
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
}

export interface SystemEvent {
  id: string;
  type:
    | 'tenant_created'
    | 'tenant_updated'
    | 'user_login'
    | 'workflow_started'
    | 'workflow_completed'
    | 'system_alert';
  tenantId?: string;
  userId?: string;
  data: any;
  timestamp: string;
}

// All types are already exported above with individual export statements
