/**
 * Utility functions for transforming workflow data between database and ReactFlow formats
 */

export interface DatabaseNode {
  id: string;
  type: string;
  name: string;
  config: Record<string, any>;
  position?: { x: number; y: number };
}

export interface DatabaseEdge {
  id?: string;
  source: string;
  target: string;
}

export interface ReactFlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    type: string;
    config: Record<string, any>;
    validation: {
      isValid: boolean;
      errors: string[];
    };
  };
}

export interface ReactFlowEdge {
  id: string;
  source: string;
  target: string;
  type: string;
}

/**
 * Map database node types to ReactFlow node types
 */
function mapNodeTypeToReactFlow(dbType: string): string {
  const typeMapping: Record<string, string> = {
    // Trigger types
    'rest-api-trigger': 'trigger',
    'mcp-function-trigger': 'trigger',
    'timer-trigger': 'trigger',
    'webhook-trigger': 'trigger',

    // Action types
    javascript: 'action',
    'javascript-action': 'action',
    sql: 'action',
    'sql-action': 'action',
    redis: 'action',
    'redis-action': 'action',
    http: 'action',
    'http-action': 'action',
    litellm: 'action',
    'litellm-action': 'action',

    // Logic types
    'if-else': 'logic',
    switch: 'logic',
    merge: 'logic',

    // Terminator types
    end: 'terminator',
    return: 'terminator',
    error: 'terminator'
  };

  return typeMapping[dbType] || 'action'; // Default to action
}

/**
 * Transform database nodes to ReactFlow format
 */
export function transformNodesToReactFlow(nodes: DatabaseNode[]): ReactFlowNode[] {
  return nodes.map((node, index) => ({
    id: node.id || `node-${index}`,
    type: mapNodeTypeToReactFlow(node.type),
    position: node.position || {
      x: 100 + (index % 3) * 200, // Auto-layout in grid
      y: 100 + Math.floor(index / 3) * 150
    },
    data: {
      label: node.name || node.id || `Node ${index + 1}`,
      type: node.type, // Keep original database type in data
      config: node.config || {},
      validation: {
        isValid: true,
        errors: []
      }
    }
  }));
}

/**
 * Transform database edges to ReactFlow format
 */
export function transformEdgesToReactFlow(edges: DatabaseEdge[]): ReactFlowEdge[] {
  return edges.map((edge, index) => ({
    id: edge.id || `edge-${index}`,
    source: edge.source,
    target: edge.target,
    type: 'default'
  }));
}

/**
 * Transform ReactFlow nodes back to database format
 */
export function transformNodesFromReactFlow(nodes: ReactFlowNode[]): DatabaseNode[] {
  return nodes.map((node) => ({
    id: node.id,
    type: node.data.type,
    name: node.data.label,
    config: node.data.config,
    position: node.position
  }));
}

/**
 * Transform ReactFlow edges back to database format
 */
export function transformEdgesFromReactFlow(edges: ReactFlowEdge[]): DatabaseEdge[] {
  return edges.map((edge) => ({
    id: edge.id,
    source: edge.source,
    target: edge.target
  }));
}

/**
 * Validate workflow data structure
 */
export function validateWorkflowData(workflowData: any): boolean {
  if (!workflowData || !workflowData.nodes_config) {
    return false;
  }

  const { nodes, edges } = workflowData.nodes_config;

  // Check if nodes is an array
  if (!Array.isArray(nodes)) {
    return false;
  }

  // Check if edges is an array
  if (!Array.isArray(edges)) {
    return false;
  }

  // Validate each node has required properties
  for (const node of nodes) {
    if (!node.id || !node.type) {
      return false;
    }
  }

  // Validate each edge has required properties
  for (const edge of edges) {
    if (!edge.source || !edge.target) {
      return false;
    }
  }

  return true;
}
