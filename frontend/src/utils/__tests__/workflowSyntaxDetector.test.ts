import { detectOldSyntax, getOldSyntaxPatterns } from '../workflowSyntaxDetector';

describe('workflowSyntaxDetector', () => {
  describe('detectOldSyntax', () => {
    test('should detect old context.input syntax', () => {
      const workflowConfig = {
        nodes: [
          {
            type: 'javascript-action',
            config: {
              script: 'const result = context.input.message.toUpperCase();'
            }
          }
        ]
      };

      expect(detectOldSyntax(workflowConfig)).toBe(true);
    });

    test('should detect old context.variables syntax', () => {
      const workflowConfig = {
        nodes: [
          {
            type: 'javascript-action',
            config: {
              script: 'context.variables.processedAt = new Date().toISOString();'
            }
          }
        ]
      };

      expect(detectOldSyntax(workflowConfig)).toBe(true);
    });

    test('should not detect new syntax as old', () => {
      const workflowConfig = {
        nodes: [
          {
            type: 'javascript-action',
            config: {
              script:
                'const result = input.message.toUpperCase();\nworkflowContext.variables.processedAt = new Date().toISOString();'
            }
          }
        ]
      };

      expect(detectOldSyntax(workflowConfig)).toBe(false);
    });

    test('should handle JSON string input', () => {
      const workflowConfigString = JSON.stringify({
        nodes: [
          {
            type: 'javascript-action',
            config: {
              script: 'context.input.message'
            }
          }
        ]
      });

      expect(detectOldSyntax(workflowConfigString)).toBe(true);
    });

    test('should handle empty or invalid config gracefully', () => {
      expect(detectOldSyntax(null)).toBe(false);
      expect(detectOldSyntax(undefined)).toBe(false);
      expect(detectOldSyntax({})).toBe(false);
      expect(detectOldSyntax('invalid json')).toBe(false);
    });

    test('should detect old syntax in condition fields', () => {
      const workflowConfig = {
        nodes: [
          {
            type: 'if-else-logic',
            config: {
              condition: 'context.input.status === "active"'
            }
          }
        ]
      };

      expect(detectOldSyntax(workflowConfig)).toBe(true);
    });
  });

  describe('getOldSyntaxPatterns', () => {
    test('should return found patterns', () => {
      const workflowConfig = {
        nodes: [
          {
            type: 'javascript-action',
            config: {
              script: 'context.input.message; context.variables.test; context.executionId;'
            }
          }
        ]
      };

      const patterns = getOldSyntaxPatterns(workflowConfig);
      expect(patterns).toContain('context.input.*');
      expect(patterns).toContain('context.variables.*');
      expect(patterns).toContain('context.executionId');
    });

    test('should return empty array for new syntax', () => {
      const workflowConfig = {
        nodes: [
          {
            type: 'javascript-action',
            config: {
              script: 'input.message; workflowContext.variables.test;'
            }
          }
        ]
      };

      const patterns = getOldSyntaxPatterns(workflowConfig);
      expect(patterns).toEqual([]);
    });
  });
});
