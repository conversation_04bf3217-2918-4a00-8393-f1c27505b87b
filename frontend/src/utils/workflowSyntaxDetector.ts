/**
 * Utility to detect old JavaScript syntax in workflow configurations
 */

interface WorkflowNode {
  type: string;
  config?: {
    script?: string;
    condition?: string;
    [key: string]: unknown;
  };
}

interface WorkflowConfig {
  nodes?: WorkflowNode[];
  [key: string]: unknown;
}

/**
 * Detects if a workflow contains old JavaScript syntax that needs updating
 */
export function detectOldSyntax(nodesConfig: unknown): boolean {
  try {
    let config: WorkflowConfig;

    // Handle string input (parse JSON)
    if (typeof nodesConfig === 'string') {
      config = JSON.parse(nodesConfig) as WorkflowConfig;
    } else {
      config = nodesConfig as WorkflowConfig;
    }

    // Check if nodes array exists
    if (!config?.nodes || !Array.isArray(config.nodes)) {
      return false;
    }

    // Check each node for old syntax
    for (const node of config.nodes) {
      if (hasOldSyntaxInNode(node)) {
        return true;
      }
    }

    return false;
  } catch (error) {
    // If we can't parse the config, assume it's fine
    console.warn('Error parsing workflow config for syntax detection:', error);
    return false;
  }
}

/**
 * Checks if a single node contains old syntax
 */
function hasOldSyntaxInNode(node: WorkflowNode): boolean {
  if (!node.config) {
    return false;
  }

  // Check script content in JavaScript action nodes
  if (node.type === 'javascript-action' && node.config.script) {
    return hasOldSyntaxInScript(node.config.script);
  }

  // Check condition content in logic nodes
  if (node.config.condition && typeof node.config.condition === 'string') {
    return hasOldSyntaxInScript(node.config.condition);
  }

  // Check any other string properties that might contain JavaScript
  for (const [, value] of Object.entries(node.config)) {
    if (typeof value === 'string' && hasOldSyntaxInScript(value)) {
      return true;
    }
  }

  return false;
}

/**
 * Checks if a script string contains old syntax patterns
 */
function hasOldSyntaxInScript(script: string): boolean {
  // Patterns that indicate old syntax
  const oldSyntaxPatterns = [
    /context\.input\./, // context.input.something
    /context\.variables\./, // context.variables.something
    /context\.executionId/, // context.executionId
    /context\.workflowId/, // context.workflowId
    /context\.tenant/ // context.tenant
  ];

  return oldSyntaxPatterns.some((pattern) => pattern.test(script));
}

/**
 * Gets a list of problematic patterns found in the workflow
 */
export function getOldSyntaxPatterns(nodesConfig: unknown): string[] {
  const patterns: string[] = [];

  try {
    let config: WorkflowConfig;

    if (typeof nodesConfig === 'string') {
      config = JSON.parse(nodesConfig) as WorkflowConfig;
    } else {
      config = nodesConfig as WorkflowConfig;
    }

    if (!config?.nodes || !Array.isArray(config.nodes)) {
      return patterns;
    }

    for (const node of config.nodes) {
      if (node.config?.script) {
        const foundPatterns = findPatternsInScript(node.config.script);
        patterns.push(...foundPatterns);
      }
    }

    // Remove duplicates
    return [...new Set(patterns)];
  } catch {
    return patterns;
  }
}

/**
 * Finds specific old syntax patterns in a script
 */
function findPatternsInScript(script: string): string[] {
  const patterns: string[] = [];

  const checks = [
    { pattern: /context\.input\.\w+/g, example: 'context.input.*' },
    { pattern: /context\.variables\.\w+/g, example: 'context.variables.*' },
    { pattern: /context\.executionId/g, example: 'context.executionId' },
    { pattern: /context\.workflowId/g, example: 'context.workflowId' }
  ];

  for (const check of checks) {
    if (check.pattern.test(script)) {
      patterns.push(check.example);
    }
  }

  return patterns;
}
