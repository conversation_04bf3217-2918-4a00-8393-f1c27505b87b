import { NodeTemplateService } from '../NodeTemplateService';

describe('NodeTemplateService', () => {
  test('should generate unique IDs', () => {
    const id1 = NodeTemplateService.generateUniqueId('test');
    const id2 = NodeTemplateService.generateUniqueId('test');

    expect(id1).not.toBe(id2);
    expect(id1).toMatch(/^test-\d+-[a-z0-9]+$/);
    expect(id2).toMatch(/^test-\d+-[a-z0-9]+$/);
  });

  test('should return all template categories', () => {
    const templates = NodeTemplateService.getAllTemplates();

    expect(Object.keys(templates)).toEqual(['trigger', 'action', 'logic', 'terminator']);
    expect(templates.trigger).toHaveLength(4);
    expect(templates.action).toHaveLength(5);
    expect(templates.logic).toHaveLength(4);
    expect(templates.terminator).toHaveLength(3);
  });

  test('should get templates by category', () => {
    const triggerTemplates = NodeTemplateService.getTemplatesByCategory('trigger');

    expect(triggerTemplates).toHaveLength(4);
    expect(triggerTemplates[0].category).toBe('trigger');
    expect(triggerTemplates.every((t) => t.category === 'trigger')).toBe(true);
  });

  test('should get template by type', () => {
    const template = NodeTemplateService.getTemplateByType('javascript-action');

    expect(template).not.toBeNull();
    expect(template?.type).toBe('javascript-action');
    expect(template?.name).toBe('JavaScript Action');
    expect(template?.category).toBe('action');
  });

  test('should return null for non-existent template type', () => {
    const template = NodeTemplateService.getTemplateByType('non-existent-type');

    expect(template).toBeNull();
  });

  test('should create node from template', () => {
    const node = NodeTemplateService.createNodeFromTemplate('http-action');

    expect(node).not.toBeNull();
    expect(node?.type).toBe('http-action');
    expect(node?.name).toBe('HTTP Request');
    expect(node?.id).toMatch(/^httpaction-\d+-[a-z0-9]+$/);
    expect(node?.position).toEqual({ x: 100, y: 100 });
    expect(node?.config).toBeDefined();
  });

  test('should create node with custom ID', () => {
    const customId = 'my-custom-id';
    const node = NodeTemplateService.createNodeFromTemplate('sql-action', customId);

    expect(node?.id).toBe(customId);
  });

  test('should return flat template list', () => {
    const flatList = NodeTemplateService.getFlatTemplateList();

    expect(flatList).toHaveLength(16); // 4 + 5 + 4 + 3
    expect(flatList[0]).toHaveProperty('value');
    expect(flatList[0]).toHaveProperty('label');
    expect(flatList[0]).toHaveProperty('category');
    expect(flatList[0]).toHaveProperty('description');
  });

  test('should have proper template structure for javascript-action', () => {
    const template = NodeTemplateService.getTemplateByType('javascript-action');

    expect(template?.config).toHaveProperty('script');
    expect(template?.config).toHaveProperty('timeout');
    expect(template?.config).toHaveProperty('enableConsole');
    expect(template?.config).toHaveProperty('allowAsync');
    expect(template?.config.script).toContain('input');
  });

  test('should have proper template structure for http-action', () => {
    const template = NodeTemplateService.getTemplateByType('http-action');

    expect(template?.config).toHaveProperty('url');
    expect(template?.config).toHaveProperty('method');
    expect(template?.config).toHaveProperty('headers');
    expect(template?.config).toHaveProperty('body');
    expect(template?.config).toHaveProperty('timeout');
    expect(template?.config).toHaveProperty('retryCount');
  });

  test('should have proper template structure for conditional logic', () => {
    const template = NodeTemplateService.getTemplateByType('if-else-logic');

    expect(template?.config).toHaveProperty('condition');
    expect(template?.config).toHaveProperty('trueOutput');
    expect(template?.config).toHaveProperty('falseOutput');
    expect(template?.config).toHaveProperty('conditionType');
  });
});
