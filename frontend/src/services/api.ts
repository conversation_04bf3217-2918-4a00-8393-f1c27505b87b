import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { API_BASE_URL } from '../config/runtime';
import {
  Tenant,
  TenantUser,
  CreateTenantRequest,
  UpdateTenantRequest,
  CreateTenantUserRequest,
  UpdateTenantUserRequest,
  PaginationParams,
  PaginatedResponse,
  ApiResponse,
  LoginRequest,
  LoginResponse,
  DashboardStats,
  TenantMetrics,
  ValidationResult,
  TenantSettings
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;

    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_data');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response: AxiosResponse<ApiResponse<LoginResponse>> = await this.api.post(
      '/auth/login',
      credentials
    );
    return response.data.data!;
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  }

  async validateToken(): Promise<boolean> {
    try {
      await this.api.get('/auth/validate');
      return true;
    } catch {
      return false;
    }
  }

  // Tenant management endpoints
  async getTenants(params?: PaginationParams): Promise<PaginatedResponse<Tenant>> {
    const response: AxiosResponse<ApiResponse<PaginatedResponse<Tenant>>> = await this.api.get(
      '/admin/tenants',
      {
        params
      }
    );
    return response.data.data!;
  }

  async getTenant(id: string): Promise<Tenant> {
    const response: AxiosResponse<ApiResponse<Tenant>> = await this.api.get(`/admin/tenants/${id}`);
    return response.data.data!;
  }

  async createTenant(data: CreateTenantRequest): Promise<Tenant> {
    const response: AxiosResponse<ApiResponse<Tenant>> = await this.api.post(
      '/admin/tenants',
      data
    );
    return response.data.data!;
  }

  async updateTenant(id: string, data: UpdateTenantRequest): Promise<Tenant> {
    const response: AxiosResponse<ApiResponse<Tenant>> = await this.api.put(
      `/admin/tenants/${id}`,
      data
    );
    return response.data.data!;
  }

  async deleteTenant(id: string): Promise<void> {
    await this.api.delete(`/admin/tenants/${id}`);
  }

  async validateTenantSettings(settings: TenantSettings): Promise<ValidationResult> {
    const response: AxiosResponse<ApiResponse<ValidationResult>> = await this.api.post(
      '/admin/tenants/validate-settings',
      settings
    );
    return response.data.data!;
  }

  // Tenant user management endpoints
  async getTenantUsers(
    tenantId: string,
    params?: PaginationParams
  ): Promise<PaginatedResponse<TenantUser>> {
    const response: AxiosResponse<ApiResponse<PaginatedResponse<TenantUser>>> = await this.api.get(
      `/admin/tenants/${tenantId}/users`,
      {
        params
      }
    );
    return response.data.data!;
  }

  async getTenantUser(tenantId: string, userId: string): Promise<TenantUser> {
    const response: AxiosResponse<ApiResponse<TenantUser>> = await this.api.get(
      `/admin/tenants/${tenantId}/users/${userId}`
    );
    return response.data.data!;
  }

  async createTenantUser(tenantId: string, data: CreateTenantUserRequest): Promise<TenantUser> {
    const response: AxiosResponse<ApiResponse<TenantUser>> = await this.api.post(
      `/admin/tenants/${tenantId}/users`,
      data
    );
    return response.data.data!;
  }

  async updateTenantUser(
    tenantId: string,
    userId: string,
    data: UpdateTenantUserRequest
  ): Promise<TenantUser> {
    const response: AxiosResponse<ApiResponse<TenantUser>> = await this.api.put(
      `/admin/tenants/${tenantId}/users/${userId}`,
      data
    );
    return response.data.data!;
  }

  async deleteTenantUser(tenantId: string, userId: string): Promise<void> {
    await this.api.delete(`/admin/tenants/${tenantId}/users/${userId}`);
  }

  // Dashboard and metrics endpoints
  async getDashboardStats(): Promise<DashboardStats> {
    const response: AxiosResponse<ApiResponse<DashboardStats>> =
      await this.api.get('/admin/dashboard/stats');
    return response.data.data!;
  }

  async getTenantMetrics(tenantId?: string): Promise<TenantMetrics[]> {
    const response: AxiosResponse<ApiResponse<TenantMetrics[]>> = await this.api.get(
      '/admin/dashboard/tenant-metrics',
      {
        params: tenantId ? { tenantId } : undefined
      }
    );
    return response.data.data!;
  }

  // System health endpoints
  async getSystemHealth(): Promise<{ status: string; details: any }> {
    const response: AxiosResponse<ApiResponse<{ status: string; details: any }>> =
      await this.api.get('/admin/system/health');
    return response.data.data!;
  }

  // Workflow management endpoints
  async getWorkflows(): Promise<any[]> {
    const response = await this.api.get('/api/workflows');
    return response.data.data || response.data || [];
  }

  async getWorkflow(id: string): Promise<any> {
    const response = await this.api.get(`/api/workflows/${id}`);
    return response.data;
  }

  async createWorkflow(data: any): Promise<any> {
    const response = await this.api.post('/api/workflows', data);
    return response.data;
  }

  async updateWorkflow(id: string, data: any): Promise<any> {
    const response = await this.api.put(`/api/workflows/${id}`, data);
    return response.data;
  }

  async deleteWorkflow(id: string): Promise<void> {
    await this.api.delete(`/api/workflows/${id}`);
  }

  async testWorkflow(workflow: any, input: any): Promise<any> {
    const response = await this.api.post('/api/workflows/test', { workflow, input });
    return response.data;
  }

  async getWorkflowExecutions(id: string): Promise<any[]> {
    const response = await this.api.get(`/api/workflows/${id}/executions`);
    return response.data;
  }

  async getWorkflowMetrics(id: string): Promise<any> {
    const response = await this.api.get(`/api/workflows/${id}/metrics`);
    return response.data;
  }

  // MCP Function management endpoints
  async getFunctions(): Promise<any[]> {
    const response = await this.api.get('/api/functions');
    return response.data.data || response.data || [];
  }

  async getFunction(id: string): Promise<any> {
    const response = await this.api.get(`/api/functions/${id}`);
    return response.data;
  }

  async createFunction(data: any): Promise<any> {
    const response = await this.api.post('/api/functions', data);
    return response.data;
  }

  async updateFunction(id: string, data: any): Promise<any> {
    const response = await this.api.put(`/api/functions/${id}`, data);
    return response.data;
  }

  async deleteFunction(id: string): Promise<void> {
    await this.api.delete(`/api/functions/${id}`);
  }

  async testFunction(func: any, input: any): Promise<any> {
    const response = await this.api.post('/api/functions/test', { function: func, input });
    return response.data;
  }

  // Utility methods
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token);
  }

  getAuthToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  clearAuthToken(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_data');
  }

  // Error handling helper
  handleApiError(error: any): string {
    if (error.response?.data?.error) {
      return error.response.data.error;
    }
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  }

  // File upload helper (for future use)
  async uploadFile(file: File, endpoint: string): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.api.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    return response.data;
  }

  // Generic GET request
  async get<T>(endpoint: string, params?: any): Promise<T> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.get(endpoint, { params });
    return response.data.data!;
  }

  // Generic POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.post(endpoint, data);
    return response.data.data!;
  }

  // Generic PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response: AxiosResponse<ApiResponse<T>> = await this.api.put(endpoint, data);
    return response.data.data!;
  }

  // Generic DELETE request
  async delete(endpoint: string): Promise<void> {
    await this.api.delete(endpoint);
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;
