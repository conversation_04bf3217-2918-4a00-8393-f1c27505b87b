/**
 * Node Template Service
 * Provides predefined templates for all supported workflow node types
 */

export interface NodeTemplate {
  id: string;
  name: string;
  type: string;
  category: 'trigger' | 'action' | 'logic' | 'terminator';
  description: string;
  config: Record<string, unknown>;
  position?: { x: number; y: number };
}

export class NodeTemplateService {
  /**
   * Generate a unique ID using timestamp
   */
  static generateUniqueId(prefix: string = 'node'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all available node templates organized by category
   */
  static getAllTemplates(): { [category: string]: NodeTemplate[] } {
    return {
      trigger: this.getTriggerTemplates(),
      action: this.getActionTemplates(),
      logic: this.getLogicTemplates(),
      terminator: this.getTerminatorTemplates()
    };
  }

  /**
   * Get templates for a specific category
   */
  static getTemplatesByCategory(category: string): NodeTemplate[] {
    const templates = this.getAllTemplates();
    return templates[category] || [];
  }

  /**
   * Get a specific template by type
   */
  static getTemplateByType(type: string): NodeTemplate | null {
    const allTemplates = this.getAllTemplates();
    for (const category of Object.values(allTemplates)) {
      const template = category.find((t) => t.type === type);
      if (template) return template;
    }
    return null;
  }

  /**
   * Create a new node instance from template
   */
  static createNodeFromTemplate(templateType: string, customId?: string): NodeTemplate | null {
    const template = this.getTemplateByType(templateType);
    if (!template) return null;

    return {
      ...template,
      id: customId || this.generateUniqueId(template.type.replace('-', '')),
      position: { x: 100, y: 100 } // Default position
    };
  }

  /**
   * Trigger node templates
   */
  private static getTriggerTemplates(): NodeTemplate[] {
    return [
      {
        id: 'rest-api-trigger-template',
        name: 'REST API Trigger',
        type: 'rest-api-trigger',
        category: 'trigger',
        description: 'Exposes workflow as a REST API endpoint',
        config: {
          method: 'POST',
          path: '/api/workflow-endpoint',
          authentication: true,
          validation: true
        }
      },
      {
        id: 'mcp-function-trigger-template',
        name: 'MCP Function Trigger',
        type: 'mcp-function-trigger',
        category: 'trigger',
        description: 'Triggers via MCP function calls',
        config: {
          functionName: 'processData',
          description: 'Process input data via MCP'
        }
      },
      {
        id: 'timer-trigger-template',
        name: 'Timer Trigger',
        type: 'timer-trigger',
        category: 'trigger',
        description: 'Time-based scheduling trigger',
        config: {
          schedule: '0 */5 * * * *', // Every 5 minutes
          timezone: 'UTC',
          enabled: true
        }
      },
      {
        id: 'webhook-trigger-template',
        name: 'Webhook Trigger',
        type: 'webhook-trigger',
        category: 'trigger',
        description: 'External webhook endpoints',
        config: {
          path: '/webhook/external-service',
          method: 'POST',
          secretValidation: true
        }
      }
    ];
  }

  /**
   * Action node templates
   */
  private static getActionTemplates(): NodeTemplate[] {
    return [
      {
        id: 'javascript-action-template',
        name: 'JavaScript Action',
        type: 'javascript-action',
        category: 'action',
        description: 'Execute custom JavaScript code',
        config: {
          script: `// Process input data
const result = input.value * 2;

// Set workflow variables
workflowContext.variables.processedAt = new Date().toISOString();

// Return result
return {
  processed: true,
  result: result,
  timestamp: Date.now()
};`,
          timeout: 30000,
          enableConsole: true,
          allowAsync: true
        }
      },
      {
        id: 'http-action-template',
        name: 'HTTP Request',
        type: 'http-action',
        category: 'action',
        description: 'Make HTTP requests to external APIs',
        config: {
          url: 'https://api.example.com/data',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ${workflowContext.variables.apiToken}'
          },
          body: {
            data: '${input.payload}',
            timestamp: '${workflowContext.variables.timestamp}'
          },
          timeout: 30000,
          retryCount: 3
        }
      },
      {
        id: 'sql-action-template',
        name: 'SQL Database',
        type: 'sql-action',
        category: 'action',
        description: 'Execute SQL database operations',
        config: {
          query: 'SELECT * FROM users WHERE id = $1 AND status = $2',
          parameters: ['${input.userId}', 'active'],
          queryType: 'SELECT',
          timeout: 30000
        }
      },
      {
        id: 'redis-action-template',
        name: 'Redis Cache',
        type: 'redis-action',
        category: 'action',
        description: 'Redis caching operations',
        config: {
          operation: 'SET',
          key: 'user:${input.userId}:data',
          value: '${input.userData}',
          expiration: 3600 // 1 hour
        }
      },
      {
        id: 'litellm-action-template',
        name: 'LLM Integration',
        type: 'litellm-action',
        category: 'action',
        description: 'Large Language Model integration',
        config: {
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that processes user data.'
            },
            {
              role: 'user',
              content: 'Please analyze this data: ${input.text}'
            }
          ],
          temperature: 0.7,
          maxTokens: 1000
        }
      }
    ];
  }

  /**
   * Logic node templates
   */
  private static getLogicTemplates(): NodeTemplate[] {
    return [
      {
        id: 'if-else-logic-template',
        name: 'If-Else Condition',
        type: 'if-else-logic',
        category: 'logic',
        description: 'Conditional branching logic',
        config: {
          condition: "input.status === 'active' && input.score > 50",
          trueOutput: {
            result: 'passed',
            qualified: true
          },
          falseOutput: {
            result: 'failed',
            qualified: false
          },
          conditionType: 'expression'
        }
      },
      {
        id: 'switch-logic-template',
        name: 'Switch Logic',
        type: 'switch-logic',
        category: 'logic',
        description: 'Multi-branch switching',
        config: {
          switchValue: '${input.type}',
          cases: {
            user: { action: 'process_user', priority: 'high' },
            order: { action: 'process_order', priority: 'medium' },
            system: { action: 'process_system', priority: 'low' }
          },
          defaultCase: { action: 'process_default', priority: 'low' }
        }
      },
      {
        id: 'merge-logic-template',
        name: 'Data Merger',
        type: 'merge-logic',
        category: 'logic',
        description: 'Merge data from multiple sources',
        config: {
          mergeStrategy: 'deep',
          sources: ['${workflowContext.nodeResults.node1}', '${workflowContext.nodeResults.node2}'],
          conflictResolution: 'last_wins'
        }
      },
      {
        id: 'loop-logic-template',
        name: 'Loop Iterator',
        type: 'loop-logic',
        category: 'logic',
        description: 'Iterate over data collections',
        config: {
          iterable: '${input.items}',
          itemVariable: 'currentItem',
          indexVariable: 'currentIndex',
          maxIterations: 100,
          collectResults: true
        }
      }
    ];
  }

  /**
   * Terminator node templates
   */
  private static getTerminatorTemplates(): NodeTemplate[] {
    return [
      {
        id: 'response-terminator-template',
        name: 'Standard Response',
        type: 'response-terminator',
        category: 'terminator',
        description: 'Format and return standard response',
        config: {
          body: {
            success: true,
            data: '${input}',
            metadata: {
              workflowId: '${workflowId}',
              executionId: '${executionId}',
              timestamp: '${timestamp}'
            }
          },
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      },
      {
        id: 'mcp-response-terminator-template',
        name: 'MCP Response',
        type: 'mcp-response-terminator',
        category: 'terminator',
        description: 'Format response for MCP protocol',
        config: {
          responseFormat: 'mcp',
          includeMetadata: true,
          dataTransformation: {
            result: '${input.result}',
            status: '${input.status}',
            executionTime: '${workflowContext.variables.executionTime}'
          }
        }
      },
      {
        id: 'error-terminator-template',
        name: 'Error Handler',
        type: 'error-terminator',
        category: 'terminator',
        description: 'Handle and format error responses',
        config: {
          errorTemplate: {
            error: true,
            message: '${error.message}',
            code: '${error.code}',
            timestamp: '${workflowContext.variables.timestamp}',
            workflowId: '${workflowContext.workflowId}'
          },
          statusCode: 500,
          logError: true
        }
      }
    ];
  }

  /**
   * Get a flat list of all templates for dropdown
   */
  static getFlatTemplateList(): Array<{
    value: string;
    label: string;
    category: string;
    description: string;
  }> {
    const allTemplates = this.getAllTemplates();
    const flatList: Array<{
      value: string;
      label: string;
      category: string;
      description: string;
    }> = [];

    Object.entries(allTemplates).forEach(([category, templates]) => {
      templates.forEach((template) => {
        flatList.push({
          value: template.type,
          label: template.name,
          category: category,
          description: template.description
        });
      });
    });

    return flatList;
  }
}

export default NodeTemplateService;
