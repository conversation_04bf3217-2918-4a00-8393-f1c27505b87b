import { apiClient } from './client';

export interface WorkflowExecuteRequest {
  input: any;
}

export interface WorkflowExecuteResponse {
  success: boolean;
  data: any;
  message: string;
}

export interface WorkflowTriggerResponse {
  [key: string]: any;
}

export interface WorkflowMetrics {
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  errorRate: number;
  lastExecution?: string;
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: 'RUNNING' | 'COMPLETED' | 'FAILED';
  input_data: any;
  output_data?: any;
  error_message?: string;
  started_at: string;
  completed_at?: string;
  execution_time_ms?: number;
}

/**
 * Workflow API service
 */
export const workflowApi = {
  /**
   * Execute a workflow with input data (admin endpoint)
   */
  async execute(workflowId: string, request: WorkflowExecuteRequest): Promise<WorkflowExecuteResponse> {
    const response = await apiClient.post(`/workflows/${workflowId}/execute`, request);
    return response.data;
  },

  /**
   * Trigger a workflow via public endpoint
   */
  async trigger(workflowId: string, payload: any): Promise<WorkflowTriggerResponse> {
    const response = await apiClient.post(`/workflows/${workflowId}/trigger`, payload);
    return response.data;
  },

  /**
   * Test a workflow without saving it
   */
  async test(workflow: any, input: any): Promise<any> {
    const response = await apiClient.post('/workflows/test', { workflow, input });
    return response.data;
  },

  /**
   * Get workflow execution history
   */
  async getExecutionHistory(workflowId: string): Promise<WorkflowExecution[]> {
    const response = await apiClient.get(`/workflows/${workflowId}/executions`);
    return response.data;
  },

  /**
   * Get workflow metrics
   */
  async getMetrics(workflowId: string, timeRange?: { startTime: string; endTime: string }): Promise<WorkflowMetrics> {
    const params = timeRange ? {
      startTime: timeRange.startTime,
      endTime: timeRange.endTime
    } : {};
    
    const response = await apiClient.get(`/workflows/${workflowId}/metrics`, { params });
    return response.data;
  },

  /**
   * Get all workflows
   */
  async getAll(): Promise<any[]> {
    const response = await apiClient.get('/workflows');
    return response.data;
  },

  /**
   * Get workflow by ID
   */
  async getById(workflowId: string): Promise<any> {
    const response = await apiClient.get(`/workflows/${workflowId}`);
    return response.data;
  },

  /**
   * Create a new workflow
   */
  async create(workflow: any): Promise<any> {
    const response = await apiClient.post('/workflows', workflow);
    return response.data;
  },

  /**
   * Update a workflow
   */
  async update(workflowId: string, workflow: any): Promise<any> {
    const response = await apiClient.put(`/workflows/${workflowId}`, workflow);
    return response.data;
  },

  /**
   * Delete a workflow
   */
  async delete(workflowId: string): Promise<void> {
    await apiClient.delete(`/workflows/${workflowId}`);
  }
};
