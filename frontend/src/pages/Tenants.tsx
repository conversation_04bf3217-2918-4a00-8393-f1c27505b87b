import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Menu,
  MenuItem,
  ListItemIcon
} from '@mui/material';
// Using simple text icons instead of @mui/icons-material
const AddIcon = () => <span>➕</span>;
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const MoreVertIcon = () => <span>⋮</span>;
const BusinessIcon = () => <span>🏢</span>;
const PeopleIcon = () => <span>👥</span>;
const SettingsIcon = () => <span>⚙️</span>;
import { Tenant, CreateTenantRequest } from '../types';
import apiService from '../services/api';

const Tenants: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);

  // Menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuTenant, setMenuTenant] = useState<Tenant | null>(null);

  // Form state
  const [formData, setFormData] = useState<CreateTenantRequest>({
    name: '',
    displayName: ''
  });

  useEffect(() => {
    loadTenants();
  }, [page, rowsPerPage]);

  const loadTenants = async () => {
    try {
      setLoading(true);

      // Load real data from API
      const response = await apiService.getTenants({
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage
      });

      setTenants(response.data);
      setTotal(response.total);
    } catch (err) {
      console.error('Failed to load tenants:', err);
      setError('Failed to load tenants');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, tenant: Tenant) => {
    setAnchorEl(event.currentTarget);
    setMenuTenant(tenant);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuTenant(null);
  };

  const handleCreateTenant = () => {
    setFormData({ name: '', displayName: '' });
    setCreateDialogOpen(true);
  };

  const handleEditTenant = (tenant: Tenant) => {
    setSelectedTenant(tenant);
    setFormData({
      name: tenant.name,
      displayName: tenant.displayName
    });
    setEditDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteTenant = async (tenant: Tenant) => {
    if (window.confirm(`Are you sure you want to delete tenant "${tenant.displayName}"?`)) {
      try {
        await apiService.deleteTenant(tenant.id);

        // Reload tenants after deletion
        await loadTenants();
      } catch (err) {
        console.error('Failed to delete tenant:', err);
        setError('Failed to delete tenant');
      }
    }
    handleMenuClose();
  };

  const handleFormSubmit = async () => {
    try {
      if (editDialogOpen && selectedTenant) {
        // Update existing tenant
        await apiService.updateTenant(selectedTenant.id, {
          name: formData.name,
          displayName: formData.displayName
        });
        setEditDialogOpen(false);
      } else {
        // Create new tenant
        await apiService.createTenant({
          name: formData.name,
          displayName: formData.displayName
        });
        setCreateDialogOpen(false);
      }

      // Reload tenants after create/update
      await loadTenants();
      setFormData({ name: '', displayName: '' });
    } catch (err) {
      console.error('Failed to save tenant:', err);
      setError('Failed to save tenant');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && tenants.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Tenants
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage multi-tenant organizations and their settings
          </Typography>
        </Box>
        <Button variant="contained" startIcon={<AddIcon />} onClick={handleCreateTenant}>
          Create Tenant
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Display Name</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Workflows</TableCell>
                <TableCell>Users</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {tenants.map((tenant) => (
                <TableRow key={tenant.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BusinessIcon />
                      <Typography variant="body2" fontFamily="monospace">
                        {tenant.name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{tenant.displayName}</TableCell>
                  <TableCell>
                    <Chip
                      label={tenant.enabled ? 'Active' : 'Disabled'}
                      color={tenant.enabled ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{formatDate(tenant.createdAt)}</TableCell>
                  <TableCell>
                    <Chip label="12" size="small" variant="outlined" />
                  </TableCell>
                  <TableCell>
                    <Chip label="8" size="small" variant="outlined" />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton onClick={(e) => handleMenuOpen(e, tenant)} size="small">
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={total}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Actions Menu */}
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
        <MenuItem onClick={() => menuTenant && handleEditTenant(menuTenant)}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          Edit
        </MenuItem>
        <MenuItem onClick={() => console.log('View users for', menuTenant?.name)}>
          <ListItemIcon>
            <PeopleIcon />
          </ListItemIcon>
          Manage Users
        </MenuItem>
        <MenuItem onClick={() => console.log('View settings for', menuTenant?.name)}>
          <ListItemIcon>
            <SettingsIcon />
          </ListItemIcon>
          Settings
        </MenuItem>
        <MenuItem
          onClick={() => menuTenant && handleDeleteTenant(menuTenant)}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          Delete
        </MenuItem>
      </Menu>

      {/* Create/Edit Dialog */}
      <Dialog
        open={createDialogOpen || editDialogOpen}
        onClose={() => {
          setCreateDialogOpen(false);
          setEditDialogOpen(false);
        }}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{editDialogOpen ? 'Edit Tenant' : 'Create New Tenant'}</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              autoFocus
              margin="dense"
              label="Tenant Name"
              type="text"
              fullWidth
              variant="outlined"
              value={formData.name}
              onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
              helperText="Unique identifier for the tenant (lowercase, no spaces)"
              disabled={editDialogOpen}
            />
            <TextField
              margin="dense"
              label="Display Name"
              type="text"
              fullWidth
              variant="outlined"
              value={formData.displayName}
              onChange={(e) => setFormData((prev) => ({ ...prev, displayName: e.target.value }))}
              helperText="Human-readable name for the tenant"
              sx={{ mt: 2 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setCreateDialogOpen(false);
              setEditDialogOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleFormSubmit}
            variant="contained"
            disabled={!formData.name || !formData.displayName}
          >
            {editDialogOpen ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Tenants;
