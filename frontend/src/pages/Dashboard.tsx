import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress
} from '@mui/material';
// Using simple text icons instead of @mui/icons-material
const BusinessIcon = () => <span>🏢</span>;
const PeopleIcon = () => <span>👥</span>;
const PlayArrowIcon = () => <span>▶️</span>;
const CheckCircleIcon = () => <span>✅</span>;
const WarningIcon = () => <span>⚠️</span>;
const ErrorIcon = () => <span>❌</span>;
const TrendingUpIcon = () => <span>📈</span>;
import { DashboardStats, TenantMetrics } from '../types/index.js';
import apiService from '../services/api';

interface StatCardProps {
  title: string;
  value: number;
  subtitle?: string;
  icon: React.ReactElement;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}

const StatCard: React.FC<StatCardProps> = ({ title, value, subtitle, icon, color = 'primary' }) => {
  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            {subtitle && (
              <Typography color="textSecondary" variant="body2">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box sx={{ color: `${color}.main`, fontSize: '2rem' }}>{icon}</Box>
        </Box>
      </CardContent>
    </Card>
  );
};

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [tenantMetrics, setTenantMetrics] = useState<TenantMetrics[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Load real data from API
        const [dashboardStats, tenantMetricsData] = await Promise.all([
          apiService.getDashboardStats(),
          apiService.getTenantMetrics()
        ]);

        setStats(dashboardStats);
        setTenantMetrics(tenantMetricsData);
      } catch (err) {
        console.error('Failed to load dashboard data:', err);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'info';
    }
  };

  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'healthy':
        return <CheckCircleIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'critical':
        return <ErrorIcon />;
      default:
        return <CheckCircleIcon />;
    }
  };

  const formatLastActivity = (timestamp: string) => {
    const now = new Date();
    const activity = new Date(timestamp);
    const diffMs = now.getTime() - activity.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins < 60) {
      return `${diffMins} minutes ago`;
    }
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) {
      return `${diffHours} hours ago`;
    }
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} days ago`;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ mb: 2 }}>
        No dashboard data available
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Overview of your Dynamic MCP Server multi-tenant system
      </Typography>

      {/* System Health Alert */}
      <Alert
        severity={getHealthColor(stats.systemHealth) as any}
        icon={getHealthIcon(stats.systemHealth)}
        sx={{ mb: 3 }}
      >
        System Status: {stats.systemHealth.charAt(0).toUpperCase() + stats.systemHealth.slice(1)}
        {' - Last updated: ' + new Date(stats.lastUpdated).toLocaleString()}
      </Alert>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Tenants"
            value={stats.totalTenants}
            subtitle={`${stats.activeTenants} active`}
            icon={<BusinessIcon />}
            color="primary"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Users"
            value={stats.totalUsers}
            subtitle={`${stats.activeUsers} active`}
            icon={<PeopleIcon />}
            color="secondary"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="Total Workflows"
            value={stats.totalWorkflows}
            subtitle={`${stats.runningWorkflows} running`}
            icon={<PlayArrowIcon />}
            color="success"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <StatCard
            title="System Health"
            value={100}
            subtitle="All systems operational"
            icon={<TrendingUpIcon />}
            color="success"
          />
        </Grid>
      </Grid>

      {/* Tenant Metrics */}
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Tenant Metrics
            </Typography>
            <List>
              {tenantMetrics.map((tenant) => (
                <ListItem key={tenant.tenantId} divider>
                  <ListItemIcon>
                    <BusinessIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1" component="span">
                          {tenant.tenantName}
                        </Typography>
                        <Chip
                          label={`${tenant.activeWorkflows} active`}
                          size="small"
                          color={tenant.activeWorkflows > 0 ? 'success' : 'default'}
                        />
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary" component="div">
                          {tenant.userCount} users • {tenant.workflowCount} workflows • Last
                          activity: {formatLastActivity(tenant.lastActivity)}
                        </Typography>
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="caption" color="text.secondary" component="span">
                            Memory: {tenant.memoryUsage}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={tenant.memoryUsage}
                            sx={{ height: 4, borderRadius: 2, mt: 0.5, mb: 0.5 }}
                            color={
                              tenant.memoryUsage > 80
                                ? 'error'
                                : tenant.memoryUsage > 60
                                  ? 'warning'
                                  : 'primary'
                            }
                          />
                          <Typography variant="caption" color="text.secondary" component="span">
                            Storage: {tenant.storageUsage}%
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={tenant.storageUsage}
                            sx={{ height: 4, borderRadius: 2, mt: 0.5 }}
                            color={
                              tenant.storageUsage > 80
                                ? 'error'
                                : tenant.storageUsage > 60
                                  ? 'warning'
                                  : 'primary'
                            }
                          />
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
