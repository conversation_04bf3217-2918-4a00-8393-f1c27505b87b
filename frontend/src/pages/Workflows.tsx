import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  // Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  Alert,
  Snackbar,
  Menu,
  MenuItem,
  // Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab
} from '@mui/material';
import apiService from '../services/api';
import VisualWorkflowEditor from '../components/workflow/VisualWorkflowEditor';
import NodeTemplateSelector from '../components/workflows/NodeTemplateSelector';
import SimpleWorkflowWarning from '../components/workflows/SimpleWorkflowWarning';
import { NodeTemplate } from '../services/NodeTemplateService';
import { detectOldSyntax } from '../utils/workflowSyntaxDetector';

// Simple text icons
const AddIcon = () => <span>➕</span>;
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const PlayIcon = () => <span>▶️</span>;
const MoreVertIcon = () => <span>⋮</span>;
const FunctionIcon = () => <span>🔧</span>;
const WorkflowIcon = () => <span>⚡</span>;

interface Workflow {
  id: string;
  name: string;
  description: string;
  version: number;
  enabled: boolean;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  nodes_config?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}

interface MCPFunction {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  created_at: string;
  updated_at: string;
  input_schema?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  handler_config?: any; // eslint-disable-line @typescript-eslint/no-explicit-any
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`workflows-tabpanel-${index}`}
      aria-labelledby={`workflows-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const Workflows: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [functions, setFunctions] = useState<MCPFunction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Dialog states
  const [workflowDialogOpen, setWorkflowDialogOpen] = useState(false);
  const [functionDialogOpen, setFunctionDialogOpen] = useState(false);
  const [workflowSelectionDialogOpen, setWorkflowSelectionDialogOpen] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState<Workflow | null>(null);
  const [editingFunction, setEditingFunction] = useState<MCPFunction | null>(null);

  // Node template and cursor tracking
  const nodeConfigRef = useRef<HTMLTextAreaElement>(null);

  // Menu states
  const [workflowMenuAnchor, setWorkflowMenuAnchor] = useState<null | HTMLElement>(null);
  const [functionMenuAnchor, setFunctionMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [selectedFunction, setSelectedFunction] = useState<MCPFunction | null>(null);

  // Form states
  const [workflowForm, setWorkflowForm] = useState({
    name: '',
    description: '',
    enabled: true,
    input_schema: '{\n  "type": "object",\n  "properties": {}\n}',
    nodes_config: '{\n  "nodes": [],\n  "edges": []\n}'
  });

  const [functionForm, setFunctionForm] = useState({
    name: '',
    description: '',
    enabled: true,
    input_schema: '{}',
    handler_config: '{}'
  });

  // Execute workflow state
  const [executeDialogOpen, setExecuteDialogOpen] = useState(false);
  const [executingWorkflow, setExecutingWorkflow] = useState<Workflow | null>(null);
  const [executeInput, setExecuteInput] = useState('{}');
  const [executeResult, setExecuteResult] = useState<Record<string, unknown> | null>(null);
  const [executing, setExecuting] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [workflowsData, functionsData] = await Promise.all([
        apiService.getWorkflows(),
        apiService.getFunctions()
      ]);

      // Ensure data is array format
      setWorkflows(Array.isArray(workflowsData) ? workflowsData : []);
      setFunctions(Array.isArray(functionsData) ? functionsData : []);
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading data:', err);
      // Set empty arrays on error
      setWorkflows([]);
      setFunctions([]);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Workflow handlers
  const handleWorkflowMenuOpen = (event: React.MouseEvent<HTMLElement>, workflow: Workflow) => {
    setWorkflowMenuAnchor(event.currentTarget);
    setSelectedWorkflow(workflow);
  };

  const handleWorkflowMenuClose = () => {
    setWorkflowMenuAnchor(null);
    setSelectedWorkflow(null);
  };

  const handleEditWorkflow = async (workflow?: Workflow) => {
    const workflowToEdit = workflow || selectedWorkflow;
    if (workflowToEdit) {
      try {
        // Fetch complete workflow data including nodes_config
        const fullWorkflow = await apiService.getWorkflow(workflowToEdit.id);
        setEditingWorkflow(fullWorkflow);
        setWorkflowForm({
          name: fullWorkflow.name,
          description: fullWorkflow.description || '',
          enabled: fullWorkflow.enabled,
          input_schema: JSON.stringify(
            fullWorkflow.input_schema || { type: 'object', properties: {} },
            null,
            2
          ),
          nodes_config: JSON.stringify(
            fullWorkflow.nodes_config || { nodes: [], edges: [] },
            null,
            2
          )
        });
        setWorkflowDialogOpen(true);
      } catch (err) {
        setError('Failed to load workflow details');
        console.error('Edit workflow error:', err);
      }
    }
    handleWorkflowMenuClose();
  };

  const handleDeleteWorkflow = async () => {
    if (selectedWorkflow) {
      try {
        await apiService.deleteWorkflow(selectedWorkflow.id);
        setSuccess('Workflow deleted successfully');
        loadData();
      } catch {
        setError('Failed to delete workflow');
      }
    }
    handleWorkflowMenuClose();
  };

  const handleWorkflowSubmit = async () => {
    try {
      const formData = {
        name: workflowForm.name,
        description: workflowForm.description,
        enabled: workflowForm.enabled,
        input_schema: JSON.parse(workflowForm.input_schema),
        nodes_config: JSON.parse(workflowForm.nodes_config)
      };

      if (editingWorkflow) {
        await apiService.updateWorkflow(editingWorkflow.id, formData);
        setSuccess('Workflow updated successfully');
      } else {
        await apiService.createWorkflow(formData);
        setSuccess('Workflow created successfully');
      }
      setWorkflowDialogOpen(false);
      setEditingWorkflow(null);
      setWorkflowForm({
        name: '',
        description: '',
        enabled: true,
        input_schema: '{\n  "type": "object",\n  "properties": {}\n}',
        nodes_config: '{\n  "nodes": [],\n  "edges": []\n}'
      });
      loadData();
    } catch {
      setError('Failed to save workflow. Please check JSON syntax.');
    }
  };

  const handleExecuteWorkflow = (workflow: Workflow) => {
    setExecutingWorkflow(workflow);
    setExecuteInput('{}');
    setExecuteResult(null);
    setExecuteDialogOpen(true);
  };

  const handleExecuteSubmit = async () => {
    if (!executingWorkflow) return;

    try {
      setExecuting(true);
      const input = JSON.parse(executeInput);
      const result = await apiService.testWorkflow(executingWorkflow, input);
      setExecuteResult(result);
      setSuccess('Workflow executed successfully');
    } catch (err) {
      setError('Failed to execute workflow');
      console.error('Execute error:', err);
    } finally {
      setExecuting(false);
    }
  };

  const insertTemplate = (type: 'simple' | 'http' | 'complex') => {
    // Generate unique IDs using the same pattern as NodeTemplateService
    const generateUniqueId = (prefix: string = 'node'): string => {
      return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    };

    // Generate IDs once and reuse them
    const simpleIds = {
      jsAction: generateUniqueId('javascriptaction'),
      responseTerminator: generateUniqueId('responseterminator')
    };

    const httpIds = {
      validateJs: generateUniqueId('javascriptaction'),
      httpAction: generateUniqueId('httpaction'),
      responseTerminator: generateUniqueId('responseterminator')
    };

    const complexIds = {
      validateJs: generateUniqueId('javascriptaction'),
      httpAction: generateUniqueId('httpaction'),
      ifElseLogic: generateUniqueId('ifelselogic'),
      processJs: generateUniqueId('javascriptaction'),
      responseTerminator: generateUniqueId('responseterminator')
    };

    const templates = {
      simple: {
        input_schema: `{
  "type": "object",
  "properties": {
    "message": {
      "type": "string",
      "description": "Input message"
    }
  },
  "required": ["message"]
}`,
        nodes_config: JSON.stringify(
          {
            nodes: [
              {
                id: simpleIds.jsAction,
                type: 'javascript-action',
                name: 'Process Message',
                position: { x: 100, y: 100 },
                config: {
                  script:
                    '// Process input message\nconst result = input.message.toUpperCase();\n\n// Set workflow variables\nworkflowContext.variables.processedAt = new Date().toISOString();\n\n// Return result\nreturn {\n  processed: true,\n  result: result,\n  originalMessage: input.message\n};',
                  timeout: 30000,
                  enableConsole: true,
                  allowAsync: true
                }
              },
              {
                id: simpleIds.responseTerminator,
                type: 'response-terminator',
                name: 'Return Response',
                position: { x: 400, y: 100 },
                config: {
                  body: {
                    success: true,
                    data: '${input}',
                    metadata: {
                      workflowId: '${workflowId}',
                      executionId: '${executionId}',
                      timestamp: '${processedAt}'
                    }
                  },
                  statusCode: 200,
                  headers: {
                    'Content-Type': 'application/json'
                  }
                }
              }
            ],
            edges: [
              {
                source: simpleIds.jsAction,
                target: simpleIds.responseTerminator
              }
            ]
          },
          null,
          2
        )
      },
      http: {
        input_schema: `{
  "type": "object",
  "properties": {
    "url": {
      "type": "string",
      "description": "API URL to call"
    }
  },
  "required": ["url"]
}`,
        nodes_config: JSON.stringify(
          {
            nodes: [
              {
                id: httpIds.validateJs,
                type: 'javascript-action',
                name: 'Validate Input',
                position: { x: 100, y: 100 },
                config: {
                  script:
                    "// Validate input URL\nif (!input.url) {\n  throw new Error('URL is required');\n}\n\n// Set workflow variables\nworkflowContext.variables.validatedUrl = input.url;\nworkflowContext.variables.validatedAt = new Date().toISOString();\n\n// Return validated data\nreturn {\n  url: input.url,\n  validated: true\n};",
                  timeout: 30000,
                  enableConsole: true,
                  allowAsync: true
                }
              },
              {
                id: httpIds.httpAction,
                type: 'http-action',
                name: 'API Call',
                position: { x: 400, y: 100 },
                config: {
                  url: '${workflowContext.variables.validatedUrl}',
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'WorkflowEngine/1.0'
                  },
                  timeout: 30000,
                  retryCount: 3
                }
              },
              {
                id: httpIds.responseTerminator,
                type: 'response-terminator',
                name: 'Return Response',
                position: { x: 700, y: 100 },
                config: {
                  body: {
                    success: true,
                    data: '${input}',
                    metadata: {
                      workflowId: '${workflowId}',
                      executionId: '${executionId}',
                      apiUrl: '${validatedUrl}',
                      timestamp: '${validatedAt}'
                    }
                  },
                  statusCode: 200,
                  headers: {
                    'Content-Type': 'application/json'
                  }
                }
              }
            ],
            edges: [
              {
                source: httpIds.validateJs,
                target: httpIds.httpAction
              },
              {
                source: httpIds.httpAction,
                target: httpIds.responseTerminator
              }
            ]
          },
          null,
          2
        )
      },
      complex: {
        input_schema: `{
  "type": "object",
  "properties": {
    "userId": {
      "type": "string",
      "description": "User ID"
    },
    "action": {
      "type": "string",
      "description": "Action to perform"
    }
  },
  "required": ["userId", "action"]
}`,
        nodes_config: JSON.stringify(
          {
            nodes: [
              {
                id: complexIds.validateJs,
                type: 'javascript-action',
                name: 'Validate Input',
                position: { x: 100, y: 100 },
                config: {
                  script:
                    "// Validate input parameters\nif (!input.userId || !input.action) {\n  throw new Error('userId and action are required');\n}\n\n// Set workflow variables\nworkflowContext.variables.userId = input.userId;\nworkflowContext.variables.action = input.action;\nworkflowContext.variables.validatedAt = new Date().toISOString();\n\n// Return validation result\nreturn {\n  valid: true,\n  userId: input.userId,\n  action: input.action\n};",
                  timeout: 30000,
                  enableConsole: true,
                  allowAsync: true
                }
              },
              {
                id: complexIds.httpAction,
                type: 'http-action',
                name: 'Fetch User Data',
                position: { x: 400, y: 100 },
                config: {
                  url: 'https://api.example.com/users/${workflowContext.variables.userId}',
                  method: 'GET',
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: 'Bearer ${workflowContext.variables.apiToken}'
                  },
                  timeout: 30000,
                  retryCount: 3
                }
              },
              {
                id: complexIds.ifElseLogic,
                type: 'if-else-logic',
                name: 'Check User Status',
                position: { x: 700, y: 100 },
                config: {
                  condition:
                    "input.status === 'active' && input.permissions.includes(workflowContext.variables.action)",
                  trueOutput: {
                    authorized: true,
                    message: 'User authorized for action'
                  },
                  falseOutput: {
                    authorized: false,
                    message: 'User not authorized for action'
                  },
                  conditionType: 'expression'
                }
              },
              {
                id: complexIds.processJs,
                type: 'javascript-action',
                name: 'Process Action',
                position: { x: 1000, y: 100 },
                config: {
                  script:
                    '// Process the user action\nconst result = {\n  userId: workflowContext.variables.userId,\n  action: workflowContext.variables.action,\n  userData: input.userData,\n  authorized: input.authorized,\n  processedAt: new Date().toISOString(),\n  executionId: workflowContext.executionId\n};\n\n// Set final workflow variables\nworkflowContext.variables.finalResult = result;\nworkflowContext.variables.completedAt = new Date().toISOString();\n\n// Return processed result\nreturn result;',
                  timeout: 30000,
                  enableConsole: true,
                  allowAsync: true
                }
              },
              {
                id: complexIds.responseTerminator,
                type: 'response-terminator',
                name: 'Return Response',
                position: { x: 1300, y: 100 },
                config: {
                  body: {
                    success: true,
                    data: '${input}',
                    metadata: {
                      workflowId: '${workflowId}',
                      executionId: '${executionId}',
                      userId: '${userId}',
                      action: '${action}',
                      processedAt: '${completedAt}'
                    }
                  },
                  statusCode: 200,
                  headers: {
                    'Content-Type': 'application/json'
                  }
                }
              }
            ],
            edges: [
              {
                source: complexIds.validateJs,
                target: complexIds.httpAction
              },
              {
                source: complexIds.httpAction,
                target: complexIds.ifElseLogic
              },
              {
                source: complexIds.ifElseLogic,
                target: complexIds.processJs
              },
              {
                source: complexIds.processJs,
                target: complexIds.responseTerminator
              }
            ]
          },
          null,
          2
        )
      }
    };

    const template = templates[type];
    setWorkflowForm({
      ...workflowForm,
      input_schema: template.input_schema,
      nodes_config: template.nodes_config
    });
  };

  // Node template insertion handler
  const handleNodeTemplateInsert = (template: NodeTemplate) => {
    try {
      // Parse current nodes config
      let nodesConfig;
      try {
        nodesConfig = JSON.parse(workflowForm.nodes_config);
      } catch {
        // If parsing fails, create a default structure
        nodesConfig = { nodes: [], edges: [] };
      }

      // Ensure nodes array exists
      if (!nodesConfig.nodes) {
        nodesConfig.nodes = [];
      }
      if (!nodesConfig.edges) {
        nodesConfig.edges = [];
      }

      // Create new node from template
      const newNode = {
        id: template.id,
        type: template.type,
        name: template.name,
        position: template.position || { x: 100 + nodesConfig.nodes.length * 200, y: 100 },
        config: template.config
      };

      // Add the new node
      nodesConfig.nodes.push(newNode);

      // Update the form with the new configuration
      const updatedConfig = JSON.stringify(nodesConfig, null, 2);
      setWorkflowForm({
        ...workflowForm,
        nodes_config: updatedConfig
      });

      setSuccess(`${template.name} node inserted successfully`);
    } catch (error) {
      setError('Failed to insert node template');
      console.error('Node insertion error:', error);
    }
  };

  // Function handlers
  const handleFunctionMenuOpen = (event: React.MouseEvent<HTMLElement>, func: MCPFunction) => {
    setFunctionMenuAnchor(event.currentTarget);
    setSelectedFunction(func);
  };

  const handleFunctionMenuClose = () => {
    setFunctionMenuAnchor(null);
    setSelectedFunction(null);
  };

  const handleEditFunction = () => {
    if (selectedFunction) {
      setEditingFunction(selectedFunction);
      setFunctionForm({
        name: selectedFunction.name,
        description: selectedFunction.description,
        enabled: selectedFunction.enabled,
        input_schema: JSON.stringify(selectedFunction.input_schema || {}, null, 2),
        handler_config: JSON.stringify(selectedFunction.handler_config || {}, null, 2)
      });
      setFunctionDialogOpen(true);
    }
    handleFunctionMenuClose();
  };

  const handleDeleteFunction = async () => {
    if (selectedFunction) {
      try {
        await apiService.deleteFunction(selectedFunction.id);
        setSuccess('Function deleted successfully');
        loadData();
      } catch {
        setError('Failed to delete function');
      }
    }
    handleFunctionMenuClose();
  };

  const handleFunctionSubmit = async () => {
    try {
      const formData = {
        ...functionForm,
        input_schema: JSON.parse(functionForm.input_schema),
        handler_config: JSON.parse(functionForm.handler_config)
      };

      if (editingFunction) {
        await apiService.updateFunction(editingFunction.id, formData);
        setSuccess('Function updated successfully');
      } else {
        await apiService.createFunction(formData);
        setSuccess('Function created successfully');
      }
      setFunctionDialogOpen(false);
      setEditingFunction(null);
      setFunctionForm({
        name: '',
        description: '',
        enabled: true,
        input_schema: '{}',
        handler_config: '{}'
      });
      loadData();
    } catch {
      setError('Failed to save function');
    }
  };

  const handleWorkflowSelection = (selectedWorkflow: Workflow) => {
    setFunctionForm({
      ...functionForm,
      handler_config: JSON.stringify(
        {
          type: 'workflow',
          workflow_id: selectedWorkflow.id
        },
        null,
        2
      )
    });
    setWorkflowSelectionDialogOpen(false);
  };

  const handleOpenWorkflowSelection = () => {
    setWorkflowSelectionDialogOpen(true);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Loading...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="workflows tabs">
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <WorkflowIcon />
                Workflows
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <span>🎨</span>
                Visual Editor
              </Box>
            }
          />
          <Tab
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FunctionIcon />
                Functions
              </Box>
            }
          />
        </Tabs>
      </Box>

      {/* Workflows Tab */}
      <TabPanel value={tabValue} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Workflows
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setEditingWorkflow(null);
              setWorkflowForm({
                name: '',
                description: '',
                enabled: true,
                input_schema: '{\n  "type": "object",\n  "properties": {}\n}',
                nodes_config: '{\n  "nodes": [],\n  "edges": []\n}'
              });
              setWorkflowDialogOpen(true);
            }}
          >
            Add Workflow
          </Button>
        </Box>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
          {workflows.map((workflow) => {
            const hasOldSyntax = workflow.nodes_config
              ? detectOldSyntax(workflow.nodes_config)
              : false;

            return (
              <Box key={workflow.id} sx={{ flex: '1 1 300px', maxWidth: '400px' }}>
                {hasOldSyntax && (
                  <SimpleWorkflowWarning
                    workflowName={workflow.name}
                    hasOldSyntax={hasOldSyntax}
                    onEdit={() => handleEditWorkflow(workflow)}
                  />
                )}
                <Card>
                  <CardContent>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'flex-start',
                        mb: 2
                      }}
                    >
                      <Typography variant="h6" component="h2">
                        {workflow.name}
                      </Typography>
                      <IconButton size="small" onClick={(e) => handleWorkflowMenuOpen(e, workflow)}>
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {workflow.description}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                      <Chip
                        label={workflow.enabled ? 'Enabled' : 'Disabled'}
                        color={workflow.enabled ? 'success' : 'default'}
                        size="small"
                      />
                      <Chip label={`v${workflow.version}`} variant="outlined" size="small" />
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      Updated: {new Date(workflow.updated_at).toLocaleDateString()}
                    </Typography>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={() => handleExecuteWorkflow(workflow)}
                    >
                      Execute
                    </Button>
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => handleEditWorkflow(workflow)}
                    >
                      Edit
                    </Button>
                  </CardActions>
                </Card>
              </Box>
            );
          })}
        </Box>
      </TabPanel>

      {/* Visual Editor Tab */}
      <TabPanel value={tabValue} index={1}>
        <Box sx={{ height: 'calc(100vh - 200px)' }}>
          <VisualWorkflowEditor />
        </Box>
      </TabPanel>

      {/* Functions Tab */}
      <TabPanel value={tabValue} index={2}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            MCP Functions
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setEditingFunction(null);
              setFunctionForm({
                name: '',
                description: '',
                enabled: true,
                input_schema: '{}',
                handler_config: '{}'
              });
              setFunctionDialogOpen(true);
            }}
          >
            Add Function
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Updated</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {functions.map((func) => (
                <TableRow key={func.id}>
                  <TableCell>
                    <Typography variant="subtitle2" component="span">
                      {func.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary" component="span">
                      {func.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={func.enabled ? 'Enabled' : 'Disabled'}
                      color={func.enabled ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="caption" color="text.secondary" component="span">
                      {new Date(func.updated_at).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <IconButton size="small" onClick={(e) => handleFunctionMenuOpen(e, func)}>
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Workflow Dialog */}
      <Dialog
        open={workflowDialogOpen}
        onClose={() => setWorkflowDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>{editingWorkflow ? 'Edit Workflow' : 'Create New Workflow'}</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
              autoFocus
              label="Name"
              variant="outlined"
              value={workflowForm.name}
              onChange={(e) => setWorkflowForm({ ...workflowForm, name: e.target.value })}
              sx={{ flex: 1 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={workflowForm.enabled}
                  onChange={(e) => setWorkflowForm({ ...workflowForm, enabled: e.target.checked })}
                />
              }
              label="Enabled"
            />
          </Box>

          <TextField
            label="Description"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            value={workflowForm.description}
            onChange={(e) => setWorkflowForm({ ...workflowForm, description: e.target.value })}
            sx={{ mb: 2 }}
          />

          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Input Schema (JSON)
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={6}
            variant="outlined"
            value={workflowForm.input_schema}
            onChange={(e) => setWorkflowForm({ ...workflowForm, input_schema: e.target.value })}
            sx={{
              mb: 2,
              '& .MuiInputBase-input': {
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }
            }}
            helperText="Define the JSON schema for workflow input parameters"
          />

          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}
          >
            <Typography variant="h6">Workflow Configuration (JSON)</Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button size="small" onClick={() => insertTemplate('simple')}>
                Simple Template
              </Button>
              <Button size="small" onClick={() => insertTemplate('http')}>
                HTTP Template
              </Button>
              <Button size="small" onClick={() => insertTemplate('complex')}>
                Complex Template
              </Button>
            </Box>
          </Box>

          {/* Node Template Selector */}
          <Typography variant="subtitle1" gutterBottom sx={{ mt: 2, mb: 1, fontWeight: 'medium' }}>
            Insert Node Template
          </Typography>
          <NodeTemplateSelector onTemplateInsert={handleNodeTemplateInsert} disabled={false} />

          <TextField
            fullWidth
            multiline
            rows={12}
            variant="outlined"
            value={workflowForm.nodes_config}
            onChange={(e) => {
              setWorkflowForm({ ...workflowForm, nodes_config: e.target.value });
            }}
            inputRef={nodeConfigRef}
            sx={{
              mb: 2,
              '& .MuiInputBase-input': {
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }
            }}
            helperText="Define nodes and edges for the workflow. Use the node template selector above to insert pre-configured nodes."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWorkflowDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleWorkflowSubmit} variant="contained">
            {editingWorkflow ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Function Dialog */}
      <Dialog
        open={functionDialogOpen}
        onClose={() => setFunctionDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{editingFunction ? 'Edit Function' : 'Create New Function'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Name"
            fullWidth
            variant="outlined"
            value={functionForm.name}
            onChange={(e) => setFunctionForm({ ...functionForm, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            value={functionForm.description}
            onChange={(e) => setFunctionForm({ ...functionForm, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Input Schema (JSON)"
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={functionForm.input_schema}
            onChange={(e) => setFunctionForm({ ...functionForm, input_schema: e.target.value })}
            sx={{ mb: 2 }}
          />
          <Box
            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}
          >
            <Typography variant="h6">Handler Configuration</Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                size="small"
                onClick={() =>
                  setFunctionForm({
                    ...functionForm,
                    handler_config:
                      '{\n  "type": "script",\n  "script_content": "return { result: input };" \n}'
                  })
                }
              >
                Script Template
              </Button>
              <Button size="small" onClick={handleOpenWorkflowSelection}>
                Workflow Template
              </Button>
            </Box>
          </Box>
          <TextField
            fullWidth
            multiline
            rows={4}
            variant="outlined"
            value={functionForm.handler_config}
            onChange={(e) => setFunctionForm({ ...functionForm, handler_config: e.target.value })}
            sx={{
              mb: 2,
              '& .MuiInputBase-input': {
                fontFamily: 'monospace',
                fontSize: '0.875rem'
              }
            }}
            helperText="Use 'script' type for JavaScript code or 'workflow' type to execute a workflow"
          />
          <FormControlLabel
            control={
              <Switch
                checked={functionForm.enabled}
                onChange={(e) => setFunctionForm({ ...functionForm, enabled: e.target.checked })}
              />
            }
            label="Enabled"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFunctionDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleFunctionSubmit} variant="contained">
            {editingFunction ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Workflow Menu */}
      <Menu
        anchorEl={workflowMenuAnchor}
        open={Boolean(workflowMenuAnchor)}
        onClose={handleWorkflowMenuClose}
      >
        <MenuItem onClick={() => handleEditWorkflow()}>
          <EditIcon /> Edit
        </MenuItem>
        <MenuItem onClick={handleDeleteWorkflow}>
          <DeleteIcon /> Delete
        </MenuItem>
      </Menu>

      {/* Function Menu */}
      <Menu
        anchorEl={functionMenuAnchor}
        open={Boolean(functionMenuAnchor)}
        onClose={handleFunctionMenuClose}
      >
        <MenuItem onClick={handleEditFunction}>
          <EditIcon /> Edit
        </MenuItem>
        <MenuItem onClick={handleDeleteFunction}>
          <DeleteIcon /> Delete
        </MenuItem>
      </Menu>

      {/* Execute Workflow Dialog */}
      <Dialog
        open={executeDialogOpen}
        onClose={() => setExecuteDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Execute Workflow: {executingWorkflow?.name}</DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            label="Input Data (JSON)"
            fullWidth
            multiline
            rows={6}
            variant="outlined"
            value={executeInput}
            onChange={(e) => setExecuteInput(e.target.value)}
            sx={{ mb: 2 }}
            helperText="Enter JSON input data for the workflow"
          />

          {executeResult && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Execution Result:
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={8}
                variant="outlined"
                value={JSON.stringify(executeResult, null, 2)}
                InputProps={{
                  readOnly: true
                }}
                sx={{
                  '& .MuiInputBase-input': {
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }
                }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExecuteDialogOpen(false)}>Close</Button>
          <Button onClick={handleExecuteSubmit} variant="contained" disabled={executing}>
            {executing ? 'Executing...' : 'Execute'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Workflow Selection Dialog */}
      <Dialog
        open={workflowSelectionDialogOpen}
        onClose={() => setWorkflowSelectionDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Select Workflow</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Choose a workflow to use as the handler for this function:
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Action</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {workflows
                  .filter((w) => w.enabled)
                  .map((workflow) => (
                    <TableRow
                      key={workflow.id}
                      hover
                      sx={{ cursor: 'pointer' }}
                      onClick={() => handleWorkflowSelection(workflow)}
                    >
                      <TableCell>
                        <Typography variant="subtitle2">{workflow.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {workflow.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {workflow.description || 'No description'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={workflow.enabled ? 'Enabled' : 'Disabled'}
                          color={workflow.enabled ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Button
                          size="small"
                          variant="contained"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleWorkflowSelection(workflow);
                          }}
                        >
                          Select
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                {workflows.filter((w) => w.enabled).length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      <Typography variant="body2" color="text.secondary">
                        No enabled workflows available. Please create and enable workflows first.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWorkflowSelectionDialogOpen(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>

      {/* Success Snackbar */}
      <Snackbar open={!!success} autoHideDuration={6000} onClose={() => setSuccess(null)}>
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>

      {/* Error Snackbar */}
      <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Workflows;
