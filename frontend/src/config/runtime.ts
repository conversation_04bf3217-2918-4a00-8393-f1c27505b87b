/**
 * Runtime configuration that can be set via environment variables
 * This config is loaded from window.APP_CONFIG which is generated at runtime
 */

interface AppConfig {
  API_BASE_URL: string;
  API_URL: string;
  NODE_ENV: string;
}

// Default configuration (fallback)
const defaultConfig: AppConfig = {
  API_BASE_URL: 'http://localhost:7002',
  API_URL: '/api',
  NODE_ENV: 'development'
};

// Get runtime configuration from window.APP_CONFIG or use defaults
function getRuntimeConfig(): AppConfig {
  // Check if window.APP_CONFIG exists (set by config.js)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if (typeof window !== 'undefined' && (window as any).APP_CONFIG) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const runtimeConfig = (window as any).APP_CONFIG;
    return {
      API_BASE_URL: runtimeConfig.API_BASE_URL || defaultConfig.API_BASE_URL,
      API_URL: runtimeConfig.API_URL || defaultConfig.API_URL,
      NODE_ENV: runtimeConfig.NODE_ENV || defaultConfig.NODE_ENV
    };
  }

  // Fallback to import.meta.env for development
  try {
    if (import.meta && import.meta.env) {
      return {
        API_BASE_URL: import.meta.env.VITE_API_BASE_URL || defaultConfig.API_BASE_URL,
        API_URL: import.meta.env.VITE_API_URL || defaultConfig.API_URL,
        NODE_ENV: import.meta.env.NODE_ENV || defaultConfig.NODE_ENV
      };
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    // import.meta not available, continue to fallback
  }

  // Final fallback
  return defaultConfig;
}

export const config = getRuntimeConfig();

// Export individual config values for convenience
export const API_BASE_URL = config.API_BASE_URL;
export const API_URL = config.API_URL;
export const NODE_ENV = config.NODE_ENV;

// Log configuration in development
if (config.NODE_ENV === 'development') {
  console.log('Runtime configuration:', config);
}
