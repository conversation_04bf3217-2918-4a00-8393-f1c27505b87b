import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Node, Edge, Connection } from '@xyflow/react';

export interface WorkflowNode extends Node {
  data: {
    label: string;
    type: string;
    config: Record<string, any>;
    validation?: {
      isValid: boolean;
      errors: string[];
    };
    execution?: {
      status: 'idle' | 'running' | 'success' | 'error';
      result?: any;
      error?: string;
    };
  };
}

export interface WorkflowEditorState {
  nodes: WorkflowNode[];
  edges: Edge[];
  selectedNode: WorkflowNode | null;
  selectedEdge: Edge | null;
  isConfigPanelOpen: boolean;
  isNodePaletteOpen: boolean;
  workflowContext: Record<string, any>;
  executionHistory: Array<{
    nodeId: string;
    timestamp: string;
    input: any;
    output: any;
    status: string;
  }>;
  isExecuting: boolean;
  debugMode: boolean;
  breakpoints: string[];
}

const initialState: WorkflowEditorState = {
  nodes: [],
  edges: [],
  selectedNode: null,
  selectedEdge: null,
  isConfigPanelOpen: false,
  isNodePaletteOpen: true,
  workflowContext: {},
  executionHistory: [],
  isExecuting: false,
  debugMode: false,
  breakpoints: [],
};

const workflowEditorSlice = createSlice({
  name: 'workflowEditor',
  initialState,
  reducers: {
    setNodes: (state, action: PayloadAction<WorkflowNode[]>) => {
      state.nodes = action.payload;
    },
    setEdges: (state, action: PayloadAction<Edge[]>) => {
      state.edges = action.payload;
    },
    addNode: (state, action: PayloadAction<WorkflowNode>) => {
      state.nodes.push(action.payload);
    },
    updateNode: (state, action: PayloadAction<{ id: string; data: Partial<WorkflowNode['data']> }>) => {
      const node = state.nodes.find(n => n.id === action.payload.id);
      if (node) {
        node.data = { ...node.data, ...action.payload.data };
      }
    },
    removeNode: (state, action: PayloadAction<string>) => {
      state.nodes = state.nodes.filter(n => n.id !== action.payload);
      state.edges = state.edges.filter(e => e.source !== action.payload && e.target !== action.payload);
    },
    addEdge: (state, action: PayloadAction<Connection>) => {
      const edge: Edge = {
        id: `${action.payload.source}-${action.payload.target}`,
        source: action.payload.source!,
        target: action.payload.target!,
        type: 'default',
      };
      state.edges.push(edge);
    },
    removeEdge: (state, action: PayloadAction<string>) => {
      state.edges = state.edges.filter(e => e.id !== action.payload);
    },
    setSelectedNode: (state, action: PayloadAction<WorkflowNode | null>) => {
      state.selectedNode = action.payload;
      state.isConfigPanelOpen = !!action.payload;
    },
    setSelectedEdge: (state, action: PayloadAction<Edge | null>) => {
      state.selectedEdge = action.payload;
    },
    toggleConfigPanel: (state) => {
      state.isConfigPanelOpen = !state.isConfigPanelOpen;
    },
    toggleNodePalette: (state) => {
      state.isNodePaletteOpen = !state.isNodePaletteOpen;
    },
    setWorkflowContext: (state, action: PayloadAction<Record<string, any>>) => {
      state.workflowContext = action.payload;
    },
    updateWorkflowContext: (state, action: PayloadAction<Record<string, any>>) => {
      state.workflowContext = { ...state.workflowContext, ...action.payload };
    },
    addExecutionHistoryEntry: (state, action: PayloadAction<WorkflowEditorState['executionHistory'][0]>) => {
      state.executionHistory.push(action.payload);
    },
    clearExecutionHistory: (state) => {
      state.executionHistory = [];
    },
    setIsExecuting: (state, action: PayloadAction<boolean>) => {
      state.isExecuting = action.payload;
    },
    toggleDebugMode: (state) => {
      state.debugMode = !state.debugMode;
    },
    addBreakpoint: (state, action: PayloadAction<string>) => {
      if (!state.breakpoints.includes(action.payload)) {
        state.breakpoints.push(action.payload);
      }
    },
    removeBreakpoint: (state, action: PayloadAction<string>) => {
      state.breakpoints = state.breakpoints.filter(bp => bp !== action.payload);
    },
    clearBreakpoints: (state) => {
      state.breakpoints = [];
    },
  },
});

export const {
  setNodes,
  setEdges,
  addNode,
  updateNode,
  removeNode,
  addEdge,
  removeEdge,
  setSelectedNode,
  setSelectedEdge,
  toggleConfigPanel,
  toggleNodePalette,
  setWorkflowContext,
  updateWorkflowContext,
  addExecutionHistoryEntry,
  clearExecutionHistory,
  setIsExecuting,
  toggleDebugMode,
  addBreakpoint,
  removeBreakpoint,
  clearBreakpoints,
} = workflowEditorSlice.actions;

export default workflowEditorSlice.reducer;
