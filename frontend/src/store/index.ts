import { configureStore } from '@reduxjs/toolkit';
import workflowEditorReducer from './slices/workflowEditorSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    workflowEditor: workflowEditorReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['workflowEditor/setNodes', 'workflowEditor/setEdges'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
