import React, { useState } from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import './App.css';
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Tenants from './pages/Tenants';
import Workflows from './pages/Workflows';
import { Box, Typography } from '@mui/material';
import { ThemeContextProvider } from './theme/ThemeContext';
import { NotificationProvider } from './components/notifications/NotificationProvider';
import { SearchProvider } from './components/search/SearchProvider';

// Placeholder components for other pages
const UsersPage: React.FC = () => (
  <Box>
    <Typography variant="h4" component="h1" gutterBottom>
      Users
    </Typography>
    <Typography variant="body1" color="text.secondary">
      User management functionality will be implemented here.
    </Typography>
  </Box>
);

const SystemPage: React.FC = () => (
  <Box>
    <Typography variant="h4" component="h1" gutterBottom>
      System
    </Typography>
    <Typography variant="body1" color="text.secondary">
      System settings and configuration will be implemented here.
    </Typography>
  </Box>
);

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <Dashboard />;
      case 'workflows':
        return <Workflows />;
      case 'tenants':
        return <Tenants />;
      case 'users':
        return <UsersPage />;
      case 'system':
        return <SystemPage />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <ThemeContextProvider>
      <NotificationProvider>
        <SearchProvider>
          <CssBaseline />
          <Layout currentPage={currentPage} onPageChange={setCurrentPage}>
            {renderPage()}
          </Layout>
        </SearchProvider>
      </NotificationProvider>
    </ThemeContextProvider>
  );
}

export default App;
