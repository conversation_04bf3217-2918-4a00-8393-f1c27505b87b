import React, { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Typography,
  Box,
  Chip,
  InputAdornment,
  IconButton,
  Divider,
  CircularProgress,
  Fade
} from '@mui/material';
import { useSearch, SearchResult } from './SearchProvider';
import { useThemeMode } from '../../theme/ThemeContext';

// Simple text icons
const SearchIcon = () => <span>🔍</span>;
const CloseIcon = () => <span>✖️</span>;
const HistoryIcon = () => <span>📜</span>;
const TrendingUpIcon = () => <span>📈</span>;

interface GlobalSearchProps {
  open: boolean;
  onClose: () => void;
}

const getTypeIcon = (type: SearchResult['type']) => {
  switch (type) {
    case 'workflow':
      return '🔄';
    case 'tenant':
      return '🏢';
    case 'user':
      return '👤';
    case 'function':
      return '⚡';
    case 'setting':
      return '⚙️';
    default:
      return '📄';
  }
};

const getTypeColor = (type: SearchResult['type']) => {
  switch (type) {
    case 'workflow':
      return 'primary';
    case 'tenant':
      return 'secondary';
    case 'user':
      return 'success';
    case 'function':
      return 'warning';
    case 'setting':
      return 'info';
    default:
      return 'default';
  }
};

export const GlobalSearch: React.FC<GlobalSearchProps> = ({ open, onClose }) => {
  const {
    query,
    setQuery,
    results,
    isSearching,
    recentSearches,
    addRecentSearch,
    searchHistory,
    addToHistory,
    clearSearch
  } = useSearch();

  const { mode } = useThemeMode();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Reset selection when results change
  useEffect(() => {
    setSelectedIndex(0);
  }, [results]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedIndex((prev) => Math.min(prev + 1, results.length - 1));
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedIndex((prev) => Math.max(prev - 1, 0));
        break;
      case 'Enter':
        event.preventDefault();
        if (results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        } else if (query.trim()) {
          addRecentSearch(query);
        }
        break;
      case 'Escape':
        event.preventDefault();
        onClose();
        break;
    }
  };

  const handleResultClick = (result: SearchResult) => {
    addToHistory(result);
    addRecentSearch(query);

    // Navigate to result (in real app, use router)
    console.log('Navigate to:', result.url);

    onClose();
  };

  const handleRecentSearchClick = (recentQuery: string) => {
    setQuery(recentQuery);
  };

  const handleClose = () => {
    clearSearch();
    onClose();
  };

  const showRecentSearches = !query && recentSearches.length > 0;
  const showHistory = !query && searchHistory.length > 0;
  const showResults = query && (results.length > 0 || !isSearching);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          maxHeight: '80vh',
          bgcolor: mode === 'dark' ? 'grey.900' : 'background.paper'
        }
      }}
      TransitionComponent={Fade}
      transitionDuration={200}
    >
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <TextField
            ref={inputRef}
            fullWidth
            placeholder="Search workflows, tenants, users..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {isSearching && <CircularProgress size={20} />}
                  <IconButton onClick={handleClose} size="small">
                    <CloseIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                '& fieldset': {
                  borderColor: 'transparent'
                },
                '&:hover fieldset': {
                  borderColor: 'primary.main'
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'primary.main'
                }
              }
            }}
          />
        </Box>

        <Box sx={{ maxHeight: '60vh', overflow: 'auto' }}>
          {/* Search Results */}
          {showResults && (
            <List sx={{ py: 0 }}>
              {results.length === 0 && !isSearching && (
                <ListItem>
                  <ListItemText
                    primary="No results found"
                    secondary={`No items match "${query}"`}
                  />
                </ListItem>
              )}
              {results.map((result, index) => (
                <ListItem
                  key={result.id}
                  onClick={() => handleResultClick(result)}
                  sx={{
                    borderRadius: 1,
                    mx: 1,
                    mb: 0.5,
                    cursor: 'pointer',
                    bgcolor: index === selectedIndex ? 'action.selected' : 'transparent',
                    '&:hover': {
                      bgcolor: 'action.hover'
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Typography variant="h6">{getTypeIcon(result.type)}</Typography>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1" component="span">
                          {result.title}
                        </Typography>
                        <Chip
                          label={result.type}
                          size="small"
                          color={getTypeColor(result.type) as any}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={result.subtitle}
                  />
                </ListItem>
              ))}
            </List>
          )}

          {/* Recent Searches */}
          {showRecentSearches && (
            <>
              <Box sx={{ p: 2, pb: 1 }}>
                <Typography
                  variant="subtitle2"
                  color="text.secondary"
                  sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                >
                  <TrendingUpIcon />
                  Recent Searches
                </Typography>
              </Box>
              <List sx={{ py: 0 }}>
                {recentSearches.map((recentQuery, index) => (
                  <ListItem
                    key={index}
                    onClick={() => handleRecentSearchClick(recentQuery)}
                    sx={{
                      borderRadius: 1,
                      mx: 1,
                      mb: 0.5,
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: 'action.hover'
                      }
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <SearchIcon />
                    </ListItemIcon>
                    <ListItemText primary={recentQuery} />
                  </ListItem>
                ))}
              </List>
            </>
          )}

          {/* Search History */}
          {showHistory && (
            <>
              {showRecentSearches && <Divider sx={{ my: 1 }} />}
              <Box sx={{ p: 2, pb: 1 }}>
                <Typography
                  variant="subtitle2"
                  color="text.secondary"
                  sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                >
                  <HistoryIcon />
                  Recently Viewed
                </Typography>
              </Box>
              <List sx={{ py: 0 }}>
                {searchHistory.slice(0, 5).map((historyItem) => (
                  <ListItem
                    key={historyItem.id}
                    onClick={() => handleResultClick(historyItem)}
                    sx={{
                      borderRadius: 1,
                      mx: 1,
                      mb: 0.5,
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: 'action.hover'
                      }
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Typography variant="h6">{getTypeIcon(historyItem.type)}</Typography>
                    </ListItemIcon>
                    <ListItemText primary={historyItem.title} secondary={historyItem.subtitle} />
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </Box>

        {/* Footer with shortcuts */}
        <Box sx={{ p: 2, pt: 1, borderTop: 1, borderColor: 'divider', bgcolor: 'action.hover' }}>
          <Typography variant="caption" color="text.secondary">
            Press <strong>↑↓</strong> to navigate, <strong>Enter</strong> to select,{' '}
            <strong>Esc</strong> to close
          </Typography>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default GlobalSearch;
