import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo } from 'react';
import { debounce } from '@mui/material/utils';

export interface SearchResult {
  id: string;
  title: string;
  subtitle?: string;
  type: 'workflow' | 'tenant' | 'user' | 'function' | 'setting';
  url?: string;
  metadata?: Record<string, any>;
  score?: number;
}

export interface SearchFilter {
  type?: SearchResult['type'][];
  dateRange?: {
    from: Date;
    to: Date;
  };
  tags?: string[];
  status?: string[];
}

interface SearchContextType {
  query: string;
  setQuery: (query: string) => void;
  results: SearchResult[];
  isSearching: boolean;
  filters: SearchFilter;
  setFilters: (filters: SearchFilter) => void;
  clearSearch: () => void;
  recentSearches: string[];
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
  searchHistory: SearchResult[];
  addToHistory: (result: SearchResult) => void;
  clearHistory: () => void;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export const useSearch = (): SearchContextType => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error('useSearch must be used within a SearchProvider');
  }
  return context;
};

interface SearchProviderProps {
  children: ReactNode;
}

// Mock data for demonstration - in real app this would come from API
const mockData: SearchResult[] = [
  {
    id: 'wf-1',
    title: 'REST API to JavaScript Processing',
    subtitle: 'Processes incoming REST requests with JavaScript',
    type: 'workflow',
    url: '/workflows/wf-1',
    metadata: { status: 'active', lastModified: '2024-01-15' },
  },
  {
    id: 'wf-2',
    title: 'Database + Redis Caching',
    subtitle: 'SQL queries with Redis caching layer',
    type: 'workflow',
    url: '/workflows/wf-2',
    metadata: { status: 'active', lastModified: '2024-01-14' },
  },
  {
    id: 'wf-3',
    title: 'MCP Function + Redis Lookup',
    subtitle: 'MCP function with Redis data lookup',
    type: 'workflow',
    url: '/workflows/wf-3',
    metadata: { status: 'draft', lastModified: '2024-01-13' },
  },
  {
    id: 't-1',
    title: 'Default Tenant',
    subtitle: 'Primary tenant for the system',
    type: 'tenant',
    url: '/tenants/t-1',
    metadata: { status: 'active', workflowCount: 15 },
  },
  {
    id: 't-2',
    title: 'Development Tenant',
    subtitle: 'Tenant for development and testing',
    type: 'tenant',
    url: '/tenants/t-2',
    metadata: { status: 'active', workflowCount: 8 },
  },
];

export const SearchProvider: React.FC<SearchProviderProps> = ({ children }) => {
  const [query, setQueryState] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<SearchFilter>({});
  const [recentSearches, setRecentSearches] = useState<string[]>(() => {
    const saved = localStorage.getItem('recentSearches');
    return saved ? JSON.parse(saved) : [];
  });
  const [searchHistory, setSearchHistory] = useState<SearchResult[]>(() => {
    const saved = localStorage.getItem('searchHistory');
    return saved ? JSON.parse(saved) : [];
  });

  // Debounced search function
  const performSearch = useMemo(
    () =>
      debounce(async (searchQuery: string, searchFilters: SearchFilter) => {
        if (!searchQuery.trim()) {
          setResults([]);
          setIsSearching(false);
          return;
        }

        setIsSearching(true);

        try {
          // Simulate API call delay
          await new Promise(resolve => setTimeout(resolve, 300));

          // Filter mock data based on query and filters
          let filteredResults = mockData.filter(item => {
            const matchesQuery = 
              item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
              item.subtitle?.toLowerCase().includes(searchQuery.toLowerCase());

            const matchesType = !searchFilters.type?.length || 
              searchFilters.type.includes(item.type);

            return matchesQuery && matchesType;
          });

          // Sort by relevance (simple scoring based on title match)
          filteredResults = filteredResults.map(item => ({
            ...item,
            score: item.title.toLowerCase().includes(searchQuery.toLowerCase()) ? 2 : 1,
          })).sort((a, b) => (b.score || 0) - (a.score || 0));

          setResults(filteredResults);
        } catch (error) {
          console.error('Search error:', error);
          setResults([]);
        } finally {
          setIsSearching(false);
        }
      }, 300),
    []
  );

  const setQuery = useCallback((newQuery: string) => {
    setQueryState(newQuery);
    performSearch(newQuery, filters);
  }, [performSearch, filters]);

  const setFiltersCallback = useCallback((newFilters: SearchFilter) => {
    setFilters(newFilters);
    performSearch(query, newFilters);
  }, [performSearch, query]);

  const clearSearch = useCallback(() => {
    setQueryState('');
    setResults([]);
    setIsSearching(false);
  }, []);

  const addRecentSearch = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setRecentSearches(prev => {
      const newSearches = [searchQuery, ...prev.filter(s => s !== searchQuery)].slice(0, 10);
      localStorage.setItem('recentSearches', JSON.stringify(newSearches));
      return newSearches;
    });
  }, []);

  const clearRecentSearches = useCallback(() => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  }, []);

  const addToHistory = useCallback((result: SearchResult) => {
    setSearchHistory(prev => {
      const newHistory = [result, ...prev.filter(h => h.id !== result.id)].slice(0, 20);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));
      return newHistory;
    });
  }, []);

  const clearHistory = useCallback(() => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  }, []);

  const contextValue: SearchContextType = {
    query,
    setQuery,
    results,
    isSearching,
    filters,
    setFilters: setFiltersCallback,
    clearSearch,
    recentSearches,
    addRecentSearch,
    clearRecentSearches,
    searchHistory,
    addToHistory,
    clearHistory,
  };

  return (
    <SearchContext.Provider value={contextValue}>
      {children}
    </SearchContext.Provider>
  );
};

export default SearchProvider;
