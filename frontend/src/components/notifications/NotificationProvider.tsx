import React, { createContext, use<PERSON>ontext, useState, use<PERSON><PERSON>back, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>bar, Alert, AlertColor, Slide, SlideProps } from '@mui/material';

export interface NotificationOptions {
  message: string;
  severity?: AlertColor;
  duration?: number;
  action?: React.ReactNode;
  persistent?: boolean;
}

interface Notification extends NotificationOptions {
  id: string;
  timestamp: number;
}

interface NotificationContextType {
  showNotification: (options: NotificationOptions) => string;
  hideNotification: (id: string) => void;
  clearAllNotifications: () => void;
  notifications: Notification[];
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Transition component for slide animation
function SlideTransition(props: Slide<PERSON>rops) {
  return <Slide {...props} direction="up" />;
}

interface NotificationProviderProps {
  children: ReactNode;
  maxNotifications?: number;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  maxNotifications = 5
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [currentNotification, setCurrentNotification] = useState<Notification | null>(null);

  const generateId = () => `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  const showNotification = useCallback(
    (options: NotificationOptions): string => {
      const id = generateId();
      const notification: Notification = {
        id,
        timestamp: Date.now(),
        severity: 'info',
        duration: 6000,
        persistent: false,
        ...options
      };

      setNotifications((prev) => {
        const newNotifications = [notification, ...prev];
        // Limit the number of notifications
        return newNotifications.slice(0, maxNotifications);
      });

      // Show the notification immediately if no current notification
      if (!currentNotification) {
        setCurrentNotification(notification);
      }

      return id;
    },
    [currentNotification, maxNotifications]
  );

  const hideNotification = useCallback(
    (id: string) => {
      setNotifications((prev) => prev.filter((n) => n.id !== id));

      // If hiding the current notification, show the next one
      if (currentNotification?.id === id) {
        setCurrentNotification(null);
      }
    },
    [currentNotification]
  );

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    setCurrentNotification(null);
  }, []);

  // Auto-show next notification when current one is hidden
  React.useEffect(() => {
    if (!currentNotification && notifications.length > 0) {
      const nextNotification = notifications[0];
      setCurrentNotification(nextNotification);
    }
  }, [currentNotification, notifications]);

  const handleClose = (_event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }

    if (currentNotification) {
      hideNotification(currentNotification.id);
    }
  };

  const handleExited = () => {
    // Show next notification after current one has exited
    const remainingNotifications = notifications.filter((n) => n.id !== currentNotification?.id);
    if (remainingNotifications.length > 0) {
      setCurrentNotification(remainingNotifications[0]);
    } else {
      setCurrentNotification(null);
    }
  };

  const contextValue: NotificationContextType = {
    showNotification,
    hideNotification,
    clearAllNotifications,
    notifications
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      <Snackbar
        open={!!currentNotification}
        autoHideDuration={currentNotification?.persistent ? null : currentNotification?.duration}
        onClose={handleClose}
        TransitionComponent={SlideTransition}
        TransitionProps={{
          onExited: handleExited
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right'
        }}
        sx={{
          '& .MuiSnackbarContent-root': {
            minWidth: '300px',
            maxWidth: '500px'
          }
        }}
      >
        {currentNotification ? (
          <Alert
            onClose={handleClose}
            severity={currentNotification.severity}
            variant="filled"
            action={currentNotification.action}
            sx={{
              width: '100%',
              '& .MuiAlert-message': {
                wordBreak: 'break-word'
              }
            }}
          >
            {currentNotification.message}
          </Alert>
        ) : undefined}
      </Snackbar>
    </NotificationContext.Provider>
  );
};

// Convenience hooks for different notification types
export const useNotificationHelpers = () => {
  const { showNotification } = useNotifications();

  return {
    showSuccess: (message: string, options?: Omit<NotificationOptions, 'message' | 'severity'>) =>
      showNotification({ message, severity: 'success', ...options }),

    showError: (message: string, options?: Omit<NotificationOptions, 'message' | 'severity'>) =>
      showNotification({ message, severity: 'error', persistent: true, ...options }),

    showWarning: (message: string, options?: Omit<NotificationOptions, 'message' | 'severity'>) =>
      showNotification({ message, severity: 'warning', ...options }),

    showInfo: (message: string, options?: Omit<NotificationOptions, 'message' | 'severity'>) =>
      showNotification({ message, severity: 'info', ...options })
  };
};

export default NotificationProvider;
