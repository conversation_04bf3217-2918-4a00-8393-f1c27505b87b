import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typo<PERSON>,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  PlayIcon,
  CodeIcon,
  DatabaseIcon,
  RedisIcon,
  ApiIcon,
  FunctionIcon,
  ViewIcon
} from '../icons/MockIcons';
import SimpleWorkflowTester from './SimpleWorkflowTester';

interface UseCase {
  id: string;
  name: string;
  description: string;
  type: 'rest-api' | 'database' | 'mcp-function';
  icon: React.ReactNode;
  features: string[];
  testData: any;
  endpoints?: string[];
}

const BasicUseCaseManager: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedWorkflow, setSelectedWorkflow] = useState<any>(null);

  // Define use cases
  const useCases: UseCase[] = [
    {
      id: 'use-case-1',
      name: 'REST API to JavaScript',
      description: 'Processes REST API requests with custom JavaScript logic',
      type: 'rest-api',
      icon: <ApiIcon color="primary" />,
      features: [
        'REST API payload extraction',
        'Custom JavaScript processing',
        'Response formatting',
        'Context variable management',
        'Error handling and validation'
      ],
      testData: {
        type: 'user',
        id: 123,
        name: 'John Doe',
        priority: 'high'
      },
      endpoints: ['POST /api/workflows/{id}/execute', 'POST /api/workflows/{id}/trigger']
    },
    {
      id: 'use-case-2',
      name: 'Database + Redis Caching',
      description: 'Stores data in database and caches in Redis for fast retrieval',
      type: 'database',
      icon: <DatabaseIcon color="success" />,
      features: [
        'Data validation and preparation',
        'SQL database insertion with parameters',
        'Data transformation pipeline',
        'Redis caching with TTL',
        'Category indexing in Redis',
        'Performance monitoring'
      ],
      testData: {
        name: 'Laptop',
        category: 'electronics',
        price: 999.99,
        description: 'High-performance laptop'
      },
      endpoints: ['POST /api/workflows/{id}/execute', 'POST /api/workflows/{id}/trigger']
    },
    {
      id: 'use-case-3',
      name: 'MCP Function + Redis Lookup',
      description: 'MCP function that performs Redis lookups and data processing',
      type: 'mcp-function',
      icon: <FunctionIcon color="warning" />,
      features: [
        'MCP function with workflow-based handler',
        'Conditional workflow execution',
        'Redis pattern matching and lookups',
        'Data processing and transformation',
        'MCP-compatible response formatting'
      ],
      testData: {
        action: 'get',
        key: 'item:123',
        includeMetadata: true
      },
      endpoints: ['MCP Function: redis-lookup(params)', 'POST /api/workflows/{id}/execute']
    }
  ];

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getUseCaseTypeColor = (type: UseCase['type']) => {
    switch (type) {
      case 'rest-api':
        return 'primary';
      case 'database':
        return 'success';
      case 'mcp-function':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" gutterBottom>
            Phase 5 Use Cases Implementation
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Visual workflow editor use cases demonstrating REST API processing, database
            integration, and MCP functions.
          </Typography>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Use Cases Overview" />
            <Tab label="Workflow Tester" />
          </Tabs>
        </Box>

        {/* Use Cases Overview Tab */}
        {tabValue === 0 && (
          <Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              {useCases.map((useCase) => (
                <Card
                  variant="outlined"
                  key={useCase.id}
                  sx={{
                    border: 2,
                    borderColor: `${getUseCaseTypeColor(useCase.type)}.main`
                  }}
                >
                  <CardContent>
                    {/* Header */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      {useCase.icon}
                      <Box sx={{ ml: 1, flex: 1 }}>
                        <Typography variant="h6" component="div">
                          {useCase.name}
                        </Typography>
                        <Chip
                          label={useCase.type.replace('-', ' ')}
                          size="small"
                          color={getUseCaseTypeColor(useCase.type)}
                          variant="outlined"
                        />
                      </Box>
                    </Box>

                    {/* Description */}
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {useCase.description}
                    </Typography>

                    {/* Status */}
                    <Box sx={{ mb: 2 }}>
                      <Chip label="Implemented" color="success" size="small" />
                    </Box>

                    {/* Features */}
                    <Typography variant="subtitle2" gutterBottom>
                      Features:
                    </Typography>
                    <List dense>
                      {useCase.features.slice(0, 3).map((feature, index) => (
                        <ListItem key={index} sx={{ py: 0, px: 0 }}>
                          <ListItemText
                            primary={feature}
                            primaryTypographyProps={{ variant: 'caption' }}
                          />
                        </ListItem>
                      ))}
                      {useCase.features.length > 3 && (
                        <ListItem sx={{ py: 0, px: 0 }}>
                          <ListItemText
                            primary={`+${useCase.features.length - 3} more...`}
                            primaryTypographyProps={{ variant: 'caption', fontStyle: 'italic' }}
                          />
                        </ListItem>
                      )}
                    </List>
                  </CardContent>

                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<PlayIcon />}
                      onClick={() => {
                        setSelectedWorkflow({
                          id: `mock-${useCase.id}`,
                          name: useCase.name
                        });
                        setTabValue(1);
                      }}
                    >
                      Test
                    </Button>
                    <Button size="small" startIcon={<ViewIcon />} variant="outlined">
                      View
                    </Button>
                  </CardActions>
                </Card>
              ))}
            </Box>

            {/* Setup Instructions */}
            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" gutterBottom>
                Setup Instructions
              </Typography>
              <Alert severity="info" sx={{ mb: 2 }}>
                To create all use cases, run:{' '}
                <code>yarn ts-node scripts/create-all-use-cases.ts</code>
              </Alert>

              <Typography variant="subtitle2" gutterBottom>
                Prerequisites:
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <DatabaseIcon />
                  </ListItemIcon>
                  <ListItemText primary="PostgreSQL running on localhost:5432" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <RedisIcon />
                  </ListItemIcon>
                  <ListItemText primary="Redis running on localhost:6379" />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CodeIcon />
                  </ListItemIcon>
                  <ListItemText primary="Create items table for Use Case 2" />
                </ListItem>
              </List>
            </Box>
          </Box>
        )}

        {/* Workflow Tester Tab */}
        {tabValue === 1 && (
          <Box>
            {selectedWorkflow ? (
              <SimpleWorkflowTester
                workflowId={selectedWorkflow.id}
                workflowName={selectedWorkflow.name}
              />
            ) : (
              <Alert severity="info">
                Select a use case from the Overview tab to test it here.
              </Alert>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default BasicUseCaseManager;
