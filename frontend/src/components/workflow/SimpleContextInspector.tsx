import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Ta<PERSON>,
  Tab,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  IconButton
} from '@mui/material';
import { RefreshIcon, CopyIcon, PlayIcon, CodeIcon, DataObjectIcon } from '../icons/MockIcons';

interface SimpleContextInspectorProps {
  executionId?: string;
  workflowId?: string;
}

const SimpleContextInspector: React.FC<SimpleContextInspectorProps> = ({
  executionId,
  workflowId
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [isLiveMode, setIsLiveMode] = useState(true);
  const [jsonPathQuery, setJsonPathQuery] = useState('');
  const [queryResult, setQueryResult] = useState<any>(null);

  // Mock context data
  const contextData = {
    input: {
      payload: {
        type: 'user',
        id: 123,
        name: '<PERSON>'
      }
    },
    variables: {
      userId: 123,
      status: 'active',
      processedAt: new Date().toISOString()
    },
    nodeResults: {
      'payload-extraction': { result: 'success', data: { extracted: true } },
      'javascript-processing': { result: 'processed', output: { transformed: true } }
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const toggleLiveMode = () => {
    setIsLiveMode(!isLiveMode);
  };

  const executeJsonPathQuery = () => {
    if (!jsonPathQuery) {
      setQueryResult(null);
      return;
    }

    try {
      // Simple mock JSONPath implementation
      if (jsonPathQuery === '$.input.payload') {
        setQueryResult(contextData.input.payload);
      } else if (jsonPathQuery === '$.variables') {
        setQueryResult(contextData.variables);
      } else if (jsonPathQuery === '$.nodeResults') {
        setQueryResult(contextData.nodeResults);
      } else {
        setQueryResult({ message: 'Query executed', path: jsonPathQuery });
      }
    } catch (error) {
      setQueryResult({ error: (error as Error).message });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatValue = (value: any): string => {
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Context Inspector</Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip label="connected" color="success" size="small" />

            <FormControlLabel
              control={<Switch checked={isLiveMode} onChange={toggleLiveMode} size="small" />}
              label="Live"
              sx={{ ml: 1 }}
            />

            <IconButton size="small" onClick={() => window.location.reload()}>
              <RefreshIcon />
            </IconButton>
          </Box>
        </Box>

        {/* JSONPath Query */}
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="JSONPath query (e.g., $.variables.userId)"
            value={jsonPathQuery}
            onChange={(e) => setJsonPathQuery(e.target.value)}
            sx={{ flex: 1 }}
            InputProps={{
              startAdornment: <CodeIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
          <IconButton size="small" onClick={executeJsonPathQuery}>
            <PlayIcon />
          </IconButton>
        </Box>

        {queryResult && (
          <Alert severity={queryResult.error ? 'error' : 'info'} sx={{ mt: 1 }}>
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {queryResult.error || formatValue(queryResult)}
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab
            icon={<DataObjectIcon />}
            label={`Context (${Object.keys(contextData).length})`}
            iconPosition="start"
          />
          <Tab label="Events" iconPosition="start" />
        </Tabs>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'hidden', p: 2 }}>
        {/* Context Tab */}
        {tabValue === 0 && (
          <Box sx={{ height: '100%', overflow: 'auto' }}>
            <Grid container spacing={2}>
              {Object.entries(contextData).map(([key, value]) => (
                <Grid item xs={12} md={6} key={key}>
                  <Card variant="outlined">
                    <CardContent>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          mb: 1
                        }}
                      >
                        <Typography variant="subtitle2" sx={{ fontFamily: 'monospace' }}>
                          {key}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={() => copyToClipboard(formatValue(value))}
                        >
                          <CopyIcon />
                        </IconButton>
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: 'monospace',
                          fontSize: '0.75rem',
                          whiteSpace: 'pre-wrap',
                          maxHeight: 200,
                          overflow: 'auto'
                        }}
                      >
                        {formatValue(value)}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Events Tab */}
        {tabValue === 1 && (
          <Box sx={{ height: '100%', overflow: 'auto' }}>
            <Alert severity="info">
              Real-time events will appear here when workflow is executed.
              {executionId && ` Execution ID: ${executionId}`}
              {workflowId && ` Workflow ID: ${workflowId}`}
            </Alert>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default SimpleContextInspector;
