import React, { useCallback, useRef } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Node,
  ReactFlowProvider,
  Panel
} from '@xyflow/react';
import { Box, Paper, Typography, IconButton, Tooltip } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  setNodes as setNodesAction,
  setEdges as setEdgesAction,
  addEdge as addEdgeToStore,
  setSelectedNode,
  toggleNodePalette,
  toggleDebugMode
} from '../../store/slices/workflowEditorSlice';
import NodePalette from './NodePalette';
import NodeConfigPanel from './NodeConfigPanel';
import ContextInspector from './ContextInspector';
import {
  MenuIcon,
  DebugIcon,
  SaveIcon,
  LoadIcon,
  ExecuteIcon,
  FitViewIcon,
  ZoomInIcon,
  ZoomOutIcon
} from '../icons/CustomIcons';

// Import custom node types
import TriggerNode from './nodes/TriggerNode';
import ActionNode from './nodes/ActionNode';
import LogicNode from './nodes/LogicNode';
import TerminatorNode from './nodes/TerminatorNode';

import '@xyflow/react/dist/style.css';

const nodeTypes = {
  trigger: TriggerNode,
  action: ActionNode,
  logic: LogicNode,
  terminator: TerminatorNode
};

const VisualWorkflowEditor: React.FC = () => {
  const dispatch = useDispatch();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  const {
    nodes: storeNodes,
    edges: storeEdges,
    isNodePaletteOpen,
    isConfigPanelOpen,
    debugMode,
    isExecuting
  } = useSelector((state: RootState) => state.workflowEditor);

  // Initialize ReactFlow with empty arrays to avoid Redux immutability issues
  const [nodes, setNodes, onNodesChange] = useNodesState([] as Node[]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([] as any[]);

  // Ref to track if we're syncing to prevent infinite loops
  const isSyncingRef = useRef(false);

  // Deep clone function to create truly mutable objects
  const deepCloneNode = useCallback((node: any) => {
    return JSON.parse(
      JSON.stringify({
        id: node.id,
        type: node.type,
        position: node.position || { x: 0, y: 0 },
        data: node.data || {},
        style: node.style,
        className: node.className,
        targetPosition: node.targetPosition,
        sourcePosition: node.sourcePosition,
        hidden: node.hidden,
        selected: node.selected,
        dragging: node.dragging,
        selectable: node.selectable !== false,
        connectable: node.connectable !== false,
        deletable: node.deletable !== false,
        dragHandle: node.dragHandle,
        width: node.width,
        height: node.height,
        parentNode: node.parentNode,
        zIndex: node.zIndex,
        extent: node.extent,
        expandParent: node.expandParent,
        positionAbsolute: node.positionAbsolute,
        ariaLabel: node.ariaLabel,
        focusable: node.focusable !== false,
        resizing: node.resizing,
        measured: node.measured
      })
    );
  }, []);

  const deepCloneEdge = useCallback((edge: any) => {
    return JSON.parse(
      JSON.stringify({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
        type: edge.type,
        data: edge.data,
        style: edge.style,
        className: edge.className,
        animated: edge.animated,
        hidden: edge.hidden,
        selected: edge.selected,
        selectable: edge.selectable !== false,
        deletable: edge.deletable !== false,
        focusable: edge.focusable !== false,
        label: edge.label,
        labelStyle: edge.labelStyle,
        labelShowBg: edge.labelShowBg,
        labelBgStyle: edge.labelBgStyle,
        labelBgPadding: edge.labelBgPadding,
        labelBgBorderRadius: edge.labelBgBorderRadius,
        markerStart: edge.markerStart,
        markerEnd: edge.markerEnd,
        pathOptions: edge.pathOptions,
        interactionWidth: edge.interactionWidth,
        zIndex: edge.zIndex
      })
    );
  }, []);

  // Sync from Redux store to ReactFlow (one-way)
  React.useEffect(() => {
    if (!isSyncingRef.current) {
      const clonedNodes = storeNodes.map(deepCloneNode);
      setNodes(clonedNodes);
    }
  }, [storeNodes, setNodes, deepCloneNode]);

  React.useEffect(() => {
    if (!isSyncingRef.current) {
      const clonedEdges = storeEdges.map(deepCloneEdge);
      setEdges(clonedEdges);
    }
  }, [storeEdges, setEdges, deepCloneEdge]);

  // Custom handlers that properly sync back to Redux
  const handleNodesChange = useCallback(
    (changes: any) => {
      onNodesChange(changes);

      // Debounced sync back to Redux store
      setTimeout(() => {
        isSyncingRef.current = true;
        dispatch(setNodesAction(nodes as any));
        setTimeout(() => {
          isSyncingRef.current = false;
        }, 50);
      }, 200);
    },
    [onNodesChange, nodes, dispatch]
  );

  const handleEdgesChange = useCallback(
    (changes: any) => {
      onEdgesChange(changes);

      // Debounced sync back to Redux store
      setTimeout(() => {
        isSyncingRef.current = true;
        dispatch(setEdgesAction(edges as any));
        setTimeout(() => {
          isSyncingRef.current = false;
        }, 50);
      }, 200);
    },
    [onEdgesChange, edges, dispatch]
  );

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = addEdge(params, edges);
      setEdges(newEdge);
      dispatch(addEdgeToStore(params));
    },
    [edges, setEdges, dispatch]
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      dispatch(setSelectedNode(node as any));
    },
    [dispatch]
  );

  const onPaneClick = useCallback(() => {
    dispatch(setSelectedNode(null));
  }, [dispatch]);

  const handleSaveWorkflow = useCallback(() => {
    // TODO: Implement save functionality
    console.log('Saving workflow...', { nodes, edges });
  }, [nodes, edges]);

  const handleLoadWorkflow = useCallback(() => {
    // TODO: Implement load functionality
    console.log('Loading workflow...');
  }, []);

  const handleExecuteWorkflow = useCallback(() => {
    // TODO: Implement execute functionality
    console.log('Executing workflow...', { nodes, edges });
  }, [nodes, edges]);

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar */}
      <Paper
        elevation={1}
        sx={{
          p: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          borderRadius: 0,
          borderBottom: 1,
          borderColor: 'divider'
        }}
      >
        <Tooltip title="Toggle Node Palette">
          <IconButton
            onClick={() => dispatch(toggleNodePalette())}
            color={isNodePaletteOpen ? 'primary' : 'default'}
          >
            <MenuIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Toggle Debug Mode">
          <IconButton
            onClick={() => dispatch(toggleDebugMode())}
            color={debugMode ? 'primary' : 'default'}
          >
            <DebugIcon />
          </IconButton>
        </Tooltip>

        <Box sx={{ mx: 1, height: 24, width: 1, bgcolor: 'divider' }} />

        <Tooltip title="Save Workflow">
          <IconButton onClick={handleSaveWorkflow}>
            <SaveIcon />
          </IconButton>
        </Tooltip>

        <Tooltip title="Load Workflow">
          <IconButton onClick={handleLoadWorkflow}>
            <LoadIcon />
          </IconButton>
        </Tooltip>

        <Box sx={{ mx: 1, height: 24, width: 1, bgcolor: 'divider' }} />

        <Tooltip title="Execute Workflow">
          <IconButton onClick={handleExecuteWorkflow} color="primary" disabled={isExecuting}>
            <ExecuteIcon />
          </IconButton>
        </Tooltip>

        <Box sx={{ flexGrow: 1 }} />

        <Typography variant="body2" color="text.secondary">
          Nodes: {nodes.length} | Edges: {edges.length}
        </Typography>
      </Paper>

      {/* Main Editor Area */}
      <Box sx={{ flex: 1, display: 'flex', position: 'relative' }}>
        {/* Node Palette */}
        {isNodePaletteOpen && (
          <Paper
            elevation={2}
            sx={{
              width: 280,
              borderRadius: 0,
              borderRight: 1,
              borderColor: 'divider',
              overflow: 'hidden'
            }}
          >
            <NodePalette />
          </Paper>
        )}

        {/* ReactFlow Canvas */}
        <Box sx={{ flex: 1, position: 'relative' }}>
          <ReactFlowProvider>
            <div ref={reactFlowWrapper} style={{ width: '100%', height: '100%' }}>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={handleNodesChange}
                onEdgesChange={handleEdgesChange}
                onConnect={onConnect}
                onNodeClick={onNodeClick}
                onPaneClick={onPaneClick}
                nodeTypes={nodeTypes}
                fitView
                attributionPosition="bottom-left"
              >
                <Background color="#f0f0f0" gap={20} />
                <Controls showZoom={true} showFitView={true} showInteractive={true} />
                <MiniMap
                  nodeColor="#1976d2"
                  maskColor="rgba(0, 0, 0, 0.1)"
                  position="bottom-right"
                />

                {/* Custom Panels */}
                <Panel position="top-right">
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Zoom In">
                      <IconButton size="small">
                        <ZoomInIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Zoom Out">
                      <IconButton size="small">
                        <ZoomOutIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Fit View">
                      <IconButton size="small">
                        <FitViewIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Panel>

                {debugMode && (
                  <Panel position="bottom-left">
                    <Paper sx={{ p: 1, bgcolor: 'warning.light' }}>
                      <Typography variant="caption" color="warning.contrastText">
                        🐛 Debug Mode Active
                      </Typography>
                    </Paper>
                  </Panel>
                )}
              </ReactFlow>
            </div>
          </ReactFlowProvider>
        </Box>

        {/* Configuration Panel */}
        {isConfigPanelOpen && (
          <Paper
            elevation={2}
            sx={{
              width: 350,
              borderRadius: 0,
              borderLeft: 1,
              borderColor: 'divider',
              overflow: 'hidden'
            }}
          >
            <NodeConfigPanel />
          </Paper>
        )}
      </Box>

      {/* Context Inspector (Debug Mode) */}
      {debugMode && (
        <Paper
          elevation={2}
          sx={{
            height: 200,
            borderRadius: 0,
            borderTop: 1,
            borderColor: 'divider',
            overflow: 'hidden'
          }}
        >
          <ContextInspector />
        </Paper>
      )}
    </Box>
  );
};

export default VisualWorkflowEditor;
