import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { Box, Paper, Typography, Chip, IconButton } from '@mui/material';
import { WorkflowNode } from '../../../store/slices/workflowEditorSlice';
import { TriggerIcon, SettingsIcon, BreakpointIcon } from '../../icons/CustomIcons';

const TriggerNode: React.FC<NodeProps> = ({ data, selected }) => {
  const nodeData = data as WorkflowNode['data'];
  
  const getStatusColor = () => {
    switch (nodeData.execution?.status) {
      case 'running':
        return 'warning';
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getValidationColor = () => {
    if (!nodeData.validation?.isValid) return 'error';
    return 'success';
  };

  return (
    <Paper
      elevation={selected ? 4 : 2}
      sx={{
        minWidth: 200,
        border: selected ? 2 : 1,
        borderColor: selected ? 'primary.main' : 'divider',
        borderRadius: 2,
        overflow: 'hidden',
        position: 'relative',
        bgcolor: 'background.paper',
        '&:hover': {
          elevation: 3,
          borderColor: 'primary.light',
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          bgcolor: 'success.main',
          color: 'success.contrastText',
          p: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <TriggerIcon sx={{ fontSize: 16 }} />
        <Typography variant="caption" sx={{ fontWeight: 'bold', flex: 1 }}>
          TRIGGER
        </Typography>
        <IconButton size="small" sx={{ color: 'inherit' }}>
          <SettingsIcon sx={{ fontSize: 14 }} />
        </IconButton>
      </Box>

      {/* Content */}
      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          {nodeData.label}
        </Typography>
        
        {/* Configuration Summary */}
        <Box sx={{ mb: 1 }}>
          {nodeData.type === 'rest-api-trigger' && (
            <Chip
              label={`${nodeData.config.method} ${nodeData.config.path || '/api/workflow'}`}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
          {nodeData.type === 'mcp-function-trigger' && (
            <Chip
              label={nodeData.config.functionName || 'Unnamed Function'}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
          {nodeData.type === 'timer-trigger' && (
            <Chip
              label={nodeData.config.cronExpression || '0 0 * * *'}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
          {nodeData.type === 'webhook-trigger' && (
            <Chip
              label="Webhook"
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
        </Box>

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', gap: 0.5, mt: 1 }}>
          {nodeData.execution?.status && (
            <Chip
              label={nodeData.execution.status}
              size="small"
              color={getStatusColor() as any}
              sx={{ fontSize: '0.6rem', height: 20 }}
            />
          )}
          <Chip
            label={nodeData.validation?.isValid ? 'Valid' : 'Invalid'}
            size="small"
            color={getValidationColor() as any}
            variant="outlined"
            sx={{ fontSize: '0.6rem', height: 20 }}
          />
        </Box>

        {/* Validation Errors */}
        {nodeData.validation?.errors && nodeData.validation.errors.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {nodeData.validation.errors.map((error, index) => (
              <Typography
                key={index}
                variant="caption"
                color="error"
                display="block"
                sx={{ fontSize: '0.7rem' }}
              >
                ⚠️ {error}
              </Typography>
            ))}
          </Box>
        )}
      </Box>

      {/* Breakpoint Indicator */}
      {nodeData.execution?.status === 'running' && (
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            animation: 'pulse 1.5s infinite',
          }}
        >
          <BreakpointIcon sx={{ fontSize: 12, color: 'warning.main' }} />
        </Box>
      )}

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#4caf50',
          border: '2px solid #fff',
          width: 12,
          height: 12,
        }}
      />

      {/* CSS for pulse animation */}
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}
      </style>
    </Paper>
  );
};

export default TriggerNode;
