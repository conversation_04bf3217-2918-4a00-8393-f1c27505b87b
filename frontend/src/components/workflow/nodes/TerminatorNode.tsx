import React from 'react';
import { <PERSON><PERSON>, <PERSON>sition, NodeProps } from '@xyflow/react';
import { Box, Paper, Typography, Chip, IconButton } from '@mui/material';
import { WorkflowNode } from '../../../store/slices/workflowEditorSlice';
import { TerminatorIcon, SettingsIcon, BreakpointIcon } from '../../icons/CustomIcons';

const TerminatorNode: React.FC<NodeProps> = ({ data, selected }) => {
  const nodeData = data as WorkflowNode['data'];
  
  const getStatusColor = () => {
    switch (nodeData.execution?.status) {
      case 'running':
        return 'warning';
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getValidationColor = () => {
    if (!nodeData.validation?.isValid) return 'error';
    return 'success';
  };

  const getTerminatorTypeIcon = () => {
    switch (nodeData.type) {
      case 'response-terminator':
        return '📤';
      case 'mcp-response-terminator':
        return '🔧';
      case 'error-terminator':
        return '❌';
      default:
        return '🏁';
    }
  };

  const getConfigSummary = () => {
    switch (nodeData.type) {
      case 'response-terminator':
        return `HTTP ${nodeData.config.statusCode || 200}`;
      case 'mcp-response-terminator':
        return 'MCP Response';
      case 'error-terminator':
        return `Error ${nodeData.config.statusCode || 500}`;
      default:
        return 'Terminator';
    }
  };

  return (
    <Paper
      elevation={selected ? 4 : 2}
      sx={{
        minWidth: 200,
        border: selected ? 2 : 1,
        borderColor: selected ? 'primary.main' : 'divider',
        borderRadius: 2,
        overflow: 'hidden',
        position: 'relative',
        bgcolor: 'background.paper',
        '&:hover': {
          elevation: 3,
          borderColor: 'primary.light',
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          bgcolor: nodeData.type === 'error-terminator' ? 'error.main' : 'grey.700',
          color: nodeData.type === 'error-terminator' ? 'error.contrastText' : 'grey.50',
          p: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <TerminatorIcon sx={{ fontSize: 16 }} />
        <Typography variant="caption" sx={{ fontWeight: 'bold', flex: 1 }}>
          TERMINATOR
        </Typography>
        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
          {getTerminatorTypeIcon()}
        </Typography>
        <IconButton size="small" sx={{ color: 'inherit' }}>
          <SettingsIcon sx={{ fontSize: 14 }} />
        </IconButton>
      </Box>

      {/* Content */}
      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          {nodeData.label}
        </Typography>
        
        {/* Configuration Summary */}
        <Box sx={{ mb: 1 }}>
          <Chip
            label={getConfigSummary()}
            size="small"
            variant="outlined"
            color={nodeData.type === 'error-terminator' ? 'error' : 'default'}
            sx={{ fontSize: '0.7rem' }}
          />
        </Box>

        {/* Additional Config Info */}
        {nodeData.type === 'response-terminator' && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              Status: {nodeData.config.statusCode || 200}
            </Typography>
            {nodeData.config.headers && Object.keys(nodeData.config.headers).length > 0 && (
              <Typography variant="caption" color="text.secondary" display="block">
                Headers: {Object.keys(nodeData.config.headers).length}
              </Typography>
            )}
            <Typography variant="caption" color="text.secondary" display="block">
              Body: {nodeData.config.body || '${result}'}
            </Typography>
          </Box>
        )}

        {nodeData.type === 'mcp-response-terminator' && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              Result: {nodeData.config.result || '${result}'}
            </Typography>
            {nodeData.config.schema && Object.keys(nodeData.config.schema).length > 0 && (
              <Typography variant="caption" color="text.secondary" display="block">
                Schema: Defined
              </Typography>
            )}
          </Box>
        )}

        {nodeData.type === 'error-terminator' && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              Code: {nodeData.config.errorCode || 'WORKFLOW_ERROR'}
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block">
              Status: {nodeData.config.statusCode || 500}
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block">
              Message: {nodeData.config.message || '${error.message}'}
            </Typography>
          </Box>
        )}

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', gap: 0.5, mt: 1 }}>
          {nodeData.execution?.status && (
            <Chip
              label={nodeData.execution.status}
              size="small"
              color={getStatusColor() as any}
              sx={{ fontSize: '0.6rem', height: 20 }}
            />
          )}
          <Chip
            label={nodeData.validation?.isValid ? 'Valid' : 'Invalid'}
            size="small"
            color={getValidationColor() as any}
            variant="outlined"
            sx={{ fontSize: '0.6rem', height: 20 }}
          />
        </Box>

        {/* Execution Result Preview */}
        {nodeData.execution?.result && (
          <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Output: {JSON.stringify(nodeData.execution.result).substring(0, 50)}...
            </Typography>
          </Box>
        )}

        {/* Validation Errors */}
        {nodeData.validation?.errors && nodeData.validation.errors.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {nodeData.validation.errors.map((error, index) => (
              <Typography
                key={index}
                variant="caption"
                color="error"
                display="block"
                sx={{ fontSize: '0.7rem' }}
              >
                ⚠️ {error}
              </Typography>
            ))}
          </Box>
        )}
      </Box>

      {/* Execution Indicator */}
      {nodeData.execution?.status === 'running' && (
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            animation: 'pulse 1.5s infinite',
          }}
        >
          <BreakpointIcon sx={{ fontSize: 12, color: 'warning.main' }} />
        </Box>
      )}

      {/* Final Node Indicator */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 4,
          right: 4,
          width: 8,
          height: 8,
          borderRadius: '50%',
          bgcolor: nodeData.type === 'error-terminator' ? 'error.main' : 'success.main',
        }}
      />

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: nodeData.type === 'error-terminator' ? '#f44336' : '#424242',
          border: '2px solid #fff',
          width: 12,
          height: 12,
        }}
      />

      {/* CSS for pulse animation */}
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}
      </style>
    </Paper>
  );
};

export default TerminatorNode;
