import React from 'react';
import { <PERSON><PERSON>, <PERSON>sition, NodeProps } from '@xyflow/react';
import { Box, Paper, Typography, Chip, IconButton } from '@mui/material';
import { WorkflowNode } from '../../../store/slices/workflowEditorSlice';
import { LogicIcon, SettingsIcon, BreakpointIcon } from '../../icons/CustomIcons';

const LogicNode: React.FC<NodeProps> = ({ data, selected }) => {
  const nodeData = data as WorkflowNode['data'];
  
  const getStatusColor = () => {
    switch (nodeData.execution?.status) {
      case 'running':
        return 'warning';
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getValidationColor = () => {
    if (!nodeData.validation?.isValid) return 'error';
    return 'success';
  };

  const getLogicTypeIcon = () => {
    switch (nodeData.type) {
      case 'if-else-logic':
        return '🔀';
      case 'switch-logic':
        return '🔄';
      case 'merge-logic':
        return '🔗';
      case 'loop-logic':
        return '🔁';
      default:
        return '⚡';
    }
  };

  const getConfigSummary = () => {
    switch (nodeData.type) {
      case 'if-else-logic':
        return nodeData.config.condition || 'No Condition';
      case 'switch-logic':
        return `Switch on ${nodeData.config.expression || 'expression'}`;
      case 'merge-logic':
        return `Merge (${nodeData.config.strategy || 'all'})`;
      case 'loop-logic':
        return `Loop (${nodeData.config.type || 'forEach'})`;
      default:
        return 'Logic';
    }
  };

  const getOutputHandles = () => {
    switch (nodeData.type) {
      case 'if-else-logic':
        return [
          { id: 'true', label: 'True', color: '#4caf50' },
          { id: 'false', label: 'False', color: '#f44336' },
        ];
      case 'switch-logic':
        const cases = nodeData.config.cases || {};
        return Object.keys(cases).map((caseKey, index) => ({
          id: caseKey,
          label: caseKey,
          color: `hsl(${(index * 60) % 360}, 70%, 50%)`,
        }));
      case 'loop-logic':
        return [
          { id: 'iteration', label: 'Each', color: '#ff9800' },
          { id: 'complete', label: 'Done', color: '#4caf50' },
        ];
      default:
        return [{ id: 'output', label: 'Out', color: '#ff9800' }];
    }
  };

  const outputHandles = getOutputHandles();

  return (
    <Paper
      elevation={selected ? 4 : 2}
      sx={{
        minWidth: 200,
        border: selected ? 2 : 1,
        borderColor: selected ? 'primary.main' : 'divider',
        borderRadius: 2,
        overflow: 'hidden',
        position: 'relative',
        bgcolor: 'background.paper',
        '&:hover': {
          elevation: 3,
          borderColor: 'primary.light',
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          bgcolor: 'warning.main',
          color: 'warning.contrastText',
          p: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <LogicIcon sx={{ fontSize: 16 }} />
        <Typography variant="caption" sx={{ fontWeight: 'bold', flex: 1 }}>
          LOGIC
        </Typography>
        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
          {getLogicTypeIcon()}
        </Typography>
        <IconButton size="small" sx={{ color: 'inherit' }}>
          <SettingsIcon sx={{ fontSize: 14 }} />
        </IconButton>
      </Box>

      {/* Content */}
      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          {nodeData.label}
        </Typography>
        
        {/* Configuration Summary */}
        <Box sx={{ mb: 1 }}>
          <Chip
            label={getConfigSummary()}
            size="small"
            variant="outlined"
            sx={{ fontSize: '0.7rem' }}
          />
        </Box>

        {/* Additional Config Info */}
        {nodeData.type === 'if-else-logic' && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              True → {nodeData.config.trueOutput || 'success'}
            </Typography>
            <Typography variant="caption" color="text.secondary" display="block">
              False → {nodeData.config.falseOutput || 'failure'}
            </Typography>
          </Box>
        )}

        {nodeData.type === 'switch-logic' && nodeData.config.cases && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              Cases: {Object.keys(nodeData.config.cases).length}
            </Typography>
          </Box>
        )}

        {nodeData.type === 'merge-logic' && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              Strategy: {nodeData.config.strategy || 'all'}
            </Typography>
            {nodeData.config.timeout && (
              <Typography variant="caption" color="text.secondary" display="block">
                Timeout: {nodeData.config.timeout}ms
              </Typography>
            )}
          </Box>
        )}

        {nodeData.type === 'loop-logic' && (
          <Box>
            <Typography variant="caption" color="text.secondary" display="block">
              Type: {nodeData.config.type || 'forEach'}
            </Typography>
            {nodeData.config.maxIterations && (
              <Typography variant="caption" color="text.secondary" display="block">
                Max: {nodeData.config.maxIterations}
              </Typography>
            )}
          </Box>
        )}

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', gap: 0.5, mt: 1 }}>
          {nodeData.execution?.status && (
            <Chip
              label={nodeData.execution.status}
              size="small"
              color={getStatusColor() as any}
              sx={{ fontSize: '0.6rem', height: 20 }}
            />
          )}
          <Chip
            label={nodeData.validation?.isValid ? 'Valid' : 'Invalid'}
            size="small"
            color={getValidationColor() as any}
            variant="outlined"
            sx={{ fontSize: '0.6rem', height: 20 }}
          />
        </Box>

        {/* Validation Errors */}
        {nodeData.validation?.errors && nodeData.validation.errors.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {nodeData.validation.errors.map((error, index) => (
              <Typography
                key={index}
                variant="caption"
                color="error"
                display="block"
                sx={{ fontSize: '0.7rem' }}
              >
                ⚠️ {error}
              </Typography>
            ))}
          </Box>
        )}
      </Box>

      {/* Execution Indicator */}
      {nodeData.execution?.status === 'running' && (
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            animation: 'pulse 1.5s infinite',
          }}
        >
          <BreakpointIcon sx={{ fontSize: 12, color: 'warning.main' }} />
        </Box>
      )}

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#ff9800',
          border: '2px solid #fff',
          width: 12,
          height: 12,
        }}
      />

      {/* Output Handles */}
      {outputHandles.map((handle, index) => (
        <Handle
          key={handle.id}
          type="source"
          position={Position.Right}
          id={handle.id}
          style={{
            background: handle.color,
            border: '2px solid #fff',
            width: 12,
            height: 12,
            top: `${50 + (index - (outputHandles.length - 1) / 2) * 30}%`,
          }}
        />
      ))}

      {/* Output Labels */}
      {outputHandles.length > 1 && (
        <Box
          sx={{
            position: 'absolute',
            right: -60,
            top: '50%',
            transform: 'translateY(-50%)',
            display: 'flex',
            flexDirection: 'column',
            gap: 1,
          }}
        >
          {outputHandles.map((handle, index) => (
            <Typography
              key={handle.id}
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: handle.color,
                fontWeight: 'bold',
                transform: `translateY(${(index - (outputHandles.length - 1) / 2) * 30}px)`,
              }}
            >
              {handle.label}
            </Typography>
          ))}
        </Box>
      )}

      {/* CSS for pulse animation */}
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}
      </style>
    </Paper>
  );
};

export default LogicNode;
