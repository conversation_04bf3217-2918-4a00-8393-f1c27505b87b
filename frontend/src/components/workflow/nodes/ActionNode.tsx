import React from 'react';
import { <PERSON>le, Position, NodeProps } from '@xyflow/react';
import { Box, Paper, Typography, Chip, IconButton } from '@mui/material';
import { WorkflowNode } from '../../../store/slices/workflowEditorSlice';
import { ActionIcon, SettingsIcon, BreakpointIcon } from '../../icons/CustomIcons';

const ActionNode: React.FC<NodeProps> = ({ data, selected }) => {
  const nodeData = data as WorkflowNode['data'];

  const getStatusColor = () => {
    switch (nodeData.execution?.status) {
      case 'running':
        return 'warning';
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getValidationColor = () => {
    if (!nodeData.validation?.isValid) return 'error';
    return 'success';
  };

  const getActionTypeIcon = () => {
    switch (nodeData.type) {
      case 'javascript':
      case 'javascript-action':
        return '🔧';
      case 'sql':
      case 'sql-action':
        return '🗄️';
      case 'redis':
      case 'redis-action':
        return '📦';
      case 'http':
      case 'http-action':
        return '🌐';
      case 'litellm':
      case 'litellm-action':
        return '🤖';
      default:
        return '⚡';
    }
  };

  const getConfigSummary = () => {
    switch (nodeData.type) {
      case 'javascript':
      case 'javascript-action':
        return nodeData.config.script ? 'Custom Script' : 'No Script';
      case 'sql':
      case 'sql-action':
        return nodeData.config.query ? 'SQL Query' : 'No Query';
      case 'redis':
      case 'redis-action':
        return `${nodeData.config.operation || 'get'} ${nodeData.config.key || 'key'}`;
      case 'http':
      case 'http-action':
        return `${nodeData.config.method || 'GET'} ${nodeData.config.url || 'URL'}`;
      case 'litellm':
      case 'litellm-action':
        return nodeData.config.model || 'No Model';
      default:
        return 'Action';
    }
  };

  return (
    <Paper
      elevation={selected ? 4 : 2}
      sx={{
        minWidth: 200,
        border: selected ? 2 : 1,
        borderColor: selected ? 'primary.main' : 'divider',
        borderRadius: 2,
        overflow: 'hidden',
        position: 'relative',
        bgcolor: 'background.paper',
        '&:hover': {
          elevation: 3,
          borderColor: 'primary.light'
        }
      }}
    >
      {/* Header */}
      <Box
        sx={{
          bgcolor: 'primary.main',
          color: 'primary.contrastText',
          p: 1,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}
      >
        <ActionIcon sx={{ fontSize: 16 }} />
        <Typography variant="caption" sx={{ fontWeight: 'bold', flex: 1 }}>
          ACTION
        </Typography>
        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
          {getActionTypeIcon()}
        </Typography>
        <IconButton size="small" sx={{ color: 'inherit' }}>
          <SettingsIcon sx={{ fontSize: 14 }} />
        </IconButton>
      </Box>

      {/* Content */}
      <Box sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          {nodeData.label}
        </Typography>

        {/* Configuration Summary */}
        <Box sx={{ mb: 1 }}>
          <Chip
            label={getConfigSummary()}
            size="small"
            variant="outlined"
            sx={{ fontSize: '0.7rem' }}
          />
        </Box>

        {/* Additional Config Info */}
        {(nodeData.type === 'javascript' || nodeData.type === 'javascript-action') &&
          nodeData.config.timeout && (
            <Typography variant="caption" color="text.secondary" display="block">
              Timeout: {nodeData.config.timeout}ms
            </Typography>
          )}

        {(nodeData.type === 'http' || nodeData.type === 'http-action') &&
          nodeData.config.timeout && (
            <Typography variant="caption" color="text.secondary" display="block">
              Timeout: {nodeData.config.timeout}ms
            </Typography>
          )}

        {(nodeData.type === 'litellm' || nodeData.type === 'litellm-action') && (
          <Box>
            {nodeData.config.temperature && (
              <Typography variant="caption" color="text.secondary" display="block">
                Temp: {nodeData.config.temperature}
              </Typography>
            )}
            {nodeData.config.maxTokens && (
              <Typography variant="caption" color="text.secondary" display="block">
                Max Tokens: {nodeData.config.maxTokens}
              </Typography>
            )}
          </Box>
        )}

        {/* Status Indicators */}
        <Box sx={{ display: 'flex', gap: 0.5, mt: 1 }}>
          {nodeData.execution?.status && (
            <Chip
              label={nodeData.execution.status}
              size="small"
              color={getStatusColor() as any}
              sx={{ fontSize: '0.6rem', height: 20 }}
            />
          )}
          <Chip
            label={nodeData.validation?.isValid ? 'Valid' : 'Invalid'}
            size="small"
            color={getValidationColor() as any}
            variant="outlined"
            sx={{ fontSize: '0.6rem', height: 20 }}
          />
        </Box>

        {/* Execution Result Preview */}
        {nodeData.execution?.result && (
          <Box sx={{ mt: 1, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Result: {JSON.stringify(nodeData.execution.result).substring(0, 50)}...
            </Typography>
          </Box>
        )}

        {/* Validation Errors */}
        {nodeData.validation?.errors && nodeData.validation.errors.length > 0 && (
          <Box sx={{ mt: 1 }}>
            {nodeData.validation.errors.map((error, index) => (
              <Typography
                key={index}
                variant="caption"
                color="error"
                display="block"
                sx={{ fontSize: '0.7rem' }}
              >
                ⚠️ {error}
              </Typography>
            ))}
          </Box>
        )}
      </Box>

      {/* Execution Indicator */}
      {nodeData.execution?.status === 'running' && (
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            animation: 'pulse 1.5s infinite'
          }}
        >
          <BreakpointIcon sx={{ fontSize: 12, color: 'warning.main' }} />
        </Box>
      )}

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: '#2196f3',
          border: '2px solid #fff',
          width: 12,
          height: 12
        }}
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#2196f3',
          border: '2px solid #fff',
          width: 12,
          height: 12
        }}
      />

      {/* CSS for pulse animation */}
      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }
        `}
      </style>
    </Paper>
  );
};

export default ActionNode;
