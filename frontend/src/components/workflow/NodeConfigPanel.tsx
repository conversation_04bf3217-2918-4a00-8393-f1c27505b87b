import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Switch,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { updateNode, setSelectedNode, removeNode } from '../../store/slices/workflowEditorSlice';
import { CloseIcon, SaveIcon, DeleteIcon } from '../icons/CustomIcons';

const NodeConfigPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { selectedNode } = useSelector((state: RootState) => state.workflowEditor);

  const [config, setConfig] = useState<Record<string, any>>({});
  const [label, setLabel] = useState('');
  const [expandedSection, setExpandedSection] = useState<string>('basic');

  useEffect(() => {
    if (selectedNode) {
      setConfig(selectedNode.data.config || {});
      setLabel(selectedNode.data.label || '');
    }
  }, [selectedNode]);

  const handleSave = () => {
    if (selectedNode) {
      dispatch(
        updateNode({
          id: selectedNode.id,
          data: {
            ...selectedNode.data,
            label,
            config
          }
        })
      );
    }
  };

  const handleClose = () => {
    dispatch(setSelectedNode(null));
  };

  const handleDelete = () => {
    if (selectedNode && confirm('Are you sure you want to delete this node?')) {
      dispatch(removeNode(selectedNode.id));
      dispatch(setSelectedNode(null));
    }
  };

  const handleConfigChange = (key: string, value: any) => {
    setConfig((prev) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSectionChange =
    (section: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedSection(isExpanded ? section : '');
    };

  if (!selectedNode) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Select a node to configure
        </Typography>
      </Box>
    );
  }

  const renderConfigFields = () => {
    switch (selectedNode.data.type) {
      case 'rest-api-trigger':
        return (
          <>
            <TextField
              fullWidth
              label="HTTP Method"
              value={config.method || 'POST'}
              onChange={(e) => handleConfigChange('method', e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Path"
              value={config.path || '/api/workflow'}
              onChange={(e) => handleConfigChange('path', e.target.value)}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={config.authentication || false}
                  onChange={(e) => handleConfigChange('authentication', e.target.checked)}
                />
              }
              label="Require Authentication"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={config.validation !== false}
                  onChange={(e) => handleConfigChange('validation', e.target.checked)}
                />
              }
              label="Enable Validation"
            />
          </>
        );

      case 'javascript-action':
        return (
          <>
            <TextField
              fullWidth
              multiline
              rows={8}
              label="JavaScript Code"
              value={config.script || 'return { result: input };'}
              onChange={(e) => handleConfigChange('script', e.target.value)}
              sx={{
                mb: 2,
                '& .MuiInputBase-input': {
                  fontFamily: 'monospace',
                  fontSize: '0.875rem'
                }
              }}
              helperText="Available variables: input, context"
            />
            <TextField
              fullWidth
              type="number"
              label="Timeout (ms)"
              value={config.timeout || 30000}
              onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
              sx={{ mb: 2 }}
            />
          </>
        );

      case 'sql-action':
        return (
          <>
            <TextField
              fullWidth
              multiline
              rows={6}
              label="SQL Query"
              value={config.query || 'SELECT * FROM table WHERE id = ?'}
              onChange={(e) => handleConfigChange('query', e.target.value)}
              sx={{
                mb: 2,
                '& .MuiInputBase-input': {
                  fontFamily: 'monospace',
                  fontSize: '0.875rem'
                }
              }}
            />
            <TextField
              fullWidth
              label="Connection"
              value={config.connection || 'default'}
              onChange={(e) => handleConfigChange('connection', e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Parameters (JSON)"
              value={JSON.stringify(config.parameters || [])}
              onChange={(e) => {
                try {
                  handleConfigChange('parameters', JSON.parse(e.target.value));
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              sx={{ mb: 2 }}
              helperText="Array of parameter values"
            />
          </>
        );

      case 'redis-action':
        return (
          <>
            <TextField
              fullWidth
              label="Operation"
              value={config.operation || 'get'}
              onChange={(e) => handleConfigChange('operation', e.target.value)}
              sx={{ mb: 2 }}
              helperText="get, set, del, exists, etc."
            />
            <TextField
              fullWidth
              label="Key"
              value={config.key || ''}
              onChange={(e) => handleConfigChange('key', e.target.value)}
              sx={{ mb: 2 }}
            />
            {(config.operation === 'set' || !config.operation) && (
              <>
                <TextField
                  fullWidth
                  label="Value"
                  value={config.value || ''}
                  onChange={(e) => handleConfigChange('value', e.target.value)}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  type="number"
                  label="TTL (seconds)"
                  value={config.ttl || 3600}
                  onChange={(e) => handleConfigChange('ttl', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
              </>
            )}
          </>
        );

      case 'http-action':
        return (
          <>
            <TextField
              fullWidth
              label="URL"
              value={config.url || ''}
              onChange={(e) => handleConfigChange('url', e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Method"
              value={config.method || 'GET'}
              onChange={(e) => handleConfigChange('method', e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Headers (JSON)"
              value={JSON.stringify(config.headers || {}, null, 2)}
              onChange={(e) => {
                try {
                  handleConfigChange('headers', JSON.parse(e.target.value));
                } catch {
                  // Invalid JSON, ignore
                }
              }}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Body"
              value={config.body || ''}
              onChange={(e) => handleConfigChange('body', e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              type="number"
              label="Timeout (ms)"
              value={config.timeout || 30000}
              onChange={(e) => handleConfigChange('timeout', parseInt(e.target.value))}
              sx={{ mb: 2 }}
            />
          </>
        );

      case 'if-else-logic':
        return (
          <>
            <TextField
              fullWidth
              label="Condition"
              value={config.condition || 'input.value > 0'}
              onChange={(e) => handleConfigChange('condition', e.target.value)}
              sx={{ mb: 2 }}
              helperText="JavaScript expression that returns boolean"
            />
            <TextField
              fullWidth
              label="True Output"
              value={config.trueOutput || 'success'}
              onChange={(e) => handleConfigChange('trueOutput', e.target.value)}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="False Output"
              value={config.falseOutput || 'failure'}
              onChange={(e) => handleConfigChange('falseOutput', e.target.value)}
              sx={{ mb: 2 }}
            />
          </>
        );

      default:
        return (
          <Typography variant="body2" color="text.secondary">
            No specific configuration available for this node type.
          </Typography>
        );
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Node Configuration</Typography>
          <IconButton size="small" onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Box>
        <Chip
          label={selectedNode.data.type}
          size="small"
          color="primary"
          variant="outlined"
          sx={{ mt: 1 }}
        />
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {/* Basic Configuration */}
        <Accordion
          expanded={expandedSection === 'basic'}
          onChange={handleSectionChange('basic')}
          disableGutters
        >
          <AccordionSummary>
            <Typography variant="subtitle2">Basic Settings</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <TextField
              fullWidth
              label="Node Label"
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              sx={{ mb: 2 }}
            />
          </AccordionDetails>
        </Accordion>

        {/* Node-specific Configuration */}
        <Accordion
          expanded={expandedSection === 'config'}
          onChange={handleSectionChange('config')}
          disableGutters
        >
          <AccordionSummary>
            <Typography variant="subtitle2">Node Configuration</Typography>
          </AccordionSummary>
          <AccordionDetails>{renderConfigFields()}</AccordionDetails>
        </Accordion>

        {/* Validation */}
        <Accordion
          expanded={expandedSection === 'validation'}
          onChange={handleSectionChange('validation')}
          disableGutters
        >
          <AccordionSummary>
            <Typography variant="subtitle2">Validation</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {selectedNode.data.validation?.errors &&
            selectedNode.data.validation.errors.length > 0 ? (
              <Box>
                {selectedNode.data.validation.errors.map((error, index) => (
                  <Typography key={index} variant="body2" color="error" gutterBottom>
                    • {error}
                  </Typography>
                ))}
              </Box>
            ) : (
              <Typography variant="body2" color="success.main">
                ✓ Node configuration is valid
              </Typography>
            )}
          </AccordionDetails>
        </Accordion>
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            sx={{ flex: 1 }}
          >
            Save
          </Button>
          <Tooltip title="Delete Node">
            <IconButton color="error" onClick={handleDelete}>
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </Box>
  );
};

export default NodeConfigPanel;
