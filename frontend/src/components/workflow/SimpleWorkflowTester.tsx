import React, { useState } from 'react';
import {
  Box,
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Button,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  PlayIcon,
  RefreshIcon
} from '../icons/MockIcons';

interface SimpleWorkflowTesterProps {
  workflowId: string;
  workflowName: string;
}

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime?: number;
  timestamp?: string;
}

const SimpleWorkflowTester: React.FC<SimpleWorkflowTesterProps> = ({ 
  workflowId, 
  workflowName 
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [inputData, setInputData] = useState('{\n  "type": "user",\n  "id": 123,\n  "name": "John Doe",\n  "priority": "high"\n}');
  const [isExecuting, setIsExecuting] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const executeWorkflow = async () => {
    setIsExecuting(true);
    const startTime = Date.now();

    try {
      // Parse input data
      let parsedInput;
      try {
        parsedInput = JSON.parse(inputData);
      } catch (error) {
        throw new Error('Invalid JSON input');
      }

      // Mock execution
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const executionTime = Date.now() - startTime;
      const result: TestResult = {
        success: true,
        data: {
          success: true,
          message: 'Workflow executed successfully',
          input: parsedInput,
          output: {
            processed: true,
            result: 'Mock execution completed',
            timestamp: new Date().toISOString()
          }
        },
        executionTime,
        timestamp: new Date().toISOString()
      };

      setTestResult(result);
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      const result: TestResult = {
        success: false,
        error: error.message || 'Unknown error',
        executionTime,
        timestamp: new Date().toISOString()
      };

      setTestResult(result);
    } finally {
      setIsExecuting(false);
    }
  };

  const formatExecutionTime = (time?: number) => {
    if (!time) return 'N/A';
    return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Workflow Tester: {workflowName}
          </Typography>
          <Chip 
            label={`ID: ${workflowId.slice(0, 8)}...`} 
            size="small" 
            variant="outlined" 
          />
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Input & Execute" />
            <Tab label="Result" />
          </Tabs>
        </Box>

        {/* Input & Execute Tab */}
        {tabValue === 0 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Input Data (JSON)
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={8}
              value={inputData}
              onChange={(e) => setInputData(e.target.value)}
              placeholder="Enter JSON input data..."
              sx={{ mb: 2 }}
              disabled={isExecuting}
            />

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="contained"
                startIcon={isExecuting ? <CircularProgress size={20} /> : <PlayIcon />}
                onClick={executeWorkflow}
                disabled={isExecuting}
              >
                {isExecuting ? 'Executing...' : 'Execute Workflow'}
              </Button>

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => setInputData('{\n  "type": "user",\n  "id": 123,\n  "name": "John Doe",\n  "priority": "high"\n}')}
                disabled={isExecuting}
              >
                Reset Input
              </Button>
            </Box>
          </Box>
        )}

        {/* Result Tab */}
        {tabValue === 1 && (
          <Box>
            {testResult ? (
              <Box>
                {/* Result Header */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Chip 
                    label={testResult.success ? 'Success' : 'Error'} 
                    color={testResult.success ? 'success' : 'error'}
                  />
                  <Typography variant="body2" color="text.secondary">
                    Execution time: {formatExecutionTime(testResult.executionTime)}
                  </Typography>
                </Box>

                {/* Result Content */}
                <TextField
                  fullWidth
                  multiline
                  rows={12}
                  value={JSON.stringify(testResult, null, 2)}
                  InputProps={{ readOnly: true }}
                  sx={{ fontFamily: 'monospace' }}
                />
              </Box>
            ) : (
              <Alert severity="info">
                No execution result yet. Run the workflow to see results here.
              </Alert>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default SimpleWorkflowTester;
