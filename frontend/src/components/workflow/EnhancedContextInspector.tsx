import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Tabs,
  Tab,
  <PERSON>ert,
  TextField,
  Chip,
  Card,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  RefreshIcon,
  CopyIcon,
  HistoryIcon,
  SearchIcon,
  PlayIcon,
  RecordIcon,
  TimelineIcon,
  CodeIcon,
  DataObjectIcon
} from '../icons/MockIcons';

interface ContextEvent {
  type: 'context-update' | 'node-start' | 'node-complete' | 'workflow-start' | 'workflow-complete';
  executionId: string;
  timestamp: string;
  context?: any;
  nodeId?: string;
  nodeType?: string;
  changeType?: 'variable' | 'nodeResult' | 'full';
}

interface EnhancedContextInspectorProps {
  executionId?: string;
  workflowId?: string;
  autoConnect?: boolean;
}

const EnhancedContextInspector: React.FC<EnhancedContextInspectorProps> = ({
  executionId,
  workflowId,
  autoConnect = true
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [isLiveMode, setIsLiveMode] = useState(true);
  const [contextData, setContextData] = useState<any>({});
  const [eventHistory, setEventHistory] = useState<ContextEvent[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [jsonPathQuery, setJsonPathQuery] = useState('');
  const [queryResult, setQueryResult] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<
    'disconnected' | 'connecting' | 'connected'
  >('disconnected');

  const eventHistoryRef = useRef<ContextEvent[]>([]);

  useEffect(() => {
    if (autoConnect && (executionId || workflowId)) {
      connectWebSocket();
    }

    return () => {
      disconnectWebSocket();
    };
  }, [executionId, workflowId, autoConnect]);

  const connectWebSocket = () => {
    // Mock WebSocket connection for now
    setConnectionStatus('connected');
  };

  const disconnectWebSocket = () => {
    setConnectionStatus('disconnected');
  };

  const handleContextEvent = (event: ContextEvent) => {
    // Update context data
    if (event.context) {
      setContextData(event.context);
    }

    // Add to event history
    const newEvent = { ...event, timestamp: event.timestamp || new Date().toISOString() };
    eventHistoryRef.current = [newEvent, ...eventHistoryRef.current.slice(0, 99)]; // Keep last 100 events
    setEventHistory([...eventHistoryRef.current]);

    console.log('Context event:', event);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const toggleLiveMode = () => {
    setIsLiveMode(!isLiveMode);
  };

  const executeJsonPathQuery = () => {
    if (!jsonPathQuery || !contextData) {
      setQueryResult(null);
      return;
    }

    try {
      // Simple JSONPath implementation for demo
      // In production, use a proper JSONPath library
      const result = eval(`contextData${jsonPathQuery.replace('$', '')}`);
      setQueryResult(result);
    } catch (error) {
      setQueryResult({ error: (error as Error).message });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatValue = (value: any): string => {
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'success';
      case 'connecting':
        return 'warning';
      default:
        return 'error';
    }
  };

  const filteredEvents = eventHistory.filter(
    (event) =>
      !searchTerm ||
      event.type.includes(searchTerm.toLowerCase()) ||
      event.nodeId?.includes(searchTerm.toLowerCase()) ||
      event.nodeType?.includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Enhanced Context Inspector</Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              icon={<RecordIcon />}
              label={connectionStatus}
              color={getConnectionStatusColor()}
              size="small"
            />

            <FormControlLabel
              control={<Switch checked={isLiveMode} onChange={toggleLiveMode} size="small" />}
              label="Live"
              sx={{ ml: 1 }}
            />

            <IconButton size="small" onClick={() => window.location.reload()}>
              <RefreshIcon />
            </IconButton>
          </Box>
        </Box>

        {/* JSONPath Query */}
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="JSONPath query (e.g., $.variables.userId)"
            value={jsonPathQuery}
            onChange={(e) => setJsonPathQuery(e.target.value)}
            sx={{ flex: 1 }}
            InputProps={{
              startAdornment: <CodeIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
          <IconButton size="small" onClick={executeJsonPathQuery}>
            <PlayIcon />
          </IconButton>
        </Box>

        {queryResult && (
          <Alert severity={queryResult.error ? 'error' : 'info'} sx={{ mt: 1 }}>
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {queryResult.error || formatValue(queryResult)}
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab
            icon={<DataObjectIcon />}
            label={`Context (${Object.keys(contextData).length})`}
            iconPosition="start"
          />
          <Tab
            icon={<TimelineIcon />}
            label={`Events (${eventHistory.length})`}
            iconPosition="start"
          />
          <Tab icon={<HistoryIcon />} label="History" iconPosition="start" />
        </Tabs>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'hidden', p: 2 }}>
        {/* Context Tab */}
        {tabValue === 0 && (
          <Box sx={{ height: '100%', overflow: 'auto' }}>
            {Object.keys(contextData).length === 0 ? (
              <Alert severity="info">
                No context data available. Start a workflow execution to see real-time context
                updates.
              </Alert>
            ) : (
              <Grid container spacing={2}>
                {Object.entries(contextData).map(([key, value]) => (
                  <Grid item xs={12} md={6} key={key}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            mb: 1
                          }}
                        >
                          <Typography variant="subtitle2" sx={{ fontFamily: 'monospace' }}>
                            {key}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => copyToClipboard(formatValue(value))}
                          >
                            <CopyIcon />
                          </IconButton>
                        </Box>
                        <Typography
                          variant="body2"
                          sx={{
                            fontFamily: 'monospace',
                            fontSize: '0.75rem',
                            whiteSpace: 'pre-wrap',
                            maxHeight: 200,
                            overflow: 'auto'
                          }}
                        >
                          {formatValue(value)}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        )}

        {/* Events Tab */}
        {tabValue === 1 && (
          <Box sx={{ height: '100%', overflow: 'auto' }}>
            <TextField
              size="small"
              placeholder="Filter events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ mb: 2, width: '100%' }}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />

            <List>
              {filteredEvents.map((event, index) => (
                <ListItem key={index} divider>
                  <ListItemIcon>
                    <Chip
                      label={event.type}
                      size="small"
                      color={event.type.includes('error') ? 'error' : 'primary'}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2">
                          {event.nodeId ? `${event.nodeId} (${event.nodeType})` : 'Workflow'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {new Date(event.timestamp).toLocaleTimeString()}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      event.changeType && (
                        <Chip label={event.changeType} size="small" variant="outlined" />
                      )
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* History Tab */}
        {tabValue === 2 && (
          <Box sx={{ height: '100%', overflow: 'auto' }}>
            <Alert severity="info">Context history and versioning features coming soon...</Alert>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default EnhancedContextInspector;
