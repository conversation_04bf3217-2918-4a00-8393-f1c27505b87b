import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  InputAdornment,
  Chip,
  Tooltip
} from '@mui/material';
import { useDispatch } from 'react-redux';
import { addNode, setSelectedNode } from '../../store/slices/workflowEditorSlice';
import { WorkflowNode } from '../../store/slices/workflowEditorSlice';
import {
  TriggerIcon,
  ActionIcon,
  LogicIcon,
  TerminatorIcon,
  FunctionIcon,
  PlayIcon
} from '../icons/CustomIcons';

// Node type definitions
interface NodeTemplate {
  id: string;
  type: string;
  category: 'trigger' | 'action' | 'logic' | 'terminator';
  name: string;
  description: string;
  icon: React.ReactNode;
  defaultConfig: Record<string, any>;
  tags: string[];
}

const nodeTemplates: NodeTemplate[] = [
  // Trigger Nodes
  {
    id: 'rest-api-trigger',
    type: 'trigger',
    category: 'trigger',
    name: 'REST API Endpoint',
    description: 'HTTP endpoint that triggers workflow execution',
    icon: <TriggerIcon />,
    defaultConfig: {
      method: 'POST',
      path: '/api/workflow',
      authentication: false,
      validation: true
    },
    tags: ['http', 'api', 'endpoint']
  },
  {
    id: 'mcp-function-trigger',
    type: 'trigger',
    category: 'trigger',
    name: 'MCP Function',
    description: 'MCP function call that triggers workflow',
    icon: <FunctionIcon />,
    defaultConfig: {
      functionName: '',
      inputSchema: {},
      outputSchema: {}
    },
    tags: ['mcp', 'function', 'call']
  },
  {
    id: 'timer-trigger',
    type: 'trigger',
    category: 'trigger',
    name: 'Timer/Cron',
    description: 'Scheduled execution based on cron expression',
    icon: <PlayIcon />,
    defaultConfig: {
      cronExpression: '0 0 * * *',
      timezone: 'UTC',
      enabled: true
    },
    tags: ['timer', 'cron', 'schedule']
  },
  {
    id: 'webhook-trigger',
    type: 'trigger',
    category: 'trigger',
    name: 'Webhook',
    description: 'External webhook that triggers workflow',
    icon: <TriggerIcon />,
    defaultConfig: {
      url: '',
      secret: '',
      headers: {}
    },
    tags: ['webhook', 'external', 'http']
  },

  // Action Nodes
  {
    id: 'javascript-action',
    type: 'action',
    category: 'action',
    name: 'JavaScript',
    description: 'Execute custom JavaScript code',
    icon: <ActionIcon />,
    defaultConfig: {
      script: 'return { result: input };',
      timeout: 30000,
      allowedModules: []
    },
    tags: ['javascript', 'code', 'custom']
  },
  {
    id: 'sql-action',
    type: 'action',
    category: 'action',
    name: 'SQL Query',
    description: 'Execute SQL query against database',
    icon: <ActionIcon />,
    defaultConfig: {
      query: 'SELECT * FROM table WHERE id = ?',
      parameters: [],
      connection: 'default'
    },
    tags: ['sql', 'database', 'query']
  },
  {
    id: 'redis-action',
    type: 'action',
    category: 'action',
    name: 'Redis Operation',
    description: 'Perform Redis cache operations',
    icon: <ActionIcon />,
    defaultConfig: {
      operation: 'get',
      key: '',
      value: '',
      ttl: 3600
    },
    tags: ['redis', 'cache', 'storage']
  },
  {
    id: 'http-action',
    type: 'action',
    category: 'action',
    name: 'HTTP Request',
    description: 'Make HTTP request to external API',
    icon: <ActionIcon />,
    defaultConfig: {
      url: '',
      method: 'GET',
      headers: {},
      body: '',
      timeout: 30000
    },
    tags: ['http', 'api', 'request']
  },
  {
    id: 'litellm-action',
    type: 'action',
    category: 'action',
    name: 'LiteLLM',
    description: 'Call LLM model via LiteLLM',
    icon: <ActionIcon />,
    defaultConfig: {
      model: 'gpt-3.5-turbo',
      prompt: '',
      temperature: 0.7,
      maxTokens: 1000
    },
    tags: ['llm', 'ai', 'gpt']
  },

  // Logic Nodes
  {
    id: 'if-else-logic',
    type: 'logic',
    category: 'logic',
    name: 'IF/ELSE',
    description: 'Conditional branching based on expression',
    icon: <LogicIcon />,
    defaultConfig: {
      condition: 'input.value > 0',
      trueOutput: 'success',
      falseOutput: 'failure'
    },
    tags: ['condition', 'branch', 'if']
  },
  {
    id: 'switch-logic',
    type: 'logic',
    category: 'logic',
    name: 'Switch/Case',
    description: 'Multi-way branching based on value',
    icon: <LogicIcon />,
    defaultConfig: {
      expression: 'input.type',
      cases: {
        type1: 'path1',
        type2: 'path2',
        default: 'defaultPath'
      }
    },
    tags: ['switch', 'case', 'branch']
  },
  {
    id: 'merge-logic',
    type: 'logic',
    category: 'logic',
    name: 'Merge/Join',
    description: 'Merge multiple inputs into single output',
    icon: <LogicIcon />,
    defaultConfig: {
      strategy: 'all', // all, any, first
      timeout: 30000
    },
    tags: ['merge', 'join', 'combine']
  },
  {
    id: 'loop-logic',
    type: 'logic',
    category: 'logic',
    name: 'Loop/Iteration',
    description: 'Iterate over array or repeat execution',
    icon: <LogicIcon />,
    defaultConfig: {
      type: 'forEach', // forEach, while, for
      condition: '',
      maxIterations: 100
    },
    tags: ['loop', 'iterate', 'repeat']
  },

  // Terminator Nodes
  {
    id: 'response-terminator',
    type: 'terminator',
    category: 'terminator',
    name: 'Response',
    description: 'Return response for REST API workflows',
    icon: <TerminatorIcon />,
    defaultConfig: {
      statusCode: 200,
      headers: {},
      body: '${result}'
    },
    tags: ['response', 'http', 'return']
  },
  {
    id: 'mcp-response-terminator',
    type: 'terminator',
    category: 'terminator',
    name: 'MCP Response',
    description: 'Return response for MCP function workflows',
    icon: <TerminatorIcon />,
    defaultConfig: {
      result: '${result}',
      schema: {}
    },
    tags: ['mcp', 'response', 'return']
  },
  {
    id: 'error-terminator',
    type: 'terminator',
    category: 'terminator',
    name: 'Error Handler',
    description: 'Handle and return error responses',
    icon: <TerminatorIcon />,
    defaultConfig: {
      errorCode: 'WORKFLOW_ERROR',
      message: '${error.message}',
      statusCode: 500
    },
    tags: ['error', 'exception', 'handler']
  }
];

const categoryIcons = {
  trigger: <TriggerIcon />,
  action: <ActionIcon />,
  logic: <LogicIcon />,
  terminator: <TerminatorIcon />
};

const categoryColors = {
  trigger: 'success',
  action: 'primary',
  logic: 'warning',
  terminator: 'error'
} as const;

const NodePalette: React.FC = () => {
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategory, setExpandedCategory] = useState<string>('trigger');

  const filteredNodes = nodeTemplates.filter(
    (node) =>
      node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const groupedNodes = filteredNodes.reduce(
    (acc, node) => {
      if (!acc[node.category]) {
        acc[node.category] = [];
      }
      acc[node.category].push(node);
      return acc;
    },
    {} as Record<string, NodeTemplate[]>
  );

  const handleNodeDragStart = (event: React.DragEvent, nodeTemplate: NodeTemplate) => {
    event.dataTransfer.setData('application/reactflow', JSON.stringify(nodeTemplate));
    event.dataTransfer.effectAllowed = 'move';
  };

  const handleNodeClick = (nodeTemplate: NodeTemplate) => {
    const newNode: WorkflowNode = {
      id: `${nodeTemplate.id}-${Date.now()}`,
      type: nodeTemplate.category,
      position: { x: Math.random() * 400, y: Math.random() * 400 },
      data: {
        label: nodeTemplate.name,
        type: nodeTemplate.id,
        config: nodeTemplate.defaultConfig,
        validation: {
          isValid: true,
          errors: []
        }
      }
    };

    dispatch(addNode(newNode));
    dispatch(setSelectedNode(newNode));
  };

  const handleCategoryChange =
    (category: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedCategory(isExpanded ? category : '');
    };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom>
          Node Palette
        </Typography>
        <TextField
          fullWidth
          size="small"
          placeholder="Search nodes..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <InputAdornment position="start">🔍</InputAdornment>
          }}
        />
      </Box>

      {/* Node Categories */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {Object.entries(groupedNodes).map(([category, nodes]) => (
          <Accordion
            key={category}
            expanded={expandedCategory === category}
            onChange={handleCategoryChange(category)}
            disableGutters
            elevation={0}
            sx={{
              '&:before': { display: 'none' },
              borderBottom: 1,
              borderColor: 'divider'
            }}
          >
            <AccordionSummary sx={{ px: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {categoryIcons[category as keyof typeof categoryIcons]}
                <Typography variant="subtitle1" sx={{ textTransform: 'capitalize' }}>
                  {category}s
                </Typography>
                <Chip
                  label={nodes.length}
                  size="small"
                  color={categoryColors[category as keyof typeof categoryColors]}
                  variant="outlined"
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              <List dense>
                {nodes.map((node) => (
                  <ListItem key={node.id} disablePadding>
                    <Tooltip title={node.description} placement="right">
                      <ListItemButton
                        draggable
                        onDragStart={(e) => handleNodeDragStart(e, node)}
                        onClick={() => handleNodeClick(node)}
                        sx={{
                          px: 3,
                          py: 1,
                          '&:hover': {
                            bgcolor: 'action.hover'
                          }
                        }}
                      >
                        <ListItemIcon sx={{ minWidth: 36 }}>{node.icon}</ListItemIcon>
                        <ListItemText
                          primary={node.name}
                          secondary={
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                              {node.tags.slice(0, 2).map((tag) => (
                                <Chip
                                  key={tag}
                                  label={tag}
                                  size="small"
                                  variant="outlined"
                                  sx={{ fontSize: '0.7rem', height: 16 }}
                                />
                              ))}
                            </Box>
                          }
                          primaryTypographyProps={{ variant: 'body2' }}
                          secondaryTypographyProps={{ component: 'div' }}
                        />
                      </ListItemButton>
                    </Tooltip>
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>

      {/* Footer */}
      <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Drag nodes to canvas or click to add
        </Typography>
      </Box>
    </Box>
  );
};

export default NodePalette;
