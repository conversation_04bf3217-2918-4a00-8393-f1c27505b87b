import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
  Chip,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip
} from '@mui/material';
import { PlayIcon, CopyIcon, DownloadIcon, RefreshIcon } from '../icons/MockIcons';
import { workflowApi } from '../../services/api';

interface WorkflowTesterProps {
  workflowId: string;
  workflowName: string;
}

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime?: number;
  timestamp?: string;
}

const WorkflowTester: React.FC<WorkflowTesterProps> = ({ workflowId, workflowName }) => {
  const [tabValue, setTabValue] = useState(0);
  const [inputData, setInputData] = useState(
    '{\n  "type": "user",\n  "id": 123,\n  "name": "<PERSON>",\n  "priority": "high"\n}'
  );
  const [isExecuting, setIsExecuting] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [executionHistory, setExecutionHistory] = useState<TestResult[]>([]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const executeWorkflow = async () => {
    setIsExecuting(true);
    const startTime = Date.now();

    try {
      // Parse input data
      let parsedInput;
      try {
        parsedInput = JSON.parse(inputData);
      } catch (error) {
        throw new Error('Invalid JSON input');
      }

      // Execute workflow
      const response = await workflowApi.execute(workflowId, { input: parsedInput });
      const executionTime = Date.now() - startTime;

      const result: TestResult = {
        success: true,
        data: response.data,
        executionTime,
        timestamp: new Date().toISOString()
      };

      setTestResult(result);
      setExecutionHistory((prev) => [result, ...prev.slice(0, 9)]); // Keep last 10 results
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      const result: TestResult = {
        success: false,
        error: error.message || 'Unknown error',
        executionTime,
        timestamp: new Date().toISOString()
      };

      setTestResult(result);
      setExecutionHistory((prev) => [result, ...prev.slice(0, 9)]);
    } finally {
      setIsExecuting(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const downloadResult = () => {
    if (!testResult) return;

    const dataStr = JSON.stringify(testResult, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `workflow-${workflowId}-result-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const formatExecutionTime = (time?: number) => {
    if (!time) return 'N/A';
    return time < 1000 ? `${time}ms` : `${(time / 1000).toFixed(2)}s`;
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">Workflow Tester: {workflowName}</Typography>
          <Chip label={`ID: ${workflowId.slice(0, 8)}...`} size="small" variant="outlined" />
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Input & Execute" />
            <Tab label="Result" />
            <Tab label="History" />
          </Tabs>
        </Box>

        {/* Input & Execute Tab */}
        {tabValue === 0 && (
          <Box>
            <Typography variant="subtitle1" gutterBottom>
              Input Data (JSON)
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={8}
              value={inputData}
              onChange={(e) => setInputData(e.target.value)}
              placeholder="Enter JSON input data..."
              sx={{ mb: 2 }}
              disabled={isExecuting}
            />

            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="contained"
                startIcon={isExecuting ? <CircularProgress size={20} /> : <PlayIcon />}
                onClick={executeWorkflow}
                disabled={isExecuting}
              >
                {isExecuting ? 'Executing...' : 'Execute Workflow'}
              </Button>

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() =>
                  setInputData(
                    '{\n  "type": "user",\n  "id": 123,\n  "name": "John Doe",\n  "priority": "high"\n}'
                  )
                }
                disabled={isExecuting}
              >
                Reset Input
              </Button>
            </Box>
          </Box>
        )}

        {/* Result Tab */}
        {tabValue === 1 && (
          <Box>
            {testResult ? (
              <Box>
                {/* Result Header */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Chip
                      label={testResult.success ? 'Success' : 'Error'}
                      color={testResult.success ? 'success' : 'error'}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Execution time: {formatExecutionTime(testResult.executionTime)}
                    </Typography>
                  </Box>

                  <Box>
                    <Tooltip title="Copy Result">
                      <IconButton
                        onClick={() => copyToClipboard(JSON.stringify(testResult, null, 2))}
                      >
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download Result">
                      <IconButton onClick={downloadResult}>
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>

                {/* Result Content */}
                <TextField
                  fullWidth
                  multiline
                  rows={12}
                  value={JSON.stringify(testResult, null, 2)}
                  InputProps={{ readOnly: true }}
                  sx={{ fontFamily: 'monospace' }}
                />
              </Box>
            ) : (
              <Alert severity="info">
                No execution result yet. Run the workflow to see results here.
              </Alert>
            )}
          </Box>
        )}

        {/* History Tab */}
        {tabValue === 2 && (
          <Box>
            {executionHistory.length > 0 ? (
              <Grid container spacing={2}>
                {executionHistory.map((result, index) => (
                  <Grid item xs={12} key={index}>
                    <Card variant="outlined">
                      <CardContent>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            mb: 1
                          }}
                        >
                          <Chip
                            label={result.success ? 'Success' : 'Error'}
                            color={result.success ? 'success' : 'error'}
                            size="small"
                          />
                          <Typography variant="caption" color="text.secondary">
                            {new Date(result.timestamp!).toLocaleString()}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          Execution time: {formatExecutionTime(result.executionTime)}
                        </Typography>
                        {result.error && (
                          <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                            Error: {result.error}
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Alert severity="info">
                No execution history yet. Run the workflow to see history here.
              </Alert>
            )}
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default WorkflowTester;
