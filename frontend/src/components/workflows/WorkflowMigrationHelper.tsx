import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  List,
  ListItem,
  ListItemText,
  Divider,
  Chip
} from '@mui/material';

interface WorkflowMigrationHelperProps {
  workflowName: string;
  hasOldSyntax: boolean;
  onMigrate?: () => void;
  onDismiss?: () => void;
}

const WorkflowMigrationHelper: React.FC<WorkflowMigrationHelperProps> = ({
  workflowName,
  hasOldSyntax,
  onMigrate,
  onDismiss
}) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!hasOldSyntax) {
    return null;
  }

  const migrationSteps = [
    {
      old: 'context.input.message',
      new: 'input.message',
      description: 'Use input directly instead of context.input'
    },
    {
      old: 'context.variables.someVar',
      new: 'workflowContext.variables.someVar',
      description: 'Use workflowContext.variables instead of context.variables'
    },
    {
      old: 'context.executionId',
      new: 'workflowContext.executionId',
      description: 'Use workflowContext.executionId instead of context.executionId'
    },
    {
      old: 'context.workflowId',
      new: 'workflowContext.workflowId',
      description: 'Use workflowContext.workflowId instead of context.workflowId'
    }
  ];

  return (
    <>
      <Alert severity="warning" sx={{ mb: 2 }}>
        <AlertTitle>Workflow Syntax Update Required</AlertTitle>
        <Typography variant="body2" sx={{ mb: 2 }}>
          The workflow "{workflowName}" was created with an older JavaScript syntax that is no
          longer supported. You need to update the JavaScript code in your workflow nodes to use the
          new syntax.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button size="small" variant="outlined" onClick={() => setShowDetails(true)}>
            View Migration Guide
          </Button>
          {onMigrate && (
            <Button size="small" variant="contained" color="primary" onClick={onMigrate}>
              Edit Workflow
            </Button>
          )}
          {onDismiss && (
            <Button size="small" variant="text" onClick={onDismiss}>
              Dismiss
            </Button>
          )}
        </Box>
      </Alert>

      <Dialog open={showDetails} onClose={() => setShowDetails(false)} maxWidth="md" fullWidth>
        <DialogTitle>Workflow JavaScript Syntax Migration Guide</DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 3 }}>
            Your workflow "{workflowName}" contains JavaScript code that uses the old syntax. Here's
            how to update it:
          </Typography>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Required Changes
              </Typography>
              <List>
                {migrationSteps.map((step, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box
                            sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}
                          >
                            <Chip label={step.old} color="error" variant="outlined" size="small" />
                            <Typography>→</Typography>
                            <Chip
                              label={step.new}
                              color="success"
                              variant="outlined"
                              size="small"
                            />
                          </Box>
                        }
                        secondary={step.description}
                      />
                    </ListItem>
                    {index < migrationSteps.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Example Migration
              </Typography>
              <Typography variant="subtitle2" color="error" sx={{ mb: 1 }}>
                ❌ Old Syntax (will cause errors):
              </Typography>
              <Box
                component="pre"
                sx={{
                  bgcolor: 'grey.100',
                  p: 2,
                  borderRadius: 1,
                  overflow: 'auto',
                  fontSize: '0.875rem',
                  mb: 2
                }}
              >
                {`// This will cause "context is not defined" error
const result = context.input.message.toUpperCase();
context.variables.processedAt = new Date().toISOString();
return {
  processed: true,
  result: result,
  executionId: context.executionId
};`}
              </Box>

              <Typography variant="subtitle2" color="success.main" sx={{ mb: 1 }}>
                ✅ New Syntax (correct):
              </Typography>
              <Box
                component="pre"
                sx={{
                  bgcolor: 'success.50',
                  p: 2,
                  borderRadius: 1,
                  overflow: 'auto',
                  fontSize: '0.875rem'
                }}
              >
                {`// This is the correct syntax that works
const result = input.message.toUpperCase();
workflowContext.variables.processedAt = new Date().toISOString();
return {
  processed: true,
  result: result,
  executionId: workflowContext.executionId
};`}
              </Box>
            </CardContent>
          </Card>

          <Alert severity="info">
            <AlertTitle>How to Fix Your Workflow</AlertTitle>
            <Typography variant="body2">
              1. Click "Edit Workflow" to open the workflow editor
              <br />
              2. Look for JavaScript Action nodes in your workflow
              <br />
              3. Update the script code in each node to use the new syntax
              <br />
              4. Save the workflow
              <br />
              5. Test the workflow to ensure it works correctly
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetails(false)}>Close</Button>
          {onMigrate && (
            <Button
              variant="contained"
              onClick={() => {
                setShowDetails(false);
                onMigrate();
              }}
            >
              Edit Workflow Now
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default WorkflowMigrationHelper;
