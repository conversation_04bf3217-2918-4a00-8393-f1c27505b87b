import React from 'react';
import { Al<PERSON>, AlertTitle, Box, Button, Typography } from '@mui/material';

interface SimpleWorkflowWarningProps {
  workflowName: string;
  hasOldSyntax: boolean;
  onEdit?: () => void;
  onDismiss?: () => void;
}

const SimpleWorkflowWarning: React.FC<SimpleWorkflowWarningProps> = ({
  workflowName,
  hasOldSyntax,
  onEdit,
  onDismiss
}) => {
  if (!hasOldSyntax) {
    return null;
  }

  return (
    <Alert severity="warning" sx={{ mb: 2 }}>
      <AlertTitle>Zastaralá syntaxe JavaScriptu</AlertTitle>
      <Typography variant="body2" sx={{ mb: 2 }}>
        Workflow "{workflowName}" obsahuje starou syntaxi JavaScriptu a nebude fungovat správně.
      </Typography>
      <Typography variant="body2" sx={{ mb: 2, fontWeight: 'bold' }}>
        Doporučení: Smažte tento workflow a vytvořte nový pomocí aktuálních šablon.
      </Typography>
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        {onEdit && (
          <Button size="small" variant="contained" color="primary" onClick={onEdit}>
            Editovat workflow
          </Button>
        )}
        {onDismiss && (
          <Button size="small" variant="text" onClick={onDismiss}>
            Skrýt upozornění
          </Button>
        )}
      </Box>
    </Alert>
  );
};

export default SimpleWorkflowWarning;
