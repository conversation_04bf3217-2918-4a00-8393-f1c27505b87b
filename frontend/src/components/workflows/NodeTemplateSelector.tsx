import React, { useState } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Typography,
  ListSubheader,
  Tooltip
} from '@mui/material';
import { NodeTemplateService, NodeTemplate } from '../../services/NodeTemplateService';

interface NodeTemplateSelectorProps {
  onTemplateInsert: (template: NodeTemplate) => void;
  disabled?: boolean;
}

const NodeTemplateSelector: React.FC<NodeTemplateSelectorProps> = ({
  onTemplateInsert,
  disabled = false
}) => {
  const [selectedTemplateType, setSelectedTemplateType] = useState<string>('');

  const handleInsertNode = () => {
    if (!selectedTemplateType) return;

    const template = NodeTemplateService.createNodeFromTemplate(selectedTemplateType);
    if (template) {
      onTemplateInsert(template);
      setSelectedTemplateType(''); // Reset selection after insert
    }
  };

  const renderMenuItems = () => {
    const allTemplates = NodeTemplateService.getAllTemplates();
    const menuItems: React.ReactNode[] = [];

    // Define category display names and icons
    const categoryInfo = {
      trigger: { name: '🚀 Trigger Nodes', description: 'Start workflow execution' },
      action: { name: '⚡ Action Nodes', description: 'Perform operations' },
      logic: { name: '🧠 Logic Nodes', description: 'Control flow and decisions' },
      terminator: { name: '🏁 Terminator Nodes', description: 'End workflow and return results' }
    };

    Object.entries(allTemplates).forEach(([category, templates]) => {
      const categoryData = categoryInfo[category as keyof typeof categoryInfo];

      // Add category header
      menuItems.push(
        <ListSubheader key={`header-${category}`} sx={{ fontWeight: 'bold' }}>
          {categoryData.name}
        </ListSubheader>
      );

      // Add template items for this category
      templates.forEach((template) => {
        menuItems.push(
          <MenuItem key={template.type} value={template.type}>
            <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
              <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                {template.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {template.description}
              </Typography>
            </Box>
          </MenuItem>
        );
      });
    });

    return menuItems;
  };

  const getSelectedTemplate = () => {
    return NodeTemplateService.getTemplateByType(selectedTemplateType);
  };

  const selectedTemplate = getSelectedTemplate();

  return (
    <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-start', mb: 2 }}>
      <FormControl variant="outlined" sx={{ minWidth: 300, flex: 1 }}>
        <InputLabel id="node-template-select-label">Select Node Type</InputLabel>
        <Select
          labelId="node-template-select-label"
          id="node-template-select"
          value={selectedTemplateType}
          onChange={(e) => setSelectedTemplateType(e.target.value)}
          label="Select Node Type"
          disabled={disabled}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 400
              }
            }
          }}
        >
          <MenuItem value="" disabled>
            <em>Choose a node type...</em>
          </MenuItem>
          {renderMenuItems()}
        </Select>
      </FormControl>

      <Tooltip
        title={selectedTemplate ? `Insert ${selectedTemplate.name}` : 'Select a node type first'}
        arrow
      >
        <span>
          <Button
            variant="contained"
            onClick={handleInsertNode}
            disabled={disabled || !selectedTemplateType}
            sx={{
              height: '56px', // Match the Select height
              minWidth: '120px'
            }}
          >
            Insert Node
          </Button>
        </span>
      </Tooltip>

      {selectedTemplate && (
        <Box sx={{ ml: 2, maxWidth: 300 }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
            Selected: <strong>{selectedTemplate.name}</strong>
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {selectedTemplate.description}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default NodeTemplateSelector;
