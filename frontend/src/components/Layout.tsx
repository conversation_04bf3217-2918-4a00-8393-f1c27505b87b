import React, { useState, useEffect } from 'react';
import {
  AppBar,
  Box,
  CssBaseline,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Badge,
  Tooltip,
  Switch,
  FormControlLabel,
  Breadcrumbs,
  Link,
  Chip,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { useThemeMode } from '../theme/ThemeContext';
import { useNotificationHelpers } from './notifications/NotificationProvider';
import { GlobalSearch } from './search/GlobalSearch';

// Using simple text icons instead of @mui/icons-material
const MenuIcon = () => <span>☰</span>;
const DashboardIcon = () => <span>📊</span>;
const BusinessIcon = () => <span>🏢</span>;
const WorkflowIcon = () => <span>⚡</span>;
const SettingsIcon = () => <span>⚙️</span>;
const AccountCircle = () => <span>👤</span>;
const Notifications = () => <span>🔔</span>;
const Logout = () => <span>🚪</span>;
const AdminPanelSettings = () => <span>🛠️</span>;
const SearchIcon = () => <span>🔍</span>;
const LightModeIcon = () => <span>☀️</span>;
const DarkModeIcon = () => <span>🌙</span>;
const KeyboardIcon = () => <span>⌨️</span>;

const drawerWidth = 240;

interface LayoutProps {
  children: React.ReactNode;
  currentPage?: string;
  onPageChange?: (page: string) => void;
}

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactElement;
  path: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard'
  },
  {
    id: 'workflows',
    label: 'Workflows',
    icon: <WorkflowIcon />,
    path: '/workflows'
  },
  {
    id: 'tenants',
    label: 'Tenants',
    icon: <BusinessIcon />,
    path: '/tenants'
  }
];

const Layout: React.FC<LayoutProps> = ({ children, currentPage = 'dashboard', onPageChange }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchOpen, setSearchOpen] = useState(false);
  const [keyboardShortcutsOpen, setKeyboardShortcutsOpen] = useState(false);

  const { mode, toggleColorMode } = useThemeMode();
  const { showSuccess, showInfo } = useNotificationHelpers();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K for search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        setSearchOpen(true);
      }
      // Ctrl/Cmd + / for keyboard shortcuts
      if ((event.ctrlKey || event.metaKey) && event.key === '/') {
        event.preventDefault();
        setKeyboardShortcutsOpen(true);
      }
      // Ctrl/Cmd + D for dashboard
      if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
        event.preventDefault();
        handleNavigationClick('dashboard');
      }
      // Ctrl/Cmd + W for workflows
      if ((event.ctrlKey || event.metaKey) && event.key === 'w') {
        event.preventDefault();
        handleNavigationClick('workflows');
      }
      // Ctrl/Cmd + T for tenants
      if ((event.ctrlKey || event.metaKey) && event.key === 't') {
        event.preventDefault();
        handleNavigationClick('tenants');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNavigationClick = (page: string) => {
    if (onPageChange) {
      onPageChange(page);
    }
    setMobileOpen(false);
    showInfo(`Navigated to ${page}`);
  };

  const handleLogout = () => {
    handleProfileMenuClose();
    showSuccess('Logged out successfully');
    console.log('Logout clicked');
  };

  const handleThemeToggle = () => {
    toggleColorMode();
    showInfo(`Switched to ${mode === 'light' ? 'dark' : 'light'} mode`);
  };

  // Generate breadcrumbs based on current page
  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [{ label: 'Home', href: '/dashboard' }];

    switch (currentPage) {
      case 'workflows':
        breadcrumbs.push({ label: 'Workflows', current: true });
        break;
      case 'tenants':
        breadcrumbs.push({ label: 'Tenants', current: true });
        break;
      case 'users':
        breadcrumbs.push({ label: 'Users', current: true });
        break;
      case 'system':
        breadcrumbs.push({ label: 'System', current: true });
        break;
      default:
        breadcrumbs.push({ label: 'Dashboard', current: true });
    }

    return breadcrumbs;
  };

  const drawer = (
    <div>
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AdminPanelSettings />
          <Typography variant="h6" noWrap component="div" color="primary">
            MCP Admin
          </Typography>
        </Box>
      </Toolbar>
      <Divider />
      <List>
        {navigationItems.map((item) => (
          <ListItem key={item.id} disablePadding>
            <ListItemButton
              selected={currentPage === item.id}
              onClick={() => handleNavigationClick(item.id)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );

  return (
    <Box sx={{ display: 'flex', width: '100%', height: '100vh' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` }
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" noWrap component="div">
              Dynamic MCP Server
            </Typography>
            {!isMobile && (
              <Breadcrumbs
                aria-label="breadcrumb"
                sx={{
                  fontSize: '0.875rem',
                  '& .MuiBreadcrumbs-separator': {
                    color: 'inherit',
                    opacity: 0.7
                  }
                }}
              >
                {getBreadcrumbs().map((crumb, index) =>
                  crumb.current ? (
                    <Typography key={index} color="inherit" variant="body2">
                      {crumb.label}
                    </Typography>
                  ) : (
                    <Link
                      key={index}
                      color="inherit"
                      href={crumb.href}
                      onClick={(e) => {
                        e.preventDefault();
                        if (crumb.href === '/dashboard') {
                          handleNavigationClick('dashboard');
                        }
                      }}
                      sx={{ textDecoration: 'none', opacity: 0.8 }}
                    >
                      {crumb.label}
                    </Link>
                  )
                )}
              </Breadcrumbs>
            )}
          </Box>

          {/* Search */}
          <Tooltip title="Search (Ctrl+K)">
            <IconButton color="inherit" onClick={() => setSearchOpen(true)} sx={{ mr: 1 }}>
              <SearchIcon />
            </IconButton>
          </Tooltip>

          {/* Theme Toggle */}
          <Tooltip title={`Switch to ${mode === 'light' ? 'dark' : 'light'} mode`}>
            <IconButton color="inherit" onClick={handleThemeToggle} sx={{ mr: 1 }}>
              {mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}
            </IconButton>
          </Tooltip>

          {/* Keyboard Shortcuts */}
          <Tooltip title="Keyboard shortcuts (Ctrl+/)">
            <IconButton
              color="inherit"
              onClick={() => setKeyboardShortcutsOpen(true)}
              sx={{ mr: 1 }}
            >
              <KeyboardIcon />
            </IconButton>
          </Tooltip>

          {/* Notifications */}
          <Tooltip title="Notifications">
            <IconButton color="inherit" sx={{ mr: 1 }}>
              <Badge badgeContent={3} color="error">
                <Notifications />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* User Profile */}
          <Tooltip title="User menu">
            <IconButton
              size="large"
              edge="end"
              aria-label="account of current user"
              aria-controls="profile-menu"
              aria-haspopup="true"
              onClick={handleProfileMenuOpen}
              color="inherit"
            >
              <Avatar sx={{ width: 32, height: 32 }}>
                <AccountCircle />
              </Avatar>
            </IconButton>
          </Tooltip>
        </Toolbar>
      </AppBar>

      {/* Profile Menu */}
      <Menu
        id="profile-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        PaperProps={{
          sx: {
            minWidth: 200,
            mt: 1
          }
        }}
      >
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="subtitle2" color="text.secondary">
            Admin User
          </Typography>
          <Typography variant="body2" color="text.primary">
            <EMAIL>
          </Typography>
        </Box>
        <Divider />

        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon>
            <AccountCircle />
          </ListItemIcon>
          Profile
        </MenuItem>

        <MenuItem onClick={handleProfileMenuClose}>
          <ListItemIcon>
            <SettingsIcon />
          </ListItemIcon>
          Settings
        </MenuItem>

        <MenuItem>
          <ListItemIcon>{mode === 'light' ? <DarkModeIcon /> : <LightModeIcon />}</ListItemIcon>
          <FormControlLabel
            control={<Switch checked={mode === 'dark'} onChange={handleThemeToggle} size="small" />}
            label={`${mode === 'light' ? 'Dark' : 'Light'} mode`}
            sx={{ ml: 0, mr: 0 }}
          />
        </MenuItem>

        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <Logout />
          </ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
        aria-label="navigation"
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true // Better open performance on mobile.
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth }
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          height: '100vh',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Toolbar />
        <Box sx={{ flex: 1, overflow: 'auto' }}>{children}</Box>
      </Box>

      {/* Global Search Dialog */}
      <GlobalSearch open={searchOpen} onClose={() => setSearchOpen(false)} />

      {/* Keyboard Shortcuts Dialog */}
      <Menu
        open={keyboardShortcutsOpen}
        onClose={() => setKeyboardShortcutsOpen(false)}
        anchorReference="anchorPosition"
        anchorPosition={{ top: 100, left: window.innerWidth / 2 - 200 }}
        PaperProps={{
          sx: {
            minWidth: 400,
            maxWidth: 500
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Keyboard Shortcuts
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">Search</Typography>
              <Chip label="Ctrl + K" size="small" variant="outlined" />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">Dashboard</Typography>
              <Chip label="Ctrl + D" size="small" variant="outlined" />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">Workflows</Typography>
              <Chip label="Ctrl + W" size="small" variant="outlined" />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">Tenants</Typography>
              <Chip label="Ctrl + T" size="small" variant="outlined" />
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">Shortcuts</Typography>
              <Chip label="Ctrl + /" size="small" variant="outlined" />
            </Box>
          </Box>
        </Box>
      </Menu>
    </Box>
  );
};

export default Layout;
