import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Skeleton,
  Stack,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar
} from '@mui/material';

// Table Skeleton
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({
  rows = 10,
  columns = 5
}) => (
  <Paper sx={{ width: '100%', overflow: 'hidden' }}>
    {/* Header */}
    <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
      <Skeleton variant="text" width="30%" height={32} />
    </Box>

    {/* Table Header */}
    <Box sx={{ display: 'flex', p: 1, borderBottom: 1, borderColor: 'divider' }}>
      {Array.from({ length: columns }).map((_, index) => (
        <Box key={index} sx={{ flex: 1, px: 1 }}>
          <Skeleton variant="text" width="80%" height={24} />
        </Box>
      ))}
    </Box>

    {/* Table Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <Box key={rowIndex} sx={{ display: 'flex', p: 1, borderBottom: 1, borderColor: 'divider' }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Box key={colIndex} sx={{ flex: 1, px: 1 }}>
            <Skeleton variant="text" width={`${60 + Math.random() * 30}%`} height={20} />
          </Box>
        ))}
      </Box>
    ))}
  </Paper>
);

// Card Grid Skeleton
export const CardGridSkeleton: React.FC<{ cards?: number; columns?: number }> = ({
  cards = 6,
  columns = 3
}) => (
  <Grid container spacing={3}>
    {Array.from({ length: cards }).map((_, index) => (
      <Grid key={index} size={{ xs: 12, sm: 6, md: 12 / columns }}>
        <Card>
          <CardContent>
            <Stack spacing={2}>
              <Skeleton variant="text" width="60%" height={28} />
              <Skeleton variant="text" width="100%" height={20} />
              <Skeleton variant="text" width="80%" height={20} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Skeleton variant="rectangular" width={80} height={32} />
                <Skeleton variant="rectangular" width={60} height={32} />
              </Box>
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    ))}
  </Grid>
);

// List Skeleton
export const ListSkeleton: React.FC<{ items?: number; showAvatar?: boolean }> = ({
  items = 8,
  showAvatar = true
}) => (
  <List>
    {Array.from({ length: items }).map((_, index) => (
      <ListItem key={index}>
        {showAvatar && (
          <ListItemAvatar>
            <Skeleton variant="circular" width={40} height={40} />
          </ListItemAvatar>
        )}
        <ListItemText
          primary={<Skeleton variant="text" width="40%" height={24} />}
          secondary={<Skeleton variant="text" width="70%" height={20} />}
        />
        <Skeleton variant="rectangular" width={60} height={24} />
      </ListItem>
    ))}
  </List>
);

// Dashboard Skeleton
export const DashboardSkeleton: React.FC = () => (
  <Box sx={{ p: 3 }}>
    {/* Header */}
    <Box sx={{ mb: 4 }}>
      <Skeleton variant="text" width="30%" height={40} />
      <Skeleton variant="text" width="50%" height={24} />
    </Box>

    {/* Stats Cards */}
    <Grid container spacing={3} sx={{ mb: 4 }}>
      {Array.from({ length: 4 }).map((_, index) => (
        <Grid key={index} size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Stack spacing={1}>
                <Box
                  sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Skeleton variant="text" width="60%" height={20} />
                  <Skeleton variant="circular" width={24} height={24} />
                </Box>
                <Skeleton variant="text" width="40%" height={32} />
                <Skeleton variant="text" width="80%" height={16} />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>

    {/* Charts */}
    <Grid container spacing={3}>
      <Grid size={{ xs: 12, md: 8 }}>
        <Card>
          <CardContent>
            <Skeleton variant="text" width="30%" height={28} />
            <Skeleton variant="rectangular" width="100%" height={300} sx={{ mt: 2 }} />
          </CardContent>
        </Card>
      </Grid>
      <Grid size={{ xs: 12, md: 4 }}>
        <Card>
          <CardContent>
            <Skeleton variant="text" width="40%" height={28} />
            <Stack spacing={2} sx={{ mt: 2 }}>
              {Array.from({ length: 5 }).map((_, index) => (
                <Box
                  key={index}
                  sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Skeleton variant="text" width="50%" height={20} />
                  <Skeleton variant="text" width="20%" height={20} />
                </Box>
              ))}
            </Stack>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  </Box>
);

// Workflow Editor Skeleton
export const WorkflowEditorSkeleton: React.FC = () => (
  <Box sx={{ height: '100vh', display: 'flex' }}>
    {/* Sidebar */}
    <Box sx={{ width: 300, borderRight: 1, borderColor: 'divider', p: 2 }}>
      <Skeleton variant="text" width="60%" height={28} sx={{ mb: 2 }} />
      <Stack spacing={1}>
        {Array.from({ length: 8 }).map((_, index) => (
          <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Skeleton variant="rectangular" width={40} height={40} />
            <Box sx={{ flex: 1 }}>
              <Skeleton variant="text" width="70%" height={20} />
              <Skeleton variant="text" width="50%" height={16} />
            </Box>
          </Box>
        ))}
      </Stack>
    </Box>

    {/* Main Canvas */}
    <Box sx={{ flex: 1, p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Skeleton variant="text" width="30%" height={32} />
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Skeleton variant="rectangular" width={80} height={32} />
          <Skeleton variant="rectangular" width={80} height={32} />
          <Skeleton variant="rectangular" width={80} height={32} />
        </Box>
      </Box>

      {/* Canvas */}
      <Box
        sx={{
          height: 'calc(100vh - 120px)',
          border: 1,
          borderColor: 'divider',
          borderRadius: 1,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Nodes */}
        {Array.from({ length: 5 }).map((_, index) => (
          <Box
            key={index}
            sx={{
              position: 'absolute',
              top: `${20 + index * 15}%`,
              left: `${10 + index * 15}%`
            }}
          >
            <Skeleton variant="rectangular" width={120} height={80} />
          </Box>
        ))}

        {/* Connections */}
        <svg style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
          {Array.from({ length: 3 }).map((_, index) => (
            <line
              key={index}
              x1={`${15 + index * 15}%`}
              y1={`${25 + index * 15}%`}
              x2={`${25 + index * 15}%`}
              y2={`${35 + index * 15}%`}
              stroke="currentColor"
              strokeWidth="2"
              opacity="0.3"
            />
          ))}
        </svg>
      </Box>
    </Box>

    {/* Properties Panel */}
    <Box sx={{ width: 300, borderLeft: 1, borderColor: 'divider', p: 2 }}>
      <Skeleton variant="text" width="50%" height={28} sx={{ mb: 2 }} />
      <Stack spacing={2}>
        {Array.from({ length: 6 }).map((_, index) => (
          <Box key={index}>
            <Skeleton variant="text" width="40%" height={20} sx={{ mb: 1 }} />
            <Skeleton variant="rectangular" width="100%" height={40} />
          </Box>
        ))}
      </Stack>
    </Box>
  </Box>
);

// Form Skeleton
export const FormSkeleton: React.FC<{ fields?: number }> = ({ fields = 6 }) => (
  <Box sx={{ p: 3 }}>
    <Skeleton variant="text" width="40%" height={32} sx={{ mb: 3 }} />

    <Stack spacing={3}>
      {Array.from({ length: fields }).map((_, index) => (
        <Box key={index}>
          <Skeleton variant="text" width="30%" height={20} sx={{ mb: 1 }} />
          <Skeleton variant="rectangular" width="100%" height={56} />
        </Box>
      ))}
    </Stack>

    <Box sx={{ display: 'flex', gap: 2, mt: 4 }}>
      <Skeleton variant="rectangular" width={100} height={36} />
      <Skeleton variant="rectangular" width={80} height={36} />
    </Box>
  </Box>
);

// Page Skeleton
export const PageSkeleton: React.FC<{
  type?: 'dashboard' | 'table' | 'form' | 'workflow' | 'cards';
}> = ({ type = 'dashboard' }) => {
  switch (type) {
    case 'dashboard':
      return <DashboardSkeleton />;
    case 'table':
      return <TableSkeleton />;
    case 'form':
      return <FormSkeleton />;
    case 'workflow':
      return <WorkflowEditorSkeleton />;
    case 'cards':
      return <CardGridSkeleton />;
    default:
      return <DashboardSkeleton />;
  }
};

export default {
  TableSkeleton,
  CardGridSkeleton,
  ListSkeleton,
  DashboardSkeleton,
  WorkflowEditorSkeleton,
  FormSkeleton,
  PageSkeleton
};
