import React, { useState, useMemo } from 'react';
import {
  DataGrid,
  GridRowsProp,
  GridToolbar,
  GridFilterModel,
  GridSortModel,
  GridRowParams,
  GridActionsCellItem,
  GridColDef
} from '@mui/x-data-grid';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  Button,
  Menu,
  MenuItem,
  Stack
} from '@mui/material';
import { useNotificationHelpers } from '../notifications/NotificationProvider';

// Simple text icons
const ExportIcon = () => <span>📤</span>;
const RefreshIcon = () => <span>🔄</span>;
const DeleteIcon = () => <span>🗑️</span>;
const EditIcon = () => <span>✏️</span>;
const ViewIcon = () => <span>👁️</span>;
const MoreIcon = () => <span>⋯</span>;

export interface EnhancedDataTableColumn {
  field: string;
  headerName: string;
  width?: number;
  filterable?: boolean;
  exportable?: boolean;
  sortable?: boolean;
  renderCell?: (params: any) => React.ReactNode;
  type?: string;
  getActions?: (params: any) => React.ReactNode[];
}

export interface EnhancedDataTableProps {
  columns: EnhancedDataTableColumn[];
  rows: GridRowsProp;
  title?: string;
  loading?: boolean;
  onRowClick?: (params: GridRowParams) => void;
  onRowEdit?: (id: string | number) => void;
  onRowDelete?: (id: string | number) => void;
  onRowView?: (id: string | number) => void;
  onBulkDelete?: (ids: (string | number)[]) => void;
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void;
  onRefresh?: () => void;
  enableSelection?: boolean;
  enableBulkActions?: boolean;
  enableExport?: boolean;
  pageSize?: number;
  height?: number | string;
  customActions?: Array<{
    icon: React.ReactNode;
    label: string;
    onClick: (id: string | number) => void;
    tooltip?: string;
  }>;
}

export const EnhancedDataTable: React.FC<EnhancedDataTableProps> = ({
  columns,
  rows,
  title,
  loading = false,
  onRowClick,
  onRowEdit,
  onRowDelete,
  onRowView,
  onBulkDelete,
  onExport,
  onRefresh,
  enableSelection = true,
  enableBulkActions = true,
  enableExport = true,
  pageSize = 25,
  height = 600,
  customActions = []
}) => {
  const [selectionModel, setSelectionModel] = useState<(string | number)[]>([]);
  const [filterModel, setFilterModel] = useState<GridFilterModel>({ items: [] });
  const [sortModel, setSortModel] = useState<GridSortModel>([]);
  const [exportMenuAnchor, setExportMenuAnchor] = useState<null | HTMLElement>(null);
  const [bulkMenuAnchor, setBulkMenuAnchor] = useState<null | HTMLElement>(null);

  const { showSuccess, showInfo } = useNotificationHelpers();

  // Enhanced columns with actions
  const enhancedColumns = useMemo(() => {
    const cols: GridColDef[] = columns.map((col) => ({
      field: col.field,
      headerName: col.headerName,
      width: col.width,
      renderCell: col.renderCell,
      sortable: col.sortable,
      filterable: col.filterable
    }));

    // Add actions column if any action handlers are provided
    if (onRowEdit || onRowDelete || onRowView || customActions.length > 0) {
      cols.push({
        field: 'actions',
        type: 'actions',
        headerName: 'Actions',
        width: 120,
        getActions: (params: any) => {
          const actions = [];

          if (onRowView) {
            actions.push(
              <GridActionsCellItem
                icon={<ViewIcon />}
                label="View"
                onClick={() => onRowView(params.id)}
                showInMenu
              />
            );
          }

          if (onRowEdit) {
            actions.push(
              <GridActionsCellItem
                icon={<EditIcon />}
                label="Edit"
                onClick={() => onRowEdit(params.id)}
                showInMenu
              />
            );
          }

          customActions.forEach((action, index) => {
            actions.push(
              <GridActionsCellItem
                key={index}
                icon={action.icon}
                label={action.label}
                onClick={() => action.onClick(params.id)}
                showInMenu
              />
            );
          });

          if (onRowDelete) {
            actions.push(
              <GridActionsCellItem
                icon={<DeleteIcon />}
                label="Delete"
                onClick={() => handleRowDelete(params.id)}
                showInMenu
              />
            );
          }

          return actions;
        }
      });
    }

    return cols;
  }, [columns, onRowEdit, onRowDelete, onRowView, customActions]);

  const handleRowDelete = (id: string | number) => {
    if (onRowDelete) {
      onRowDelete(id);
      showSuccess('Item deleted successfully');
    }
  };

  const handleBulkDelete = () => {
    if (onBulkDelete && selectionModel.length > 0) {
      onBulkDelete(selectionModel);
      setSelectionModel([]);
      setBulkMenuAnchor(null);
      showSuccess(`${selectionModel.length} items deleted successfully`);
    }
  };

  const handleExport = (format: 'csv' | 'excel' | 'pdf') => {
    if (onExport) {
      onExport(format);
      setExportMenuAnchor(null);
      showInfo(`Exporting data as ${format.toUpperCase()}...`);
    }
  };

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
      showInfo('Data refreshed');
    }
  };

  return (
    <Paper sx={{ height, width: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          {title && (
            <Typography variant="h6" component="h2">
              {title}
            </Typography>
          )}

          <Stack direction="row" spacing={1}>
            {/* Bulk Actions */}
            {enableBulkActions && selectionModel.length > 0 && (
              <>
                <Chip
                  label={`${selectionModel.length} selected`}
                  color="primary"
                  variant="outlined"
                />
                <Button
                  size="small"
                  onClick={(e) => setBulkMenuAnchor(e.currentTarget)}
                  endIcon={<MoreIcon />}
                >
                  Actions
                </Button>
              </>
            )}

            {/* Export */}
            {enableExport && (
              <Tooltip title="Export data">
                <IconButton onClick={(e) => setExportMenuAnchor(e.currentTarget)}>
                  <ExportIcon />
                </IconButton>
              </Tooltip>
            )}

            {/* Refresh */}
            {onRefresh && (
              <Tooltip title="Refresh data">
                <IconButton onClick={handleRefresh}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            )}
          </Stack>
        </Box>

        {/* Filters Summary */}
        {filterModel.items.length > 0 && (
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {filterModel.items.map((item, index) => (
              <Chip
                key={index}
                label={`${item.field}: ${item.value}`}
                size="small"
                onDelete={() => {
                  const newItems = filterModel.items.filter((_, i) => i !== index);
                  setFilterModel({ items: newItems });
                }}
              />
            ))}
          </Box>
        )}
      </Box>

      {/* Data Grid */}
      <Box sx={{ flex: 1 }}>
        <DataGrid
          rows={rows}
          columns={enhancedColumns}
          initialState={{
            pagination: { paginationModel: { pageSize } }
          }}
          pageSizeOptions={[10, 25, 50, 100]}
          checkboxSelection={enableSelection}
          disableRowSelectionOnClick
          loading={loading}
          filterModel={filterModel}
          onFilterModelChange={setFilterModel}
          sortModel={sortModel}
          onSortModelChange={setSortModel}
          rowSelectionModel={selectionModel as any}
          onRowSelectionModelChange={(newSelection) =>
            setSelectionModel(newSelection as unknown as (string | number)[])
          }
          onRowClick={onRowClick}
          slots={{
            toolbar: GridToolbar
          }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 500 }
            }
          }}
          sx={{
            border: 0,
            '& .MuiDataGrid-cell:focus': {
              outline: 'none'
            },
            '& .MuiDataGrid-row:hover': {
              backgroundColor: 'action.hover'
            }
          }}
        />
      </Box>

      {/* Export Menu */}
      <Menu
        anchorEl={exportMenuAnchor}
        open={Boolean(exportMenuAnchor)}
        onClose={() => setExportMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleExport('csv')}>Export as CSV</MenuItem>
        <MenuItem onClick={() => handleExport('excel')}>Export as Excel</MenuItem>
        <MenuItem onClick={() => handleExport('pdf')}>Export as PDF</MenuItem>
      </Menu>

      {/* Bulk Actions Menu */}
      <Menu
        anchorEl={bulkMenuAnchor}
        open={Boolean(bulkMenuAnchor)}
        onClose={() => setBulkMenuAnchor(null)}
      >
        {onBulkDelete && (
          <MenuItem onClick={handleBulkDelete}>
            <DeleteIcon /> Delete Selected
          </MenuItem>
        )}
      </Menu>
    </Paper>
  );
};

export default EnhancedDataTable;
