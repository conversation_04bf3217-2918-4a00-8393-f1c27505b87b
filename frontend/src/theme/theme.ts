import { createTheme, Theme, PaletteMode } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';

// Material Design 3 Color Tokens
const colorTokens = {
  light: {
    primary: {
      main: '#1976d2',
      light: '#42a5f5',
      dark: '#1565c0',
      contrastText: '#ffffff'
    },
    secondary: {
      main: '#dc004e',
      light: '#ff5983',
      dark: '#9a0036',
      contrastText: '#ffffff'
    },
    error: {
      main: '#d32f2f',
      light: '#ef5350',
      dark: '#c62828',
      contrastText: '#ffffff'
    },
    warning: {
      main: '#ed6c02',
      light: '#ff9800',
      dark: '#e65100',
      contrastText: '#ffffff'
    },
    info: {
      main: '#0288d1',
      light: '#03dac6',
      dark: '#01579b',
      contrastText: '#ffffff'
    },
    success: {
      main: '#2e7d32',
      light: '#4caf50',
      dark: '#1b5e20',
      contrastText: '#ffffff'
    },
    background: {
      default: '#fafafa',
      paper: '#ffffff'
    },
    text: {
      primary: 'rgba(0, 0, 0, 0.87)',
      secondary: 'rgba(0, 0, 0, 0.6)',
      disabled: 'rgba(0, 0, 0, 0.38)'
    },
    divider: 'rgba(0, 0, 0, 0.12)'
  },
  dark: {
    primary: {
      main: '#90caf9',
      light: '#e3f2fd',
      dark: '#42a5f5',
      contrastText: '#000000'
    },
    secondary: {
      main: '#f48fb1',
      light: '#fce4ec',
      dark: '#ad1457',
      contrastText: '#000000'
    },
    error: {
      main: '#f44336',
      light: '#e57373',
      dark: '#d32f2f',
      contrastText: '#ffffff'
    },
    warning: {
      main: '#ffa726',
      light: '#ffb74d',
      dark: '#f57c00',
      contrastText: '#000000'
    },
    info: {
      main: '#29b6f6',
      light: '#4fc3f7',
      dark: '#0288d1',
      contrastText: '#000000'
    },
    success: {
      main: '#66bb6a',
      light: '#81c784',
      dark: '#388e3c',
      contrastText: '#000000'
    },
    background: {
      default: '#121212',
      paper: '#1e1e1e'
    },
    text: {
      primary: '#ffffff',
      secondary: 'rgba(255, 255, 255, 0.7)',
      disabled: 'rgba(255, 255, 255, 0.5)'
    },
    divider: 'rgba(255, 255, 255, 0.12)'
  }
};

// Typography scale based on Material Design 3
const typography = {
  fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
  h1: {
    fontSize: '3.5rem',
    fontWeight: 300,
    lineHeight: 1.167,
    letterSpacing: '-0.01562em'
  },
  h2: {
    fontSize: '2.25rem',
    fontWeight: 300,
    lineHeight: 1.2,
    letterSpacing: '-0.00833em'
  },
  h3: {
    fontSize: '1.875rem',
    fontWeight: 400,
    lineHeight: 1.167,
    letterSpacing: '0em'
  },
  h4: {
    fontSize: '1.5rem',
    fontWeight: 400,
    lineHeight: 1.235,
    letterSpacing: '0.00735em'
  },
  h5: {
    fontSize: '1.25rem',
    fontWeight: 400,
    lineHeight: 1.334,
    letterSpacing: '0em'
  },
  h6: {
    fontSize: '1.125rem',
    fontWeight: 500,
    lineHeight: 1.6,
    letterSpacing: '0.0075em'
  },
  subtitle1: {
    fontSize: '1rem',
    fontWeight: 400,
    lineHeight: 1.75,
    letterSpacing: '0.00938em'
  },
  subtitle2: {
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: 1.57,
    letterSpacing: '0.00714em'
  },
  body1: {
    fontSize: '1rem',
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: '0.00938em'
  },
  body2: {
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: 1.43,
    letterSpacing: '0.01071em'
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: 1.75,
    letterSpacing: '0.02857em',
    textTransform: 'uppercase' as const
  },
  caption: {
    fontSize: '0.75rem',
    fontWeight: 400,
    lineHeight: 1.66,
    letterSpacing: '0.03333em'
  },
  overline: {
    fontSize: '0.75rem',
    fontWeight: 400,
    lineHeight: 2.66,
    letterSpacing: '0.08333em',
    textTransform: 'uppercase' as const
  }
};

// Enhanced breakpoints for responsive design
const breakpoints = {
  values: {
    xs: 0,
    sm: 600,
    md: 900,
    lg: 1200,
    xl: 1536
  }
};

// Enhanced spacing scale
const spacing = (factor: number) => `${0.25 * factor}rem`;

// Enhanced shadows for Material Design 3
const shadows = [
  'none',
  '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)',
  '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15)',
  '0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)',
  '0px 2px 3px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15)',
  '0px 4px 4px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)',
  '0px 6px 10px rgba(0, 0, 0, 0.3), 0px 20px 25px 5px rgba(0, 0, 0, 0.15)',
  '0px 8px 10px rgba(0, 0, 0, 0.3), 0px 30px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 10px 14px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 12px 17px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 14px 20px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 16px 24px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 18px 28px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 20px 32px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 22px 36px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 24px 40px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 26px 44px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 28px 48px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 30px 52px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 32px 56px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 34px 60px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 36px 64px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 38px 68px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 40px 72px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)',
  '0px 42px 76px rgba(0, 0, 0, 0.3), 0px 26px 50px 12px rgba(0, 0, 0, 0.15)'
];

export const createAppTheme = (mode: PaletteMode): Theme => {
  const colors = colorTokens[mode];

  return createTheme({
    palette: {
      mode,
      ...colors
    },
    typography,
    breakpoints,
    spacing,
    shadows: shadows as any,
    shape: {
      borderRadius: 12 // More rounded corners for MD3
    },
    components: {
      // Button overrides for Material Design 3
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 20,
            textTransform: 'none',
            fontWeight: 500,
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)'
            }
          },
          contained: {
            '&:hover': {
              boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)'
            }
          }
        }
      },

      // Card overrides
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            boxShadow:
              mode === 'light'
                ? '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)'
                : '0px 2px 4px rgba(0, 0, 0, 0.5), 0px 2px 6px 2px rgba(0, 0, 0, 0.25)',
            '&:hover': {
              boxShadow:
                mode === 'light'
                  ? '0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15)'
                  : '0px 4px 8px rgba(0, 0, 0, 0.5), 0px 4px 12px 6px rgba(0, 0, 0, 0.25)'
            }
          }
        }
      },

      // Paper overrides
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: 12
          }
        }
      },

      // Chip overrides
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            fontWeight: 500
          }
        }
      },

      // TextField overrides
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 12
            }
          }
        }
      },

      // AppBar overrides
      MuiAppBar: {
        styleOverrides: {
          root: {
            boxShadow:
              mode === 'light'
                ? '0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)'
                : '0px 2px 4px rgba(0, 0, 0, 0.5), 0px 2px 6px 2px rgba(0, 0, 0, 0.25)'
          }
        }
      },

      // Drawer overrides
      MuiDrawer: {
        styleOverrides: {
          paper: {
            borderRadius: 0,
            borderRight:
              mode === 'light'
                ? '1px solid rgba(0, 0, 0, 0.12)'
                : '1px solid rgba(255, 255, 255, 0.12)'
          }
        }
      },

      // List overrides
      MuiListItemButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            margin: '2px 8px',
            '&.Mui-selected': {
              backgroundColor: alpha(colors.primary.main, 0.12),
              '&:hover': {
                backgroundColor: alpha(colors.primary.main, 0.16)
              }
            }
          }
        }
      },

      // Menu overrides
      MuiMenu: {
        styleOverrides: {
          paper: {
            borderRadius: 12,
            boxShadow:
              mode === 'light'
                ? '0px 4px 4px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15)'
                : '0px 8px 16px rgba(0, 0, 0, 0.5), 0px 8px 24px 12px rgba(0, 0, 0, 0.25)'
          }
        }
      },

      // Dialog overrides
      MuiDialog: {
        styleOverrides: {
          paper: {
            borderRadius: 16
          }
        }
      },

      // Snackbar overrides
      MuiSnackbar: {
        styleOverrides: {
          root: {
            '& .MuiSnackbarContent-root': {
              borderRadius: 12
            }
          }
        }
      }
    }
  });
};

export default createAppTheme;
