# Dynamic MCP Server

A dynamic Model Context Protocol (MCP) server implementation with hot-reload capabilities, multi-tenancy support, and comprehensive workflow management.

## Overview

This project implements a custom MCP server that follows the Model Context Protocol specification. The server provides dynamic function loading, multi-tenancy, workflow management, and a React-based admin interface.

## Features

- **MCP Protocol Implementation**: Full implementation of the Anthropic Model Context Protocol
- **Streamable HTTP Transport**: Support for Streamable HTTP transport for web-based clients
- **Tool Management**: Registration, discovery, and execution of tools with validation
- **Memory Management**: Short-term memory storage in Redis with TTL
- **Prompt Management**: Dynamic generation of system prompts
- **Docker Support**: Containerized deployment for easy scaling
- **MCP Inspector Integration**: Development tools for testing and debugging
- **Workflow Engine**: Support for workflow-based functions with sequential execution
- **Dynamic Function Loading**: Load functions and workflows from a database
- **JavaScript VM**: Secure execution of JavaScript code in a sandboxed environment

## Getting Started

### Prerequisites

- Node.js 22 LTS
- Yarn package manager
- <PERSON><PERSON> and <PERSON><PERSON> Compose
- Redis (for development, included in Docker Compose)

### Installation

1. Clone the repository:

   ```bash
   git clone https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver.git
   cd aidcc-ccczautomationmcpserver
   ```

2. Install dependencies:

   ```bash
   yarn install
   ```

3. Build the project:
   ```bash
   yarn build
   ```

### Running with Kubernetes

The application is designed to be deployed to Kubernetes using Helm charts:

1. Deploy to development environment:

   ```bash
   helm upgrade --install aidcc-ccczautomationmcpserver ./charts/ccczautomationmcpserver --values ./charts/ccczautomationmcpserver/values-dev.yaml
   ```

2. The server will be available at the configured ingress host.

### Running Locally

1. Build the server:

   ```bash
   yarn build
   ```

2. Start the server:

   ```bash
   yarn start
   ```

3. The server will be available at http://localhost:3000/mcp

## Development

### Development Environment Setup

The project uses environment variables for configuration. These can be set in the following ways:

1. System environment variables
2. `.env` file in the project root
3. `.env.development` file for development-specific settings (overrides `.env`)

The `.env.development` file contains all the necessary configuration for development and is automatically loaded when `NODE_ENV=development` (which is the default).

### Environment Variables

| Variable                      | Description                                 | Default Value         |
| ----------------------------- | ------------------------------------------- | --------------------- |
| NODE_ENV                      | Environment (development, production, test) | development           |
| PORT                          | MCP server port                             | 7000                  |
| HOST                          | MCP server host                             | 0.0.0.0               |
| API_PORT                      | API server port                             | 7001                  |
| LOG_LEVEL                     | Log level (debug, info, warn, error)        | info                  |
| DB_HOST                       | PostgreSQL host                             | localhost             |
| DB_PORT                       | PostgreSQL port                             | 5432                  |
| DB_USERNAME                   | PostgreSQL username                         | postgres              |
| DB_PASSWORD                   | PostgreSQL password                         | postgres              |
| DB_DATABASE                   | PostgreSQL database name                    | mcp_server            |
| DB_MIGRATIONS_RUN             | Whether to run migrations on startup        | false                 |
| REDIS_HOST                    | Redis host                                  | localhost             |
| REDIS_PORT                    | Redis port                                  | 6379                  |
| REDIS_PASSWORD                | Redis password                              |                       |
| USE_REDIS_WORKFLOW_MEMORY     | Whether to use Redis for workflow memory    | false                 |
| WORKFLOW_CONTEXT_TTL          | Workflow context TTL in seconds             | 3600                  |
| DATA_RETENTION_DAYS           | Data retention period in days               | 5                     |
| DATA_CLEANUP_INTERVAL_MINUTES | Data cleanup interval in minutes            | 60                    |
| DATA_CLEANUP_BATCH_SIZE       | Data cleanup batch size                     | 1000                  |
| LITELLM_API_KEY               | LiteLLM API key                             |                       |
| LITELLM_BASE_URL              | LiteLLM base URL                            | http://localhost:8000 |
| ADMIN_PORT                    | Admin API server port                       | 7002                  |
| JWT_SECRET                    | JWT token secret key                        | your-secret-key       |
| JWT_EXPIRES_IN                | JWT token expiration time                   | 24h                   |

### Development Scripts

The following scripts are available for development:

| Script                  | Description                                                                              |
| ----------------------- | ---------------------------------------------------------------------------------------- |
| `yarn build`            | Build the project                                                                        |
| `yarn start`            | Start the server (production)                                                            |
| `yarn dev`              | Start the server in development mode                                                     |
| `yarn dev:migrate`      | Start the server in development mode with automatic migrations                           |
| `yarn migration:run`    | Run database migrations                                                                  |
| `yarn migration:revert` | Revert the last database migration                                                       |
| `yarn setup`            | Set up the development environment (create database, run migrations, create sample data) |
| `yarn create-admin`     | Create default admin user (runs create-admin-user.ts script)                             |

### Setting Up the Development Environment

1. Make sure you have PostgreSQL and Redis running:

   ```bash
   # PostgreSQL
   docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=postgres postgres

   # Redis
   docker run -d --name redis -p 6379:6379 redis
   ```

2. Install dependencies:

   ```bash
   yarn install
   ```

3. Set up the development environment:

   ```bash
   yarn setup
   ```

4. Start the server with automatic migrations:
   ```bash
   yarn dev:migrate
   ```

### Development Mode

Run the server in development mode with hot reloading:

```bash
yarn dev
```

### Using MCP Inspector

The MCP Inspector is not currently integrated directly into the server. To use it for testing and debugging:

1. Build the server:

   ```bash
   yarn build
   ```

2. Start the server in one command window:

   ```bash
   yarn run dev
   ```

3. In a separate command window, run the MCP Inspector pointing to the server:

   ```bash
   npx @modelcontextprotocol/inspector node ./dist/server/index.js
   ```

4. The Inspector will connect to your running server and provide a testing interface.
   - Transport type = Streamable HTTP
   - Server URL = http://localhost:3000/mcp

### Database Migrations

Database migrations are managed by TypeORM. By default, migrations are not run automatically on server startup. To enable automatic migrations, set `DB_MIGRATIONS_RUN=true` in your environment or use the `yarn dev:migrate` script.

To manually run migrations:

```bash
yarn migration:run
```

To create a new migration:

```bash
yarn migration:generate src/infrastructure/database/migrations/MigrationName
```

### Using the Workflow Engine

The server includes a workflow engine that allows for the execution of workflows defined in the database. Each workflow consists of a series of nodes connected by edges.

1. Create a sample workflow:

   ```bash
   yarn create-sample-workflow
   ```

2. Test the workflow directly:

   ```bash
   yarn test-workflow <workflow-id> '{"message":"Hello, world!"}'
   ```

3. Use the workflow through the MCP Inspector by calling the corresponding function.

### Creating Default Admin User

Before accessing the admin interface, you need to create a default tenant and admin user:

```bash
node -r ts-node/register scripts/create-admin-user.ts
```

This script will create:

- **Default tenant**: `default`
- **Admin user**: `admin` with password `admin123`

If the tenant and user already exist, the script will display the existing credentials.

### Admin Interface Access

The server provides a web-based admin interface for managing tenants, users, workflows, and system settings.

#### Admin API Endpoints

- **Base URL**: `http://localhost:7002` (configurable via `ADMIN_PORT` environment variable)
- **Authentication**: JWT token-based
- **Frontend**: React-based UI (if running on port 7005)

#### Login Process

1. **Get JWT Token** via login endpoint:

```bash
curl -X POST http://localhost:7002/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "tenantName": "default",
    "username": "admin",
    "password": "admin123"
  }'
```

2. **Use Token** for admin API calls:

```bash
curl -X GET http://localhost:7002/admin/dashboard/stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

#### Available Admin Endpoints

- `/auth/login` - User authentication
- `/auth/logout` - User logout
- `/auth/validate` - Token validation
- `/auth/refresh` - Token refresh
- `/admin/dashboard/stats` - Dashboard statistics
- `/admin/dashboard/tenant-metrics` - Tenant metrics
- `/admin/tenants` - Tenant management
- `/admin/users` - User management
- `/admin/workflows` - Workflow management
- `/admin/system` - System settings

#### Metrics Endpoints

The system exposes metrics on two different servers:

**Prometheus Metrics (API Server - Port 7001):**

```bash
curl http://localhost:7001/metrics
```

- Standard Prometheus format (text/plain)
- Node.js system metrics (CPU, memory, garbage collection)
- Workflow execution metrics
- Node execution metrics
- Custom application metrics

**Performance Metrics (MCP Server - Port 7000):**

```bash
curl http://localhost:7000/metrics
curl http://localhost:7000/metrics?workflowId=workflow-id
```

- JSON format
- Workflow performance statistics
- Execution times and success rates
- Optional filtering by workflow ID

#### Default Credentials

After running the setup script, use these credentials to access the admin interface:

- **Tenant**: `default`
- **Username**: `admin`
- **Password**: `admin123`

### Multi-tenancy Demo

To see the multi-tenancy features in action, run the demo script:

```bash
node -r ts-node/register examples/multitenancy-demo.ts
```

This demo creates sample tenants and users, demonstrates tenant isolation, authentication, and various multi-tenancy features. Note that demo data is cleaned up automatically at the end.

### Troubleshooting Admin Access

If you encounter "Access denied. No token provided." error when accessing admin endpoints:

1. **Ensure admin user exists**:

   ```bash
   yarn create-admin
   ```

2. **Verify server is running**:

   - MCP Server: `http://localhost:7000` (or your configured PORT)
   - API Server: `http://localhost:7001` (or your configured API_PORT)
   - Admin API: `http://localhost:7002` (or your configured ADMIN_PORT)
   - Frontend: `http://localhost:7005` (React UI)

3. **Check authentication flow**:

   ```bash
   # Test login endpoint
   curl -X POST http://localhost:7002/auth/login \
     -H "Content-Type: application/json" \
     -d '{"tenantName": "default", "username": "admin", "password": "admin123"}'

   # Should return JWT token in response
   ```

4. **Common issues**:
   - Database not initialized: Run `yarn migration:run`
   - Missing environment variables: Check `.env.development` file
   - Port conflicts: Verify ports 7000, 7001, 7002, 7005 are available
   - PostgreSQL/Redis not running: Start required services

### Running Tests

Run the test suite:

```bash
yarn test
```

Run tests with coverage:

```bash
yarn test:coverage
```

## Project Structure

```
aidcc-ccczautomationmcpserver/
├── src/                      # Source code
│   ├── config/               # Configuration
│   ├── core/                 # Core interfaces and types
│   ├── infrastructure/       # Infrastructure components
│   │   ├── database/         # Database configuration and entities
│   │   └── script-engine/    # JavaScript VM implementation
│   ├── mcp/                  # MCP-specific components
│   ├── server/               # MCP server implementation
│   ├── services/             # Service implementations
│   ├── utils/                # Utility functions
│   ├── workflow/             # Workflow engine components
│   │   ├── engine/           # Workflow engine implementation
│   │   ├── nodes/            # Node implementations
│   │   └── tools/            # Workflow-based tools
│   └── index.ts              # Entry point
├── test/                     # Tests
│   ├── tools/                # Tool tests
│   ├── workflow/             # Workflow tests
│   └── client/               # Test client
├── scripts/                  # Utility scripts
│   └── create-admin-user.ts  # Script to create default admin user
├── charts/                   # Helm charts for Kubernetes deployment
│   └── ccczautomationmcpserver/ # Main application chart
├── .project-idea/            # Project documentation
├── tsconfig.json             # TypeScript configuration
├── jest.config.js            # Jest configuration
├── .eslintrc.js              # ESLint configuration
├── .prettierrc               # Prettier configuration
├── Dockerfile                # Docker configuration
└── README.md                 # This file
```

## Available Tools

### getDateTime

Returns the current date and time.

**Parameters:**

- `format` (optional): The format of the date and time. Default: "ISO".

**Example:**

```json
{
  "name": "getDateTime",
  "arguments": {
    "format": "ISO"
  }
}
```

**Response:**

```json
{
  "result": "2025-05-14T12:34:56.789Z"
}
```

---

# IAM Permissions

To access your Application's runtime management and observability tools following permission out of the [IAM](https://iam.intranet.cz.o2) system are required:

1. General access (Kubernetes web console, CLI, `ArgoCD`): [K8s Role-Based Access Control](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/RBAC_IAM.md)
2. `Grafana` Metrics: `NETWORK-GRAFANA_CNOC_reader`

# Deployment

`ArgoCD` console of your deployment is to be found here:

- **aidcc-ccczautomationmcpserver-dev**: https://argocd.okd-jzm.network.cz.o2/applications/argocd/aidcc-ccczautomationmcpserver-dev
- **aidcc-ccczautomationmcpserver-prod**: https://argocd.okd-jzm.network.cz.o2/applications/argocd/aidcc-ccczautomationmcpserver-prod

_The `okd-jzm` of the URI needs to be customized to your actual K8s Cluster, see [GitOpsNTW](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/README.md) for list of available targets._

# Metrics & Alerts

**K8s Namespaces Metrics**

- [aidcc-ccczautomationmcpserver-dev](https://grafana.network.o2.cz/d/k8s_views_ns/kubernetes-views-namespaces?orgId=1&refresh=30s&var-datasource=d2836409-3011-42e9-9e5b-3c6cbde1d465&var-resolution=30s&var-created_by=All&var-namespace=aidcc-ccczautomationmcpserver-dev)
- [aidcc-ccczautomationmcpserver-prod](https://grafana.network.o2.cz/d/k8s_views_ns/kubernetes-views-namespaces?orgId=1&refresh=30s&var-datasource=d2836409-3011-42e9-9e5b-3c6cbde1d465&var-resolution=30s&var-created_by=All&var-namespace=aidcc-ccczautomationmcpserver-prod)

**K8s Compute / Resources Metrics**

- [aidcc-ccczautomationmcpserver-dev](https://grafana.network.o2.cz/d/85a562078cdf77779eaa1add43ccec1e/kubernetes-compute-resources-namespace-pods?orgId=1&refresh=10s&var-datasource=d2836409-3011-42e9-9e5b-3c6cbde1d465&var-cluster=&var-namespace=aidcc-ccczautomationmcpserver-dev)
- [aidcc-ccczautomationmcpserver-prod](https://grafana.network.o2.cz/d/85a562078cdf77779eaa1add43ccec1e/kubernetes-compute-resources-namespace-pods?orgId=1&refresh=10s&var-datasource=d2836409-3011-42e9-9e5b-3c6cbde1d465&var-cluster=&var-namespace=aidcc-ccczautomationmcpserver-prod)

**Prometheus Alerts Definitions**

Alerts are only enabled for the `aidcc-ccczautomationmcpserver-prod` deployment (this is by design). You may inspect the configured alerts using OKD Console GUI: `Administrator` / `Observe` / [Alerts](https://console-openshift-console.apps.njzmp.okd.cz.o2/dev-monitoring/ns/aidcc-ccczautomationmcpserver-prod/alerts) (emptying browser caches may be required, `Alt` + `Cmd` + `E` on MacOS) or using [this path](https://console-openshift-console.apps.njzmp.okd.cz.o2/monitoring/alertrules?rowFilter-alerting-rule-source=user&observe-rules=namespace%3Daidcc-ccczautomationmcpserver-prod) from web GUI manually. To see relevant `PrometheusRules` use following **Filter** definition:

| name   | value                                          |
| ------ | ---------------------------------------------- |
| Source | `User`                                         |
| Label  | `namespace=aidcc-ccczautomationmcpserver-prod` |

![OKD_ALERTS_CONSOLE.png](docs/OKD_ALERTS_CONSOLE.png)

**CNFM Console**

All the alerts are propagated into the [CNFM infrastructure](https://cnfm.network.cz.o2/zabbix/zabbix.php?action=dashboard.view). Alerts are routed using the `APP_PROJECT` variable of the deployment `Namespace` (see [GitOps Application Registration](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_REGISTRATION.md) for more information).

---

_This repository was created using the [GitOps](https://network.git.cz.o2/ntwcl/gitopsntw/) CI/CD Pipeline [gitlab-ci-repository.yml](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/cicd/gitlab-ci-repository.yml). For information about the Creation itself please visit the [Repository Properties](/docs/REPOSITORY_PROPERTIES.md) documentation. The pre-deployed CI/CD Pipeline workflow is documented as [CI/CD Pipeline Operations](/docs/PIPELINE.md). Other important destinations within this GitLab Repository are:_

**Local CI/CD Pipeline**

- [Run CI/CD Pipeline](https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver/-/pipelines/new)
- [CI/CD Overview](https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver/-/pipelines)
- [CI/CD Settings](https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver/-/settings/ci_cd)

**Local Registries**

- [Private Helm Chart Registry](https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver/-/packages)
- [Private Docker Image Registry](https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver/container_registry)

**Remote GitOps Documentation**

- [All Docs](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/README.md)
- [Application Workflow](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_WORKFLOW.md)
- [NGINX Demo Application](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/NGINX_DEMO_APPLICATION.md)

---

**To use the Helm Chart Starters for bootstraping your K8s Deployment please refer to the [Helm Starters](/docs/HELM_STARTERS.md) documentation included within this project. The Starter Templates themselves are located here: [charts/starters](/charts/starters).**

---

_To use the Helm Charts packaged in this private GitLab Helm Chart Registry, use the following commands:_

```shell
$ helm repo add --username <DEPLOY_TOKEN_USERNAME> --password <DEPLOY_TOKEN_PASSWORD> aidcc-ccczautomationmcpserver https://network.git.cz.o2/api/v4/projects/1664/packages/helm/stable
$ helm repo update aidcc-ccczautomationmcpserver
$ helm search repo aidcc-ccczautomationmcpserver --versions
```

_To download the NGINX Demo Application dependency Helm Chart (after managing the Helm Chart Registry as shown above) to the `charts/nginx/charts` folder:_

```shell
$ cd charts
$ helm dependency update nginx
$ ls nginx/charts
nginx-15.1.2.tgz
```

_To render the NGINX Demo Application Templates to the newly created `charts/nginx-demo` folder in order to see what the actual `Deployment` will look like:_

```shell
$ cd charts
$ helm template nginx-demo nginx --values nginx/values-dev.yaml --output-dir nginx-demo
```

_To use the Docker Images available in this private GitLab Docker Registry, use the following commands:_

```shell
$ docker login --username <DEPLOY_TOKEN_USERNAME> --password <DEPLOY_TOKEN_PASSWORD> network.git.cz.o2:5005
$ docker pull network.git.cz.o2:5005/deployments/aidcc-ccczautomationmcpserver/nginx:1.23.2-debian-11-r2
```
