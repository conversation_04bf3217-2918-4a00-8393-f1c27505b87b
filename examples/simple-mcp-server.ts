import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import express from 'express';
import cors from 'cors';
import * as crypto from 'crypto';

// Create Express app
const app = express();
app.use(cors());
app.use(express.json());

// Create MCP server
const server = new McpServer({
  name: 'simple-mcp-server',
  version: '1.0.0'
});

// Register a simple echo tool
server.tool(
  'echo',
  {
    type: 'object',
    properties: {
      message: {
        type: 'string',
        description: 'Message to echo back'
      }
    },
    required: ['message']
  },
  async (params) => {
    return {
      content: [
        {
          type: 'text',
          text: `Echo: ${params.message}`
        }
      ]
    };
  }
);

// Create HTTP transport
const transport = new StreamableHTTPServerTransport({
  sessionIdGenerator: () => crypto.randomUUID()
});

// Connect to the MCP server
server.connect(transport);

// Set up Express routes
app.post('/mcp', async (req, res) => {
  await transport.handleRequest(req, res, req.body);
});

app.get('/mcp', async (req, res) => {
  await transport.handleRequest(req, res);
});

app.delete('/mcp', async (req, res) => {
  await transport.handleRequest(req, res);
});

// Start the server
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Simple MCP server listening on port ${PORT}`);
  console.log(`MCP endpoint: http://localhost:${PORT}/mcp`);
});
