/**
 * Example of the STANDARD MCP SDK implementation
 * This shows how tools/call should be handled automatically by the SDK
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { 
  ListToolsRequestSchema, 
  CallToolRequestSchema 
} from '@modelcontextprotocol/sdk/types.js';

// Create MCP server with standard SDK approach
const server = new McpServer({
  name: 'standard-mcp-server',
  version: '1.0.0'
});

// STANDARD WAY: Use setRequestHandler for tools/list
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'echo',
        description: 'Echo back a message',
        inputSchema: {
          type: 'object',
          properties: {
            message: { type: 'string' }
          },
          required: ['message']
        }
      },
      {
        name: 'calculate',
        description: 'Perform basic calculations',
        inputSchema: {
          type: 'object',
          properties: {
            operation: { type: 'string', enum: ['add', 'subtract', 'multiply', 'divide'] },
            a: { type: 'number' },
            b: { type: 'number' }
          },
          required: ['operation', 'a', 'b']
        }
      }
    ]
  };
});

// STANDARD WAY: Use setRequestHandler for tools/call
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  switch (name) {
    case 'echo':
      return {
        content: [
          {
            type: 'text',
            text: `Echo: ${args.message}`
          }
        ]
      };

    case 'calculate':
      const { operation, a, b } = args;
      let result: number;
      
      switch (operation) {
        case 'add':
          result = a + b;
          break;
        case 'subtract':
          result = a - b;
          break;
        case 'multiply':
          result = a * b;
          break;
        case 'divide':
          if (b === 0) {
            return {
              isError: true,
              content: [
                {
                  type: 'text',
                  text: 'Error: Division by zero'
                }
              ]
            };
          }
          result = a / b;
          break;
        default:
          return {
            isError: true,
            content: [
              {
                type: 'text',
                text: `Error: Unknown operation ${operation}`
              }
            ]
          };
      }

      return {
        content: [
          {
            type: 'text',
            text: `Result: ${result}`
          }
        ]
      };

    default:
      return {
        isError: true,
        content: [
          {
            type: 'text',
            text: `Error: Unknown tool ${name}`
          }
        ]
      };
  }
});

// Create transport and connect
const transport = new StreamableHTTPServerTransport({
  sessionIdGenerator: () => crypto.randomUUID()
});

// Connect server to transport
server.connect(transport);

console.log('Standard MCP server started');
console.log('The SDK automatically handles:');
console.log('- tools/list requests');
console.log('- tools/call requests');
console.log('- Protocol validation');
console.log('- Error handling');
console.log('- Session management');

export { server, transport };
