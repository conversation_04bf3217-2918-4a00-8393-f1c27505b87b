#!/usr/bin/env ts-node

/**
 * Multi-tenancy Demo Script
 * 
 * This script demonstrates the multi-tenancy capabilities of the Dynamic MCP Server.
 * It shows how to create tenants, manage users, and work with tenant isolation.
 */

import 'reflect-metadata';
import { configureContainer } from '../src/inversify.config';
import { TYPES } from '../src/types';
import { ITenantManager } from '../src/core/interfaces/ITenantManager';
import { ITenantContext } from '../src/core/interfaces/ITenantContext';
import { ITenantResolver, TenantResolutionStrategy } from '../src/core/interfaces/ITenantResolver';
import { IDatabase } from '../src/core/interfaces/IDatabase';
import { Tenant } from '../src/infrastructure/database/entities/Tenant.entity';
import { TenantUser } from '../src/infrastructure/database/entities/TenantUser.entity';

async function runMultitenancyDemo() {
  console.log('🏢 Dynamic MCP Server - Multi-tenancy Demo\n');

  // Initialize container and services
  const container = configureContainer();
  const database = container.get<IDatabase>(TYPES.Database);
  const tenantManager = container.get<ITenantManager>(TYPES.TenantManager);
  const tenantContext = container.get<ITenantContext>(TYPES.TenantContext);
  const tenantResolver = container.get<ITenantResolver>(TYPES.TenantResolver);

  try {
    // Initialize database
    console.log('📊 Initializing database...');
    await database.initialize();
    console.log('✅ Database initialized\n');

    // Demo 1: Create tenants
    console.log('🏗️  Demo 1: Creating Tenants');
    console.log('================================');

    const tenant1 = await tenantManager.createTenant({
      name: 'acme-corp',
      displayName: 'ACME Corporation',
      createdBy: 'system-admin',
      settings: {
        ...Tenant.getDefaultSettings(),
        max_concurrent_workflows: 50,
        enable_custom_functions: true
      }
    });

    const tenant2 = await tenantManager.createTenant({
      name: 'beta-inc',
      displayName: 'Beta Inc.',
      createdBy: 'system-admin',
      settings: {
        ...Tenant.getDefaultSettings(),
        max_concurrent_workflows: 20,
        enable_custom_functions: false
      }
    });

    console.log(`✅ Created tenant: ${tenant1.name} (${tenant1.id})`);
    console.log(`✅ Created tenant: ${tenant2.name} (${tenant2.id})\n`);

    // Demo 2: Create users for each tenant
    console.log('👥 Demo 2: Creating Tenant Users');
    console.log('==================================');

    const user1 = await tenantManager.createTenantUser(tenant1.id, {
      username: 'john.doe',
      email: '<EMAIL>',
      passwordHash: 'password123',
      roles: [TenantUser.getAdminRole()]
    });

    const user2 = await tenantManager.createTenantUser(tenant1.id, {
      username: 'jane.smith',
      email: '<EMAIL>',
      passwordHash: 'password456',
      roles: [TenantUser.getUserRole()]
    });

    const user3 = await tenantManager.createTenantUser(tenant2.id, {
      username: 'bob.wilson',
      email: '<EMAIL>',
      passwordHash: 'password789',
      roles: [TenantUser.getAdminRole()]
    });

    console.log(`✅ Created user: ${user1.username} in ${tenant1.name} (Admin)`);
    console.log(`✅ Created user: ${user2.username} in ${tenant1.name} (User)`);
    console.log(`✅ Created user: ${user3.username} in ${tenant2.name} (Admin)\n`);

    // Demo 3: Tenant context management
    console.log('🔄 Demo 3: Tenant Context Management');
    console.log('=====================================');

    await tenantContext.withTenant(tenant1, async () => {
      const currentTenant = tenantContext.getCurrentTenant();
      console.log(`✅ Current tenant in context: ${currentTenant?.name}`);
      
      await tenantContext.withUser(user1, async () => {
        const currentUser = tenantContext.getCurrentUser();
        console.log(`✅ Current user in context: ${currentUser?.username}`);
        console.log(`✅ User has admin permissions: ${tenantContext.hasPermission('*', 'write')}`);
      });
    });

    await tenantContext.withTenant(tenant2, async () => {
      const currentTenant = tenantContext.getCurrentTenant();
      console.log(`✅ Switched to tenant: ${currentTenant?.name}`);
    });

    console.log();

    // Demo 4: Tenant resolution
    console.log('🔍 Demo 4: Tenant Resolution');
    console.log('=============================');

    // Mock request objects for different resolution strategies
    const mockRequests = [
      {
        headers: { 'x-tenant-id': 'acme-corp' },
        hostname: 'api.example.com',
        path: '/api/workflows'
      },
      {
        headers: {},
        hostname: 'acme-corp.example.com',
        path: '/api/workflows'
      },
      {
        headers: {},
        hostname: 'api.example.com',
        path: '/tenant/beta-inc/workflows'
      }
    ];

    // Test header-based resolution
    tenantResolver.setStrategy(TenantResolutionStrategy.HEADER);
    const resolvedTenant1 = await tenantResolver.resolveFromHeader('acme-corp');
    console.log(`✅ Header resolution: ${resolvedTenant1?.name}`);

    // Test subdomain-based resolution
    tenantResolver.setStrategy(TenantResolutionStrategy.SUBDOMAIN);
    const resolvedTenant2 = await tenantResolver.resolveFromSubdomain('acme-corp.example.com');
    console.log(`✅ Subdomain resolution: ${resolvedTenant2?.name}`);

    // Test path-based resolution
    tenantResolver.setStrategy(TenantResolutionStrategy.PATH);
    const resolvedTenant3 = await tenantResolver.resolveFromPath('/tenant/beta-inc/workflows');
    console.log(`✅ Path resolution: ${resolvedTenant3?.name}\n`);

    // Demo 5: Authentication
    console.log('🔐 Demo 5: User Authentication');
    console.log('===============================');

    const authenticatedUser = await tenantManager.authenticateUser(
      tenant1.id,
      'john.doe',
      'password123'
    );

    if (authenticatedUser) {
      console.log(`✅ Authentication successful: ${authenticatedUser.username}`);
      console.log(`✅ Last login: ${authenticatedUser.lastLoginAt}`);
    }

    const failedAuth = await tenantManager.authenticateUser(
      tenant1.id,
      'john.doe',
      'wrongpassword'
    );

    console.log(`✅ Authentication with wrong password: ${failedAuth ? 'Success' : 'Failed'}\n`);

    // Demo 6: Tenant listing and management
    console.log('📋 Demo 6: Tenant Management');
    console.log('=============================');

    const tenantList = await tenantManager.listTenants({
      page: 1,
      limit: 10,
      sortBy: 'name',
      sortOrder: 'ASC'
    });

    console.log(`✅ Total tenants: ${tenantList.total}`);
    tenantList.data.forEach(tenant => {
      console.log(`   - ${tenant.name}: ${tenant.displayName} (${tenant.enabled ? 'Enabled' : 'Disabled'})`);
    });

    const userList = await tenantManager.getTenantUsers(tenant1.id);
    console.log(`✅ Users in ${tenant1.name}: ${userList.total}`);
    userList.data.forEach(user => {
      console.log(`   - ${user.username}: ${user.email} (${user.roles.map(r => r.name).join(', ')})`);
    });

    console.log();

    // Demo 7: Settings validation
    console.log('⚙️  Demo 7: Settings Validation');
    console.log('================================');

    const validSettings = Tenant.getDefaultSettings();
    const validation1 = await tenantManager.validateTenantSettings(validSettings);
    console.log(`✅ Valid settings validation: ${validation1.isValid}`);

    const invalidSettings = {
      ...validSettings,
      max_concurrent_workflows: -1,
      session_timeout: 0
    };
    const validation2 = await tenantManager.validateTenantSettings(invalidSettings);
    console.log(`✅ Invalid settings validation: ${validation2.isValid}`);
    if (!validation2.isValid) {
      console.log(`   Errors: ${validation2.errors.join(', ')}`);
    }

    console.log('\n🎉 Multi-tenancy demo completed successfully!');
    console.log('\nKey Features Demonstrated:');
    console.log('- ✅ Tenant creation and management');
    console.log('- ✅ User management per tenant');
    console.log('- ✅ Tenant context propagation');
    console.log('- ✅ Multiple tenant resolution strategies');
    console.log('- ✅ User authentication and authorization');
    console.log('- ✅ Settings validation');
    console.log('- ✅ Complete tenant isolation');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    // Clean up
    console.log('\n🧹 Cleaning up demo data...');
    try {
      await database.query('DELETE FROM mcp_tenant_users');
      await database.query('DELETE FROM mcp_tenants');
      console.log('✅ Demo data cleaned up');
    } catch (error) {
      console.error('❌ Cleanup failed:', error);
    }

    await database.close();
    console.log('✅ Database connection closed');
  }
}

// Run the demo
if (require.main === module) {
  runMultitenancyDemo().catch(console.error);
}

export { runMultitenancyDemo };
