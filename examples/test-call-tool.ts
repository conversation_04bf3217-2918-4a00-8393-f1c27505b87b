/**
 * Test script to demonstrate the new callTool functionality
 * This shows how MCP clients can use the standard callTool method
 */

interface CallToolResponse {
  jsonrpc: string;
  result?: any;
  error?: {
    code: number;
    message: string;
  };
  id: number | null;
}

class MCPClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3000/mcp') {
    this.baseUrl = baseUrl;
  }

  /**
   * Call a tool on the MCP server using the standard callTool method
   */
  async callTool(tool: string, args: Record<string, unknown>): Promise<any> {
    console.log(`[MCP SDK] Calling tool: ${tool} with arguments: ${JSON.stringify(args)}`);

    // Debug: Log the exact request being sent
    const request = {
      name: tool,
      arguments: args,
    };
    console.log(`[MCP SDK] Request object: ${JSON.stringify(request, null, 2)}`);

    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: tool,
          arguments: args
        },
        id: Math.floor(Math.random() * 1000)
      })
    });

    const data: CallToolResponse = await response.json();
    console.log(`[MCP SDK] Raw result: ${JSON.stringify(data, null, 2)}`);

    if (data.error) {
      throw new Error(`Tool call failed: ${data.error.message}`);
    }

    console.log(`[MCP SDK] Tool call successful: ${tool}`);
    return data.result;
  }

  /**
   * List available tools
   */
  async listTools(): Promise<any[]> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/list',
        params: {},
        id: Math.floor(Math.random() * 1000)
      })
    });

    const data = await response.json();
    
    if (data.error) {
      throw new Error(`Failed to list tools: ${data.error.message}`);
    }

    return data.result.tools;
  }
}

// Test the functionality
async function testCallTool() {
  const client = new MCPClient();

  try {
    console.log('=== Testing MCP callTool functionality ===\n');

    // First, list available tools
    console.log('1. Listing available tools...');
    const tools = await client.listTools();
    console.log(`Found ${tools.length} tools:`);
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });
    console.log();

    // Test calling a tool (assuming there's an echo or similar tool available)
    if (tools.length > 0) {
      const firstTool = tools[0];
      console.log(`2. Testing callTool with: ${firstTool.name}`);
      
      // Try to call the first available tool with some test arguments
      const testArgs = { message: 'Hello from callTool test!' };
      
      try {
        const result = await client.callTool(firstTool.name, testArgs);
        console.log('✅ Tool call successful!');
        console.log('Result:', result);
      } catch (error) {
        console.log('❌ Tool call failed:', error instanceof Error ? error.message : error);
      }
    } else {
      console.log('No tools available to test');
    }

  } catch (error) {
    console.error('Test failed:', error instanceof Error ? error.message : error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testCallTool().catch(console.error);
}

export { MCPClient, testCallTool };
