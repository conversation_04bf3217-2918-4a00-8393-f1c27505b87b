import fetch from 'node-fetch';

/**
 * Test the custom tools/list response
 */
async function testCustomToolsList(): Promise<void> {
  const baseUrl = 'http://localhost:3000/mcp';
  
  try {
    // Initialize the MCP session
    console.log('Initializing MCP session...');
    const initResponse = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          clientName: 'test-client',
          clientVersion: '1.0.0'
        },
        id: 1
      })
    });
    
    const initData = await initResponse.json();
    console.log('Initialize response:', JSON.stringify(initData, null, 2));
    
    // Get the session ID from the response headers
    const sessionId = initResponse.headers.get('mcp-session-id');
    if (!sessionId) {
      throw new Error('No session ID returned');
    }
    
    console.log(`Session ID: ${sessionId}`);
    
    // List available tools
    console.log('\nListing available tools...');
    const listResponse = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': sessionId
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/list',
        params: {},
        id: 2
      })
    });
    
    const listData = await listResponse.json();
    console.log('Tools list response:', JSON.stringify(listData, null, 2));
    
    // Test echo tool
    console.log('\nTesting echo tool...');
    const echoResponse = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': sessionId
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'echo',
          parameters: {
            message: 'Hello, MCP!'
          }
        },
        id: 3
      })
    });
    
    const echoData = await echoResponse.json();
    console.log('Echo tool response:', JSON.stringify(echoData, null, 2));
    
    // Test getDateTime tool
    console.log('\nTesting getDateTime tool...');
    const dateTimeResponse = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'mcp-session-id': sessionId
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'getDateTime',
          parameters: {
            format: 'UTC'
          }
        },
        id: 4
      })
    });
    
    const dateTimeData = await dateTimeResponse.json();
    console.log('getDateTime tool response:', JSON.stringify(dateTimeData, null, 2));
    
    // Close the session
    console.log('\nClosing session...');
    const closeResponse = await fetch(baseUrl, {
      method: 'DELETE',
      headers: {
        'mcp-session-id': sessionId
      }
    });
    
    console.log('Session closed with status:', closeResponse.status);
  } catch (error) {
    console.error('Error testing custom tools list:', error);
  }
}

// Run the test
testCustomToolsList();
