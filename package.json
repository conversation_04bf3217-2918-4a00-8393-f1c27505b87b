{"name": "aidcc-ccczautomationmcpserver", "version": "1.5.0", "main": "dist/index.js", "repository": "https://network.git.cz.o2/deployments/aidcc-ccczautomationmcpserver.git", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"build": "yarn tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "dev:migrate": "cross-env DB_MIGRATIONS_RUN=true ts-node src/index.ts", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "yarn typeorm -- migration:generate -d src/infrastructure/database/config.ts", "migration:run": "yarn typeorm -- migration:run -d src/infrastructure/database/config.ts", "migration:revert": "yarn typeorm -- migration:revert -d src/infrastructure/database/config.ts", "create-sample": "ts-node scripts/create-sample-function.ts", "create-sample-workflow": "ts-node scripts/create-sample-workflow.ts", "test-workflow": "ts-node scripts/test-workflow.ts", "create-db": "ts-node scripts/create-database.ts", "create-admin": "node -r ts-node/register scripts/create-admin-user.ts", "frontend:dev": "cd frontend && yarn dev", "frontend:build": "cd frontend && yarn build", "frontend:install": "cd frontend && yarn install", "setup": "yarn create-db && yarn migration:run && yarn create-sample && yarn create-sample-workflow", "semantic-release": "semantic-release"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.1.0", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.18", "@types/supertest": "^6.0.3", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "cross-env": "^7.0.3", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "prettier": "^3.5.3", "semantic-release": "^24.0.0", "supertest": "^7.1.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "@types/cors": "^2.8.18", "@types/jsonpath-plus": "^5.0.5", "@types/node-fetch": "2", "@types/pg": "^8.15.2", "@types/socket.io": "^3.0.2", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "inversify": "^6.0.2", "jsonpath-plus": "^10.3.0", "jsonwebtoken": "^9.0.2", "node-fetch": "2", "pg": "^8.11.3", "pino": "^8.19.0", "pino-pretty": "^11.0.0", "prom-client": "^15.1.3", "redis": "^4.6.13", "reflect-metadata": "^0.2.1", "typeorm": "^0.3.20", "vm2": "^3.9.19", "ws": "^8.16.0", "zod": "^3.24.4"}}