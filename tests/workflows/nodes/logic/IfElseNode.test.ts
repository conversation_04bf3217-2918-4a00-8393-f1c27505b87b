import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { IfElseNode } from '../../../../src/workflows/nodes/logic/IfElseNode';
import { IConditionalProcessor, LogicResult } from '../../../../src/core/interfaces/IConditionalProcessor';
import { ILogger } from '../../../../src/core/interfaces/ILogger';
import { WorkflowContext } from '../../../../src/core/types/WorkflowContext';

describe('IfElseNode', () => {
  let ifElseNode: IfElseNode;
  let mockConditionalProcessor: jest.Mocked<IConditionalProcessor>;
  let mockLogger: jest.Mocked<ILogger>;

  beforeEach(() => {
    mockConditionalProcessor = {
      evaluateCondition: jest.fn(),
      routeExecution: jest.fn(),
      evaluateMultipleConditions: jest.fn(),
      createEvaluationContext: jest.fn(),
      validateExpression: jest.fn(),
      registerConditionFunction: jest.fn(),
      getAvailableOperators: jest.fn(),
      optimizeExpression: jest.fn()
    } as jest.Mocked<IConditionalProcessor>;

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as jest.Mocked<ILogger>;

    const config = {
      id: 'if-else-1',
      condition: {
        type: 'simple' as const,
        field: 'input.value',
        operator: 'equals' as const,
        value: 'test'
      },
      trueNodes: ['node-true'],
      falseNodes: ['node-false']
    };

    ifElseNode = new IfElseNode(config, mockLogger, mockConditionalProcessor);
  });

  describe('execute', () => {
    it('should execute true branch when condition is true', async () => {
      const input = { value: 'test' };
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input,
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date(),
        conditionalResults: new Map(),
        executionPath: []
      };

      const mockLogicResult: LogicResult = {
        result: true,
        evaluatedConditions: { 'input.value': true },
        executionPath: ['condition_true'],
        metadata: {}
      };

      mockConditionalProcessor.validateExpression.mockResolvedValue({
        valid: true,
        errors: [],
        warnings: []
      });

      mockConditionalProcessor.evaluateCondition.mockResolvedValue(mockLogicResult);

      const result = await ifElseNode.execute(input, context);

      expect(result._routing.condition).toBe(true);
      expect(result._routing.nextNodes).toEqual(['node-true']);
      expect(context.conditionalResults?.has('input.value_equals_"test"')).toBe(true);
      expect(context.executionPath).toContain('if_else_input.value_equals_"test"_true');
    });

    it('should execute false branch when condition is false', async () => {
      const input = { value: 'other' };
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input,
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date(),
        conditionalResults: new Map(),
        executionPath: []
      };

      const mockLogicResult: LogicResult = {
        result: false,
        evaluatedConditions: { 'input.value': false },
        executionPath: ['condition_false'],
        metadata: {}
      };

      mockConditionalProcessor.validateExpression.mockResolvedValue({
        valid: true,
        errors: [],
        warnings: []
      });

      mockConditionalProcessor.evaluateCondition.mockResolvedValue(mockLogicResult);

      const result = await ifElseNode.execute(input, context);

      expect(result._routing.condition).toBe(false);
      expect(result._routing.nextNodes).toEqual(['node-false']);
    });

    it('should handle validation errors', async () => {
      const input = { value: 'test' };
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input,
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      mockConditionalProcessor.validateExpression.mockResolvedValue({
        valid: false,
        errors: ['Invalid condition'],
        warnings: []
      });

      await expect(ifElseNode.execute(input, context)).rejects.toThrow('Invalid condition: Invalid condition');
    });

    it('should add error to context on failure', async () => {
      const input = { value: 'test' };
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input,
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      mockConditionalProcessor.validateExpression.mockRejectedValue(new Error('Validation failed'));

      await expect(ifElseNode.execute(input, context)).rejects.toThrow('Validation failed');
      expect(context.errorContext?.errorHistory).toHaveLength(1);
      expect(context.errorContext?.errorHistory[0].error.message).toBe('Validation failed');
    });
  });

  describe('evaluateLogic', () => {
    it('should evaluate condition and return logic result', async () => {
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'workflow-1',
        input: { value: 'test' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      const mockLogicResult: LogicResult = {
        result: true,
        evaluatedConditions: { 'input.value': true },
        executionPath: ['condition_evaluated'],
        metadata: {}
      };

      mockConditionalProcessor.validateExpression.mockResolvedValue({
        valid: true,
        errors: [],
        warnings: []
      });

      mockConditionalProcessor.evaluateCondition.mockResolvedValue(mockLogicResult);

      const result = await ifElseNode.evaluateLogic(context);

      expect(result).toEqual(mockLogicResult);
      expect(mockConditionalProcessor.validateExpression).toHaveBeenCalledWith(ifElseNode.condition);
      expect(mockConditionalProcessor.evaluateCondition).toHaveBeenCalledWith(ifElseNode.condition, context);
    });
  });

  describe('getNextNodes', () => {
    it('should return true nodes when condition is true', async () => {
      const logicResult: LogicResult = {
        result: true,
        evaluatedConditions: {},
        executionPath: [],
        metadata: {}
      };

      const nextNodes = await ifElseNode.getNextNodes(logicResult);

      expect(nextNodes).toEqual(['node-true']);
    });

    it('should return false nodes when condition is false', async () => {
      const logicResult: LogicResult = {
        result: false,
        evaluatedConditions: {},
        executionPath: [],
        metadata: {}
      };

      const nextNodes = await ifElseNode.getNextNodes(logicResult);

      expect(nextNodes).toEqual(['node-false']);
    });
  });

  describe('configuration validation', () => {
    it('should throw error for missing condition', () => {
      expect(() => {
        new IfElseNode(
          {
            id: 'test',
            condition: null as any,
            trueNodes: ['node-true'],
            falseNodes: ['node-false']
          },
          mockLogger,
          mockConditionalProcessor
        );
      }).toThrow('IF/ELSE node requires a condition');
    });

    it('should throw error for missing condition field', () => {
      expect(() => {
        new IfElseNode(
          {
            id: 'test',
            condition: {
              type: 'simple' as const,
              field: '',
              operator: 'equals' as const,
              value: 'test'
            },
            trueNodes: ['node-true'],
            falseNodes: ['node-false']
          },
          mockLogger,
          mockConditionalProcessor
        );
      }).toThrow('IF/ELSE condition requires a field');
    });

    it('should throw error for missing branches', () => {
      expect(() => {
        new IfElseNode(
          {
            id: 'test',
            condition: {
              type: 'simple' as const,
              field: 'input.value',
              operator: 'equals' as const,
              value: 'test'
            },
            trueNodes: null as any,
            falseNodes: null as any
          },
          mockLogger,
          mockConditionalProcessor
        );
      }).toThrow('IF/ELSE node requires at least one branch');
    });
  });

  describe('utility methods', () => {
    it('should return correct config summary', () => {
      const summary = ifElseNode.getConfigSummary();

      expect(summary).toEqual({
        type: 'if_else',
        condition: {
          field: 'input.value',
          operator: 'equals',
          value: 'test',
          type: 'simple'
        },
        branches: {
          trueNodes: 1,
          falseNodes: 1
        },
        description: undefined
      });
    });

    it('should indicate cannot execute in parallel', () => {
      expect(ifElseNode.canExecuteInParallel()).toBe(false);
    });

    it('should return estimated execution time', () => {
      const time = ifElseNode.getEstimatedExecutionTime();
      expect(time).toBe(10);
    });

    it('should return resource requirements', () => {
      const requirements = ifElseNode.getResourceRequirements();
      expect(requirements).toEqual({
        memory: 1024 * 1024,
        cpu: 5,
        timeout: 5000
      });
    });
  });
});
