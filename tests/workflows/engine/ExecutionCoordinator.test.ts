import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ExecutionCoordinator } from '../../../src/workflows/engine/ExecutionCoordinator';
import { IParallelExecutor } from '../../../src/core/interfaces/IParallelExecutor';
import { IConditionalProcessor } from '../../../src/core/interfaces/IConditionalProcessor';
import { INodeRegistry } from '../../../src/core/interfaces/INodeRegistry';
import { ILogger } from '../../../src/core/interfaces/ILogger';
import { WorkflowContext } from '../../../src/core/types/WorkflowContext';
import { WorkflowDefinition } from '../../../src/core/interfaces/IExecutionCoordinator';

describe('ExecutionCoordinator', () => {
  let executionCoordinator: ExecutionCoordinator;
  let mockParallelExecutor: jest.Mocked<IParallelExecutor>;
  let mockConditionalProcessor: jest.Mocked<IConditionalProcessor>;
  let mockNodeRegistry: jest.Mocked<INodeRegistry>;
  let mockLogger: jest.Mocked<ILogger>;

  beforeEach(() => {
    mockParallelExecutor = {
      executeBranches: jest.fn(),
      synchronizeBranches: jest.fn(),
      allocateResources: jest.fn(),
      getExecutionStatus: jest.fn(),
      cancelExecution: jest.fn(),
      handleBranchFailure: jest.fn(),
      cleanupResources: jest.fn()
    } as jest.Mocked<IParallelExecutor>;

    mockConditionalProcessor = {
      evaluateCondition: jest.fn(),
      routeExecution: jest.fn(),
      evaluateMultipleConditions: jest.fn(),
      createEvaluationContext: jest.fn(),
      validateExpression: jest.fn(),
      registerConditionFunction: jest.fn(),
      getAvailableOperators: jest.fn(),
      optimizeExpression: jest.fn()
    } as jest.Mocked<IConditionalProcessor>;

    mockNodeRegistry = {
      registerNode: jest.fn(),
      getNode: jest.fn(),
      hasNode: jest.fn(),
      getRegisteredTypes: jest.fn()
    } as jest.Mocked<INodeRegistry>;

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as jest.Mocked<ILogger>;

    executionCoordinator = new ExecutionCoordinator(
      mockParallelExecutor,
      mockConditionalProcessor,
      mockNodeRegistry,
      mockLogger
    );
  });

  describe('executeWorkflow', () => {
    it('should execute a simple sequential workflow', async () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        version: 1,
        nodes: [
          {
            id: 'node1',
            type: 'javascript',
            name: 'Node 1',
            config: { code: 'return input;' }
          }
        ],
        edges: []
      };

      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'test-workflow',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      const mockNode = {
        execute: jest.fn().mockResolvedValue({ result: 'success' })
      };

      mockNodeRegistry.getNode.mockReturnValue(mockNode);

      const result = await executionCoordinator.executeWorkflow(workflow, context);

      expect(result.status).toBe('completed');
      expect(result.executionId).toBe('exec-1');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'Starting workflow execution',
        expect.objectContaining({
          workflowId: 'test-workflow',
          executionId: 'exec-1'
        })
      );
    });

    it('should handle workflow execution errors', async () => {
      const workflow: WorkflowDefinition = {
        id: 'test-workflow',
        name: 'Test Workflow',
        version: 1,
        nodes: [
          {
            id: 'node1',
            type: 'javascript',
            name: 'Node 1',
            config: { code: 'throw new Error("Test error");' }
          }
        ],
        edges: []
      };

      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'test-workflow',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      const mockNode = {
        execute: jest.fn().mockRejectedValue(new Error('Test error'))
      };

      mockNodeRegistry.getNode.mockReturnValue(mockNode);

      const result = await executionCoordinator.executeWorkflow(workflow, context);

      expect(result.status).toBe('failed');
      expect(result.error).toBeInstanceOf(Error);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Workflow execution failed',
        expect.objectContaining({
          workflowId: 'test-workflow',
          executionId: 'exec-1'
        })
      );
    });
  });

  describe('executeParallel', () => {
    it('should execute nodes in parallel', async () => {
      const nodes = [
        {
          id: 'node1',
          type: 'javascript',
          name: 'Node 1',
          config: { code: 'return "result1";' }
        },
        {
          id: 'node2',
          type: 'javascript',
          name: 'Node 2',
          config: { code: 'return "result2";' }
        }
      ];

      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'test-workflow',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date()
      };

      const mockBranchResults = [
        {
          branchId: 'branch_0',
          nodeResults: { node1: 'result1' },
          status: 'completed' as const,
          executionTime: 100
        },
        {
          branchId: 'branch_1',
          nodeResults: { node2: 'result2' },
          status: 'completed' as const,
          executionTime: 150
        }
      ];

      mockParallelExecutor.executeBranches.mockResolvedValue(mockBranchResults);

      const result = await executionCoordinator.executeParallel(nodes, context);

      expect(result.successCount).toBe(2);
      expect(result.failureCount).toBe(0);
      expect(result.totalExecutionTime).toBe(150);
      expect(mockParallelExecutor.executeBranches).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            id: 'branch_0',
            nodeIds: ['node1']
          }),
          expect.objectContaining({
            id: 'branch_1',
            nodeIds: ['node2']
          })
        ]),
        context
      );
    });
  });

  describe('evaluateCondition', () => {
    it('should evaluate a condition and cache the result', async () => {
      const condition = {
        type: 'simple' as const,
        field: 'input.value',
        operator: 'equals' as const,
        value: 'test'
      };

      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'test-workflow',
        input: { value: 'test' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date(),
        conditionalResults: new Map()
      };

      const mockLogicResult = {
        result: true,
        evaluatedConditions: { 'input.value': true },
        executionPath: ['condition_evaluated'],
        metadata: {}
      };

      mockConditionalProcessor.evaluateCondition.mockResolvedValue(mockLogicResult);

      const result = await executionCoordinator.evaluateCondition(condition, context);

      expect(result).toBe(true);
      expect(context.conditionalResults?.has('input.value_equals_"test"')).toBe(true);
      expect(context.conditionalResults?.get('input.value_equals_"test"')).toBe(true);
    });
  });

  describe('planExecution', () => {
    it('should create execution plan for sequential nodes', async () => {
      const nodes = [
        {
          id: 'node1',
          type: 'javascript',
          name: 'Node 1',
          config: {}
        },
        {
          id: 'node2',
          type: 'javascript',
          name: 'Node 2',
          config: {}
        }
      ];

      const edges = [
        {
          source: 'node1',
          target: 'node2'
        }
      ];

      const executionPlan = await executionCoordinator.planExecution(nodes, edges);

      expect(executionPlan).toHaveLength(2);
      expect(executionPlan[0].nodeIds).toContain('node1');
      expect(executionPlan[1].nodeIds).toContain('node2');
    });

    it('should identify parallel execution opportunities', async () => {
      const nodes = [
        {
          id: 'node1',
          type: 'javascript',
          name: 'Node 1',
          config: {}
        },
        {
          id: 'node2',
          type: 'javascript',
          name: 'Node 2',
          config: {}
        },
        {
          id: 'node3',
          type: 'javascript',
          name: 'Node 3',
          config: {}
        }
      ];

      const edges = [
        {
          source: 'node1',
          target: 'node3'
        },
        {
          source: 'node2',
          target: 'node3'
        }
      ];

      const executionPlan = await executionCoordinator.planExecution(nodes, edges);

      // Should have parallel execution for node1 and node2, then node3
      expect(executionPlan.length).toBeGreaterThan(0);
      
      // First branch should contain the start nodes (node1 and node2)
      const firstBranch = executionPlan[0];
      expect(firstBranch.nodeIds).toEqual(expect.arrayContaining(['node1', 'node2']));
    });
  });

  describe('handleExecutionError', () => {
    it('should retry on recoverable errors', async () => {
      const error = new Error('Temporary failure');
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'test-workflow',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date(),
        metadata: { retryCount: 3 }
      };

      const shouldRetry = await executionCoordinator.handleExecutionError(error, context, 1);

      expect(shouldRetry).toBe(true);
      expect(context.errorContext?.retryAttempts).toBe(1);
      expect(context.errorContext?.lastError).toBe(error);
    });

    it('should not retry after max attempts', async () => {
      const error = new Error('Persistent failure');
      const context: WorkflowContext = {
        executionId: 'exec-1',
        workflowId: 'test-workflow',
        input: { test: 'data' },
        output: null,
        nodeResults: {},
        variables: {},
        startedAt: new Date(),
        metadata: { retryCount: 3 }
      };

      const shouldRetry = await executionCoordinator.handleExecutionError(error, context, 3);

      expect(shouldRetry).toBe(false);
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Max retries exceeded',
        expect.objectContaining({
          executionId: 'exec-1',
          retryCount: 3
        })
      );
    });
  });
});
